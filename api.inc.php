<?php
// +----------------------------------------------------------------------
// | Copyright:    (c) 2014-2016 http://www.moqu8.com All rights reserved.
// +----------------------------------------------------------------------
// | Developer:    魔趣吧源码论坛 (www.moqu8.com 请收藏备用!)
// +----------------------------------------------------------------------
// | Author:       by 魔趣吧. 技术支持/更新维护：QQ 1218894030
// +----------------------------------------------------------------------
// | 异步API接口文件
// +----------------------------------------------------------------------

if(!defined('IN_DISCUZ')) {
    exit('Access Denied');
}

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');

include_once ('db.class.php');
require_once dirname(__FILE__) . '/config.inc.php';
include_once ('function.func.php');

/**
 * API响应类
 */
class HejinFormsAPI {
    
    /**
     * 发送JSON响应
     */
    public static function response($success = true, $message = '', $data = null, $code = 200) {
        $response = [
            'success' => $success,
            'message' => $message,
            'data' => $data,
            'code' => $code,
            'timestamp' => time()
        ];
        
        echo json_encode($response, JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    /**
     * 验证表单字段
     */
    public static function validateField($fieldId, $value, $config) {
        $errors = [];
        
        // 必填验证
        if (!empty($config['GROUP_MUST']) && (empty($value) || $value === null)) {
            $errors[] = '此字段为必填项';
        }
        
        // 类型验证
        if (!empty($value) && !empty($config['GROUP_VERIFY'])) {
            switch ($config['GROUP_VERIFY']) {
                case 'number':
                    if (!preg_match('/^[0-9]*$/', $value)) {
                        $errors[] = '请输入有效的数字';
                    }
                    break;
                case 'email':
                    if (!filter_var($value, FILTER_VALIDATE_EMAIL)) {
                        $errors[] = '请输入有效的邮箱地址';
                    }
                    break;
                case 'mobile':
                    if (!preg_match('/^\d{11}$/', $value)) {
                        $errors[] = '请输入有效的手机号码';
                    }
                    break;
                case 'idcard':
                    if (!preg_match('/^\d{15}$|\d{17}([0-9]|X)$/', $value)) {
                        $errors[] = '请输入有效的身份证号码';
                    }
                    break;
            }
        }
        
        return $errors;
    }
    
    /**
     * 处理文件上传
     */
    public static function handleFileUpload($fieldName) {
        if (!isset($_FILES[$fieldName]) || $_FILES[$fieldName]['error'] !== UPLOAD_ERR_OK) {
            return ['success' => false, 'message' => '文件上传失败'];
        }
        
        $file = $_FILES[$fieldName];
        $allowedTypes = ['jpg', 'jpeg', 'gif', 'png', 'doc', 'dot', 'docx', 'xls', 'xlt', 'xlsx', 'wps'];
        $fileParts = pathinfo($file['name']);
        $extension = strtolower($fileParts['extension']);
        
        if (!in_array($extension, $allowedTypes)) {
            return ['success' => false, 'message' => '不支持的文件类型'];
        }
        
        $fileName = date('mdHis') . '-' . rand(100, 999) . '.' . $extension;
        $uploadDir = 'data/uploads/submit';
        $targetFolder = HEJIN_ROOT . '/' . $uploadDir;
        
        if (!is_dir($targetFolder)) {
            mkdir($targetFolder, 0777, true);
        }
        
        $targetPath = $targetFolder . '/' . $fileName;
        
        if (move_uploaded_file($file['tmp_name'], $targetPath)) {
            return [
                'success' => true, 
                'message' => '文件上传成功',
                'data' => [
                    'filename' => $fileName,
                    'path' => $uploadDir . '/' . $fileName,
                    'size' => $file['size'],
                    'type' => $file['type']
                ]
            ];
        } else {
            return ['success' => false, 'message' => '文件保存失败'];
        }
    }
    
    /**
     * 获取表单信息
     */
    public static function getFormInfo($formId) {
        global $_G;
        
        $form = C::t('#hejin_forms#hejin_form')->fetch_by_id($formId);
        if (!$form) {
            self::response(false, '表单不存在', null, 404);
        }
        
        $groups = C::t('#hejin_forms#hejin_group')->fetch_by_fida($formId);
        
        // 处理表单字段配置
        foreach ($groups as $key => $group) {
            $groups[$key]['config'] = fix_json($group['config']);
            
            // 获取选项数据
            if (in_array($group['type'], ['radio', 'checkbox', 'select'])) {
                $groups[$key]['options'] = Hejin::_option_lists($group['id']);
            }
        }
        
        $data = [
            'form' => $form,
            'groups' => $groups,
            'user' => [
                'uid' => $_G['uid'],
                'username' => $_G['username'],
                'logo' => $_G['setting']['ucenterurl'] . '/avatar.php?uid=' . $_G['uid'] . '&size=small'
            ]
        ];
        
        self::response(true, '获取成功', $data);
    }
    
    /**
     * 验证单个字段
     */
    public static function validateSingleField() {
        global $_G;
        
        $fieldId = intval($_POST['field_id']);
        $value = $_POST['value'] ?? '';
        $formId = intval($_POST['form_id']);
        
        // 获取字段配置
        $group = C::t('#hejin_forms#hejin_group')->fetch_by_id($fieldId);
        if (!$group) {
            self::response(false, '字段不存在');
        }
        
        $config = fix_json($group['config']);
        $errors = self::validateField($fieldId, $value, $config);
        
        if (empty($errors)) {
            self::response(true, '验证通过');
        } else {
            self::response(false, implode(', ', $errors));
        }
    }
    
    /**
     * 提交表单
     */
    public static function submitForm() {
        global $_G;
        
        if (!submitcheck('commit')) {
            self::response(false, '表单验证失败');
        }
        
        $formId = intval($_POST['fid']);
        $form = C::t('#hejin_forms#hejin_form')->fetch_by_id($formId);
        
        if (!$form) {
            self::response(false, '表单不存在');
        }
        
        // 检查表单状态和时间
        if ($form['start'] >= time() || $form['expire'] <= time() || $form['state'] <= 0) {
            self::response(false, '表单已过期或未开始');
        }
        
        $groups = C::t('#hejin_forms#hejin_group')->fetch_by_fid($formId);
        $config = [];
        $errors = [];
        
        // 处理表单数据
        foreach ($groups as $gid => $group) {
            $postName = 'G-' . $group['id'];
            $groupConfig = fix_json($group['config']);
            
            if ($group['type'] == 'file') {
                if (isset($_FILES[$postName]) && $_FILES[$postName]['error'] == 0) {
                    $uploadResult = self::handleFileUpload($postName);
                    if ($uploadResult['success']) {
                        $config[$postName] = $uploadResult['data']['path'];
                    } else {
                        $errors[] = $group['name'] . ': ' . $uploadResult['message'];
                    }
                } else {
                    $config[$postName] = '';
                }
            } elseif ($group['type'] == 'checkbox') {
                $config[$postName] = !empty($_POST[$postName]) ? implode(',', $_POST[$postName]) : '';
            } elseif (in_array($group['type'], ['date', 'dati'])) {
                $postValue = addslashes($_POST[$postName] ?? '');
                $config[$postName] = !empty($postValue) ? strtotime(str_replace('/', '-', $postValue)) : 0;
            } else {
                $config[$postName] = addslashes($_POST[$postName] ?? '');
            }
            
            // 验证字段
            $fieldErrors = self::validateField($group['id'], $config[$postName], $groupConfig);
            if (!empty($fieldErrors)) {
                $errors = array_merge($errors, array_map(function($error) use ($group) {
                    return $group['name'] . ': ' . $error;
                }, $fieldErrors));
            }
        }
        
        if (!empty($errors)) {
            self::response(false, '表单验证失败', ['errors' => $errors]);
        }
        
        // 保存数据
        $configJson = fix_json($config, $_G['charset']);
        $postAdd = [
            'fid' => $formId,
            'appid' => 'form',
            'uid' => intval($_POST['uid']),
            'account' => addslashes($_POST['account']),
            'dateline' => time(),
            'state' => 0,
            'ip' => GetIP(),
            'config' => $configJson,
        ];
        
        $addValue = C::t('#hejin_forms#hejin_value')->insert($postAdd);
        
        if ($addValue) {
            // 更新统计
            $data = ['stat' => ++$form['stat']];
            C::t('#hejin_forms#hejin_form')->update_by_id($form['id'], $data);
            
            self::response(true, '提交成功', ['id' => $addValue]);
        } else {
            self::response(false, '保存失败');
        }
    }
}

// 路由处理
$action = $_GET['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'get_form':
        $formId = intval($_GET['formid']);
        HejinFormsAPI::getFormInfo($formId);
        break;
        
    case 'validate_field':
        HejinFormsAPI::validateSingleField();
        break;
        
    case 'submit_form':
        HejinFormsAPI::submitForm();
        break;
        
    case 'upload_file':
        $fieldName = $_POST['field_name'] ?? '';
        $result = HejinFormsAPI::handleFileUpload($fieldName);
        HejinFormsAPI::response($result['success'], $result['message'], $result['data'] ?? null);
        break;
        
    default:
        HejinFormsAPI::response(false, '无效的操作', null, 400);
}
?>
