<?php
// +----------------------------------------------------------------------
// | Copyright:    (c) 2014-2016 http://www.moqu8.com All rights reserved.
// +----------------------------------------------------------------------
// | Developer:    魔趣吧源码论坛 (www.moqu8.com 请收藏备用!)
// +----------------------------------------------------------------------
// | Author:       by 魔趣吧. 技术支持/更新维护：QQ **********
// +----------------------------------------------------------------------
if(!defined('IN_DISCUZ')) {
	exit('Access Denied');
}
include_once ('db.class.php');
require_once DISCUZ_ROOT.'./source/plugin/hejin_forms/config.inc.php';
include_once ('function.func.php');

$temp = array();
$temp['default'] ='<style type="text/css">.entry-container{background-image:url('.HEJIN_PATH.'public/them/noisy_grid-6d17d6711eacfb0bd746f39bb5af9150.png);background-repeat:repeat;background-size:auto;}.bg-image{}</style><style type="text/css">.entry-container .banner{background-color:#F4F4F4;color:#AAAAAA;}.entry-container form .form-name{font-size:22px;color:#000;font-weight:normal;}.entry-container form .form-description{font-size:12px;color:#666;font-weight:normal;}.entry-container form .field label.control-label{font-size:14px;color:#333;font-weight:bold;}.entry-container form .field .help-block{font-size:12px;color:#777;font-weight:normal;}.entry-container form .field.section-break label{font-size:16px;color:#333;font-weight:bold;}.entry-container form .field.section-break .help-block{font-size:12px;color:#999;font-weight:normal;}</style>';

$temp['yellow'] ='<style type="text/css">.entry-container{background-color:#F3FE71;background-image:none;}.bg-image{background-image:url('.HEJIN_PATH.'public/them/autumn-e1b46b77d6a2b2c9682a7954b15b70ee.jpg);background-repeat:no-repeat;background-position:top center;}</style><style type="text/css">.entry-container .banner{background-color:#F1B627;color:#FFF;}.entry-container form .form-name{font-size:22px;color:#000;font-weight:normal;}.entry-container form .form-description{font-size:12px;color:#666;font-weight:normal;}.entry-container form .field label.control-label{font-size:14px;color:#333;font-weight:bold;}.entry-container form .field .help-block{font-size:12px;color:#666;font-weight:normal;}.entry-container form .field.section-break label{font-size:16px;color:#333;font-weight:bold;}.entry-container form .field.section-break .help-block{font-size:12px;color:#666;font-weight:normal;}</style>';


$temp['bule'] = '<style type="text/css">.entry-container{background-color:#FFF;background-image:none;}.bg-image{background-image:url('.HEJIN_PATH.'public/them/blue_flower-c13e434ad4c2737f8c73ff91b32b28bb.jpg);background-repeat:no-repeat;background-position:top center;}</style><style type="text/css">.entry-container .banner{background-color:#9DE6F5;color:#FFF;}.entry-container form .form-name{font-size:22px;color:#000;font-weight:normal;}.entry-container form .form-description{font-size:12px;color:#666;font-weight:normal;}.entry-container form .field label.control-label{font-size:14px;color:#333;font-weight:bold;}.entry-container form .field .help-block{font-size:12px;color:#777;font-weight:normal;}.entry-container form .field.section-break label{font-size:16px;color:#333;font-weight:bold;}.entry-container form .field.section-break .help-block{font-size:12px;color:#999;font-weight:normal;}</style>';

$temp['mise'] = '<style type="text/css">.entry-container{background-color:#F4EFE9;background-image:none;}.bg-image{background-image:url('.HEJIN_PATH.'public/them/birds-680c7b45aeb433ce8d9bdcdda87f9796.jpg);background-repeat:no-repeat;background-position:top center;}</style><style type="text/css">.entry-container .banner{background-color:#87B01D;color:#FFF;}.entry-container form .form-name{font-size:22px;color:#000;font-weight:normal;}.entry-container form .form-description{font-size:12px;color:#666;font-weight:normal;}.entry-container form .field label.control-label{font-size:14px;color:#333;font-weight:bold;}.entry-container form .field .help-block{font-size:12px;color:#777;font-weight:normal;}.entry-container form .field.section-break label{font-size:16px;color:#333;font-weight:bold;}.entry-container form .field.section-break .help-block{font-size:12px;color:#999;font-weight:normal;}</style>';

$temp['ross'] = '<style type="text/css">.entry-container{background-color:#64836C;background-image:none;}.bg-image{background-image:url('.HEJIN_PATH.'public/them/roses-a8209de4c51098228f064cf1e24f35f6.jpg);background-repeat:no-repeat;background-position:top center;}</style><style type="text/css">.entry-container .banner{background-color:#64836C;color:#FFF;}.entry-container form .form-name{font-size:22px;color:#000;font-weight:normal;}.entry-container form .form-description{font-size:12px;color:#666;font-weight:normal;}.entry-container form .field label.control-label{font-size:14px;color:#333;font-weight:bold;}.entry-container form .field .help-block{font-size:12px;color:#777;font-weight:normal;}.entry-container form .field.section-break label{font-size:16px;color:#333;font-weight:bold;}.entry-container form .field.section-break .help-block{font-size:12px;color:#999;font-weight:normal;}</style>';


$temp['shuixian'] = '<style type="text/css">.entry-container{background-color:#FFF;background-image:none;}.bg-image{background-image:url('.HEJIN_PATH.'public/them/lily.jpg);background-repeat:no-repeat;background-position:top center;}</style><style type="text/css">.entry-container .banner{background-color:#89D334;color:#FFF;}.entry-container form .form-name{font-size:22px;color:#000;font-weight:normal;}.entry-container form .form-description{font-size:12px;color:#666;font-weight:normal;}.entry-container form .field label.control-label{font-size:14px;color:#333;font-weight:bold;}.entry-container form .field .help-block{font-size:12px;color:#777;font-weight:normal;}.entry-container form .field.section-break label{font-size:16px;color:#333;font-weight:bold;}.entry-container form .field.section-break .help-block{font-size:12px;color:#999;font-weight:normal;}</style>';

$temp['qnhc'] = '<style type="text/css">.entry-container{background-color:#FFF;background-image:none;}.bg-image{background-image:url('.HEJIN_PATH.'public/them/morning_glory.jpg);background-repeat:no-repeat;background-position:top center;}</style><style type="text/css">.entry-container .banner{background-color:#92C03A;color:#FFF;}.entry-container form .form-name{font-size:22px;color:#000;font-weight:normal;}.entry-container form .form-description{font-size:12px;color:#666;font-weight:normal;}.entry-container form .field label.control-label{font-size:14px;color:#333;font-weight:bold;}.entry-container form .field .help-block{font-size:12px;color:#777;font-weight:normal;}.entry-container form .field.section-break label{font-size:16px;color:#333;font-weight:bold;}.entry-container form .field.section-break .help-block{font-size:12px;color:#999;font-weight:normal;}</style>';

$temp['hxcq'] = '<style type="text/css">.entry-container{background-color:#665A4D;background-image:none;}.bg-image{background-image:url('.HEJIN_PATH.'public/them/sparrow.jpg);background-repeat:no-repeat;background-position:top center;}</style><style type="text/css">.entry-container .banner{background-color:#C0DA83;color:#FFF;}.entry-container form .form-name{font-size:22px;color:#000;font-weight:normal;}.entry-container form .form-description{font-size:12px;color:#666;font-weight:normal;}.entry-container form .field label.control-label{font-size:14px;color:#333;font-weight:bold;}.entry-container form .field .help-block{font-size:12px;color:#777;font-weight:normal;}.entry-container form .field.section-break label{font-size:16px;color:#333;font-weight:bold;}.entry-container form .field.section-break .help-block{font-size:12px;color:#999;font-weight:normal;}</style>';

$temp['kaqt'] = '<style type="text/css">.entry-container{background-color:#73BCB8;background-image:none;}.bg-image{background-image:url('.HEJIN_PATH.'public/them/submarine.png);background-repeat:no-repeat;background-position:top center;}</style><style type="text/css">.entry-container .banner{background-color:#F15A29;color:#FFF;}.entry-container form .form-name{font-size:22px;color:#000;font-weight:normal;}.entry-container form .form-description{font-size:12px;color:#666;font-weight:normal;}.entry-container form .field label.control-label{font-size:14px;color:#333;font-weight:bold;}.entry-container form .field .help-block{font-size:12px;color:#777;font-weight:normal;}.entry-container form .field.section-break label{font-size:16px;color:#333;font-weight:bold;}.entry-container form .field.section-break .help-block{font-size:12px;color:#999;font-weight:normal;}</style>';

$temp['muwen'] = '<style type="text/css">.entry-container{background-image:url('.HEJIN_PATH.'public/them/pattern_wood.png);background-repeat:repeat;background-size:auto;}.bg-image{}</style><style type="text/css">.entry-container .banner{background-color:#937151;color:#AAAAAA;}.entry-container form .form-name{font-size:22px;color:#000;font-weight:normal;}.entry-container form .form-description{font-size:12px;color:#666;font-weight:normal;}.entry-container form .field label.control-label{font-size:14px;color:#333;font-weight:bold;}.entry-container form .field .help-block{font-size:12px;color:#777;font-weight:normal;}.entry-container form .field.section-break label{font-size:16px;color:#333;font-weight:bold;}.entry-container form .field.section-break .help-block{font-size:12px;color:#999;font-weight:normal;}</style>';

$uid = $_G['uid'];
$formhash = $_G['formhash'];
if($uid != 0){
	$user = array();
	$user['uid'] = $_G['uid'];
	$user['username'] = $_G['username'];
	$user['logo'] = $_G['setting']['ucenterurl'].'/avatar.php?uid='.$uid.'&size=small';
}
$gfid = intval($_GET['formid']);
$gmodel = addslashes($_GET['model']);

if($gfid){
		$form = C::t('#hejin_forms#hejin_form')->fetch_by_id($gfid);

		$groups = C::t('#hejin_forms#hejin_group')->fetch_by_fida($gfid);
	
	}


if(submitcheck('commit')){
	if($_POST){
		$pfid = intval($_POST['fid']);
		
		$gorups = C::t('#hejin_forms#hejin_group')->fetch_by_fid($pfid);
		foreach( $gorups as $gid => $group ){
		
		$postname = 'G-'.$group['id'];
		$postn = addslashes($_POST[$postname]);
		if($group['type'] == 'file'){
			if($_FILES[$postname]['error']==0){
		$config[$postname] = uploads($postname,'data/uploads/submit');
			}else{
			$config[$postname] = '';	
				}
			}elseif($group['type'] == 'checkbox'){
				
			if(!empty($_POST[$postname])){ //checkbox类型  数据为数组
				
				$config[$postname] = implode(',',$_POST[$postname]); //把数组转化为字符串
				}else{
					
					$config[$postname] = "";
					}	
				
				
				
				}elseif($group['type'] == 'date' or $group['type'] == 'dati' ){
					
					
				
		$config[$postname] = strtotime(str_replace('/','-',$postn));
					
				}else{
				
		$config[$postname] = $postn;
		
				}	
		}

		$config = fix_json($config,$_G['charset']); //把数据转换成json模式的字符串
				 $post_add = array(
	       			 'fid'  =>intval($_POST['fid']),               
	       			 'appid'  =>'form',              
	       			 'uid'  =>intval($_POST['uid']),               
	       			 'account'  =>addslashes($_POST['account']),              
	        		'dateline'  =>time(),              
	        		'state'  =>0,               
	        		'ip'  =>GetIP(),              
	        		'config'  =>$config,              
					);	
		    $addvalue = C::t('#hejin_forms#hejin_value')->insert($post_add);
			
			if($addvalue){
			
			$data=array();	
	         $data['stat']  = ++$form['stat'];         
			$upstat = C::t('#hejin_forms#hejin_form')->update_by_id($form['id'],$data);			
			include template('hejin_forms:form/success');
				}
			}
	}else{
		//formid存在
		if($gfid){
		foreach ($groups as $key=>$group)
{
	$configss = fix_json($group['config']);
	if(!empty($configss['GROUP_MUST']) and $group["type"] != "radio" and $group["type"] != "select" and $group["type"] != "checkbox"){
	$grojs[$key]['config'] = $configss;
	$grojs[$key]['name'] = $group['name'];
	$grojs[$key]['id'] = $group['id'];
	$grojs[$key]['type'] = $group['type'];
	$jstj[$key]="e".$group['id']."&&";
	$jsmsg[$key]="error".$group['id']."+";
	}

	if(!empty($configss['GROUP_VERIFY']) and $configss['GROUP_VERIFY']!= ""){
	$groajs[$key]['config'] = $configss;
	$groajs[$key]['name'] = $group['name'];
	$groajs[$key]['id'] = $group['id'];
	$groajs[$key]['type'] = $group['type'];
	$jstj[$key.'10']="ea".$group['id']."&&";
	$jsmsg[$key.'10']="error".$group['id']."+";
	}

}


$jstjex = implode("",$jstj);
$jstj = rtrim($jstjex, '&&');

			
		$uid = intval($_G['uid']);
		if($form['modify']==2){
			if($uid==0){
			include template('hejin_forms:form/nologin');
			}else{
			include template('hejin_forms:form/show');
			}
		}else{
		include template('hejin_forms:form/show');
		}			
			}
			//formid不存在，活动页
			else{
			
			
		$pdname = $_G['cache']['plugin']['hejin_forms']['hjform_name'];	
		$sitename = $_G['setting']['bbname'];	
		$siteurl = $_G['setting']['siteurl'];	
		$icp = $_G['setting']['icp'];	
		
		include_once ("page.class.php");
		$page=$_GET['page'];
		$hdbmes = C::t('#hejin_forms#hejin_form')->fetch_pd();
		$totail = count($hdbmes);
		$number = 10;
		$url = $_G['siteurl'].'plugin.php?id=hejin_forms&page={page}';
		$my_page=new PageClass($totail,$number,$page,$url);//参数设定：总记录，每页显示的条数，当前页，连接的地址
		$startnum = $my_page->page_limit;
		$count = $my_page->myde_size;


		$hdbms = C::t('#hejin_forms#hejin_form')->fetch_limit_pd($startnum,$count);

		$pagelist = $my_page->myde_write();
		$pagelista = $my_page->myde_writehd();
		$hdphs = C::t('#hejin_forms#hejin_form')->fetch_ph();
		
		include template('hejin_forms:index/index');
				}

	}

 function uploads($postname,$dir){
	$tempFile = $_FILES[$postname]['tmp_name'];
    $fileTypes = array('jpg','jpeg','gif','png','doc','dot','docx','xls','xlt','xlsx','wps');
 	$fileParts = pathinfo($_FILES[$postname]['name']);
	$extension = strtolower($fileParts['extension']);
	$name   = date('mdHis').'-'.rand(100,999).'.'.$extension;
    $targetFolder = HEJIN_ROOT.'/'.$dir;
 if(!is_dir($targetFolder)){mkdir($targetFolder,0777,TRUE);}
	@chmod($targetFolder,0777); 
	$loca   = $targetFolder.'/'.$name;
 	if (in_array($extension,$fileTypes)) {
		if(copy($tempFile,$loca)){
		   return $dir.'/'.$name;
		   }
	   }else{
			include template('hejin_forms:form/error');
			exit;
		   }
 }
/*
 * CopyRight  : [moqu8.com!] (C)2014-2016
 * Document   : 魔趣吧：www.moqu8.com，www.moqu8.com
 * Created on : 2016-08-25,11:30:56
 * Author     : 魔趣吧(QQ：**********) wWw.moqu8.com $
 * Description: This is NOT a freeware, use is subject to license terms.
 *              魔趣吧出品 必属精品。
 *              魔趣吧源码论坛 全网首发 http://www.moqu8.com；
 */
?>