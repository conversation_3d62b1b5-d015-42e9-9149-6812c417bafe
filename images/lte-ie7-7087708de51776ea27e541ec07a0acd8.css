.entry-container form fieldset .goods-items .goods-item .text-wrapper .number-container a{font-size:20px}.results_content .table-content table th.sort div i,#reports .field_content .field_data table th.sort div i{*zoom:expression(this.runtimeStyle['zoom'] = '1', this.innerHTML = '\2195')}.results_content .table-content table th.sort-up div i,#reports .field_content .field_data table th.sort-up div i{*zoom:expression(this.runtimeStyle['zoom'] = '1', this.innerHTML = '\2191')}.results_content .table-content table th.sort-down div i,#reports .field_content .field_data table th.sort-down div i{*zoom:expression(this.runtimeStyle['zoom'] = '1', this.innerHTML = '\2193')}.results_content .filter-content .filter .dropdown-menu i{visibility:hidden}.results_content .filter-content .filter .dropdown-menu .selected i{visibility:visible;*zoom:expression(this.runtimeStyle['zoom'] = '1', this.innerHTML = '\2714')}
