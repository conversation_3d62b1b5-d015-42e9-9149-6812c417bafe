/*!
 * jQuery JavaScript Library v1.8.3
 * http://jquery.com/
 *
 * Includes Sizzle.js
 * http://sizzlejs.com/
 *
 * Copyright 2012 jQuery Foundation and other contributors
 * Released under the MIT license
 * http://jquery.org/license
 *
 * Date: Tue Nov 13 2012 08:20:33 GMT-0500 (Eastern Standard Time)
 */
(function(e,t){function _(e){var t=M[e]={};return v.each(e.split(y),function(e,n){t[n]=!0}),t}function H(e,n,r){if(r===t&&e.nodeType===1){var i="data-"+n.replace(P,"-$1").toLowerCase();r=e.getAttribute(i);if(typeof r=="string"){try{r=r==="true"?!0:r==="false"?!1:r==="null"?null:+r+""===r?+r:D.test(r)?v.parseJSON(r):r}catch(s){}v.data(e,n,r)}else r=t}return r}function B(e){var t;for(t in e){if(t==="data"&&v.isEmptyObject(e[t]))continue;if(t!=="toJSON")return!1}return!0}function et(){return!1}function tt(){return!0}function ut(e){return!e||!e.parentNode||e.parentNode.nodeType===11}function at(e,t){do e=e[t];while(e&&e.nodeType!==1);return e}function ft(e,t,n){t=t||0;if(v.isFunction(t))return v.grep(e,function(e,r){var i=!!t.call(e,r,e);return i===n});if(t.nodeType)return v.grep(e,function(e,r){return e===t===n});if(typeof t=="string"){var r=v.grep(e,function(e){return e.nodeType===1});if(it.test(t))return v.filter(t,r,!n);t=v.filter(t,r)}return v.grep(e,function(e,r){return v.inArray(e,t)>=0===n})}function lt(e){var t=ct.split("|"),n=e.createDocumentFragment();if(n.createElement)while(t.length)n.createElement(t.pop());return n}function Lt(e,t){return e.getElementsByTagName(t)[0]||e.appendChild(e.ownerDocument.createElement(t))}function At(e,t){if(t.nodeType!==1||!v.hasData(e))return;var n,r,i,s=v._data(e),o=v._data(t,s),u=s.events;if(u){delete o.handle,o.events={};for(n in u)for(r=0,i=u[n].length;r<i;r++)v.event.add(t,n,u[n][r])}o.data&&(o.data=v.extend({},o.data))}function Ot(e,t){var n;if(t.nodeType!==1)return;t.clearAttributes&&t.clearAttributes(),t.mergeAttributes&&t.mergeAttributes(e),n=t.nodeName.toLowerCase(),n==="object"?(t.parentNode&&(t.outerHTML=e.outerHTML),v.support.html5Clone&&e.innerHTML&&!v.trim(t.innerHTML)&&(t.innerHTML=e.innerHTML)):n==="input"&&Et.test(e.type)?(t.defaultChecked=t.checked=e.checked,t.value!==e.value&&(t.value=e.value)):n==="option"?t.selected=e.defaultSelected:n==="input"||n==="textarea"?t.defaultValue=e.defaultValue:n==="script"&&t.text!==e.text&&(t.text=e.text),t.removeAttribute(v.expando)}function Mt(e){return typeof e.getElementsByTagName!="undefined"?e.getElementsByTagName("*"):typeof e.querySelectorAll!="undefined"?e.querySelectorAll("*"):[]}function _t(e){Et.test(e.type)&&(e.defaultChecked=e.checked)}function Qt(e,t){if(t in e)return t;var n=t.charAt(0).toUpperCase()+t.slice(1),r=t,i=Jt.length;while(i--){t=Jt[i]+n;if(t in e)return t}return r}function Gt(e,t){return e=t||e,v.css(e,"display")==="none"||!v.contains(e.ownerDocument,e)}function Yt(e,t){var n,r,i=[],s=0,o=e.length;for(;s<o;s++){n=e[s];if(!n.style)continue;i[s]=v._data(n,"olddisplay"),t?(!i[s]&&n.style.display==="none"&&(n.style.display=""),n.style.display===""&&Gt(n)&&(i[s]=v._data(n,"olddisplay",nn(n.nodeName)))):(r=Dt(n,"display"),!i[s]&&r!=="none"&&v._data(n,"olddisplay",r))}for(s=0;s<o;s++){n=e[s];if(!n.style)continue;if(!t||n.style.display==="none"||n.style.display==="")n.style.display=t?i[s]||"":"none"}return e}function Zt(e,t,n){var r=Rt.exec(t);return r?Math.max(0,r[1]-(n||0))+(r[2]||"px"):t}function en(e,t,n,r){var i=n===(r?"border":"content")?4:t==="width"?1:0,s=0;for(;i<4;i+=2)n==="margin"&&(s+=v.css(e,n+$t[i],!0)),r?(n==="content"&&(s-=parseFloat(Dt(e,"padding"+$t[i]))||0),n!=="margin"&&(s-=parseFloat(Dt(e,"border"+$t[i]+"Width"))||0)):(s+=parseFloat(Dt(e,"padding"+$t[i]))||0,n!=="padding"&&(s+=parseFloat(Dt(e,"border"+$t[i]+"Width"))||0));return s}function tn(e,t,n){var r=t==="width"?e.offsetWidth:e.offsetHeight,i=!0,s=v.support.boxSizing&&v.css(e,"boxSizing")==="border-box";if(r<=0||r==null){r=Dt(e,t);if(r<0||r==null)r=e.style[t];if(Ut.test(r))return r;i=s&&(v.support.boxSizingReliable||r===e.style[t]),r=parseFloat(r)||0}return r+en(e,t,n||(s?"border":"content"),i)+"px"}function nn(e){if(Wt[e])return Wt[e];var t=v("<"+e+">").appendTo(i.body),n=t.css("display");t.remove();if(n==="none"||n===""){Pt=i.body.appendChild(Pt||v.extend(i.createElement("iframe"),{frameBorder:0,width:0,height:0}));if(!Ht||!Pt.createElement)Ht=(Pt.contentWindow||Pt.contentDocument).document,Ht.write("<!doctype html><html><body>"),Ht.close();t=Ht.body.appendChild(Ht.createElement(e)),n=Dt(t,"display"),i.body.removeChild(Pt)}return Wt[e]=n,n}function fn(e,t,n,r){var i;if(v.isArray(t))v.each(t,function(t,i){n||sn.test(e)?r(e,i):fn(e+"["+(typeof i=="object"?t:"")+"]",i,n,r)});else if(!n&&v.type(t)==="object")for(i in t)fn(e+"["+i+"]",t[i],n,r);else r(e,t)}function Cn(e){return function(t,n){typeof t!="string"&&(n=t,t="*");var r,i,s,o=t.toLowerCase().split(y),u=0,a=o.length;if(v.isFunction(n))for(;u<a;u++)r=o[u],s=/^\+/.test(r),s&&(r=r.substr(1)||"*"),i=e[r]=e[r]||[],i[s?"unshift":"push"](n)}}function kn(e,n,r,i,s,o){s=s||n.dataTypes[0],o=o||{},o[s]=!0;var u,a=e[s],f=0,l=a?a.length:0,c=e===Sn;for(;f<l&&(c||!u);f++)u=a[f](n,r,i),typeof u=="string"&&(!c||o[u]?u=t:(n.dataTypes.unshift(u),u=kn(e,n,r,i,u,o)));return(c||!u)&&!o["*"]&&(u=kn(e,n,r,i,"*",o)),u}function Ln(e,n){var r,i,s=v.ajaxSettings.flatOptions||{};for(r in n)n[r]!==t&&((s[r]?e:i||(i={}))[r]=n[r]);i&&v.extend(!0,e,i)}function An(e,n,r){var i,s,o,u,a=e.contents,f=e.dataTypes,l=e.responseFields;for(s in l)s in r&&(n[l[s]]=r[s]);while(f[0]==="*")f.shift(),i===t&&(i=e.mimeType||n.getResponseHeader("content-type"));if(i)for(s in a)if(a[s]&&a[s].test(i)){f.unshift(s);break}if(f[0]in r)o=f[0];else{for(s in r){if(!f[0]||e.converters[s+" "+f[0]]){o=s;break}u||(u=s)}o=o||u}if(o)return o!==f[0]&&f.unshift(o),r[o]}function On(e,t){var n,r,i,s,o=e.dataTypes.slice(),u=o[0],a={},f=0;e.dataFilter&&(t=e.dataFilter(t,e.dataType));if(o[1])for(n in e.converters)a[n.toLowerCase()]=e.converters[n];for(;i=o[++f];)if(i!=="*"){if(u!=="*"&&u!==i){n=a[u+" "+i]||a["* "+i];if(!n)for(r in a){s=r.split(" ");if(s[1]===i){n=a[u+" "+s[0]]||a["* "+s[0]];if(n){n===!0?n=a[r]:a[r]!==!0&&(i=s[0],o.splice(f--,0,i));break}}}if(n!==!0)if(n&&e["throws"])t=n(t);else try{t=n(t)}catch(l){return{state:"parsererror",error:n?l:"No conversion from "+u+" to "+i}}}u=i}return{state:"success",data:t}}function Fn(){try{return new e.XMLHttpRequest}catch(t){}}function In(){try{return new e.ActiveXObject("Microsoft.XMLHTTP")}catch(t){}}function $n(){return setTimeout(function(){qn=t},0),qn=v.now()}function Jn(e,t){v.each(t,function(t,n){var r=(Vn[t]||[]).concat(Vn["*"]),i=0,s=r.length;for(;i<s;i++)if(r[i].call(e,t,n))return})}function Kn(e,t,n){var r,i=0,s=0,o=Xn.length,u=v.Deferred().always(function(){delete a.elem}),a=function(){var t=qn||$n(),n=Math.max(0,f.startTime+f.duration-t),r=n/f.duration||0,i=1-r,s=0,o=f.tweens.length;for(;s<o;s++)f.tweens[s].run(i);return u.notifyWith(e,[f,i,n]),i<1&&o?n:(u.resolveWith(e,[f]),!1)},f=u.promise({elem:e,props:v.extend({},t),opts:v.extend(!0,{specialEasing:{}},n),originalProperties:t,originalOptions:n,startTime:qn||$n(),duration:n.duration,tweens:[],createTween:function(t,n,r){var i=v.Tween(e,f.opts,t,n,f.opts.specialEasing[t]||f.opts.easing);return f.tweens.push(i),i},stop:function(t){var n=0,r=t?f.tweens.length:0;for(;n<r;n++)f.tweens[n].run(1);return t?u.resolveWith(e,[f,t]):u.rejectWith(e,[f,t]),this}}),l=f.props;Qn(l,f.opts.specialEasing);for(;i<o;i++){r=Xn[i].call(f,e,l,f.opts);if(r)return r}return Jn(f,l),v.isFunction(f.opts.start)&&f.opts.start.call(e,f),v.fx.timer(v.extend(a,{anim:f,queue:f.opts.queue,elem:e})),f.progress(f.opts.progress).done(f.opts.done,f.opts.complete).fail(f.opts.fail).always(f.opts.always)}function Qn(e,t){var n,r,i,s,o;for(n in e){r=v.camelCase(n),i=t[r],s=e[n],v.isArray(s)&&(i=s[1],s=e[n]=s[0]),n!==r&&(e[r]=s,delete e[n]),o=v.cssHooks[r];if(o&&"expand"in o){s=o.expand(s),delete e[r];for(n in s)n in e||(e[n]=s[n],t[n]=i)}else t[r]=i}}function Gn(e,t,n){var r,i,s,o,u,a,f,l,c,h=this,p=e.style,d={},m=[],g=e.nodeType&&Gt(e);n.queue||(l=v._queueHooks(e,"fx"),l.unqueued==null&&(l.unqueued=0,c=l.empty.fire,l.empty.fire=function(){l.unqueued||c()}),l.unqueued++,h.always(function(){h.always(function(){l.unqueued--,v.queue(e,"fx").length||l.empty.fire()})})),e.nodeType===1&&("height"in t||"width"in t)&&(n.overflow=[p.overflow,p.overflowX,p.overflowY],v.css(e,"display")==="inline"&&v.css(e,"float")==="none"&&(!v.support.inlineBlockNeedsLayout||nn(e.nodeName)==="inline"?p.display="inline-block":p.zoom=1)),n.overflow&&(p.overflow="hidden",v.support.shrinkWrapBlocks||h.done(function(){p.overflow=n.overflow[0],p.overflowX=n.overflow[1],p.overflowY=n.overflow[2]}));for(r in t){s=t[r];if(Un.exec(s)){delete t[r],a=a||s==="toggle";if(s===(g?"hide":"show"))continue;m.push(r)}}o=m.length;if(o){u=v._data(e,"fxshow")||v._data(e,"fxshow",{}),"hidden"in u&&(g=u.hidden),a&&(u.hidden=!g),g?v(e).show():h.done(function(){v(e).hide()}),h.done(function(){var t;v.removeData(e,"fxshow",!0);for(t in d)v.style(e,t,d[t])});for(r=0;r<o;r++)i=m[r],f=h.createTween(i,g?u[i]:0),d[i]=u[i]||v.style(e,i),i in u||(u[i]=f.start,g&&(f.end=f.start,f.start=i==="width"||i==="height"?1:0))}}function Yn(e,t,n,r,i){return new Yn.prototype.init(e,t,n,r,i)}function Zn(e,t){var n,r={height:e},i=0;t=t?1:0;for(;i<4;i+=2-t)n=$t[i],r["margin"+n]=r["padding"+n]=e;return t&&(r.opacity=r.width=e),r}function tr(e){return v.isWindow(e)?e:e.nodeType===9?e.defaultView||e.parentWindow:!1}var n,r,i=e.document,s=e.location,o=e.navigator,u=e.jQuery,a=e.$,f=Array.prototype.push,l=Array.prototype.slice,c=Array.prototype.indexOf,h=Object.prototype.toString,p=Object.prototype.hasOwnProperty,d=String.prototype.trim,v=function(e,t){return new v.fn.init(e,t,n)},m=/[\-+]?(?:\d*\.|)\d+(?:[eE][\-+]?\d+|)/.source,g=/\S/,y=/\s+/,b=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,w=/^(?:[^#<]*(<[\w\W]+>)[^>]*$|#([\w\-]*)$)/,E=/^<(\w+)\s*\/?>(?:<\/\1>|)$/,S=/^[\],:{}\s]*$/,x=/(?:^|:|,)(?:\s*\[)+/g,T=/\\(?:["\\\/bfnrt]|u[\da-fA-F]{4})/g,N=/"[^"\\\r\n]*"|true|false|null|-?(?:\d\d*\.|)\d+(?:[eE][\-+]?\d+|)/g,C=/^-ms-/,k=/-([\da-z])/gi,L=function(e,t){return(t+"").toUpperCase()},A=function(){i.addEventListener?(i.removeEventListener("DOMContentLoaded",A,!1),v.ready()):i.readyState==="complete"&&(i.detachEvent("onreadystatechange",A),v.ready())},O={};v.fn=v.prototype={constructor:v,init:function(e,n,r){var s,o,u,a;if(!e)return this;if(e.nodeType)return this.context=this[0]=e,this.length=1,this;if(typeof e=="string"){e.charAt(0)==="<"&&e.charAt(e.length-1)===">"&&e.length>=3?s=[null,e,null]:s=w.exec(e);if(s&&(s[1]||!n)){if(s[1])return n=n instanceof v?n[0]:n,a=n&&n.nodeType?n.ownerDocument||n:i,e=v.parseHTML(s[1],a,!0),E.test(s[1])&&v.isPlainObject(n)&&this.attr.call(e,n,!0),v.merge(this,e);o=i.getElementById(s[2]);if(o&&o.parentNode){if(o.id!==s[2])return r.find(e);this.length=1,this[0]=o}return this.context=i,this.selector=e,this}return!n||n.jquery?(n||r).find(e):this.constructor(n).find(e)}return v.isFunction(e)?r.ready(e):(e.selector!==t&&(this.selector=e.selector,this.context=e.context),v.makeArray(e,this))},selector:"",jquery:"1.8.3",length:0,size:function(){return this.length},toArray:function(){return l.call(this)},get:function(e){return e==null?this.toArray():e<0?this[this.length+e]:this[e]},pushStack:function(e,t,n){var r=v.merge(this.constructor(),e);return r.prevObject=this,r.context=this.context,t==="find"?r.selector=this.selector+(this.selector?" ":"")+n:t&&(r.selector=this.selector+"."+t+"("+n+")"),r},each:function(e,t){return v.each(this,e,t)},ready:function(e){return v.ready.promise().done(e),this},eq:function(e){return e=+e,e===-1?this.slice(e):this.slice(e,e+1)},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},slice:function(){return this.pushStack(l.apply(this,arguments),"slice",l.call(arguments).join(","))},map:function(e){return this.pushStack(v.map(this,function(t,n){return e.call(t,n,t)}))},end:function(){return this.prevObject||this.constructor(null)},push:f,sort:[].sort,splice:[].splice},v.fn.init.prototype=v.fn,v.extend=v.fn.extend=function(){var e,n,r,i,s,o,u=arguments[0]||{},a=1,f=arguments.length,l=!1;typeof u=="boolean"&&(l=u,u=arguments[1]||{},a=2),typeof u!="object"&&!v.isFunction(u)&&(u={}),f===a&&(u=this,--a);for(;a<f;a++)if((e=arguments[a])!=null)for(n in e){r=u[n],i=e[n];if(u===i)continue;l&&i&&(v.isPlainObject(i)||(s=v.isArray(i)))?(s?(s=!1,o=r&&v.isArray(r)?r:[]):o=r&&v.isPlainObject(r)?r:{},u[n]=v.extend(l,o,i)):i!==t&&(u[n]=i)}return u},v.extend({noConflict:function(t){return e.$===v&&(e.$=a),t&&e.jQuery===v&&(e.jQuery=u),v},isReady:!1,readyWait:1,holdReady:function(e){e?v.readyWait++:v.ready(!0)},ready:function(e){if(e===!0?--v.readyWait:v.isReady)return;if(!i.body)return setTimeout(v.ready,1);v.isReady=!0;if(e!==!0&&--v.readyWait>0)return;r.resolveWith(i,[v]),v.fn.trigger&&v(i).trigger("ready").off("ready")},isFunction:function(e){return v.type(e)==="function"},isArray:Array.isArray||function(e){return v.type(e)==="array"},isWindow:function(e){return e!=null&&e==e.window},isNumeric:function(e){return!isNaN(parseFloat(e))&&isFinite(e)},type:function(e){return e==null?String(e):O[h.call(e)]||"object"},isPlainObject:function(e){if(!e||v.type(e)!=="object"||e.nodeType||v.isWindow(e))return!1;try{if(e.constructor&&!p.call(e,"constructor")&&!p.call(e.constructor.prototype,"isPrototypeOf"))return!1}catch(n){return!1}var r;for(r in e);return r===t||p.call(e,r)},isEmptyObject:function(e){var t;for(t in e)return!1;return!0},error:function(e){throw new Error(e)},parseHTML:function(e,t,n){var r;return!e||typeof e!="string"?null:(typeof t=="boolean"&&(n=t,t=0),t=t||i,(r=E.exec(e))?[t.createElement(r[1])]:(r=v.buildFragment([e],t,n?null:[]),v.merge([],(r.cacheable?v.clone(r.fragment):r.fragment).childNodes)))},parseJSON:function(t){if(!t||typeof t!="string")return null;t=v.trim(t);if(e.JSON&&e.JSON.parse)return e.JSON.parse(t);if(S.test(t.replace(T,"@").replace(N,"]").replace(x,"")))return(new Function("return "+t))();v.error("Invalid JSON: "+t)},parseXML:function(n){var r,i;if(!n||typeof n!="string")return null;try{e.DOMParser?(i=new DOMParser,r=i.parseFromString(n,"text/xml")):(r=new ActiveXObject("Microsoft.XMLDOM"),r.async="false",r.loadXML(n))}catch(s){r=t}return(!r||!r.documentElement||r.getElementsByTagName("parsererror").length)&&v.error("Invalid XML: "+n),r},noop:function(){},globalEval:function(t){t&&g.test(t)&&(e.execScript||function(t){e.eval.call(e,t)})(t)},camelCase:function(e){return e.replace(C,"ms-").replace(k,L)},nodeName:function(e,t){return e.nodeName&&e.nodeName.toLowerCase()===t.toLowerCase()},each:function(e,n,r){var i,s=0,o=e.length,u=o===t||v.isFunction(e);if(r){if(u){for(i in e)if(n.apply(e[i],r)===!1)break}else for(;s<o;)if(n.apply(e[s++],r)===!1)break}else if(u){for(i in e)if(n.call(e[i],i,e[i])===!1)break}else for(;s<o;)if(n.call(e[s],s,e[s++])===!1)break;return e},trim:d&&!d.call("﻿ ")?function(e){return e==null?"":d.call(e)}:function(e){return e==null?"":(e+"").replace(b,"")},makeArray:function(e,t){var n,r=t||[];return e!=null&&(n=v.type(e),e.length==null||n==="string"||n==="function"||n==="regexp"||v.isWindow(e)?f.call(r,e):v.merge(r,e)),r},inArray:function(e,t,n){var r;if(t){if(c)return c.call(t,e,n);r=t.length,n=n?n<0?Math.max(0,r+n):n:0;for(;n<r;n++)if(n in t&&t[n]===e)return n}return-1},merge:function(e,n){var r=n.length,i=e.length,s=0;if(typeof r=="number")for(;s<r;s++)e[i++]=n[s];else while(n[s]!==t)e[i++]=n[s++];return e.length=i,e},grep:function(e,t,n){var r,i=[],s=0,o=e.length;n=!!n;for(;s<o;s++)r=!!t(e[s],s),n!==r&&i.push(e[s]);return i},map:function(e,n,r){var i,s,o=[],u=0,a=e.length,f=e instanceof v||a!==t&&typeof a=="number"&&(a>0&&e[0]&&e[a-1]||a===0||v.isArray(e));if(f)for(;u<a;u++)i=n(e[u],u,r),i!=null&&(o[o.length]=i);else for(s in e)i=n(e[s],s,r),i!=null&&(o[o.length]=i);return o.concat.apply([],o)},guid:1,proxy:function(e,n){var r,i,s;return typeof n=="string"&&(r=e[n],n=e,e=r),v.isFunction(e)?(i=l.call(arguments,2),s=function(){return e.apply(n,i.concat(l.call(arguments)))},s.guid=e.guid=e.guid||v.guid++,s):t},access:function(e,n,r,i,s,o,u){var a,f=r==null,l=0,c=e.length;if(r&&typeof r=="object"){for(l in r)v.access(e,n,l,r[l],1,o,i);s=1}else if(i!==t){a=u===t&&v.isFunction(i),f&&(a?(a=n,n=function(e,t,n){return a.call(v(e),n)}):(n.call(e,i),n=null));if(n)for(;l<c;l++)n(e[l],r,a?i.call(e[l],l,n(e[l],r)):i,u);s=1}return s?e:f?n.call(e):c?n(e[0],r):o},now:function(){return(new Date).getTime()}}),v.ready.promise=function(t){if(!r){r=v.Deferred();if(i.readyState==="complete")setTimeout(v.ready,1);else if(i.addEventListener)i.addEventListener("DOMContentLoaded",A,!1),e.addEventListener("load",v.ready,!1);else{i.attachEvent("onreadystatechange",A),e.attachEvent("onload",v.ready);var n=!1;try{n=e.frameElement==null&&i.documentElement}catch(s){}n&&n.doScroll&&function o(){if(!v.isReady){try{n.doScroll("left")}catch(e){return setTimeout(o,50)}v.ready()}}()}}return r.promise(t)},v.each("Boolean Number String Function Array Date RegExp Object".split(" "),function(e,t){O["[object "+t+"]"]=t.toLowerCase()}),n=v(i);var M={};v.Callbacks=function(e){e=typeof e=="string"?M[e]||_(e):v.extend({},e);var n,r,i,s,o,u,a=[],f=!e.once&&[],l=function(t){n=e.memory&&t,r=!0,u=s||0,s=0,o=a.length,i=!0;for(;a&&u<o;u++)if(a[u].apply(t[0],t[1])===!1&&e.stopOnFalse){n=!1;break}i=!1,a&&(f?f.length&&l(f.shift()):n?a=[]:c.disable())},c={add:function(){if(a){var t=a.length;(function r(t){v.each(t,function(t,n){var i=v.type(n);i==="function"?(!e.unique||!c.has(n))&&a.push(n):n&&n.length&&i!=="string"&&r(n)})})(arguments),i?o=a.length:n&&(s=t,l(n))}return this},remove:function(){return a&&v.each(arguments,function(e,t){var n;while((n=v.inArray(t,a,n))>-1)a.splice(n,1),i&&(n<=o&&o--,n<=u&&u--)}),this},has:function(e){return v.inArray(e,a)>-1},empty:function(){return a=[],this},disable:function(){return a=f=n=t,this},disabled:function(){return!a},lock:function(){return f=t,n||c.disable(),this},locked:function(){return!f},fireWith:function(e,t){return t=t||[],t=[e,t.slice?t.slice():t],a&&(!r||f)&&(i?f.push(t):l(t)),this},fire:function(){return c.fireWith(this,arguments),this},fired:function(){return!!r}};return c},v.extend({Deferred:function(e){var t=[["resolve","done",v.Callbacks("once memory"),"resolved"],["reject","fail",v.Callbacks("once memory"),"rejected"],["notify","progress",v.Callbacks("memory")]],n="pending",r={state:function(){return n},always:function(){return i.done(arguments).fail(arguments),this},then:function(){var e=arguments;return v.Deferred(function(n){v.each(t,function(t,r){var s=r[0],o=e[t];i[r[1]](v.isFunction(o)?function(){var e=o.apply(this,arguments);e&&v.isFunction(e.promise)?e.promise().done(n.resolve).fail(n.reject).progress(n.notify):n[s+"With"](this===i?n:this,[e])}:n[s])}),e=null}).promise()},promise:function(e){return e!=null?v.extend(e,r):r}},i={};return r.pipe=r.then,v.each(t,function(e,s){var o=s[2],u=s[3];r[s[1]]=o.add,u&&o.add(function(){n=u},t[e^1][2].disable,t[2][2].lock),i[s[0]]=o.fire,i[s[0]+"With"]=o.fireWith}),r.promise(i),e&&e.call(i,i),i},when:function(e){var t=0,n=l.call(arguments),r=n.length,i=r!==1||e&&v.isFunction(e.promise)?r:0,s=i===1?e:v.Deferred(),o=function(e,t,n){return function(r){t[e]=this,n[e]=arguments.length>1?l.call(arguments):r,n===u?s.notifyWith(t,n):--i||s.resolveWith(t,n)}},u,a,f;if(r>1){u=new Array(r),a=new Array(r),f=new Array(r);for(;t<r;t++)n[t]&&v.isFunction(n[t].promise)?n[t].promise().done(o(t,f,n)).fail(s.reject).progress(o(t,a,u)):--i}return i||s.resolveWith(f,n),s.promise()}}),v.support=function(){var t,n,r,s,o,u,a,f,l,c,h,p=i.createElement("div");p.setAttribute("className","t"),p.innerHTML="  <link/><table></table><a href='/a'>a</a><input type='checkbox'/>",n=p.getElementsByTagName("*"),r=p.getElementsByTagName("a")[0];if(!n||!r||!n.length)return{};s=i.createElement("select"),o=s.appendChild(i.createElement("option")),u=p.getElementsByTagName("input")[0],r.style.cssText="top:1px;float:left;opacity:.5",t={leadingWhitespace:p.firstChild.nodeType===3,tbody:!p.getElementsByTagName("tbody").length,htmlSerialize:!!p.getElementsByTagName("link").length,style:/top/.test(r.getAttribute("style")),hrefNormalized:r.getAttribute("href")==="/a",opacity:/^0.5/.test(r.style.opacity),cssFloat:!!r.style.cssFloat,checkOn:u.value==="on",optSelected:o.selected,getSetAttribute:p.className!=="t",enctype:!!i.createElement("form").enctype,html5Clone:i.createElement("nav").cloneNode(!0).outerHTML!=="<:nav></:nav>",boxModel:i.compatMode==="CSS1Compat",submitBubbles:!0,changeBubbles:!0,focusinBubbles:!1,deleteExpando:!0,noCloneEvent:!0,inlineBlockNeedsLayout:!1,shrinkWrapBlocks:!1,reliableMarginRight:!0,boxSizingReliable:!0,pixelPosition:!1},u.checked=!0,t.noCloneChecked=u.cloneNode(!0).checked,s.disabled=!0,t.optDisabled=!o.disabled;try{delete p.test}catch(d){t.deleteExpando=!1}!p.addEventListener&&p.attachEvent&&p.fireEvent&&(p.attachEvent("onclick",h=function(){t.noCloneEvent=!1}),p.cloneNode(!0).fireEvent("onclick"),p.detachEvent("onclick",h)),u=i.createElement("input"),u.value="t",u.setAttribute("type","radio"),t.radioValue=u.value==="t",u.setAttribute("checked","checked"),u.setAttribute("name","t"),p.appendChild(u),a=i.createDocumentFragment(),a.appendChild(p.lastChild),t.checkClone=a.cloneNode(!0).cloneNode(!0).lastChild.checked,t.appendChecked=u.checked,a.removeChild(u),a.appendChild(p);if(p.attachEvent)for(l in{submit:!0,change:!0,focusin:!0})f="on"+l,c=f in p,c||(p.setAttribute(f,"return;"),c=typeof p[f]=="function"),t[l+"Bubbles"]=c;return v(function(){var n,r,s,o,u="padding:0;margin:0;border:0;display:block;overflow:hidden;",a=i.getElementsByTagName("body")[0];if(!a)return;n=i.createElement("div"),n.style.cssText="visibility:hidden;border:0;width:0;height:0;position:static;top:0;margin-top:1px",a.insertBefore(n,a.firstChild),r=i.createElement("div"),n.appendChild(r),r.innerHTML="<table><tr><td></td><td>t</td></tr></table>",s=r.getElementsByTagName("td"),s[0].style.cssText="padding:0;margin:0;border:0;display:none",c=s[0].offsetHeight===0,s[0].style.display="",s[1].style.display="none",t.reliableHiddenOffsets=c&&s[0].offsetHeight===0,r.innerHTML="",r.style.cssText="box-sizing:border-box;-moz-box-sizing:border-box;-webkit-box-sizing:border-box;padding:1px;border:1px;display:block;width:4px;margin-top:1%;position:absolute;top:1%;",t.boxSizing=r.offsetWidth===4,t.doesNotIncludeMarginInBodyOffset=a.offsetTop!==1,e.getComputedStyle&&(t.pixelPosition=(e.getComputedStyle(r,null)||{}).top!=="1%",t.boxSizingReliable=(e.getComputedStyle(r,null)||{width:"4px"}).width==="4px",o=i.createElement("div"),o.style.cssText=r.style.cssText=u,o.style.marginRight=o.style.width="0",r.style.width="1px",r.appendChild(o),t.reliableMarginRight=!parseFloat((e.getComputedStyle(o,null)||{}).marginRight)),typeof r.style.zoom!="undefined"&&(r.innerHTML="",r.style.cssText=u+"width:1px;padding:1px;display:inline;zoom:1",t.inlineBlockNeedsLayout=r.offsetWidth===3,r.style.display="block",r.style.overflow="visible",r.innerHTML="<div></div>",r.firstChild.style.width="5px",t.shrinkWrapBlocks=r.offsetWidth!==3,n.style.zoom=1),a.removeChild(n),n=r=s=o=null}),a.removeChild(p),n=r=s=o=u=a=p=null,t}();var D=/(?:\{[\s\S]*\}|\[[\s\S]*\])$/,P=/([A-Z])/g;v.extend({cache:{},deletedIds:[],uuid:0,expando:"jQuery"+(v.fn.jquery+Math.random()).replace(/\D/g,""),noData:{embed:!0,object:"clsid:D27CDB6E-AE6D-11cf-96B8-************",applet:!0},hasData:function(e){return e=e.nodeType?v.cache[e[v.expando]]:e[v.expando],!!e&&!B(e)},data:function(e,n,r,i){if(!v.acceptData(e))return;var s,o,u=v.expando,a=typeof n=="string",f=e.nodeType,l=f?v.cache:e,c=f?e[u]:e[u]&&u;if((!c||!l[c]||!i&&!l[c].data)&&a&&r===t)return;c||(f?e[u]=c=v.deletedIds.pop()||v.guid++:c=u),l[c]||(l[c]={},f||(l[c].toJSON=v.noop));if(typeof n=="object"||typeof n=="function")i?l[c]=v.extend(l[c],n):l[c].data=v.extend(l[c].data,n);return s=l[c],i||(s.data||(s.data={}),s=s.data),r!==t&&(s[v.camelCase(n)]=r),a?(o=s[n],o==null&&(o=s[v.camelCase(n)])):o=s,o},removeData:function(e,t,n){if(!v.acceptData(e))return;var r,i,s,o=e.nodeType,u=o?v.cache:e,a=o?e[v.expando]:v.expando;if(!u[a])return;if(t){r=n?u[a]:u[a].data;if(r){v.isArray(t)||(t in r?t=[t]:(t=v.camelCase(t),t in r?t=[t]:t=t.split(" ")));for(i=0,s=t.length;i<s;i++)delete r[t[i]];if(!(n?B:v.isEmptyObject)(r))return}}if(!n){delete u[a].data;if(!B(u[a]))return}o?v.cleanData([e],!0):v.support.deleteExpando||u!=u.window?delete u[a]:u[a]=null},_data:function(e,t,n){return v.data(e,t,n,!0)},acceptData:function(e){var t=e.nodeName&&v.noData[e.nodeName.toLowerCase()];return!t||t!==!0&&e.getAttribute("classid")===t}}),v.fn.extend({data:function(e,n){var r,i,s,o,u,a=this[0],f=0,l=null;if(e===t){if(this.length){l=v.data(a);if(a.nodeType===1&&!v._data(a,"parsedAttrs")){s=a.attributes;for(u=s.length;f<u;f++)o=s[f].name,o.indexOf("data-")||(o=v.camelCase(o.substring(5)),H(a,o,l[o]));v._data(a,"parsedAttrs",!0)}}return l}return typeof e=="object"?this.each(function(){v.data(this,e)}):(r=e.split(".",2),r[1]=r[1]?"."+r[1]:"",i=r[1]+"!",v.access(this,function(n){if(n===t)return l=this.triggerHandler("getData"+i,[r[0]]),l===t&&a&&(l=v.data(a,e),l=H(a,e,l)),l===t&&r[1]?this.data(r[0]):l;r[1]=n,this.each(function(){var t=v(this);t.triggerHandler("setData"+i,r),v.data(this,e,n),t.triggerHandler("changeData"+i,r)})},null,n,arguments.length>1,null,!1))},removeData:function(e){return this.each(function(){v.removeData(this,e)})}}),v.extend({queue:function(e,t,n){var r;if(e)return t=(t||"fx")+"queue",r=v._data(e,t),n&&(!r||v.isArray(n)?r=v._data(e,t,v.makeArray(n)):r.push(n)),r||[]},dequeue:function(e,t){t=t||"fx";var n=v.queue(e,t),r=n.length,i=n.shift(),s=v._queueHooks(e,t),o=function(){v.dequeue(e,t)};i==="inprogress"&&(i=n.shift(),r--),i&&(t==="fx"&&n.unshift("inprogress"),delete s.stop,i.call(e,o,s)),!r&&s&&s.empty.fire()},_queueHooks:function(e,t){var n=t+"queueHooks";return v._data(e,n)||v._data(e,n,{empty:v.Callbacks("once memory").add(function(){v.removeData(e,t+"queue",!0),v.removeData(e,n,!0)})})}}),v.fn.extend({queue:function(e,n){var r=2;return typeof e!="string"&&(n=e,e="fx",r--),arguments.length<r?v.queue(this[0],e):n===t?this:this.each(function(){var t=v.queue(this,e,n);v._queueHooks(this,e),e==="fx"&&t[0]!=="inprogress"&&v.dequeue(this,e)})},dequeue:function(e){return this.each(function(){v.dequeue(this,e)})},delay:function(e,t){return e=v.fx?v.fx.speeds[e]||e:e,t=t||"fx",this.queue(t,function(t,n){var r=setTimeout(t,e);n.stop=function(){clearTimeout(r)}})},clearQueue:function(e){return this.queue(e||"fx",[])},promise:function(e,n){var r,i=1,s=v.Deferred(),o=this,u=this.length,a=function(){--i||s.resolveWith(o,[o])};typeof e!="string"&&(n=e,e=t),e=e||"fx";while(u--)r=v._data(o[u],e+"queueHooks"),r&&r.empty&&(i++,r.empty.add(a));return a(),s.promise(n)}});var j,F,I,q=/[\t\r\n]/g,R=/\r/g,U=/^(?:button|input)$/i,z=/^(?:button|input|object|select|textarea)$/i,W=/^a(?:rea|)$/i,X=/^(?:autofocus|autoplay|async|checked|controls|defer|disabled|hidden|loop|multiple|open|readonly|required|scoped|selected)$/i,V=v.support.getSetAttribute;v.fn.extend({attr:function(e,t){return v.access(this,v.attr,e,t,arguments.length>1)},removeAttr:function(e){return this.each(function(){v.removeAttr(this,e)})},prop:function(e,t){return v.access(this,v.prop,e,t,arguments.length>1)},removeProp:function(e){return e=v.propFix[e]||e,this.each(function(){try{this[e]=t,delete this[e]}catch(n){}})},addClass:function(e){var t,n,r,i,s,o,u;if(v.isFunction(e))return this.each(function(t){v(this).addClass(e.call(this,t,this.className))});if(e&&typeof e=="string"){t=e.split(y);for(n=0,r=this.length;n<r;n++){i=this[n];if(i.nodeType===1)if(!i.className&&t.length===1)i.className=e;else{s=" "+i.className+" ";for(o=0,u=t.length;o<u;o++)s.indexOf(" "+t[o]+" ")<0&&(s+=t[o]+" ");i.className=v.trim(s)}}}return this},removeClass:function(e){var n,r,i,s,o,u,a;if(v.isFunction(e))return this.each(function(t){v(this).removeClass(e.call(this,t,this.className))});if(e&&typeof e=="string"||e===t){n=(e||"").split(y);for(u=0,a=this.length;u<a;u++){i=this[u];if(i.nodeType===1&&i.className){r=(" "+i.className+" ").replace(q," ");for(s=0,o=n.length;s<o;s++)while(r.indexOf(" "+n[s]+" ")>=0)r=r.replace(" "+n[s]+" "," ");i.className=e?v.trim(r):""}}}return this},toggleClass:function(e,t){var n=typeof e,r=typeof t=="boolean";return v.isFunction(e)?this.each(function(n){v(this).toggleClass(e.call(this,n,this.className,t),t)}):this.each(function(){if(n==="string"){var i,s=0,o=v(this),u=t,a=e.split(y);while(i=a[s++])u=r?u:!o.hasClass(i),o[u?"addClass":"removeClass"](i)}else if(n==="undefined"||n==="boolean")this.className&&v._data(this,"__className__",this.className),this.className=this.className||e===!1?"":v._data(this,"__className__")||""})},hasClass:function(e){var t=" "+e+" ",n=0,r=this.length;for(;n<r;n++)if(this[n].nodeType===1&&(" "+this[n].className+" ").replace(q," ").indexOf(t)>=0)return!0;return!1},val:function(e){var n,r,i,s=this[0];if(!arguments.length){if(s)return n=v.valHooks[s.type]||v.valHooks[s.nodeName.toLowerCase()],n&&"get"in n&&(r=n.get(s,"value"))!==t?r:(r=s.value,typeof r=="string"?r.replace(R,""):r==null?"":r);return}return i=v.isFunction(e),this.each(function(r){var s,o=v(this);if(this.nodeType!==1)return;i?s=e.call(this,r,o.val()):s=e,s==null?s="":typeof s=="number"?s+="":v.isArray(s)&&(s=v.map(s,function(e){return e==null?"":e+""})),n=v.valHooks[this.type]||v.valHooks[this.nodeName.toLowerCase()];if(!n||!("set"in n)||n.set(this,s,"value")===t)this.value=s})}}),v.extend({valHooks:{option:{get:function(e){var t=e.attributes.value;return!t||t.specified?e.value:e.text}},select:{get:function(e){var t,n,r=e.options,i=e.selectedIndex,s=e.type==="select-one"||i<0,o=s?null:[],u=s?i+1:r.length,a=i<0?u:s?i:0;for(;a<u;a++){n=r[a];if((n.selected||a===i)&&(v.support.optDisabled?!n.disabled:n.getAttribute("disabled")===null)&&(!n.parentNode.disabled||!v.nodeName(n.parentNode,"optgroup"))){t=v(n).val();if(s)return t;o.push(t)}}return o},set:function(e,t){var n=v.makeArray(t);return v(e).find("option").each(function(){this.selected=v.inArray(v(this).val(),n)>=0}),n.length||(e.selectedIndex=-1),n}}},attrFn:{},attr:function(e,n,r,i){var s,o,u,a=e.nodeType;if(!e||a===3||a===8||a===2)return;if(i&&v.isFunction(v.fn[n]))return v(e)[n](r);if(typeof e.getAttribute=="undefined")return v.prop(e,n,r);u=a!==1||!v.isXMLDoc(e),u&&(n=n.toLowerCase(),o=v.attrHooks[n]||(X.test(n)?F:j));if(r!==t){if(r===null){v.removeAttr(e,n);return}return o&&"set"in o&&u&&(s=o.set(e,r,n))!==t?s:(e.setAttribute(n,r+""),r)}return o&&"get"in o&&u&&(s=o.get(e,n))!==null?s:(s=e.getAttribute(n),s===null?t:s)},removeAttr:function(e,t){var n,r,i,s,o=0;if(t&&e.nodeType===1){r=t.split(y);for(;o<r.length;o++)i=r[o],i&&(n=v.propFix[i]||i,s=X.test(i),s||v.attr(e,i,""),e.removeAttribute(V?i:n),s&&n in e&&(e[n]=!1))}},attrHooks:{type:{set:function(e,t){if(U.test(e.nodeName)&&e.parentNode)v.error("type property can't be changed");else if(!v.support.radioValue&&t==="radio"&&v.nodeName(e,"input")){var n=e.value;return e.setAttribute("type",t),n&&(e.value=n),t}}},value:{get:function(e,t){return j&&v.nodeName(e,"button")?j.get(e,t):t in e?e.value:null},set:function(e,t,n){if(j&&v.nodeName(e,"button"))return j.set(e,t,n);e.value=t}}},propFix:{tabindex:"tabIndex",readonly:"readOnly","for":"htmlFor","class":"className",maxlength:"maxLength",cellspacing:"cellSpacing",cellpadding:"cellPadding",rowspan:"rowSpan",colspan:"colSpan",usemap:"useMap",frameborder:"frameBorder",contenteditable:"contentEditable"},prop:function(e,n,r){var i,s,o,u=e.nodeType;if(!e||u===3||u===8||u===2)return;return o=u!==1||!v.isXMLDoc(e),o&&(n=v.propFix[n]||n,s=v.propHooks[n]),r!==t?s&&"set"in s&&(i=s.set(e,r,n))!==t?i:e[n]=r:s&&"get"in s&&(i=s.get(e,n))!==null?i:e[n]},propHooks:{tabIndex:{get:function(e){var n=e.getAttributeNode("tabindex");return n&&n.specified?parseInt(n.value,10):z.test(e.nodeName)||W.test(e.nodeName)&&e.href?0:t}}}}),F={get:function(e,n){var r,i=v.prop(e,n);return i===!0||typeof i!="boolean"&&(r=e.getAttributeNode(n))&&r.nodeValue!==!1?n.toLowerCase():t},set:function(e,t,n){var r;return t===!1?v.removeAttr(e,n):(r=v.propFix[n]||n,r in e&&(e[r]=!0),e.setAttribute(n,n.toLowerCase())),n}},V||(I={name:!0,id:!0,coords:!0},j=v.valHooks.button={get:function(e,n){var r;return r=e.getAttributeNode(n),r&&(I[n]?r.value!=="":r.specified)?r.value:t},set:function(e,t,n){var r=e.getAttributeNode(n);return r||(r=i.createAttribute(n),e.setAttributeNode(r)),r.value=t+""}},v.each(["width","height"],function(e,t){v.attrHooks[t]=v.extend(v.attrHooks[t],{set:function(e,n){if(n==="")return e.setAttribute(t,"auto"),n}})}),v.attrHooks.contenteditable={get:j.get,set:function(e,t,n){t===""&&(t="false"),j.set(e,t,n)}}),v.support.hrefNormalized||v.each(["href"
,"src","width","height"],function(e,n){v.attrHooks[n]=v.extend(v.attrHooks[n],{get:function(e){var r=e.getAttribute(n,2);return r===null?t:r}})}),v.support.style||(v.attrHooks.style={get:function(e){return e.style.cssText.toLowerCase()||t},set:function(e,t){return e.style.cssText=t+""}}),v.support.optSelected||(v.propHooks.selected=v.extend(v.propHooks.selected,{get:function(e){var t=e.parentNode;return t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex),null}})),v.support.enctype||(v.propFix.enctype="encoding"),v.support.checkOn||v.each(["radio","checkbox"],function(){v.valHooks[this]={get:function(e){return e.getAttribute("value")===null?"on":e.value}}}),v.each(["radio","checkbox"],function(){v.valHooks[this]=v.extend(v.valHooks[this],{set:function(e,t){if(v.isArray(t))return e.checked=v.inArray(v(e).val(),t)>=0}})});var $=/^(?:textarea|input|select)$/i,J=/^([^\.]*|)(?:\.(.+)|)$/,K=/(?:^|\s)hover(\.\S+|)\b/,Q=/^key/,G=/^(?:mouse|contextmenu)|click/,Y=/^(?:focusinfocus|focusoutblur)$/,Z=function(e){return v.event.special.hover?e:e.replace(K,"mouseenter$1 mouseleave$1")};v.event={add:function(e,n,r,i,s){var o,u,a,f,l,c,h,p,d,m,g;if(e.nodeType===3||e.nodeType===8||!n||!r||!(o=v._data(e)))return;r.handler&&(d=r,r=d.handler,s=d.selector),r.guid||(r.guid=v.guid++),a=o.events,a||(o.events=a={}),u=o.handle,u||(o.handle=u=function(e){return typeof v=="undefined"||!!e&&v.event.triggered===e.type?t:v.event.dispatch.apply(u.elem,arguments)},u.elem=e),n=v.trim(Z(n)).split(" ");for(f=0;f<n.length;f++){l=J.exec(n[f])||[],c=l[1],h=(l[2]||"").split(".").sort(),g=v.event.special[c]||{},c=(s?g.delegateType:g.bindType)||c,g=v.event.special[c]||{},p=v.extend({type:c,origType:l[1],data:i,handler:r,guid:r.guid,selector:s,needsContext:s&&v.expr.match.needsContext.test(s),namespace:h.join(".")},d),m=a[c];if(!m){m=a[c]=[],m.delegateCount=0;if(!g.setup||g.setup.call(e,i,h,u)===!1)e.addEventListener?e.addEventListener(c,u,!1):e.attachEvent&&e.attachEvent("on"+c,u)}g.add&&(g.add.call(e,p),p.handler.guid||(p.handler.guid=r.guid)),s?m.splice(m.delegateCount++,0,p):m.push(p),v.event.global[c]=!0}e=null},global:{},remove:function(e,t,n,r,i){var s,o,u,a,f,l,c,h,p,d,m,g=v.hasData(e)&&v._data(e);if(!g||!(h=g.events))return;t=v.trim(Z(t||"")).split(" ");for(s=0;s<t.length;s++){o=J.exec(t[s])||[],u=a=o[1],f=o[2];if(!u){for(u in h)v.event.remove(e,u+t[s],n,r,!0);continue}p=v.event.special[u]||{},u=(r?p.delegateType:p.bindType)||u,d=h[u]||[],l=d.length,f=f?new RegExp("(^|\\.)"+f.split(".").sort().join("\\.(?:.*\\.|)")+"(\\.|$)"):null;for(c=0;c<d.length;c++)m=d[c],(i||a===m.origType)&&(!n||n.guid===m.guid)&&(!f||f.test(m.namespace))&&(!r||r===m.selector||r==="**"&&m.selector)&&(d.splice(c--,1),m.selector&&d.delegateCount--,p.remove&&p.remove.call(e,m));d.length===0&&l!==d.length&&((!p.teardown||p.teardown.call(e,f,g.handle)===!1)&&v.removeEvent(e,u,g.handle),delete h[u])}v.isEmptyObject(h)&&(delete g.handle,v.removeData(e,"events",!0))},customEvent:{getData:!0,setData:!0,changeData:!0},trigger:function(n,r,s,o){if(!s||s.nodeType!==3&&s.nodeType!==8){var u,a,f,l,c,h,p,d,m,g,y=n.type||n,b=[];if(Y.test(y+v.event.triggered))return;y.indexOf("!")>=0&&(y=y.slice(0,-1),a=!0),y.indexOf(".")>=0&&(b=y.split("."),y=b.shift(),b.sort());if((!s||v.event.customEvent[y])&&!v.event.global[y])return;n=typeof n=="object"?n[v.expando]?n:new v.Event(y,n):new v.Event(y),n.type=y,n.isTrigger=!0,n.exclusive=a,n.namespace=b.join("."),n.namespace_re=n.namespace?new RegExp("(^|\\.)"+b.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,h=y.indexOf(":")<0?"on"+y:"";if(!s){u=v.cache;for(f in u)u[f].events&&u[f].events[y]&&v.event.trigger(n,r,u[f].handle.elem,!0);return}n.result=t,n.target||(n.target=s),r=r!=null?v.makeArray(r):[],r.unshift(n),p=v.event.special[y]||{};if(p.trigger&&p.trigger.apply(s,r)===!1)return;m=[[s,p.bindType||y]];if(!o&&!p.noBubble&&!v.isWindow(s)){g=p.delegateType||y,l=Y.test(g+y)?s:s.parentNode;for(c=s;l;l=l.parentNode)m.push([l,g]),c=l;c===(s.ownerDocument||i)&&m.push([c.defaultView||c.parentWindow||e,g])}for(f=0;f<m.length&&!n.isPropagationStopped();f++)l=m[f][0],n.type=m[f][1],d=(v._data(l,"events")||{})[n.type]&&v._data(l,"handle"),d&&d.apply(l,r),d=h&&l[h],d&&v.acceptData(l)&&d.apply&&d.apply(l,r)===!1&&n.preventDefault();return n.type=y,!o&&!n.isDefaultPrevented()&&(!p._default||p._default.apply(s.ownerDocument,r)===!1)&&(y!=="click"||!v.nodeName(s,"a"))&&v.acceptData(s)&&h&&s[y]&&(y!=="focus"&&y!=="blur"||n.target.offsetWidth!==0)&&!v.isWindow(s)&&(c=s[h],c&&(s[h]=null),v.event.triggered=y,s[y](),v.event.triggered=t,c&&(s[h]=c)),n.result}return},dispatch:function(n){n=v.event.fix(n||e.event);var r,i,s,o,u,a,f,c,h,p,d=(v._data(this,"events")||{})[n.type]||[],m=d.delegateCount,g=l.call(arguments),y=!n.exclusive&&!n.namespace,b=v.event.special[n.type]||{},w=[];g[0]=n,n.delegateTarget=this;if(b.preDispatch&&b.preDispatch.call(this,n)===!1)return;if(m&&(!n.button||n.type!=="click"))for(s=n.target;s!=this;s=s.parentNode||this)if(s.disabled!==!0||n.type!=="click"){u={},f=[];for(r=0;r<m;r++)c=d[r],h=c.selector,u[h]===t&&(u[h]=c.needsContext?v(h,this).index(s)>=0:v.find(h,this,null,[s]).length),u[h]&&f.push(c);f.length&&w.push({elem:s,matches:f})}d.length>m&&w.push({elem:this,matches:d.slice(m)});for(r=0;r<w.length&&!n.isPropagationStopped();r++){a=w[r],n.currentTarget=a.elem;for(i=0;i<a.matches.length&&!n.isImmediatePropagationStopped();i++){c=a.matches[i];if(y||!n.namespace&&!c.namespace||n.namespace_re&&n.namespace_re.test(c.namespace))n.data=c.data,n.handleObj=c,o=((v.event.special[c.origType]||{}).handle||c.handler).apply(a.elem,g),o!==t&&(n.result=o,o===!1&&(n.preventDefault(),n.stopPropagation()))}}return b.postDispatch&&b.postDispatch.call(this,n),n.result},props:"attrChange attrName relatedNode srcElement altKey bubbles cancelable ctrlKey currentTarget eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(e,t){return e.which==null&&(e.which=t.charCode!=null?t.charCode:t.keyCode),e}},mouseHooks:{props:"button buttons clientX clientY fromElement offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(e,n){var r,s,o,u=n.button,a=n.fromElement;return e.pageX==null&&n.clientX!=null&&(r=e.target.ownerDocument||i,s=r.documentElement,o=r.body,e.pageX=n.clientX+(s&&s.scrollLeft||o&&o.scrollLeft||0)-(s&&s.clientLeft||o&&o.clientLeft||0),e.pageY=n.clientY+(s&&s.scrollTop||o&&o.scrollTop||0)-(s&&s.clientTop||o&&o.clientTop||0)),!e.relatedTarget&&a&&(e.relatedTarget=a===e.target?n.toElement:a),!e.which&&u!==t&&(e.which=u&1?1:u&2?3:u&4?2:0),e}},fix:function(e){if(e[v.expando])return e;var t,n,r=e,s=v.event.fixHooks[e.type]||{},o=s.props?this.props.concat(s.props):this.props;e=v.Event(r);for(t=o.length;t;)n=o[--t],e[n]=r[n];return e.target||(e.target=r.srcElement||i),e.target.nodeType===3&&(e.target=e.target.parentNode),e.metaKey=!!e.metaKey,s.filter?s.filter(e,r):e},special:{load:{noBubble:!0},focus:{delegateType:"focusin"},blur:{delegateType:"focusout"},beforeunload:{setup:function(e,t,n){v.isWindow(this)&&(this.onbeforeunload=n)},teardown:function(e,t){this.onbeforeunload===t&&(this.onbeforeunload=null)}}},simulate:function(e,t,n,r){var i=v.extend(new v.Event,n,{type:e,isSimulated:!0,originalEvent:{}});r?v.event.trigger(i,null,t):v.event.dispatch.call(t,i),i.isDefaultPrevented()&&n.preventDefault()}},v.event.handle=v.event.dispatch,v.removeEvent=i.removeEventListener?function(e,t,n){e.removeEventListener&&e.removeEventListener(t,n,!1)}:function(e,t,n){var r="on"+t;e.detachEvent&&(typeof e[r]=="undefined"&&(e[r]=null),e.detachEvent(r,n))},v.Event=function(e,t){if(!(this instanceof v.Event))return new v.Event(e,t);e&&e.type?(this.originalEvent=e,this.type=e.type,this.isDefaultPrevented=e.defaultPrevented||e.returnValue===!1||e.getPreventDefault&&e.getPreventDefault()?tt:et):this.type=e,t&&v.extend(this,t),this.timeStamp=e&&e.timeStamp||v.now(),this[v.expando]=!0},v.Event.prototype={preventDefault:function(){this.isDefaultPrevented=tt;var e=this.originalEvent;if(!e)return;e.preventDefault?e.preventDefault():e.returnValue=!1},stopPropagation:function(){this.isPropagationStopped=tt;var e=this.originalEvent;if(!e)return;e.stopPropagation&&e.stopPropagation(),e.cancelBubble=!0},stopImmediatePropagation:function(){this.isImmediatePropagationStopped=tt,this.stopPropagation()},isDefaultPrevented:et,isPropagationStopped:et,isImmediatePropagationStopped:et},v.each({mouseenter:"mouseover",mouseleave:"mouseout"},function(e,t){v.event.special[e]={delegateType:t,bindType:t,handle:function(e){var n,r=this,i=e.relatedTarget,s=e.handleObj,o=s.selector;if(!i||i!==r&&!v.contains(r,i))e.type=s.origType,n=s.handler.apply(this,arguments),e.type=t;return n}}}),v.support.submitBubbles||(v.event.special.submit={setup:function(){if(v.nodeName(this,"form"))return!1;v.event.add(this,"click._submit keypress._submit",function(e){var n=e.target,r=v.nodeName(n,"input")||v.nodeName(n,"button")?n.form:t;r&&!v._data(r,"_submit_attached")&&(v.event.add(r,"submit._submit",function(e){e._submit_bubble=!0}),v._data(r,"_submit_attached",!0))})},postDispatch:function(e){e._submit_bubble&&(delete e._submit_bubble,this.parentNode&&!e.isTrigger&&v.event.simulate("submit",this.parentNode,e,!0))},teardown:function(){if(v.nodeName(this,"form"))return!1;v.event.remove(this,"._submit")}}),v.support.changeBubbles||(v.event.special.change={setup:function(){if($.test(this.nodeName)){if(this.type==="checkbox"||this.type==="radio")v.event.add(this,"propertychange._change",function(e){e.originalEvent.propertyName==="checked"&&(this._just_changed=!0)}),v.event.add(this,"click._change",function(e){this._just_changed&&!e.isTrigger&&(this._just_changed=!1),v.event.simulate("change",this,e,!0)});return!1}v.event.add(this,"beforeactivate._change",function(e){var t=e.target;$.test(t.nodeName)&&!v._data(t,"_change_attached")&&(v.event.add(t,"change._change",function(e){this.parentNode&&!e.isSimulated&&!e.isTrigger&&v.event.simulate("change",this.parentNode,e,!0)}),v._data(t,"_change_attached",!0))})},handle:function(e){var t=e.target;if(this!==t||e.isSimulated||e.isTrigger||t.type!=="radio"&&t.type!=="checkbox")return e.handleObj.handler.apply(this,arguments)},teardown:function(){return v.event.remove(this,"._change"),!$.test(this.nodeName)}}),v.support.focusinBubbles||v.each({focus:"focusin",blur:"focusout"},function(e,t){var n=0,r=function(e){v.event.simulate(t,e.target,v.event.fix(e),!0)};v.event.special[t]={setup:function(){n++===0&&i.addEventListener(e,r,!0)},teardown:function(){--n===0&&i.removeEventListener(e,r,!0)}}}),v.fn.extend({on:function(e,n,r,i,s){var o,u;if(typeof e=="object"){typeof n!="string"&&(r=r||n,n=t);for(u in e)this.on(u,n,r,e[u],s);return this}r==null&&i==null?(i=n,r=n=t):i==null&&(typeof n=="string"?(i=r,r=t):(i=r,r=n,n=t));if(i===!1)i=et;else if(!i)return this;return s===1&&(o=i,i=function(e){return v().off(e),o.apply(this,arguments)},i.guid=o.guid||(o.guid=v.guid++)),this.each(function(){v.event.add(this,e,i,r,n)})},one:function(e,t,n,r){return this.on(e,t,n,r,1)},off:function(e,n,r){var i,s;if(e&&e.preventDefault&&e.handleObj)return i=e.handleObj,v(e.delegateTarget).off(i.namespace?i.origType+"."+i.namespace:i.origType,i.selector,i.handler),this;if(typeof e=="object"){for(s in e)this.off(s,n,e[s]);return this}if(n===!1||typeof n=="function")r=n,n=t;return r===!1&&(r=et),this.each(function(){v.event.remove(this,e,r,n)})},bind:function(e,t,n){return this.on(e,null,t,n)},unbind:function(e,t){return this.off(e,null,t)},live:function(e,t,n){return v(this.context).on(e,this.selector,t,n),this},die:function(e,t){return v(this.context).off(e,this.selector||"**",t),this},delegate:function(e,t,n,r){return this.on(t,e,n,r)},undelegate:function(e,t,n){return arguments.length===1?this.off(e,"**"):this.off(t,e||"**",n)},trigger:function(e,t){return this.each(function(){v.event.trigger(e,t,this)})},triggerHandler:function(e,t){if(this[0])return v.event.trigger(e,t,this[0],!0)},toggle:function(e){var t=arguments,n=e.guid||v.guid++,r=0,i=function(n){var i=(v._data(this,"lastToggle"+e.guid)||0)%r;return v._data(this,"lastToggle"+e.guid,i+1),n.preventDefault(),t[i].apply(this,arguments)||!1};i.guid=n;while(r<t.length)t[r++].guid=n;return this.click(i)},hover:function(e,t){return this.mouseenter(e).mouseleave(t||e)}}),v.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),function(e,t){v.fn[t]=function(e,n){return n==null&&(n=e,e=null),arguments.length>0?this.on(t,null,e,n):this.trigger(t)},Q.test(t)&&(v.event.fixHooks[t]=v.event.keyHooks),G.test(t)&&(v.event.fixHooks[t]=v.event.mouseHooks)}),function(e,t){function nt(e,t,n,r){n=n||[],t=t||g;var i,s,a,f,l=t.nodeType;if(!e||typeof e!="string")return n;if(l!==1&&l!==9)return[];a=o(t);if(!a&&!r)if(i=R.exec(e))if(f=i[1]){if(l===9){s=t.getElementById(f);if(!s||!s.parentNode)return n;if(s.id===f)return n.push(s),n}else if(t.ownerDocument&&(s=t.ownerDocument.getElementById(f))&&u(t,s)&&s.id===f)return n.push(s),n}else{if(i[2])return S.apply(n,x.call(t.getElementsByTagName(e),0)),n;if((f=i[3])&&Z&&t.getElementsByClassName)return S.apply(n,x.call(t.getElementsByClassName(f),0)),n}return vt(e.replace(j,"$1"),t,n,r,a)}function rt(e){return function(t){var n=t.nodeName.toLowerCase();return n==="input"&&t.type===e}}function it(e){return function(t){var n=t.nodeName.toLowerCase();return(n==="input"||n==="button")&&t.type===e}}function st(e){return N(function(t){return t=+t,N(function(n,r){var i,s=e([],n.length,t),o=s.length;while(o--)n[i=s[o]]&&(n[i]=!(r[i]=n[i]))})})}function ot(e,t,n){if(e===t)return n;var r=e.nextSibling;while(r){if(r===t)return-1;r=r.nextSibling}return 1}function ut(e,t){var n,r,s,o,u,a,f,l=L[d][e+" "];if(l)return t?0:l.slice(0);u=e,a=[],f=i.preFilter;while(u){if(!n||(r=F.exec(u)))r&&(u=u.slice(r[0].length)||u),a.push(s=[]);n=!1;if(r=I.exec(u))s.push(n=new m(r.shift())),u=u.slice(n.length),n.type=r[0].replace(j," ");for(o in i.filter)(r=J[o].exec(u))&&(!f[o]||(r=f[o](r)))&&(s.push(n=new m(r.shift())),u=u.slice(n.length),n.type=o,n.matches=r);if(!n)break}return t?u.length:u?nt.error(e):L(e,a).slice(0)}function at(e,t,r){var i=t.dir,s=r&&t.dir==="parentNode",o=w++;return t.first?function(t,n,r){while(t=t[i])if(s||t.nodeType===1)return e(t,n,r)}:function(t,r,u){if(!u){var a,f=b+" "+o+" ",l=f+n;while(t=t[i])if(s||t.nodeType===1){if((a=t[d])===l)return t.sizset;if(typeof a=="string"&&a.indexOf(f)===0){if(t.sizset)return t}else{t[d]=l;if(e(t,r,u))return t.sizset=!0,t;t.sizset=!1}}}else while(t=t[i])if(s||t.nodeType===1)if(e(t,r,u))return t}}function ft(e){return e.length>1?function(t,n,r){var i=e.length;while(i--)if(!e[i](t,n,r))return!1;return!0}:e[0]}function lt(e,t,n,r,i){var s,o=[],u=0,a=e.length,f=t!=null;for(;u<a;u++)if(s=e[u])if(!n||n(s,r,i))o.push(s),f&&t.push(u);return o}function ct(e,t,n,r,i,s){return r&&!r[d]&&(r=ct(r)),i&&!i[d]&&(i=ct(i,s)),N(function(s,o,u,a){var f,l,c,h=[],p=[],d=o.length,v=s||dt(t||"*",u.nodeType?[u]:u,[]),m=e&&(s||!t)?lt(v,h,e,u,a):v,g=n?i||(s?e:d||r)?[]:o:m;n&&n(m,g,u,a);if(r){f=lt(g,p),r(f,[],u,a),l=f.length;while(l--)if(c=f[l])g[p[l]]=!(m[p[l]]=c)}if(s){if(i||e){if(i){f=[],l=g.length;while(l--)(c=g[l])&&f.push(m[l]=c);i(null,g=[],f,a)}l=g.length;while(l--)(c=g[l])&&(f=i?T.call(s,c):h[l])>-1&&(s[f]=!(o[f]=c))}}else g=lt(g===o?g.splice(d,g.length):g),i?i(null,o,g,a):S.apply(o,g)})}function ht(e){var t,n,r,s=e.length,o=i.relative[e[0].type],u=o||i.relative[" "],a=o?1:0,f=at(function(e){return e===t},u,!0),l=at(function(e){return T.call(t,e)>-1},u,!0),h=[function(e,n,r){return!o&&(r||n!==c)||((t=n).nodeType?f(e,n,r):l(e,n,r))}];for(;a<s;a++)if(n=i.relative[e[a].type])h=[at(ft(h),n)];else{n=i.filter[e[a].type].apply(null,e[a].matches);if(n[d]){r=++a;for(;r<s;r++)if(i.relative[e[r].type])break;return ct(a>1&&ft(h),a>1&&e.slice(0,a-1).join("").replace(j,"$1"),n,a<r&&ht(e.slice(a,r)),r<s&&ht(e=e.slice(r)),r<s&&e.join(""))}h.push(n)}return ft(h)}function pt(e,t){var r=t.length>0,s=e.length>0,o=function(u,a,f,l,h){var p,d,v,m=[],y=0,w="0",x=u&&[],T=h!=null,N=c,C=u||s&&i.find.TAG("*",h&&a.parentNode||a),k=b+=N==null?1:Math.E;T&&(c=a!==g&&a,n=o.el);for(;(p=C[w])!=null;w++){if(s&&p){for(d=0;v=e[d];d++)if(v(p,a,f)){l.push(p);break}T&&(b=k,n=++o.el)}r&&((p=!v&&p)&&y--,u&&x.push(p))}y+=w;if(r&&w!==y){for(d=0;v=t[d];d++)v(x,m,a,f);if(u){if(y>0)while(w--)!x[w]&&!m[w]&&(m[w]=E.call(l));m=lt(m)}S.apply(l,m),T&&!u&&m.length>0&&y+t.length>1&&nt.uniqueSort(l)}return T&&(b=k,c=N),x};return o.el=0,r?N(o):o}function dt(e,t,n){var r=0,i=t.length;for(;r<i;r++)nt(e,t[r],n);return n}function vt(e,t,n,r,s){var o,u,f,l,c,h=ut(e),p=h.length;if(!r&&h.length===1){u=h[0]=h[0].slice(0);if(u.length>2&&(f=u[0]).type==="ID"&&t.nodeType===9&&!s&&i.relative[u[1].type]){t=i.find.ID(f.matches[0].replace($,""),t,s)[0];if(!t)return n;e=e.slice(u.shift().length)}for(o=J.POS.test(e)?-1:u.length-1;o>=0;o--){f=u[o];if(i.relative[l=f.type])break;if(c=i.find[l])if(r=c(f.matches[0].replace($,""),z.test(u[0].type)&&t.parentNode||t,s)){u.splice(o,1),e=r.length&&u.join("");if(!e)return S.apply(n,x.call(r,0)),n;break}}}return a(e,h)(r,t,s,n,z.test(e)),n}function mt(){}var n,r,i,s,o,u,a,f,l,c,h=!0,p="undefined",d=("sizcache"+Math.random()).replace(".",""),m=String,g=e.document,y=g.documentElement,b=0,w=0,E=[].pop,S=[].push,x=[].slice,T=[].indexOf||function(e){var t=0,n=this.length;for(;t<n;t++)if(this[t]===e)return t;return-1},N=function(e,t){return e[d]=t==null||t,e},C=function(){var e={},t=[];return N(function(n,r){return t.push(n)>i.cacheLength&&delete e[t.shift()],e[n+" "]=r},e)},k=C(),L=C(),A=C(),O="[\\x20\\t\\r\\n\\f]",M="(?:\\\\.|[-\\w]|[^\\x00-\\xa0])+",_=M.replace("w","w#"),D="([*^$|!~]?=)",P="\\["+O+"*("+M+")"+O+"*(?:"+D+O+"*(?:(['\"])((?:\\\\.|[^\\\\])*?)\\3|("+_+")|)|)"+O+"*\\]",H=":("+M+")(?:\\((?:(['\"])((?:\\\\.|[^\\\\])*?)\\2|([^()[\\]]*|(?:(?:"+P+")|[^:]|\\\\.)*|.*))\\)|)",B=":(even|odd|eq|gt|lt|nth|first|last)(?:\\("+O+"*((?:-\\d)?\\d*)"+O+"*\\)|)(?=[^-]|$)",j=new RegExp("^"+O+"+|((?:^|[^\\\\])(?:\\\\.)*)"+O+"+$","g"),F=new RegExp("^"+O+"*,"+O+"*"),I=new RegExp("^"+O+"*([\\x20\\t\\r\\n\\f>+~])"+O+"*"),q=new RegExp(H),R=/^(?:#([\w\-]+)|(\w+)|\.([\w\-]+))$/,U=/^:not/,z=/[\x20\t\r\n\f]*[+~]/,W=/:not\($/,X=/h\d/i,V=/input|select|textarea|button/i,$=/\\(?!\\)/g,J={ID:new RegExp("^#("+M+")"),CLASS:new RegExp("^\\.("+M+")"),NAME:new RegExp("^\\[name=['\"]?("+M+")['\"]?\\]"),TAG:new RegExp("^("+M.replace("w","w*")+")"),ATTR:new RegExp("^"+P),PSEUDO:new RegExp("^"+H),POS:new RegExp(B,"i"),CHILD:new RegExp("^:(only|nth|first|last)-child(?:\\("+O+"*(even|odd|(([+-]|)(\\d*)n|)"+O+"*(?:([+-]|)"+O+"*(\\d+)|))"+O+"*\\)|)","i"),needsContext:new RegExp("^"+O+"*[>+~]|"+B,"i")},K=function(e){var t=g.createElement("div");try{return e(t)}catch(n){return!1}finally{t=null}},Q=K(function(e){return e.appendChild(g.createComment("")),!e.getElementsByTagName("*").length}),G=K(function(e){return e.innerHTML="<a href='#'></a>",e.firstChild&&typeof e.firstChild.getAttribute!==p&&e.firstChild.getAttribute("href")==="#"}),Y=K(function(e){e.innerHTML="<select></select>";var t=typeof e.lastChild.getAttribute("multiple");return t!=="boolean"&&t!=="string"}),Z=K(function(e){return e.innerHTML="<div class='hidden e'></div><div class='hidden'></div>",!e.getElementsByClassName||!e.getElementsByClassName("e").length?!1:(e.lastChild.className="e",e.getElementsByClassName("e").length===2)}),et=K(function(e){e.id=d+0,e.innerHTML="<a name='"+d+"'></a><div name='"+d+"'></div>",y.insertBefore(e,y.firstChild);var t=g.getElementsByName&&g.getElementsByName(d).length===2+g.getElementsByName(d+0).length;return r=!g.getElementById(d),y.removeChild(e),t});try{x.call(y.childNodes,0)[0].nodeType}catch(tt){x=function(e){var t,n=[];for(;t=this[e];e++)n.push(t);return n}}nt.matches=function(e,t){return nt(e,null,null,t)},nt.matchesSelector=function(e,t){return nt(t,null,null,[e]).length>0},s=nt.getText=function(e){var t,n="",r=0,i=e.nodeType;if(i){if(i===1||i===9||i===11){if(typeof e.textContent=="string")return e.textContent;for(e=e.firstChild;e;e=e.nextSibling)n+=s(e)}else if(i===3||i===4)return e.nodeValue}else for(;t=e[r];r++)n+=s(t);return n},o=nt.isXML=function(e){var t=e&&(e.ownerDocument||e).documentElement;return t?t.nodeName!=="HTML":!1},u=nt.contains=y.contains?function(e,t){var n=e.nodeType===9?e.documentElement:e,r=t&&t.parentNode;return e===r||!!(r&&r.nodeType===1&&n.contains&&n.contains(r))}:y.compareDocumentPosition?function(e,t){return t&&!!(e.compareDocumentPosition(t)&16)}:function(e,t){while(t=t.parentNode)if(t===e)return!0;return!1},nt.attr=function(e,t){var n,r=o(e);return r||(t=t.toLowerCase()),(n=i.attrHandle[t])?n(e):r||Y?e.getAttribute(t):(n=e.getAttributeNode(t),n?typeof e[t]=="boolean"?e[t]?t:null:n.specified?n.value:null:null)},i=nt.selectors={cacheLength:50,createPseudo:N,match:J,attrHandle:G?{}:{href:function(e){return e.getAttribute("href",2)},type:function(e){return e.getAttribute("type")}},find:{ID:r?function(e,t,n){if(typeof t.getElementById!==p&&!n){var r=t.getElementById(e);return r&&r.parentNode?[r]:[]}}:function(e,n,r){if(typeof n.getElementById!==p&&!r){var i=n.getElementById(e);return i?i.id===e||typeof i.getAttributeNode!==p&&i.getAttributeNode("id").value===e?[i]:t:[]}},TAG:Q?function(e,t){if(typeof t.getElementsByTagName!==p)return t.getElementsByTagName(e)}:function(e,t){var n=t.getElementsByTagName(e);if(e==="*"){var r,i=[],s=0;for(;r=n[s];s++)r.nodeType===1&&i.push(r);return i}return n},NAME:et&&function(e,t){if(typeof t.getElementsByName!==p)return t.getElementsByName(name)},CLASS:Z&&function(e,t,n){if(typeof t.getElementsByClassName!==p&&!n)return t.getElementsByClassName(e)}},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(e){return e[1]=e[1].replace($,""),e[3]=(e[4]||e[5]||"").replace($,""),e[2]==="~="&&(e[3]=" "+e[3]+" "),e.slice(0,4)},CHILD:function(e){return e[1]=e[1].toLowerCase(),e[1]==="nth"?(e[2]||nt.error(e[0]),e[3]=+(e[3]?e[4]+(e[5]||1):2*(e[2]==="even"||e[2]==="odd")),e[4]=+(e[6]+e[7]||e[2]==="odd")):e[2]&&nt.error(e[0]),e},PSEUDO:function(e){var t,n;if(J.CHILD.test(e[0]))return null;if(e[3])e[2]=e[3];else if(t=e[4])q.test(t)&&(n=ut(t,!0))&&(n=t.indexOf(")",t.length-n)-t.length)&&(t=t.slice(0,n),e[0]=e[0].slice(0,n)),e[2]=t;return e.slice(0,3)}},filter:{ID:r?function(e){return e=e.replace($,""),function(t){return t.getAttribute("id")===e}}:function(e){return e=e.replace($,""),function(t){var n=typeof t.getAttributeNode!==p&&t.getAttributeNode("id");return n&&n.value===e}},TAG:function(e){return e==="*"?function(){return!0}:(e=e.replace($,"").toLowerCase(),function(t){return t.nodeName&&t.nodeName.toLowerCase()===e})},CLASS:function(e){var t=k[d][e+" "];return t||(t=new RegExp("(^|"+O+")"+e+"("+O+"|$)"))&&k(e,function(e){return t.test(e.className||typeof e.getAttribute!==p&&e.getAttribute("class")||"")})},ATTR:function(e,t,n){return function(r,i){var s=nt.attr(r,e);return s==null?t==="!=":t?(s+="",t==="="?s===n:t==="!="?s!==n:t==="^="?n&&s.indexOf(n)===0:t==="*="?n&&s.indexOf(n)>-1:t==="$="?n&&s.substr(s.length-n.length)===n:t==="~="?(" "+s+" ").indexOf(n)>-1:t==="|="?s===n||s.substr(0,n.length+1)===n+"-":!1):!0}},CHILD:function(e,t,n,r){return e==="nth"?function(e){var t,i,s=e.parentNode;if(n===1&&r===0)return!0;if(s){i=0;for(t=s.firstChild;t;t=t.nextSibling)if(t.nodeType===1){i++;if(e===t)break}}return i-=r,i===n||i%n===0&&i/n>=0}:function(t){var n=t;switch(e){case"only":case"first":while(n=n.previousSibling)if(n.nodeType===1)return!1;if(e==="first")return!0;n=t;case"last":while(n=n.nextSibling)if(n.nodeType===1)return!1;return!0}}},PSEUDO:function(e,t){var n,r=i.pseudos[e]||i.setFilters[e.toLowerCase()]||nt.error("unsupported pseudo: "+e);return r[d]?r(t):r.length>1?(n=[e,e,"",t],i.setFilters.hasOwnProperty(e.toLowerCase())?N(function(e,n){var i,s=r(e,t),o=s.length;while(o--)i=T.call(e,s[o]),e[i]=!(n[i]=s[o])}):function(e){return r(e,0,n)}):r}},pseudos:{not:N(function(e){var t=[],n=[],r=a(e.replace(j,"$1"));return r[d]?N(function(e,t,n,i){var s,o=r(e,null,i,[]),u=e.length;while(u--)if(s=o[u])e[u]=!(t[u]=s)}):function(e,i,s){return t[0]=e,r(t,null,s,n),!n.pop()}}),has:N(function(e){return function(t){return nt(e,t).length>0}}),contains:N(function(e){return function(t){return(t.textContent||t.innerText||s(t)).indexOf(e)>-1}}),enabled:function(e){return e.disabled===!1},disabled:function(e){return e.disabled===!0},checked:function(e){var t=e.nodeName.toLowerCase();return t==="input"&&!!e.checked||t==="option"&&!!e.selected},selected:function(e){return e.parentNode&&e.parentNode.selectedIndex,e.selected===!0},parent:function(e){return!i.pseudos.empty(e)},empty:function(e){var t;e=e.firstChild;while(e){if(e.nodeName>"@"||(t=e.nodeType)===3||t===4)return!1;e=e.nextSibling}return!0},header:function(e){return X.test(e.nodeName)},text:function(e){var t,n;return e.nodeName.toLowerCase()==="input"&&(t=e.type)==="text"&&((n=e.getAttribute("type"))==null||n.toLowerCase()===t)},radio:rt("radio"),checkbox:rt("checkbox"),file:rt("file"),password:rt("password"),image:rt("image"),submit:it("submit"),reset:it("reset"),button:function(e){var t=e.nodeName.toLowerCase();return t==="input"&&e.type==="button"||t==="button"},input:function(e){return V.test(e.nodeName)},focus:function(e){var t=e.ownerDocument;return e===t.activeElement&&(!t.hasFocus||t.hasFocus())&&!!(e.type||e.href||~e.tabIndex)},active:function(e){return e===e.ownerDocument.activeElement},first:st(function(){return[0]}),last:st(function(e,t){return[t-1]}),eq:st(function(e,t,n){return[n<0?n+t:n]}),even:st(function(e,t){for(var n=0;n<t;n+=2)e.push(n);return e}),odd:st(function(e,t){for(var n=1;n<t;n+=2)e.push(n);return e}),lt:st(function(e,t,n){for(var r=n<0?n+t:n;--r>=0;)e.push(r);return e}),gt:st(function(e,t,n){for(var r=n<0?n+t:n;++r<t;)e.push(r);return e})}},f=y.compareDocumentPosition?function(e,t){return e===t?(l=!0,0):(!e.compareDocumentPosition||!t.compareDocumentPosition?e.compareDocumentPosition:e.compareDocumentPosition(t)&4)?-1:1}:function(e,t){if(e===t)return l=!0,0;if(e.sourceIndex&&t.sourceIndex)return e.sourceIndex-t.sourceIndex;var n,r,i=[],s=[],o=e.parentNode,u=t.parentNode,a=o;if(o===u)return ot(e,t);if(!o)return-1;if(!u)return 1;while(a)i.unshift(a),a=a.parentNode;a=u;while(a)s.unshift(a),a=a.parentNode;n=i.length,r=s.length;for(var f=0;f<n&&f<r;f++)if(i[f]!==s[f])return ot(i[f],s[f]);return f===n?ot(e,s[f],-1):ot(i[f],t,1)},[0,0].sort(f),h=!l,nt.uniqueSort=function(e){var t,n=[],r=1,i=0;l=h,e.sort(f);if(l){for(;t=e[r];r++)t===e[r-1]&&(i=n.push(r));while(i--)e.splice(n[i],1)}return e},nt.error=function(e){throw new Error("Syntax error, unrecognized expression: "+e)},a=nt.compile=function(e,t){var n,r=[],i=[],s=A[d][e+" "];if(!s){t||(t=ut(e)),n=t.length;while(n--)s=ht(t[n]),s[d]?r.push(s):i.push(s);s=A(e,pt(i,r))}return s},g.querySelectorAll&&function(){var e,t=vt,n=/'|\\/g,r=/\=[\x20\t\r\n\f]*([^'"\]]*)[\x20\t\r\n\f]*\]/g,i=[":focus"],s=[":active"],u=y.matchesSelector||y.mozMatchesSelector||y.webkitMatchesSelector||y.oMatchesSelector||y.msMatchesSelector;K(function(e){e.innerHTML="<select><option selected=''></option></select>",e.querySelectorAll("[selected]").length||i.push("\\["+O+"*(?:checked|disabled|ismap|multiple|readonly|selected|value)"),e.querySelectorAll(":checked").length||i.push(":checked")}),K(function(e){e.innerHTML="<p test=''></p>",e.querySelectorAll("[test^='']").length&&i.push("[*^$]="+O+"*(?:\"\"|'')"),e.innerHTML="<input type='hidden'/>",e.querySelectorAll(":enabled").length||i.push(":enabled",":disabled")}),i=new RegExp(i.join("|")),vt=function(e,r,s,o,u){if(!o&&!u&&!i.test(e)){var a,f,l=!0,c=d,h=r,p=r.nodeType===9&&e;if(r.nodeType===1&&r.nodeName.toLowerCase()!=="object"){a=ut(e),(l=r.getAttribute("id"))?c=l.replace(n,"\\$&"):r.setAttribute("id",c),c="[id='"+c+"'] ",f=a.length;while(f--)a[f]=c+a[f].join("");h=z.test(e)&&r.parentNode||r,p=a.join(",")}if(p)try{return S.apply(s,x.call(h.querySelectorAll(p),0)),s}catch(v){}finally{l||r.removeAttribute("id")}}return t(e,r,s,o,u)},u&&(K(function(t){e=u.call(t,"div");try{u.call(t,"[test!='']:sizzle"),s.push("!=",H)}catch(n){}}),s=new RegExp(s.join("|")),nt.matchesSelector=function(t,n){n=n.replace(r,"='$1']");if(!o(t)&&!s.test(n)&&!i.test(n))try{var a=u.call(t,n);if(a||e||t.document&&t.document.nodeType!==11)return a}catch(f){}return nt(n,null,null,[t]).length>0})}(),i.pseudos.nth=i.pseudos.eq,i.filters=mt.prototype=i.pseudos,i.setFilters=new mt,nt.attr=v.attr,v.find=nt,v.expr=nt.selectors,v.expr[":"]=v.expr.pseudos,v.unique=nt.uniqueSort,v.text=nt.getText,v.isXMLDoc=nt.isXML,v.contains=nt.contains}(e);var nt=/Until$/,rt=/^(?:parents|prev(?:Until|All))/,it=/^.[^:#\[\.,]*$/,st=v.expr.match.needsContext,ot={children:!0,contents:!0,next:!0,prev:!0};v.fn.extend({find:function(e){var t,n,r,i,s,o,u=this;if(typeof e!="string")return v(e).filter(function(){for(t=0,n=u.length;t<n;t++)if(v.contains(u[t],this))return!0});o=this.pushStack("","find",e);for(t=0,n=this.length;t<n;t++){r=o.length,v.find(e,this[t],o);if(t>0)for(i=r;i<o.length;i++)for(s=0;s<r;s++)if(o[s]===o[i]){o.splice(i--,1);break}}return o},has:function(e){var t,n=v(e,this),r=n.length;return this.filter(function(){for(t=0;t<r;t++)if(v.contains(this,n[t]))return!0})},not:function(e){return this.pushStack(ft(this,e,!1),"not",e)},filter:function(e){return this.pushStack(ft(this,e,!0),"filter",e)},is:function(e){return!!e&&(typeof e=="string"?st.test(e)?v(e,this.context).index(this[0])>=0:v.filter(e,this).length>0:this.filter(e).length>0)},closest:function(e,t){var n,r=0,i=this.length,s=[],o=st.test(e)||typeof e!="string"?v(e,t||this.context):0;for(;r<i;r++){n=this[r];while(n&&n.ownerDocument&&n!==t&&n.nodeType!==11){if(o?o.index(n)>-1:v.find.matchesSelector(n,e)){s.push(n);break}n=n.parentNode}}return s=s.length>1?v.unique(s):s,this.pushStack(s,"closest",e)},index:function(e){return e?typeof e=="string"?v.inArray(this[0],v(e)):v.inArray(e.jquery?e[0]:e,this):this[0]&&this[0].parentNode?this.prevAll().length:-1},add:function(e,t){var n=typeof e=="string"?v(e,t):v.makeArray(e&&e.nodeType?[e]:e),r=v.merge(this.get(),n);return this.pushStack(ut(n[0])||ut(r[0])?r:v.unique(r))},addBack:function(e){return this.add(e==null?this.prevObject:this.prevObject.filter(e))}}),v.fn.andSelf=v.fn.addBack,v.each({parent:function(e){var t=e.parentNode;return t&&t.nodeType!==11?t:null},parents:function(e){return v.dir(e,"parentNode")},parentsUntil:function(e,t,n){return v.dir(e,"parentNode",n)},next:function(e){return at(e,"nextSibling")},prev:function(e){return at(e,"previousSibling")},nextAll:function(e){return v.dir(e,"nextSibling")},prevAll:function(e){return v.dir(e,"previousSibling")},nextUntil:function(e,t,n){return v.dir(e,"nextSibling",n)},prevUntil:function(e,t,n){return v.dir(e,"previousSibling",n)},siblings:function(e){return v.sibling((e.parentNode||{}).firstChild,e)},children:function(e){return v.sibling(e.firstChild)},contents:function(e){return v.nodeName(e,"iframe")?e.contentDocument||e.contentWindow.document:v.merge([],e.childNodes)}},function(e,t){v.fn[e]=function(n,r){var i=v.map(this,t,n);return nt.test(e)||(r=n),r&&typeof r=="string"&&(i=v.filter(r,i)),i=this.length>1&&!ot[e]?v.unique(i):i,this.length>1&&rt.test(e)&&(i=i.reverse()),this.pushStack(i,e,l.call(arguments).join(","))}}),v.extend({filter:function(e,t,n){return n&&(e=":not("+e+")"),t.length===1?v.find.matchesSelector(t[0],e)?[t[0]]:[]:v.find.matches(e,t)},dir:function(e,n,r){var i=[],s=e[n];while(s&&s.nodeType!==9&&(r===t||s.nodeType!==1||!v(s).is(r)))s.nodeType===1&&i.push(s),s=s[n];return i},sibling:function(e,t){var n=[];for(;e;e=e.nextSibling)e.nodeType===1&&e!==t&&n.push(e);return n}});var ct="abbr|article|aside|audio|bdi|canvas|data|datalist|details|figcaption|figure|footer|header|hgroup|mark|meter|nav|output|progress|section|summary|time|video",ht=/ jQuery\d+="(?:null|\d+)"/g,pt=/^\s+/,dt=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:]+)[^>]*)\/>/gi,vt=/<([\w:]+)/,mt=/<tbody/i,gt=/<|&#?\w+;/,yt=/<(?:script|style|link)/i,bt=/<(?:script|object|embed|option|style)/i,wt=new RegExp("<(?:"+ct+")[\\s/>]","i"),Et=/^(?:checkbox|radio)$/,St=/checked\s*(?:[^=]|=\s*.checked.)/i,xt=/\/(java|ecma)script/i,Tt=/^\s*<!(?:\[CDATA\[|\-\-)|[\]\-]{2}>\s*$/g,Nt={option:[1,"<select multiple='multiple'>","</select>"],legend:[1,"<fieldset>","</fieldset>"],thead:[1,"<table>","</table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],col:[2,"<table><tbody></tbody><colgroup>","</colgroup></table>"],area:[1,"<map>"
,"</map>"],_default:[0,"",""]},Ct=lt(i),kt=Ct.appendChild(i.createElement("div"));Nt.optgroup=Nt.option,Nt.tbody=Nt.tfoot=Nt.colgroup=Nt.caption=Nt.thead,Nt.th=Nt.td,v.support.htmlSerialize||(Nt._default=[1,"X<div>","</div>"]),v.fn.extend({text:function(e){return v.access(this,function(e){return e===t?v.text(this):this.empty().append((this[0]&&this[0].ownerDocument||i).createTextNode(e))},null,e,arguments.length)},wrapAll:function(e){if(v.isFunction(e))return this.each(function(t){v(this).wrapAll(e.call(this,t))});if(this[0]){var t=v(e,this[0].ownerDocument).eq(0).clone(!0);this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){var e=this;while(e.firstChild&&e.firstChild.nodeType===1)e=e.firstChild;return e}).append(this)}return this},wrapInner:function(e){return v.isFunction(e)?this.each(function(t){v(this).wrapInner(e.call(this,t))}):this.each(function(){var t=v(this),n=t.contents();n.length?n.wrapAll(e):t.append(e)})},wrap:function(e){var t=v.isFunction(e);return this.each(function(n){v(this).wrapAll(t?e.call(this,n):e)})},unwrap:function(){return this.parent().each(function(){v.nodeName(this,"body")||v(this).replaceWith(this.childNodes)}).end()},append:function(){return this.domManip(arguments,!0,function(e){(this.nodeType===1||this.nodeType===11)&&this.appendChild(e)})},prepend:function(){return this.domManip(arguments,!0,function(e){(this.nodeType===1||this.nodeType===11)&&this.insertBefore(e,this.firstChild)})},before:function(){if(!ut(this[0]))return this.domManip(arguments,!1,function(e){this.parentNode.insertBefore(e,this)});if(arguments.length){var e=v.clean(arguments);return this.pushStack(v.merge(e,this),"before",this.selector)}},after:function(){if(!ut(this[0]))return this.domManip(arguments,!1,function(e){this.parentNode.insertBefore(e,this.nextSibling)});if(arguments.length){var e=v.clean(arguments);return this.pushStack(v.merge(this,e),"after",this.selector)}},remove:function(e,t){var n,r=0;for(;(n=this[r])!=null;r++)if(!e||v.filter(e,[n]).length)!t&&n.nodeType===1&&(v.cleanData(n.getElementsByTagName("*")),v.cleanData([n])),n.parentNode&&n.parentNode.removeChild(n);return this},empty:function(){var e,t=0;for(;(e=this[t])!=null;t++){e.nodeType===1&&v.cleanData(e.getElementsByTagName("*"));while(e.firstChild)e.removeChild(e.firstChild)}return this},clone:function(e,t){return e=e==null?!1:e,t=t==null?e:t,this.map(function(){return v.clone(this,e,t)})},html:function(e){return v.access(this,function(e){var n=this[0]||{},r=0,i=this.length;if(e===t)return n.nodeType===1?n.innerHTML.replace(ht,""):t;if(typeof e=="string"&&!yt.test(e)&&(v.support.htmlSerialize||!wt.test(e))&&(v.support.leadingWhitespace||!pt.test(e))&&!Nt[(vt.exec(e)||["",""])[1].toLowerCase()]){e=e.replace(dt,"<$1></$2>");try{for(;r<i;r++)n=this[r]||{},n.nodeType===1&&(v.cleanData(n.getElementsByTagName("*")),n.innerHTML=e);n=0}catch(s){}}n&&this.empty().append(e)},null,e,arguments.length)},replaceWith:function(e){return ut(this[0])?this.length?this.pushStack(v(v.isFunction(e)?e():e),"replaceWith",e):this:v.isFunction(e)?this.each(function(t){var n=v(this),r=n.html();n.replaceWith(e.call(this,t,r))}):(typeof e!="string"&&(e=v(e).detach()),this.each(function(){var t=this.nextSibling,n=this.parentNode;v(this).remove(),t?v(t).before(e):v(n).append(e)}))},detach:function(e){return this.remove(e,!0)},domManip:function(e,n,r){e=[].concat.apply([],e);var i,s,o,u,a=0,f=e[0],l=[],c=this.length;if(!v.support.checkClone&&c>1&&typeof f=="string"&&St.test(f))return this.each(function(){v(this).domManip(e,n,r)});if(v.isFunction(f))return this.each(function(i){var s=v(this);e[0]=f.call(this,i,n?s.html():t),s.domManip(e,n,r)});if(this[0]){i=v.buildFragment(e,this,l),o=i.fragment,s=o.firstChild,o.childNodes.length===1&&(o=s);if(s){n=n&&v.nodeName(s,"tr");for(u=i.cacheable||c-1;a<c;a++)r.call(n&&v.nodeName(this[a],"table")?Lt(this[a],"tbody"):this[a],a===u?o:v.clone(o,!0,!0))}o=s=null,l.length&&v.each(l,function(e,t){t.src?v.ajax?v.ajax({url:t.src,type:"GET",dataType:"script",async:!1,global:!1,"throws":!0}):v.error("no ajax"):v.globalEval((t.text||t.textContent||t.innerHTML||"").replace(Tt,"")),t.parentNode&&t.parentNode.removeChild(t)})}return this}}),v.buildFragment=function(e,n,r){var s,o,u,a=e[0];return n=n||i,n=!n.nodeType&&n[0]||n,n=n.ownerDocument||n,e.length===1&&typeof a=="string"&&a.length<512&&n===i&&a.charAt(0)==="<"&&!bt.test(a)&&(v.support.checkClone||!St.test(a))&&(v.support.html5Clone||!wt.test(a))&&(o=!0,s=v.fragments[a],u=s!==t),s||(s=n.createDocumentFragment(),v.clean(e,n,s,r),o&&(v.fragments[a]=u&&s)),{fragment:s,cacheable:o}},v.fragments={},v.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(e,t){v.fn[e]=function(n){var r,i=0,s=[],o=v(n),u=o.length,a=this.length===1&&this[0].parentNode;if((a==null||a&&a.nodeType===11&&a.childNodes.length===1)&&u===1)return o[t](this[0]),this;for(;i<u;i++)r=(i>0?this.clone(!0):this).get(),v(o[i])[t](r),s=s.concat(r);return this.pushStack(s,e,o.selector)}}),v.extend({clone:function(e,t,n){var r,i,s,o;v.support.html5Clone||v.isXMLDoc(e)||!wt.test("<"+e.nodeName+">")?o=e.cloneNode(!0):(kt.innerHTML=e.outerHTML,kt.removeChild(o=kt.firstChild));if((!v.support.noCloneEvent||!v.support.noCloneChecked)&&(e.nodeType===1||e.nodeType===11)&&!v.isXMLDoc(e)){Ot(e,o),r=Mt(e),i=Mt(o);for(s=0;r[s];++s)i[s]&&Ot(r[s],i[s])}if(t){At(e,o);if(n){r=Mt(e),i=Mt(o);for(s=0;r[s];++s)At(r[s],i[s])}}return r=i=null,o},clean:function(e,t,n,r){var s,o,u,a,f,l,c,h,p,d,m,g,y=t===i&&Ct,b=[];if(!t||typeof t.createDocumentFragment=="undefined")t=i;for(s=0;(u=e[s])!=null;s++){typeof u=="number"&&(u+="");if(!u)continue;if(typeof u=="string")if(!gt.test(u))u=t.createTextNode(u);else{y=y||lt(t),c=t.createElement("div"),y.appendChild(c),u=u.replace(dt,"<$1></$2>"),a=(vt.exec(u)||["",""])[1].toLowerCase(),f=Nt[a]||Nt._default,l=f[0],c.innerHTML=f[1]+u+f[2];while(l--)c=c.lastChild;if(!v.support.tbody){h=mt.test(u),p=a==="table"&&!h?c.firstChild&&c.firstChild.childNodes:f[1]==="<table>"&&!h?c.childNodes:[];for(o=p.length-1;o>=0;--o)v.nodeName(p[o],"tbody")&&!p[o].childNodes.length&&p[o].parentNode.removeChild(p[o])}!v.support.leadingWhitespace&&pt.test(u)&&c.insertBefore(t.createTextNode(pt.exec(u)[0]),c.firstChild),u=c.childNodes,c.parentNode.removeChild(c)}u.nodeType?b.push(u):v.merge(b,u)}c&&(u=c=y=null);if(!v.support.appendChecked)for(s=0;(u=b[s])!=null;s++)v.nodeName(u,"input")?_t(u):typeof u.getElementsByTagName!="undefined"&&v.grep(u.getElementsByTagName("input"),_t);if(n){m=function(e){if(!e.type||xt.test(e.type))return r?r.push(e.parentNode?e.parentNode.removeChild(e):e):n.appendChild(e)};for(s=0;(u=b[s])!=null;s++)if(!v.nodeName(u,"script")||!m(u))n.appendChild(u),typeof u.getElementsByTagName!="undefined"&&(g=v.grep(v.merge([],u.getElementsByTagName("script")),m),b.splice.apply(b,[s+1,0].concat(g)),s+=g.length)}return b},cleanData:function(e,t){var n,r,i,s,o=0,u=v.expando,a=v.cache,f=v.support.deleteExpando,l=v.event.special;for(;(i=e[o])!=null;o++)if(t||v.acceptData(i)){r=i[u],n=r&&a[r];if(n){if(n.events)for(s in n.events)l[s]?v.event.remove(i,s):v.removeEvent(i,s,n.handle);a[r]&&(delete a[r],f?delete i[u]:i.removeAttribute?i.removeAttribute(u):i[u]=null,v.deletedIds.push(r))}}}}),function(){var e,t;v.uaMatch=function(e){e=e.toLowerCase();var t=/(chrome)[ \/]([\w.]+)/.exec(e)||/(webkit)[ \/]([\w.]+)/.exec(e)||/(opera)(?:.*version|)[ \/]([\w.]+)/.exec(e)||/(msie) ([\w.]+)/.exec(e)||e.indexOf("compatible")<0&&/(mozilla)(?:.*? rv:([\w.]+)|)/.exec(e)||[];return{browser:t[1]||"",version:t[2]||"0"}},e=v.uaMatch(o.userAgent),t={},e.browser&&(t[e.browser]=!0,t.version=e.version),t.chrome?t.webkit=!0:t.webkit&&(t.safari=!0),v.browser=t,v.sub=function(){function e(t,n){return new e.fn.init(t,n)}v.extend(!0,e,this),e.superclass=this,e.fn=e.prototype=this(),e.fn.constructor=e,e.sub=this.sub,e.fn.init=function(r,i){return i&&i instanceof v&&!(i instanceof e)&&(i=e(i)),v.fn.init.call(this,r,i,t)},e.fn.init.prototype=e.fn;var t=e(i);return e}}();var Dt,Pt,Ht,Bt=/alpha\([^)]*\)/i,jt=/opacity=([^)]*)/,Ft=/^(top|right|bottom|left)$/,It=/^(none|table(?!-c[ea]).+)/,qt=/^margin/,Rt=new RegExp("^("+m+")(.*)$","i"),Ut=new RegExp("^("+m+")(?!px)[a-z%]+$","i"),zt=new RegExp("^([-+])=("+m+")","i"),Wt={BODY:"block"},Xt={position:"absolute",visibility:"hidden",display:"block"},Vt={letterSpacing:0,fontWeight:400},$t=["Top","Right","Bottom","Left"],Jt=["Webkit","O","Moz","ms"],Kt=v.fn.toggle;v.fn.extend({css:function(e,n){return v.access(this,function(e,n,r){return r!==t?v.style(e,n,r):v.css(e,n)},e,n,arguments.length>1)},show:function(){return Yt(this,!0)},hide:function(){return Yt(this)},toggle:function(e,t){var n=typeof e=="boolean";return v.isFunction(e)&&v.isFunction(t)?Kt.apply(this,arguments):this.each(function(){(n?e:Gt(this))?v(this).show():v(this).hide()})}}),v.extend({cssHooks:{opacity:{get:function(e,t){if(t){var n=Dt(e,"opacity");return n===""?"1":n}}}},cssNumber:{fillOpacity:!0,fontWeight:!0,lineHeight:!0,opacity:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{"float":v.support.cssFloat?"cssFloat":"styleFloat"},style:function(e,n,r,i){if(!e||e.nodeType===3||e.nodeType===8||!e.style)return;var s,o,u,a=v.camelCase(n),f=e.style;n=v.cssProps[a]||(v.cssProps[a]=Qt(f,a)),u=v.cssHooks[n]||v.cssHooks[a];if(r===t)return u&&"get"in u&&(s=u.get(e,!1,i))!==t?s:f[n];o=typeof r,o==="string"&&(s=zt.exec(r))&&(r=(s[1]+1)*s[2]+parseFloat(v.css(e,n)),o="number");if(r==null||o==="number"&&isNaN(r))return;o==="number"&&!v.cssNumber[a]&&(r+="px");if(!u||!("set"in u)||(r=u.set(e,r,i))!==t)try{f[n]=r}catch(l){}},css:function(e,n,r,i){var s,o,u,a=v.camelCase(n);return n=v.cssProps[a]||(v.cssProps[a]=Qt(e.style,a)),u=v.cssHooks[n]||v.cssHooks[a],u&&"get"in u&&(s=u.get(e,!0,i)),s===t&&(s=Dt(e,n)),s==="normal"&&n in Vt&&(s=Vt[n]),r||i!==t?(o=parseFloat(s),r||v.isNumeric(o)?o||0:s):s},swap:function(e,t,n){var r,i,s={};for(i in t)s[i]=e.style[i],e.style[i]=t[i];r=n.call(e);for(i in t)e.style[i]=s[i];return r}}),e.getComputedStyle?Dt=function(t,n){var r,i,s,o,u=e.getComputedStyle(t,null),a=t.style;return u&&(r=u.getPropertyValue(n)||u[n],r===""&&!v.contains(t.ownerDocument,t)&&(r=v.style(t,n)),Ut.test(r)&&qt.test(n)&&(i=a.width,s=a.minWidth,o=a.maxWidth,a.minWidth=a.maxWidth=a.width=r,r=u.width,a.width=i,a.minWidth=s,a.maxWidth=o)),r}:i.documentElement.currentStyle&&(Dt=function(e,t){var n,r,i=e.currentStyle&&e.currentStyle[t],s=e.style;return i==null&&s&&s[t]&&(i=s[t]),Ut.test(i)&&!Ft.test(t)&&(n=s.left,r=e.runtimeStyle&&e.runtimeStyle.left,r&&(e.runtimeStyle.left=e.currentStyle.left),s.left=t==="fontSize"?"1em":i,i=s.pixelLeft+"px",s.left=n,r&&(e.runtimeStyle.left=r)),i===""?"auto":i}),v.each(["height","width"],function(e,t){v.cssHooks[t]={get:function(e,n,r){if(n)return e.offsetWidth===0&&It.test(Dt(e,"display"))?v.swap(e,Xt,function(){return tn(e,t,r)}):tn(e,t,r)},set:function(e,n,r){return Zt(e,n,r?en(e,t,r,v.support.boxSizing&&v.css(e,"boxSizing")==="border-box"):0)}}}),v.support.opacity||(v.cssHooks.opacity={get:function(e,t){return jt.test((t&&e.currentStyle?e.currentStyle.filter:e.style.filter)||"")?.01*parseFloat(RegExp.$1)+"":t?"1":""},set:function(e,t){var n=e.style,r=e.currentStyle,i=v.isNumeric(t)?"alpha(opacity="+t*100+")":"",s=r&&r.filter||n.filter||"";n.zoom=1;if(t>=1&&v.trim(s.replace(Bt,""))===""&&n.removeAttribute){n.removeAttribute("filter");if(r&&!r.filter)return}n.filter=Bt.test(s)?s.replace(Bt,i):s+" "+i}}),v(function(){v.support.reliableMarginRight||(v.cssHooks.marginRight={get:function(e,t){return v.swap(e,{display:"inline-block"},function(){if(t)return Dt(e,"marginRight")})}}),!v.support.pixelPosition&&v.fn.position&&v.each(["top","left"],function(e,t){v.cssHooks[t]={get:function(e,n){if(n){var r=Dt(e,t);return Ut.test(r)?v(e).position()[t]+"px":r}}}})}),v.expr&&v.expr.filters&&(v.expr.filters.hidden=function(e){return e.offsetWidth===0&&e.offsetHeight===0||!v.support.reliableHiddenOffsets&&(e.style&&e.style.display||Dt(e,"display"))==="none"},v.expr.filters.visible=function(e){return!v.expr.filters.hidden(e)}),v.each({margin:"",padding:"",border:"Width"},function(e,t){v.cssHooks[e+t]={expand:function(n){var r,i=typeof n=="string"?n.split(" "):[n],s={};for(r=0;r<4;r++)s[e+$t[r]+t]=i[r]||i[r-2]||i[0];return s}},qt.test(e)||(v.cssHooks[e+t].set=Zt)});var rn=/%20/g,sn=/\[\]$/,on=/\r?\n/g,un=/^(?:color|date|datetime|datetime-local|email|hidden|month|number|password|range|search|tel|text|time|url|week)$/i,an=/^(?:select|textarea)/i;v.fn.extend({serialize:function(){return v.param(this.serializeArray())},serializeArray:function(){return this.map(function(){return this.elements?v.makeArray(this.elements):this}).filter(function(){return this.name&&!this.disabled&&(this.checked||an.test(this.nodeName)||un.test(this.type))}).map(function(e,t){var n=v(this).val();return n==null?null:v.isArray(n)?v.map(n,function(e,n){return{name:t.name,value:e.replace(on,"\r\n")}}):{name:t.name,value:n.replace(on,"\r\n")}}).get()}}),v.param=function(e,n){var r,i=[],s=function(e,t){t=v.isFunction(t)?t():t==null?"":t,i[i.length]=encodeURIComponent(e)+"="+encodeURIComponent(t)};n===t&&(n=v.ajaxSettings&&v.ajaxSettings.traditional);if(v.isArray(e)||e.jquery&&!v.isPlainObject(e))v.each(e,function(){s(this.name,this.value)});else for(r in e)fn(r,e[r],n,s);return i.join("&").replace(rn,"+")};var ln,cn,hn=/#.*$/,pn=/^(.*?):[ \t]*([^\r\n]*)\r?$/mg,dn=/^(?:about|app|app\-storage|.+\-extension|file|res|widget):$/,vn=/^(?:GET|HEAD)$/,mn=/^\/\//,gn=/\?/,yn=/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,bn=/([?&])_=[^&]*/,wn=/^([\w\+\.\-]+:)(?:\/\/([^\/?#:]*)(?::(\d+)|)|)/,En=v.fn.load,Sn={},xn={},Tn=["*/"]+["*"];try{cn=s.href}catch(Nn){cn=i.createElement("a"),cn.href="",cn=cn.href}ln=wn.exec(cn.toLowerCase())||[],v.fn.load=function(e,n,r){if(typeof e!="string"&&En)return En.apply(this,arguments);if(!this.length)return this;var i,s,o,u=this,a=e.indexOf(" ");return a>=0&&(i=e.slice(a,e.length),e=e.slice(0,a)),v.isFunction(n)?(r=n,n=t):n&&typeof n=="object"&&(s="POST"),v.ajax({url:e,type:s,dataType:"html",data:n,complete:function(e,t){r&&u.each(r,o||[e.responseText,t,e])}}).done(function(e){o=arguments,u.html(i?v("<div>").append(e.replace(yn,"")).find(i):e)}),this},v.each("ajaxStart ajaxStop ajaxComplete ajaxError ajaxSuccess ajaxSend".split(" "),function(e,t){v.fn[t]=function(e){return this.on(t,e)}}),v.each(["get","post"],function(e,n){v[n]=function(e,r,i,s){return v.isFunction(r)&&(s=s||i,i=r,r=t),v.ajax({type:n,url:e,data:r,success:i,dataType:s})}}),v.extend({getScript:function(e,n){return v.get(e,t,n,"script")},getJSON:function(e,t,n){return v.get(e,t,n,"json")},ajaxSetup:function(e,t){return t?Ln(e,v.ajaxSettings):(t=e,e=v.ajaxSettings),Ln(e,t),e},ajaxSettings:{url:cn,isLocal:dn.test(ln[1]),global:!0,type:"GET",contentType:"application/x-www-form-urlencoded; charset=UTF-8",processData:!0,async:!0,accepts:{xml:"application/xml, text/xml",html:"text/html",text:"text/plain",json:"application/json, text/javascript","*":Tn},contents:{xml:/xml/,html:/html/,json:/json/},responseFields:{xml:"responseXML",text:"responseText"},converters:{"* text":e.String,"text html":!0,"text json":v.parseJSON,"text xml":v.parseXML},flatOptions:{context:!0,url:!0}},ajaxPrefilter:Cn(Sn),ajaxTransport:Cn(xn),ajax:function(e,n){function T(e,n,s,a){var l,y,b,w,S,T=n;if(E===2)return;E=2,u&&clearTimeout(u),o=t,i=a||"",x.readyState=e>0?4:0,s&&(w=An(c,x,s));if(e>=200&&e<300||e===304)c.ifModified&&(S=x.getResponseHeader("Last-Modified"),S&&(v.lastModified[r]=S),S=x.getResponseHeader("Etag"),S&&(v.etag[r]=S)),e===304?(T="notmodified",l=!0):(l=On(c,w),T=l.state,y=l.data,b=l.error,l=!b);else{b=T;if(!T||e)T="error",e<0&&(e=0)}x.status=e,x.statusText=(n||T)+"",l?d.resolveWith(h,[y,T,x]):d.rejectWith(h,[x,T,b]),x.statusCode(g),g=t,f&&p.trigger("ajax"+(l?"Success":"Error"),[x,c,l?y:b]),m.fireWith(h,[x,T]),f&&(p.trigger("ajaxComplete",[x,c]),--v.active||v.event.trigger("ajaxStop"))}typeof e=="object"&&(n=e,e=t),n=n||{};var r,i,s,o,u,a,f,l,c=v.ajaxSetup({},n),h=c.context||c,p=h!==c&&(h.nodeType||h instanceof v)?v(h):v.event,d=v.Deferred(),m=v.Callbacks("once memory"),g=c.statusCode||{},b={},w={},E=0,S="canceled",x={readyState:0,setRequestHeader:function(e,t){if(!E){var n=e.toLowerCase();e=w[n]=w[n]||e,b[e]=t}return this},getAllResponseHeaders:function(){return E===2?i:null},getResponseHeader:function(e){var n;if(E===2){if(!s){s={};while(n=pn.exec(i))s[n[1].toLowerCase()]=n[2]}n=s[e.toLowerCase()]}return n===t?null:n},overrideMimeType:function(e){return E||(c.mimeType=e),this},abort:function(e){return e=e||S,o&&o.abort(e),T(0,e),this}};d.promise(x),x.success=x.done,x.error=x.fail,x.complete=m.add,x.statusCode=function(e){if(e){var t;if(E<2)for(t in e)g[t]=[g[t],e[t]];else t=e[x.status],x.always(t)}return this},c.url=((e||c.url)+"").replace(hn,"").replace(mn,ln[1]+"//"),c.dataTypes=v.trim(c.dataType||"*").toLowerCase().split(y),c.crossDomain==null&&(a=wn.exec(c.url.toLowerCase()),c.crossDomain=!(!a||a[1]===ln[1]&&a[2]===ln[2]&&(a[3]||(a[1]==="http:"?80:443))==(ln[3]||(ln[1]==="http:"?80:443)))),c.data&&c.processData&&typeof c.data!="string"&&(c.data=v.param(c.data,c.traditional)),kn(Sn,c,n,x);if(E===2)return x;f=c.global,c.type=c.type.toUpperCase(),c.hasContent=!vn.test(c.type),f&&v.active++===0&&v.event.trigger("ajaxStart");if(!c.hasContent){c.data&&(c.url+=(gn.test(c.url)?"&":"?")+c.data,delete c.data),r=c.url;if(c.cache===!1){var N=v.now(),C=c.url.replace(bn,"$1_="+N);c.url=C+(C===c.url?(gn.test(c.url)?"&":"?")+"_="+N:"")}}(c.data&&c.hasContent&&c.contentType!==!1||n.contentType)&&x.setRequestHeader("Content-Type",c.contentType),c.ifModified&&(r=r||c.url,v.lastModified[r]&&x.setRequestHeader("If-Modified-Since",v.lastModified[r]),v.etag[r]&&x.setRequestHeader("If-None-Match",v.etag[r])),x.setRequestHeader("Accept",c.dataTypes[0]&&c.accepts[c.dataTypes[0]]?c.accepts[c.dataTypes[0]]+(c.dataTypes[0]!=="*"?", "+Tn+"; q=0.01":""):c.accepts["*"]);for(l in c.headers)x.setRequestHeader(l,c.headers[l]);if(!c.beforeSend||c.beforeSend.call(h,x,c)!==!1&&E!==2){S="abort";for(l in{success:1,error:1,complete:1})x[l](c[l]);o=kn(xn,c,n,x);if(!o)T(-1,"No Transport");else{x.readyState=1,f&&p.trigger("ajaxSend",[x,c]),c.async&&c.timeout>0&&(u=setTimeout(function(){x.abort("timeout")},c.timeout));try{E=1,o.send(b,T)}catch(k){if(!(E<2))throw k;T(-1,k)}}return x}return x.abort()},active:0,lastModified:{},etag:{}});var Mn=[],_n=/\?/,Dn=/(=)\?(?=&|$)|\?\?/,Pn=v.now();v.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var e=Mn.pop()||v.expando+"_"+Pn++;return this[e]=!0,e}}),v.ajaxPrefilter("json jsonp",function(n,r,i){var s,o,u,a=n.data,f=n.url,l=n.jsonp!==!1,c=l&&Dn.test(f),h=l&&!c&&typeof a=="string"&&!(n.contentType||"").indexOf("application/x-www-form-urlencoded")&&Dn.test(a);if(n.dataTypes[0]==="jsonp"||c||h)return s=n.jsonpCallback=v.isFunction(n.jsonpCallback)?n.jsonpCallback():n.jsonpCallback,o=e[s],c?n.url=f.replace(Dn,"$1"+s):h?n.data=a.replace(Dn,"$1"+s):l&&(n.url+=(_n.test(f)?"&":"?")+n.jsonp+"="+s),n.converters["script json"]=function(){return u||v.error(s+" was not called"),u[0]},n.dataTypes[0]="json",e[s]=function(){u=arguments},i.always(function(){e[s]=o,n[s]&&(n.jsonpCallback=r.jsonpCallback,Mn.push(s)),u&&v.isFunction(o)&&o(u[0]),u=o=t}),"script"}),v.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/javascript|ecmascript/},converters:{"text script":function(e){return v.globalEval(e),e}}}),v.ajaxPrefilter("script",function(e){e.cache===t&&(e.cache=!1),e.crossDomain&&(e.type="GET",e.global=!1)}),v.ajaxTransport("script",function(e){if(e.crossDomain){var n,r=i.head||i.getElementsByTagName("head")[0]||i.documentElement;return{send:function(s,o){n=i.createElement("script"),n.async="async",e.scriptCharset&&(n.charset=e.scriptCharset),n.src=e.url,n.onload=n.onreadystatechange=function(e,i){if(i||!n.readyState||/loaded|complete/.test(n.readyState))n.onload=n.onreadystatechange=null,r&&n.parentNode&&r.removeChild(n),n=t,i||o(200,"success")},r.insertBefore(n,r.firstChild)},abort:function(){n&&n.onload(0,1)}}}});var Hn,Bn=e.ActiveXObject?function(){for(var e in Hn)Hn[e](0,1)}:!1,jn=0;v.ajaxSettings.xhr=e.ActiveXObject?function(){return!this.isLocal&&Fn()||In()}:Fn,function(e){v.extend(v.support,{ajax:!!e,cors:!!e&&"withCredentials"in e})}(v.ajaxSettings.xhr()),v.support.ajax&&v.ajaxTransport(function(n){if(!n.crossDomain||v.support.cors){var r;return{send:function(i,s){var o,u,a=n.xhr();n.username?a.open(n.type,n.url,n.async,n.username,n.password):a.open(n.type,n.url,n.async);if(n.xhrFields)for(u in n.xhrFields)a[u]=n.xhrFields[u];n.mimeType&&a.overrideMimeType&&a.overrideMimeType(n.mimeType),!n.crossDomain&&!i["X-Requested-With"]&&(i["X-Requested-With"]="XMLHttpRequest");try{for(u in i)a.setRequestHeader(u,i[u])}catch(f){}a.send(n.hasContent&&n.data||null),r=function(e,i){var u,f,l,c,h;try{if(r&&(i||a.readyState===4)){r=t,o&&(a.onreadystatechange=v.noop,Bn&&delete Hn[o]);if(i)a.readyState!==4&&a.abort();else{u=a.status,l=a.getAllResponseHeaders(),c={},h=a.responseXML,h&&h.documentElement&&(c.xml=h);try{c.text=a.responseText}catch(p){}try{f=a.statusText}catch(p){f=""}!u&&n.isLocal&&!n.crossDomain?u=c.text?200:404:u===1223&&(u=204)}}}catch(d){i||s(-1,d)}c&&s(u,f,c,l)},n.async?a.readyState===4?setTimeout(r,0):(o=++jn,Bn&&(Hn||(Hn={},v(e).unload(Bn)),Hn[o]=r),a.onreadystatechange=r):r()},abort:function(){r&&r(0,1)}}}});var qn,Rn,Un=/^(?:toggle|show|hide)$/,zn=new RegExp("^(?:([-+])=|)("+m+")([a-z%]*)$","i"),Wn=/queueHooks$/,Xn=[Gn],Vn={"*":[function(e,t){var n,r,i=this.createTween(e,t),s=zn.exec(t),o=i.cur(),u=+o||0,a=1,f=20;if(s){n=+s[2],r=s[3]||(v.cssNumber[e]?"":"px");if(r!=="px"&&u){u=v.css(i.elem,e,!0)||n||1;do a=a||".5",u/=a,v.style(i.elem,e,u+r);while(a!==(a=i.cur()/o)&&a!==1&&--f)}i.unit=r,i.start=u,i.end=s[1]?u+(s[1]+1)*n:n}return i}]};v.Animation=v.extend(Kn,{tweener:function(e,t){v.isFunction(e)?(t=e,e=["*"]):e=e.split(" ");var n,r=0,i=e.length;for(;r<i;r++)n=e[r],Vn[n]=Vn[n]||[],Vn[n].unshift(t)},prefilter:function(e,t){t?Xn.unshift(e):Xn.push(e)}}),v.Tween=Yn,Yn.prototype={constructor:Yn,init:function(e,t,n,r,i,s){this.elem=e,this.prop=n,this.easing=i||"swing",this.options=t,this.start=this.now=this.cur(),this.end=r,this.unit=s||(v.cssNumber[n]?"":"px")},cur:function(){var e=Yn.propHooks[this.prop];return e&&e.get?e.get(this):Yn.propHooks._default.get(this)},run:function(e){var t,n=Yn.propHooks[this.prop];return this.options.duration?this.pos=t=v.easing[this.easing](e,this.options.duration*e,0,1,this.options.duration):this.pos=t=e,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),n&&n.set?n.set(this):Yn.propHooks._default.set(this),this}},Yn.prototype.init.prototype=Yn.prototype,Yn.propHooks={_default:{get:function(e){var t;return e.elem[e.prop]==null||!!e.elem.style&&e.elem.style[e.prop]!=null?(t=v.css(e.elem,e.prop,!1,""),!t||t==="auto"?0:t):e.elem[e.prop]},set:function(e){v.fx.step[e.prop]?v.fx.step[e.prop](e):e.elem.style&&(e.elem.style[v.cssProps[e.prop]]!=null||v.cssHooks[e.prop])?v.style(e.elem,e.prop,e.now+e.unit):e.elem[e.prop]=e.now}}},Yn.propHooks.scrollTop=Yn.propHooks.scrollLeft={set:function(e){e.elem.nodeType&&e.elem.parentNode&&(e.elem[e.prop]=e.now)}},v.each(["toggle","show","hide"],function(e,t){var n=v.fn[t];v.fn[t]=function(r,i,s){return r==null||typeof r=="boolean"||!e&&v.isFunction(r)&&v.isFunction(i)?n.apply(this,arguments):this.animate(Zn(t,!0),r,i,s)}}),v.fn.extend({fadeTo:function(e,t,n,r){return this.filter(Gt).css("opacity",0).show().end().animate({opacity:t},e,n,r)},animate:function(e,t,n,r){var i=v.isEmptyObject(e),s=v.speed(t,n,r),o=function(){var t=Kn(this,v.extend({},e),s);i&&t.stop(!0)};return i||s.queue===!1?this.each(o):this.queue(s.queue,o)},stop:function(e,n,r){var i=function(e){var t=e.stop;delete e.stop,t(r)};return typeof e!="string"&&(r=n,n=e,e=t),n&&e!==!1&&this.queue(e||"fx",[]),this.each(function(){var t=!0,n=e!=null&&e+"queueHooks",s=v.timers,o=v._data(this);if(n)o[n]&&o[n].stop&&i(o[n]);else for(n in o)o[n]&&o[n].stop&&Wn.test(n)&&i(o[n]);for(n=s.length;n--;)s[n].elem===this&&(e==null||s[n].queue===e)&&(s[n].anim.stop(r),t=!1,s.splice(n,1));(t||!r)&&v.dequeue(this,e)})}}),v.each({slideDown:Zn("show"),slideUp:Zn("hide"),slideToggle:Zn("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(e,t){v.fn[e]=function(e,n,r){return this.animate(t,e,n,r)}}),v.speed=function(e,t,n){var r=e&&typeof e=="object"?v.extend({},e):{complete:n||!n&&t||v.isFunction(e)&&e,duration:e,easing:n&&t||t&&!v.isFunction(t)&&t};r.duration=v.fx.off?0:typeof r.duration=="number"?r.duration:r.duration in v.fx.speeds?v.fx.speeds[r.duration]:v.fx.speeds._default;if(r.queue==null||r.queue===!0)r.queue="fx";return r.old=r.complete,r.complete=function(){v.isFunction(r.old)&&r.old.call(this),r.queue&&v.dequeue(this,r.queue)},r},v.easing={linear:function(e){return e},swing:function(e){return.5-Math.cos(e*Math.PI)/2}},v.timers=[],v.fx=Yn.prototype.init,v.fx.tick=function(){var e,n=v.timers,r=0;qn=v.now();for(;r<n.length;r++)e=n[r],!e()&&n[r]===e&&n.splice(r--,1);n.length||v.fx.stop(),qn=t},v.fx.timer=function(e){e()&&v.timers.push(e)&&!Rn&&(Rn=setInterval(v.fx.tick,v.fx.interval))},v.fx.interval=13,v.fx.stop=function(){clearInterval(Rn),Rn=null},v.fx.speeds={slow:600,fast:200,_default:400},v.fx.step={},v.expr&&v.expr.filters&&(v.expr.filters.animated=function(e){return v.grep(v.timers,function(t){return e===t.elem}).length});var er=/^(?:body|html)$/i;v.fn.offset=function(e){if(arguments.length)return e===t?this:this.each(function(t){v.offset.setOffset(this,e,t)});var n,r,i,s,o,u,a,f={top:0,left:0},l=this[0],c=l&&l.ownerDocument;if(!c)return;return(r=c.body)===l?v.offset.bodyOffset(l):(n=c.documentElement,v.contains(n,l)?(typeof l.getBoundingClientRect!="undefined"&&(f=l.getBoundingClientRect()),i=tr(c),s=n.clientTop||r.clientTop||0,o=n.clientLeft||r.clientLeft||0,u=i.pageYOffset||n.scrollTop,a=i.pageXOffset||n.scrollLeft,{top:f.top+u-s,left:f.left+a-o}):f)},v.offset={bodyOffset:function(e){var t=e.offsetTop,n=e.offsetLeft;return v.support.doesNotIncludeMarginInBodyOffset&&(t+=parseFloat(v.css(e,"marginTop"))||0,n+=parseFloat(v.css(e,"marginLeft"))||0),{top:t,left:n}},setOffset:function(e,t,n){var r=v.css(e,"position");r==="static"&&(e.style.position="relative");var i=v(e),s=i.offset(),o=v.css(e,"top"),u=v.css(e,"left"),a=(r==="absolute"||r==="fixed")&&v.inArray("auto",[o,u])>-1,f={},l={},c,h;a?(l=i.position(),c=l.top,h=l.left):(c=parseFloat(o)||0,h=parseFloat(u)||0),v.isFunction(t)&&(t=t.call(e,n,s)),t.top!=null&&(f.top=t.top-s.top+c),t.left!=null&&(f.left=t.left-s.left+h),"using"in t?t.using.call(e,f):i.css(f)}},v.fn.extend({position:function(){if(!this[0])return;var e=this[0],t=this.offsetParent(),n=this.offset(),r=er.test(t[0].nodeName)?{top:0,left:0}:t.offset();return n.top-=parseFloat(v.css(e,"marginTop"))||0,n.left-=parseFloat(v.css(e,"marginLeft"))||0,r.top+=parseFloat(v.css(t[0],"borderTopWidth"))||0,r.left+=parseFloat(v.css(t[0],"borderLeftWidth"))||0,{top:n.top-r.top,left:n.left-r.left}},offsetParent:function(){return this.map(function(){var e=this.offsetParent||i.body;while(e&&!er.test(e.nodeName)&&v.css(e,"position")==="static")e=e.offsetParent;return e||i.body})}}),v.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(e,n){var r=/Y/.test(n);v.fn[e]=function(i){return v.access(this,function(e,i,s){var o=tr(e);if(s===t)return o?n in o?o[n]:o.document.documentElement[i]:e[i];o?o.scrollTo(r?v(o).scrollLeft():s,r?s:v(o).scrollTop()):e[i]=s},e,i,arguments.length,null)}}),v.each({Height:"height",Width:"width"},function(e,n){v.each({padding:"inner"+e,content:n,"":"outer"+e},function(r,i){v.fn[i]=function(i,s){var o=arguments.length&&(r||typeof i!="boolean"),u=r||(i===!0||s===!0?"margin":"border");return v.access(this,function(n,r,i){var s;return v.isWindow(n)?n.document.documentElement["client"+e]:n.nodeType===9?(s=n.documentElement,Math.max(n.body["scroll"+e],s["scroll"+e],n.body["offset"+e],s["offset"+e],s["client"+e])):i===t?v.css(n,r,i,u):v.style(n,r,i,u)},n,o?i:t,o,null)}})}),e.jQuery=e.$=v,typeof define=="function"&&define.amd&&define.amd.jQuery&&define("jquery",[],function(){return v})})(window),function(e,t){var n=function(){var t=e._data(document,"events");return t&&t.click&&e.grep(t.click,function(e){return e.namespace==="rails"}).length};n()&&e.error("jquery-ujs has already been loaded!");var r;e.rails=r={linkClickSelector:"a[data-confirm], a[data-method], a[data-remote], a[data-disable-with]",inputChangeSelector:"select[data-remote], input[data-remote], textarea[data-remote]",formSubmitSelector:"form",formInputClickSelector:"form input[type=submit], form input[type=image], form button[type=submit], form button:not([type])",disableSelector:"input[data-disable-with], button[data-disable-with], textarea[data-disable-with]",enableSelector:"input[data-disable-with]:disabled, button[data-disable-with]:disabled, textarea[data-disable-with]:disabled",requiredInputSelector:"input[name][required]:not([disabled]),textarea[name][required]:not([disabled])",fileInputSelector:"input:file",linkDisableSelector:"a[data-disable-with]",CSRFProtection:function(t){var n=e('meta[name="csrf-token"]').attr("content");n&&t.setRequestHeader("X-CSRF-Token",n)},fire:function(t,n,r){var i=e.Event(n);return t.trigger(i,r),i.result!==!1},confirm:function(e){return confirm(e)},ajax:function(t){return e.ajax(t)},href:function(e){return e.attr("href")},handleRemote:function(n){var i,s,o,u,a,f,l,c;if(r.fire(n,"ajax:before")){u=n.data("cross-domain"),a=u===t?null:u,f=n.data("with-credentials")||null,l=n.data("type")||e.ajaxSettings&&e.ajaxSettings.dataType;if(n.is("form")){i=n.attr("method"),s=n.attr("action"),o=n.serializeArray();var h=n.data("ujs:submit-button");h&&(o.push(h),n.data("ujs:submit-button",null))}else n.is(r.inputChangeSelector)?(i=n.data("method"),s=n.data("url"),o=n.serialize(),n.data("params")&&(o=o+"&"+n.data("params"))):(i=n.data("method"),s=r.href(n),o=n.data("params")||null);c={type:i||"GET",data:o,dataType:l,beforeSend:function(e,i){return i.dataType===t&&e.setRequestHeader("accept","*/*;q=0.5, "+i.accepts.script),r.fire(n,"ajax:beforeSend",[e,i])},success:function(e,t,r){n.trigger("ajax:success",[e,t,r])},complete:function(e,t){n.trigger("ajax:complete",[e,t])},error:function(e,t,r){n.trigger("ajax:error",[e,t,r])},xhrFields:{withCredentials:f},crossDomain:a},s&&(c.url=s);var p=r.ajax(c);return n.trigger("ajax:send",p),p}return!1},handleMethod:function(n){var i=r.href(n),s=n.data("method"),o=n.attr("target"),u=e("meta[name=csrf-token]").attr("content"),a=e("meta[name=csrf-param]").attr("content"),f=e('<form method="post" action="'+i+'"></form>'),l='<input name="_method" value="'+s+'" type="hidden" />';a!==t&&u!==t&&(l+='<input name="'+a+'" value="'+u+'" type="hidden" />'),o&&f.attr("target",o),f.hide().append(l).appendTo("body"),f.submit()},disableFormElements:function(t){t.find(r.disableSelector).each(function(){var t=e(this),n=t.is("button")?"html":"val";t.data("ujs:enable-with",t[n]()),t[n](t.data("disable-with")),t.prop("disabled",!0)})},enableFormElements:function(t){t.find(r.enableSelector).each(function(){var t=e(this),n=t.is("button")?"html":"val";t.data("ujs:enable-with")&&t[n](t.data("ujs:enable-with")),t.prop("disabled",!1)})},allowAction:function(e){var t=e.data("confirm"),n=!1,i;return t?(r.fire(e,"confirm")&&(n=r.confirm(t),i=r.fire(e,"confirm:complete",[n])),n&&i):!0},blankInputs:function(t,n,r){var i=e(),s,o,u=n||"input,textarea",a=t.find(u);return a.each(function(){s=e(this),o=s.is(":checkbox,:radio")?s.is(":checked"):s.val();if(!o==!r){if(s.is(":radio")&&a.filter('input:radio:checked[name="'+s.attr("name")+'"]').length)return!0;i=i.add(s)}}),i.length?i:!1},nonBlankInputs:function(e,t){return r.blankInputs(e,t,!0)},stopEverything:function(t){return e(t.target).trigger("ujs:everythingStopped"),t.stopImmediatePropagation(),!1},callFormSubmitBindings:function(n,r){var i=n.data("events"),s=!0;return i!==t&&i.submit!==t&&e.each(i.submit,function(e,t){if(typeof t.handler=="function")return s=t.handler(r)}),s},disableElement:function(e){e.data("ujs:enable-with",e.html()),e.html(e.data("disable-with")),e.bind("click.railsDisable",function(e){return r.stopEverything(e)})},enableElement:function(e){e.data("ujs:enable-with")!==t&&(e.html(e.data("ujs:enable-with")),e.data("ujs:enable-with",!1)),e.unbind("click.railsDisable")}},r.fire(e(document),"rails:attachBindings")&&(e.ajaxPrefilter(function(e,t,n){e.crossDomain||r.CSRFProtection(n)}),e(document).delegate(r.linkDisableSelector,"ajax:complete",function(){r.enableElement(e(this))}),e(document).delegate
(r.linkClickSelector,"click.rails",function(n){var i=e(this),s=i.data("method"),o=i.data("params");if(!r.allowAction(i))return r.stopEverything(n);i.is(r.linkDisableSelector)&&r.disableElement(i);if(i.data("remote")!==t){if((n.metaKey||n.ctrlKey)&&(!s||s==="GET")&&!o)return!0;var u=r.handleRemote(i);return u===!1?r.enableElement(i):u.error(function(){r.enableElement(i)}),!1}if(i.data("method"))return r.handleMethod(i),!1}),e(document).delegate(r.inputChangeSelector,"change.rails",function(t){var n=e(this);return r.allowAction(n)?(r.handleRemote(n),!1):r.stopEverything(t)}),e(document).delegate(r.formSubmitSelector,"submit.rails",function(n){var i=e(this),s=i.data("remote")!==t,o=r.blankInputs(i,r.requiredInputSelector),u=r.nonBlankInputs(i,r.fileInputSelector);if(!r.allowAction(i))return r.stopEverything(n);if(o&&i.attr("novalidate")==t&&r.fire(i,"ajax:aborted:required",[o]))return r.stopEverything(n);if(s){if(u){setTimeout(function(){r.disableFormElements(i)},13);var a=r.fire(i,"ajax:aborted:file",[u]);return a||setTimeout(function(){r.enableFormElements(i)},13),a}return!e.support.submitBubbles&&e().jquery<"1.7"&&r.callFormSubmitBindings(i,n)===!1?r.stopEverything(n):(r.handleRemote(i),!1)}setTimeout(function(){r.disableFormElements(i)},13)}),e(document).delegate(r.formInputClickSelector,"click.rails",function(t){var n=e(this);if(!r.allowAction(n))return r.stopEverything(t);var i=n.attr("name"),s=i?{name:i,value:n.val()}:null;n.closest("form").data("ujs:submit-button",s)}),e(document).delegate(r.formSubmitSelector,"ajax:beforeSend.rails",function(t){this==t.target&&r.disableFormElements(e(this))}),e(document).delegate(r.formSubmitSelector,"ajax:complete.rails",function(t){this==t.target&&r.enableFormElements(e(this))}),e(function(){csrf_token=e("meta[name=csrf-token]").attr("content"),csrf_param=e("meta[name=csrf-param]").attr("content"),e('form input[name="'+csrf_param+'"]').val(csrf_token)}))}(jQuery),function(e){e.extend(e.fn,{livequery:function(t,n,r){var i=this,s;return e.isFunction(t)&&(r=n,n=t,t=undefined),e.each(e.livequery.queries,function(e,o){if(i.selector==o.selector&&i.context==o.context&&t==o.type&&(!n||n.$lqguid==o.fn.$lqguid)&&(!r||r.$lqguid==o.fn2.$lqguid))return(s=o)&&!1}),s=s||new e.livequery(this.selector,this.context,t,n,r),s.stopped=!1,s.run(),this},expire:function(t,n,r){var i=this;return e.isFunction(t)&&(r=n,n=t,t=undefined),e.each(e.livequery.queries,function(s,o){i.selector==o.selector&&i.context==o.context&&(!t||t==o.type)&&(!n||n.$lqguid==o.fn.$lqguid)&&(!r||r.$lqguid==o.fn2.$lqguid)&&!this.stopped&&e.livequery.stop(o.id)}),this}}),e.livequery=function(t,n,r,i,s){return this.selector=t,this.context=n,this.type=r,this.fn=i,this.fn2=s,this.elements=[],this.stopped=!1,this.id=e.livequery.queries.push(this)-1,i.$lqguid=i.$lqguid||e.livequery.guid++,s&&(s.$lqguid=s.$lqguid||e.livequery.guid++),this},e.livequery.prototype={stop:function(){var e=this;this.type?this.elements.unbind(this.type,this.fn):this.fn2&&this.elements.each(function(t,n){e.fn2.apply(n)}),this.elements=[],this.stopped=!0},run:function(){if(this.stopped)return;var t=this,n=this.elements,r=e(this.selector,this.context),i=r.not(n);this.elements=r,this.type?(i.bind(this.type,this.fn),n.length>0&&e.each(n,function(n,i){e.inArray(i,r)<0&&e.event.remove(i,t.type,t.fn)})):(i.each(function(){t.fn.apply(this)}),this.fn2&&n.length>0&&e.each(n,function(n,i){e.inArray(i,r)<0&&t.fn2.apply(i)}))}},e.extend(e.livequery,{guid:0,queries:[],queue:[],running:!1,timeout:null,checkQueue:function(){if(e.livequery.running&&e.livequery.queue.length){var t=e.livequery.queue.length;while(t--)e.livequery.queries[e.livequery.queue.shift()].run()}},pause:function(){e.livequery.running=!1},play:function(){e.livequery.running=!0,e.livequery.run()},registerPlugin:function(){e.each(arguments,function(t,n){if(!e.fn[n])return;var r=e.fn[n];e.fn[n]=function(){var t=r.apply(this,arguments);return e.livequery.run(),t}})},run:function(t){t!=undefined?e.inArray(t,e.livequery.queue)<0&&e.livequery.queue.push(t):e.each(e.livequery.queries,function(t){e.inArray(t,e.livequery.queue)<0&&e.livequery.queue.push(t)}),e.livequery.timeout&&clearTimeout(e.livequery.timeout),e.livequery.timeout=setTimeout(e.livequery.checkQueue,20)},stop:function(t){t!=undefined?e.livequery.queries[t].stop():e.each(e.livequery.queries,function(t){e.livequery.queries[t].stop()})}}),e.livequery.registerPlugin("append","prepend","after","before","wrap","attr","removeAttr","addClass","removeClass","toggleClass","empty","remove","html"),e(function(){e.livequery.play()})}(jQuery),function(e){typeof define=="function"&&define.amd?define(["jquery"],e):e(window.jQuery||window.$)}(function(e){var t={className:"autosizejs",append:"",callback:!1,resizeDelay:10},n='<textarea tabindex="-1" style="position:absolute; top:-999px; left:0; right:auto; bottom:auto; border:0; -moz-box-sizing:content-box; -webkit-box-sizing:content-box; box-sizing:content-box; word-wrap:break-word; height:0 !important; min-height:0 !important; overflow:hidden; transition:none; -webkit-transition:none; -moz-transition:none;"/>',r=["fontFamily","fontSize","fontWeight","fontStyle","letterSpacing","textTransform","wordSpacing","textIndent"],i,s=e(n).data("autosize",!0)[0];s.style.lineHeight="99px",e(s).css("lineHeight")==="99px"&&r.push("lineHeight"),s.style.lineHeight="",e.fn.autosize=function(n){return n=e.extend({},t,n||{}),s.parentNode!==document.body&&e(document.body).append(s),this.each(function(){function d(){var n,r;"getComputedStyle"in window?(n=window.getComputedStyle(t),r=t.getBoundingClientRect().width,e.each(["paddingLeft","paddingRight","borderLeftWidth","borderRightWidth"],function(e,t){r-=parseInt(n[t],10)}),s.style.width=r+"px"):s.style.width=Math.max(o.width(),0)+"px"}function v(){var a={};i=t,s.className=n.className,u=parseInt(o.css("maxHeight"),10),e.each(r,function(e,t){a[t]=o.css(t)}),e(s).css(a),d();if("oninput"in t&&"setSelectionRange"in t){var f=s.offsetWidth,l=t.selectionStart;t.value+=" ",t.value=t.value.slice(0,-1),t.setSelectionRange(l,l)}}function m(){var e,r;i!==t?v():d(),s.value=t.value+n.append,s.style.overflowY=t.style.overflowY,r=parseInt(t.style.height,10),s.scrollTop=0,s.scrollTop=9e4,e=s.scrollTop,u&&e>u?(t.style.overflowY="scroll",e=u):(t.style.overflowY="hidden",e<a&&(e=a)),e+=f,r!==e&&(t.style.height=e+"px",l&&n.callback.call(t,t))}function g(){clearTimeout(h),h=setTimeout(function(){var e=o.width();e!==p&&(p=e,m())},parseInt(n.resizeDelay,10))}var t=this,o=e(t),u,a,f=0,l=e.isFunction(n.callback),c={height:t.style.height,overflow:t.style.overflow,overflowY:t.style.overflowY,wordWrap:t.style.wordWrap,resize:t.style.resize},h,p=o.width();if(o.data("autosize"))return;o.data("autosize",!0);if(o.css("box-sizing")==="border-box"||o.css("-moz-box-sizing")==="border-box"||o.css("-webkit-box-sizing")==="border-box")f=o.outerHeight()-o.height();a=Math.max(parseInt(o.css("minHeight"),10)-f||0,o.height()),o.css({overflow:"hidden",overflowY:"hidden",wordWrap:"break-word",resize:o.css("resize")==="none"||o.css("resize")==="vertical"?"none":"horizontal"}),"onpropertychange"in t?"oninput"in t?o.on("input.autosize keyup.autosize",m):o.on("propertychange.autosize",function(){event.propertyName==="value"&&m()}):o.on("input.autosize",m),n.resizeDelay!==!1&&e(window).on("resize.autosize",g),o.on("autosize.resize",m),o.on("autosize.resizeIncludeStyle",function(){i=null,m()}),o.on("autosize.destroy",function(){i=null,clearTimeout(h),e(window).off("resize",g),o.off("autosize").off(".autosize").css(c).removeData("autosize")}),m()})}}),function(e,t,n){function i(e){return e}function s(e){return decodeURIComponent(e.replace(r," "))}var r=/\+/g,o=e.cookie=function(r,u,a){if(u!==n){a=e.extend({},o.defaults,a),u===null&&(a.expires=-1);if(typeof a.expires=="number"){var f=a.expires,l=a.expires=new Date;l.setDate(l.getDate()+f)}return u=o.json?JSON.stringify(u):String(u),t.cookie=[encodeURIComponent(r),"=",o.raw?u:encodeURIComponent(u),a.expires?"; expires="+a.expires.toUTCString():"",a.path?"; path="+a.path:"",a.domain?"; domain="+a.domain:"",a.secure?"; secure":""].join("")}var c=o.raw?i:s,h=t.cookie.split("; ");for(var p=0,d=h.length;p<d;p++){var v=h[p].split("=");if(c(v.shift())===r){var m=c(v.join("="));return o.json?JSON.parse(m):m}}return null};o.defaults={},e.removeCookie=function(t,n){return e.cookie(t)!==null?(e.cookie(t,null,n),!0):!1}}(jQuery,document),typeof JSON!="object"&&(JSON={}),function(){"use strict";function f(e){return e<10?"0"+e:e}function quote(e){return escapable.lastIndex=0,escapable.test(e)?'"'+e.replace(escapable,function(e){var t=meta[e];return typeof t=="string"?t:"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)})+'"':'"'+e+'"'}function str(e,t){var n,r,i,s,o=gap,u,a=t[e];a&&typeof a=="object"&&typeof a.toJSON=="function"&&(a=a.toJSON(e)),typeof rep=="function"&&(a=rep.call(t,e,a));switch(typeof a){case"string":return quote(a);case"number":return isFinite(a)?String(a):"null";case"boolean":case"null":return String(a);case"object":if(!a)return"null";gap+=indent,u=[];if(Object.prototype.toString.apply(a)==="[object Array]"){s=a.length;for(n=0;n<s;n+=1)u[n]=str(n,a)||"null";return i=u.length===0?"[]":gap?"[\n"+gap+u.join(",\n"+gap)+"\n"+o+"]":"["+u.join(",")+"]",gap=o,i}if(rep&&typeof rep=="object"){s=rep.length;for(n=0;n<s;n+=1)typeof rep[n]=="string"&&(r=rep[n],i=str(r,a),i&&u.push(quote(r)+(gap?": ":":")+i))}else for(r in a)Object.prototype.hasOwnProperty.call(a,r)&&(i=str(r,a),i&&u.push(quote(r)+(gap?": ":":")+i));return i=u.length===0?"{}":gap?"{\n"+gap+u.join(",\n"+gap)+"\n"+o+"}":"{"+u.join(",")+"}",gap=o,i}}typeof Date.prototype.toJSON!="function"&&(Date.prototype.toJSON=function(e){return isFinite(this.valueOf())?this.getUTCFullYear()+"-"+f(this.getUTCMonth()+1)+"-"+f(this.getUTCDate())+"T"+f(this.getUTCHours())+":"+f(this.getUTCMinutes())+":"+f(this.getUTCSeconds())+"Z":null},String.prototype.toJSON=Number.prototype.toJSON=Boolean.prototype.toJSON=function(e){return this.valueOf()});var cx=/[\u0000\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,escapable=/[\\\"\x00-\x1f\x7f-\x9f\u00ad\u0600-\u0604\u070f\u17b4\u17b5\u200c-\u200f\u2028-\u202f\u2060-\u206f\ufeff\ufff0-\uffff]/g,gap,indent,meta={"\b":"\\b","	":"\\t","\n":"\\n","\f":"\\f","\r":"\\r",'"':'\\"',"\\":"\\\\"},rep;typeof JSON.stringify!="function"&&(JSON.stringify=function(e,t,n){var r;gap="",indent="";if(typeof n=="number")for(r=0;r<n;r+=1)indent+=" ";else typeof n=="string"&&(indent=n);rep=t;if(!t||typeof t=="function"||typeof t=="object"&&typeof t.length=="number")return str("",{"":e});throw new Error("JSON.stringify")}),typeof JSON.parse!="function"&&(JSON.parse=function(text,reviver){function walk(e,t){var n,r,i=e[t];if(i&&typeof i=="object")for(n in i)Object.prototype.hasOwnProperty.call(i,n)&&(r=walk(i,n),r!==undefined?i[n]=r:delete i[n]);return reviver.call(e,t,i)}var j;text=String(text),cx.lastIndex=0,cx.test(text)&&(text=text.replace(cx,function(e){return"\\u"+("0000"+e.charCodeAt(0).toString(16)).slice(-4)}));if(/^[\],:{}\s]*$/.test(text.replace(/\\(?:["\\\/bfnrt]|u[0-9a-fA-F]{4})/g,"@").replace(/"[^"\\\n\r]*"|true|false|null|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?/g,"]").replace(/(?:^|:|,)(?:\s*\[)+/g,"")))return j=eval("("+text+")"),typeof reviver=="function"?walk({"":j},""):j;throw new SyntaxError("JSON.parse")})}(),!function(e){function t(){return new Date(Date.UTC.apply(Date,arguments))}function n(){var e=new Date;return t(e.getUTCFullYear(),e.getUTCMonth(),e.getUTCDate())}var r=function(t,n){var r=this;this.element=e(t),this.language=n.language||this.element.data("date-language")||"en",this.language=this.language in i?this.language:"en",this.format=s.parseFormat(n.format||this.element.data("date-format")||"mm/dd/yyyy"),this.isInline=!1,this.isInput=this.element.is("input"),this.component=this.element.is(".date")?this.element.find(".add-on"):!1,this.hasInput=this.component&&this.element.find("input").length,this.component&&this.component.length===0&&(this.component=!1),this._attachEvents(),this.forceParse=!0,"forceParse"in n?this.forceParse=n.forceParse:"dateForceParse"in this.element.data()&&(this.forceParse=this.element.data("date-force-parse")),this.picker=e(s.template).appendTo(this.isInline?this.element:"body").on({click:e.proxy(this.click,this),mousedown:e.proxy(this.mousedown,this)}),this.isInline?this.picker.addClass("datepicker-inline"):this.picker.addClass("datepicker-dropdown dropdown-menu"),e(document).on("mousedown",function(t){e(t.target).closest(".datepicker").length===0&&r.hide()}),this.autoclose=!1,"autoclose"in n?this.autoclose=n.autoclose:"dateAutoclose"in this.element.data()&&(this.autoclose=this.element.data("date-autoclose")),this.keyboardNavigation=!0,"keyboardNavigation"in n?this.keyboardNavigation=n.keyboardNavigation:"dateKeyboardNavigation"in this.element.data()&&(this.keyboardNavigation=this.element.data("date-keyboard-navigation")),this.viewMode=this.startViewMode=0;switch(n.startView||this.element.data("date-start-view")){case 2:case"decade":this.viewMode=this.startViewMode=2;break;case 1:case"year":this.viewMode=this.startViewMode=1}this.todayBtn=n.todayBtn||this.element.data("date-today-btn")||!1,this.todayHighlight=n.todayHighlight||this.element.data("date-today-highlight")||!1,this.weekStart=(n.weekStart||this.element.data("date-weekstart")||i[this.language].weekStart||0)%7,this.weekEnd=(this.weekStart+6)%7,this.startDate=-Infinity,this.endDate=Infinity,this.daysOfWeekDisabled=[],this.setStartDate(n.startDate||this.element.data("date-startdate")),this.setEndDate(n.endDate||this.element.data("date-enddate")),this.setDaysOfWeekDisabled(n.daysOfWeekDisabled||this.element.data("date-days-of-week-disabled")),this.fillDow(),this.fillMonths(),this.update(),this.showMode(),this.isInline&&this.show()};r.prototype={constructor:r,_events:[],_attachEvents:function(){this._detachEvents(),this.isInput?this._events=[[this.element,{focus:e.proxy(this.show,this),keyup:e.proxy(this.update,this),keydown:e.proxy(this.keydown,this)}]]:this.component&&this.hasInput?this._events=[[this.element.find("input"),{focus:e.proxy(this.show,this),keyup:e.proxy(this.update,this),keydown:e.proxy(this.keydown,this)}],[this.component,{click:e.proxy(this.show,this)}]]:this.element.is("div")?this.isInline=!0:this._events=[[this.element,{click:e.proxy(this.show,this)}]];for(var t=0,n,r;t<this._events.length;t++)n=this._events[t][0],r=this._events[t][1],n.on(r)},_detachEvents:function(){for(var e=0,t,n;e<this._events.length;e++)t=this._events[e][0],n=this._events[e][1],t.off(n);this._events=[]},show:function(t){this.picker.show(),this.height=this.component?this.component.outerHeight():this.element.outerHeight(),this.update(),this.place(),e(window).on("resize",e.proxy(this.place,this)),t&&(t.stopPropagation(),t.preventDefault()),this.element.trigger({type:"show",date:this.date})},hide:function(t){if(this.isInline)return;this.picker.hide(),e(window).off("resize",this.place),this.viewMode=this.startViewMode,this.showMode(),this.isInput||e(document).off("mousedown",this.hide),this.forceParse&&(this.isInput&&this.element.val()||this.hasInput&&this.element.find("input").val())&&this.setValue(),this.element.trigger({type:"hide",date:this.date})},remove:function(){this._detachEvents(),this.picker.remove(),delete this.element.data().datepicker},getDate:function(){var e=this.getUTCDate();return new Date(e.getTime()+e.getTimezoneOffset()*6e4)},getUTCDate:function(){return this.date},setDate:function(e){this.setUTCDate(new Date(e.getTime()-e.getTimezoneOffset()*6e4))},setUTCDate:function(e){this.date=e,this.setValue()},setValue:function(){var e=this.getFormattedDate();this.isInput?this.element.prop("value",e):(this.component&&this.element.find("input").prop("value",e),this.element.data("date",e))},getFormattedDate:function(e){return e==undefined&&(e=this.format),s.formatDate(this.date,e,this.language)},setStartDate:function(e){this.startDate=e||-Infinity,this.startDate!==-Infinity&&(this.startDate=s.parseDate(this.startDate,this.format,this.language)),this.update(),this.updateNavArrows()},setEndDate:function(e){this.endDate=e||Infinity,this.endDate!==Infinity&&(this.endDate=s.parseDate(this.endDate,this.format,this.language)),this.update(),this.updateNavArrows()},setDaysOfWeekDisabled:function(t){this.daysOfWeekDisabled=t||[],e.isArray(this.daysOfWeekDisabled)||(this.daysOfWeekDisabled=this.daysOfWeekDisabled.split(/,\s*/)),this.daysOfWeekDisabled=e.map(this.daysOfWeekDisabled,function(e){return parseInt(e,10)}),this.update(),this.updateNavArrows()},place:function(){if(this.isInline)return;var t=parseInt(this.element.parents().filter(function(){return e(this).css("z-index")!="auto"}).first().css("z-index"))+10,n=this.component?this.component.offset():this.element.offset();this.picker.css({top:n.top+this.height,left:n.left,zIndex:t})},update:function(){var e,t=!1;arguments&&arguments.length&&(typeof arguments[0]=="string"||arguments[0]instanceof Date)?(e=arguments[0],t=!0):e=this.isInput?this.element.prop("value"):this.element.data("date")||this.element.find("input").prop("value"),this.date=s.parseDate(e,this.format,this.language),t&&this.setValue(),this.date<this.startDate?this.viewDate=new Date(this.startDate):this.date>this.endDate?this.viewDate=new Date(this.endDate):this.viewDate=new Date(this.date),this.fill()},fillDow:function(){var e=this.weekStart,t="<tr>";while(e<this.weekStart+7)t+='<th class="dow">'+i[this.language].daysMin[e++%7]+"</th>";t+="</tr>",this.picker.find(".datepicker-days thead").append(t)},fillMonths:function(){var e="",t=0;while(t<12)e+='<span class="month">'+i[this.language].monthsShort[t++]+"</span>";this.picker.find(".datepicker-months td").html(e)},fill:function(){var n=new Date(this.viewDate),r=n.getUTCFullYear(),o=n.getUTCMonth(),u=this.startDate!==-Infinity?this.startDate.getUTCFullYear():-Infinity,a=this.startDate!==-Infinity?this.startDate.getUTCMonth():-Infinity,f=this.endDate!==Infinity?this.endDate.getUTCFullYear():Infinity,l=this.endDate!==Infinity?this.endDate.getUTCMonth():Infinity,c=this.date.valueOf(),h=new Date;this.picker.find(".datepicker-days thead th:eq(1)").text(i[this.language].months[o]+" "+r),this.picker.find("tfoot th.today").text(i[this.language].today).toggle(this.todayBtn!==!1),this.updateNavArrows(),this.fillMonths();var p=t(r,o-1,28,0,0,0,0),d=s.getDaysInMonth(p.getUTCFullYear(),p.getUTCMonth());p.setUTCDate(d),p.setUTCDate(d-(p.getUTCDay()-this.weekStart+7)%7);var v=new Date(p);v.setUTCDate(v.getUTCDate()+42),v=v.valueOf();var m=[],g;while(p.valueOf()<v){p.getUTCDay()==this.weekStart&&m.push("<tr>"),g="";if(p.getUTCFullYear()<r||p.getUTCFullYear()==r&&p.getUTCMonth()<o)g+=" old";else if(p.getUTCFullYear()>r||p.getUTCFullYear()==r&&p.getUTCMonth()>o)g+=" new";this.todayHighlight&&p.getUTCFullYear()==h.getFullYear()&&p.getUTCMonth()==h.getMonth()&&p.getUTCDate()==h.getDate()&&(g+=" today"),p.valueOf()==c&&(g+=" active");if(p.valueOf()<this.startDate||p.valueOf()>this.endDate||e.inArray(p.getUTCDay(),this.daysOfWeekDisabled)!==-1)g+=" disabled";m.push('<td class="day'+g+'">'+p.getUTCDate()+"</td>"),p.getUTCDay()==this.weekEnd&&m.push("</tr>"),p.setUTCDate(p.getUTCDate()+1)}this.picker.find(".datepicker-days tbody").empty().append(m.join(""));var y=this.date.getUTCFullYear(),b=this.picker.find(".datepicker-months").find("th:eq(1)").text(r).end().find("span").removeClass("active");y==r&&b.eq(this.date.getUTCMonth()).addClass("active"),(r<u||r>f)&&b.addClass("disabled"),r==u&&b.slice(0,a).addClass("disabled"),r==f&&b.slice(l+1).addClass("disabled"),m="",r=parseInt(r/10,10)*10;var w=this.picker.find(".datepicker-years").find("th:eq(1)").text(r+"-"+(r+9)).end().find("td");r-=1;for(var E=-1;E<11;E++)m+='<span class="year'+(E==-1||E==10?" old":"")+(y==r?" active":"")+(r<u||r>f?" disabled":"")+'">'+r+"</span>",r+=1;w.html(m)},updateNavArrows:function(){var e=new Date(this.viewDate),t=e.getUTCFullYear(),n=e.getUTCMonth();switch(this.viewMode){case 0:this.startDate!==-Infinity&&t<=this.startDate.getUTCFullYear()&&n<=this.startDate.getUTCMonth()?this.picker.find(".prev").css({visibility:"hidden"}):this.picker.find(".prev").css({visibility:"visible"}),this.endDate!==Infinity&&t>=this.endDate.getUTCFullYear()&&n>=this.endDate.getUTCMonth()?this.picker.find(".next").css({visibility:"hidden"}):this.picker.find(".next").css({visibility:"visible"});break;case 1:case 2:this.startDate!==-Infinity&&t<=this.startDate.getUTCFullYear()?this.picker.find(".prev").css({visibility:"hidden"}):this.picker.find(".prev").css({visibility:"visible"}),this.endDate!==Infinity&&t>=this.endDate.getUTCFullYear()?this.picker.find(".next").css({visibility:"hidden"}):this.picker.find(".next").css({visibility:"visible"})}},click:function(n){n.stopPropagation(),n.preventDefault();var r=e(n.target).closest("span, td, th");if(r.length==1)switch(r[0].nodeName.toLowerCase()){case"th":switch(r[0].className){case"switch":this.showMode(1);break;case"prev":case"next":var i=s.modes[this.viewMode].navStep*(r[0].className=="prev"?-1:1);switch(this.viewMode){case 0:this.viewDate=this.moveMonth(this.viewDate,i);break;case 1:case 2:this.viewDate=this.moveYear(this.viewDate,i)}this.fill();break;case"today":var o=new Date;o=t(o.getFullYear(),o.getMonth(),o.getDate(),0,0,0),this.showMode(-2);var u=this.todayBtn=="linked"?null:"view";this._setDate(o,u)}break;case"span":if(!r.is(".disabled")){this.viewDate.setUTCDate(1);if(r.is(".month")){var a=r.parent().find("span").index(r);this.viewDate.setUTCMonth(a),this.element.trigger({type:"changeMonth",date:this.viewDate})}else{var f=parseInt(r.text(),10)||0;this.viewDate.setUTCFullYear(f),this.element.trigger({type:"changeYear",date:this.viewDate})}this.showMode(-1),this.fill()}break;case"td":if(r.is(".day")&&!r.is(".disabled")){var l=parseInt(r.text(),10)||1,f=this.viewDate.getUTCFullYear(),a=this.viewDate.getUTCMonth();r.is(".old")?a===0?(a=11,f-=1):a-=1:r.is(".new")&&(a==11?(a=0,f+=1):a+=1),this._setDate(t(f,a,l,0,0,0,0))}}},_setDate:function(e,t){if(!t||t=="date")this.date=e;if(!t||t=="view")this.viewDate=e;this.fill(),this.setValue(),this.element.trigger({type:"changeDate",date:this.date});var n;this.isInput?n=this.element:this.component&&(n=this.element.find("input")),n&&(n.change(),this.autoclose&&(!t||t=="date")&&this.hide())},moveMonth:function(e,t){if(!t)return e;var n=new Date(e.valueOf()),r=n.getUTCDate(),i=n.getUTCMonth(),s=Math.abs(t),o,u;t=t>0?1:-1;if(s==1){u=t==-1?function(){return n.getUTCMonth()==i}:function(){return n.getUTCMonth()!=o},o=i+t,n.setUTCMonth(o);if(o<0||o>11)o=(o+12)%12}else{for(var a=0;a<s;a++)n=this.moveMonth(n,t);o=n.getUTCMonth(),n.setUTCDate(r),u=function(){return o!=n.getUTCMonth()}}while(u())n.setUTCDate(--r),n.setUTCMonth(o);return n},moveYear:function(e,t){return this.moveMonth(e,t*12)},dateWithinRange:function(e){return e>=this.startDate&&e<=this.endDate},keydown:function(e){if(this.picker.is(":not(:visible)")){e.keyCode==27&&this.show();return}var t=!1,n,r,i,s,o;switch(e.keyCode){case 27:this.hide(),e.preventDefault();break;case 37:case 39:if(!this.keyboardNavigation)break;n=e.keyCode==37?-1:1,e.ctrlKey?(s=this.moveYear(this.date,n),o=this.moveYear(this.viewDate,n)):e.shiftKey?(s=this.moveMonth(this.date,n),o=this.moveMonth(this.viewDate,n)):(s=new Date(this.date),s.setUTCDate(this.date.getUTCDate()+n),o=new Date(this.viewDate),o.setUTCDate(this.viewDate.getUTCDate()+n)),this.dateWithinRange(s)&&(this.date=s,this.viewDate=o,this.setValue(),this.update(),e.preventDefault(),t=!0);break;case 38:case 40:if(!this.keyboardNavigation)break;n=e.keyCode==38?-1:1,e.ctrlKey?(s=this.moveYear(this.date,n),o=this.moveYear(this.viewDate,n)):e.shiftKey?(s=this.moveMonth(this.date,n),o=this.moveMonth(this.viewDate,n)):(s=new Date(this.date),s.setUTCDate(this.date.getUTCDate()+n*7),o=new Date(this.viewDate),o.setUTCDate(this.viewDate.getUTCDate()+n*7)),this.dateWithinRange(s)&&(this.date=s,this.viewDate=o,this.setValue(),this.update(),e.preventDefault(),t=!0);break;case 13:this.hide(),e.preventDefault();break;case 9:this.hide()}if(t){this.element.trigger({type:"changeDate",date:this.date});var u;this.isInput?u=this.element:this.component&&(u=this.element.find("input")),u&&u.change()}},showMode:function(e){e&&(this.viewMode=Math.max(0,Math.min(2,this.viewMode+e))),this.picker.find(">div").hide().filter(".datepicker-"+s.modes[this.viewMode].clsName).css("display","block"),this.updateNavArrows()}},e.fn.datepicker=function(t){var n=Array.apply(null,arguments);return n.shift(),this.each(function(){var i=e(this),s=i.data("datepicker"),o=typeof t=="object"&&t;s||i.data("datepicker",s=new r(this,e.extend({},e.fn.datepicker.defaults,o))),typeof t=="string"&&typeof s[t]=="function"&&s[t].apply(s,n)})},e.fn.datepicker.defaults={},e.fn.datepicker.Constructor=r;var i=e.fn.datepicker.dates={en:{days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday"],daysShort:["Sun","Mon","Tue","Wed","Thu","Fri","Sat","Sun"],daysMin:["Su","Mo","Tu","We","Th","Fr","Sa","Su"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],monthsShort:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],today:"Today"}},s={modes:[{clsName:"days",navFnc:"Month",navStep:1},{clsName:"months",navFnc:"FullYear",navStep:1},{clsName:"years",navFnc:"FullYear",navStep:10}],isLeapYear:function(e){return e%4===0&&e%100!==0||e%400===0},getDaysInMonth:function(e,t){return[31,s.isLeapYear(e)?29:28,31,30,31,30,31,31,30,31,30,31][t]},validParts:/dd?|mm?|MM?|yy(?:yy)?/g,nonpunctuation:/[^ -\/:-@\[-`{-~\t\n\r]+/g,parseFormat:function(e){var t=e.replace(this.validParts,"\0").split("\0"),n=e.match(this.validParts);if(!t||!t.length||!n||n.length==0)throw new Error("Invalid date format.");return{separators:t,parts:n}},parseDate:function(n,s,o){if(n instanceof Date)return n;if(/^[-+]\d+[dmwy]([\s,]+[-+]\d+[dmwy])*$/.test(n)){var u=/([-+]\d+)([dmwy])/,a=n.match(/([-+]\d+)([dmwy])/g),f,l;n=new Date;for(var c=0;c<a.length;c++){f=u.exec(a[c]),l=parseInt(f[1]);switch(f[2]){case"d":n.setUTCDate(n.getUTCDate()+l);break;case"m":n=r.prototype.moveMonth.call(r.prototype,n,l);break;case"w":n.setUTCDate(n.getUTCDate()+l*7);break;case"y":n=r.prototype.moveYear.call(r.prototype,n,l)}}return t(n.getUTCFullYear(),n.getUTCMonth(),n.getUTCDate(),0,0,0)}var a=n&&n.match(this.nonpunctuation)||[],n=new Date,h={},p=["yyyy","yy","M","MM","m","mm","d","dd"],d={yyyy:function(e,t){return e.setUTCFullYear(t)},yy:function(e,t){return e.setUTCFullYear(2e3+t)},m:function(e,t){t-=1;while(t<0)t+=12;t%=12,e.setUTCMonth(t);while(e.getUTCMonth()!=t)e.setUTCDate(e.getUTCDate()-1);return e},d:function(e,t){return e.setUTCDate(t)}},v,m,f;d.M=d.MM=d.mm=d.m,d.dd=d.d,n=t(n.getFullYear(),n.getMonth(),n.getDate(),0,0,0);if(a.length==s.parts.length){for(var c=0,g=s.parts.length;c<g;c++){v=parseInt(a[c],10),f=s.parts[c];if(isNaN(v))switch(f){case"MM":m=e(i[o].months).filter(function(){var e=this.slice(0,a[c].length),t=a[c].slice(0,e.length);return e==t}),v=e.inArray(m[0],i[o].months)+1;break;case"M":m=e(i[o].monthsShort).filter(function(){var e=this.slice(0,a[c].length),t=a[c].slice(0,e.length);return e==t}),v=e.inArray(m[0],i[o].monthsShort)+1}h[f]=v}for(var c=0,y;c<p.length;c++)y=p[c],y in h&&!isNaN(h[y])&&d[y](n,h[y])}return n},formatDate:function(t,n,r){var s={d:t.getUTCDate(),m:t.getUTCMonth()+1,M:i[r].monthsShort[t.getUTCMonth()],MM:i[r].months[t.getUTCMonth()],yy:t.getUTCFullYear().toString().substring(2),yyyy:t.getUTCFullYear()};s.dd=(s.d<10?"0":"")+s.d,s.mm=(s.m<10?"0":"")+s.m;var t=[],o=e.extend([],n.separators);for(var u=0,a=n.parts.length;u<a;u++)o.length&&t.push(o.shift()),t.push(s[n.parts[u]]);return t.join("")},headTemplate:'<thead><tr><th class="prev"><i class="fa fa-arrow-left"/></th><th colspan="5" class="switch"></th><th class="next"><i class="fa fa-arrow-right"/></th></tr></thead>',contTemplate:'<tbody><tr><td colspan="7"></td></tr></tbody>',footTemplate:'<tfoot><tr><th colspan="7" class="today"></th></tr></tfoot>'};s.template='<div class="datepicker"><div class="datepicker-days"><table class=" table-condensed">'+s.headTemplate+"<tbody></tbody>"+s.footTemplate+"</table>"+"</div>"+'<div class="datepicker-months">'+'<table class="table-condensed">'+s.headTemplate+s.contTemplate+s.footTemplate+"</table>"+"</div>"+'<div class="datepicker-years">'+'<table class="table-condensed">'+s.headTemplate+s.contTemplate+s.footTemplate+"</table>"+"</div>"+"</div>",e.fn.datepicker.DPGlobal=s}(window.jQuery),function(e){e.fn.datepicker.dates["zh-CN"]={days:["星期日","星期一","星期二","星期三","星期四","星期五","星期六","星期日"],daysShort:["周日","周一","周二","周三","周四","周五","周六","周日"],daysMin:["日","一","二","三","四","五","六","日"],months:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthsShort:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"]}}(jQuery),window.Modernizr=function(e,t,n){function A(e){f.cssText=e}function O(e,t){return A(p.join(e+";")+(t||""))}function M(e,t){return typeof e===t}function _(e,t){return!!~(""+e).indexOf(t)}function D(e,t){for(var r in e){var i=e[r];if(!_(i,"-")&&f[i]!==n)return t=="pfx"?i:!0}return!1}function P(e,t,r){for(var i in e){var s=t[e[i]];if(s!==n)return r===!1?e[i]:M(s,"function")?s.bind(r||t):s}return!1}function H(e,t,n){var r=e.charAt(0).toUpperCase()+e.slice(1),i=(e+" "+v.join(r+" ")+r).split(" ");return M(t,"string")||M(t,"undefined")?D(i,t):(i=(e+" "+m.join(r+" ")+r).split(" "),P(i,t,n))}function B(){i.input=function(n){for(var r=0,i=n.length;r<i;r++)w[n[r]]=n[r]in l;return w.list&&(w.list=!!t.createElement("datalist")&&!!e.HTMLDataListElement),w}("autocomplete autofocus list placeholder max min multiple pattern required step".split(" ")),i.inputtypes=function(e){for(var r=0,i,s,u,a=e.length;r<a;r++)l.setAttribute("type",s=e[r]),i=l.type!=="text",i&&(l.value=c,l.style.cssText="position:absolute;visibility:hidden;",/^range$/.test(s)&&l.style.WebkitAppearance!==n?(o.appendChild(l),u=t.defaultView,i=u.getComputedStyle&&u.getComputedStyle(l,null).WebkitAppearance!=="textfield"&&l.offsetHeight!==0,o.removeChild(l)):/^(search|tel)$/.test(s)||(/^(url|email)$/.test(s)?i=l.checkValidity&&l.checkValidity()===!1:i=l.value!=c)),b[e[r]]=!!i;return b}("search tel url email datetime date month week time datetime-local number range color".split(" "))}var r="2.6.2",i={},s=!0,o=t.documentElement,u="modernizr",a=t.createElement(u),f=a.style,l=t.createElement("input"),c=":)",h={}.toString,p=" -webkit- -moz- -o- -ms- ".split(" "),d="Webkit Moz O ms",v=d.split(" "),m=d.toLowerCase().split(" "),g={svg:"http://www.w3.org/2000/svg"},y={},b={},w={},E=[],S=E.slice,x,T=function(e,n,r,i){var s,a,f,l,c=t.createElement("div"),h=t.body,p=h||t.createElement("body");if(parseInt(r,10))while(r--)f=t.createElement("div"),f.id=i?i[r]:u+(r+1),c.appendChild(f);return s=["&#173;",'<style id="s',u,'">',e,"</style>"].join(""),c.id=u,(h?c:p).innerHTML+=s,p.appendChild(c),h||(p.style.background="",p.style.overflow="hidden",l=o.style.overflow,o.style.overflow="hidden",o.appendChild(p)),a=n(c,e),h?c.parentNode.removeChild(c):(p.parentNode.removeChild(p),o.style.overflow=l),!!a},N=function(t){var n=e.matchMedia||e.msMatchMedia;if(n)return n(t).matches;var r;return T("@media "+t+" { #"+u+" { position: absolute; } }",function(t){r=(e.getComputedStyle?getComputedStyle(t,null):t.currentStyle)["position"]=="absolute"}),r},C=function(){function r(r,i){i=i||t.createElement(e[r]||"div"),r="on"+r;var s=r in i;return s||(i.setAttribute||(i=t.createElement("div")),i.setAttribute&&i.removeAttribute&&(i.setAttribute(r,""),s=M(i[r],"function"),M(i[r],"undefined")||(i[r]=n),i.removeAttribute(r))),i=null,s}var e={select:"input",change:"input",submit:"form",reset:"form",error:"img",load:"img",abort:"img"};return r}(),k={}.hasOwnProperty,L;!M(k,"undefined")&&!M(k.call,"undefined")?L=function(e,t){return k.call(e,t)}:L=function(e,t){return t in e&&M(e.constructor.prototype[t],"undefined")},Function.prototype.bind||(Function.prototype.bind=function(t){var n=this;if(typeof n!="function")throw new TypeError;var r=S.call(arguments,1),i=function(){if(this instanceof i){var e=function(){};e.prototype=n.prototype;var s=new e,o=n.apply(s,r.concat(S.call(arguments)));return Object(o)===o?o:s}return n.apply(t,r.concat(S.call(arguments)))};return i}),y.flexbox=function(){return H("flexWrap")},y.flexboxlegacy=function(){return H("boxDirection")},y.canvas=function(){var e=t.createElement("canvas");return!!e.getContext&&!!e.getContext("2d")},y.canvastext=function(){return!!i.canvas&&!!M(t.createElement("canvas").getContext("2d").fillText,"function")},y.webgl=function(){return!!e.WebGLRenderingContext},y.touch=function(){var n;return"ontouchstart"in e||e.DocumentTouch&&t instanceof 
DocumentTouch?n=!0:T(["@media (",p.join("touch-enabled),("),u,")","{#modernizr{top:9px;position:absolute}}"].join(""),function(e){n=e.offsetTop===9}),n},y.geolocation=function(){return"geolocation"in navigator},y.postmessage=function(){return!!e.postMessage},y.websqldatabase=function(){return!!e.openDatabase},y.indexedDB=function(){return!!H("indexedDB",e)},y.hashchange=function(){return C("hashchange",e)&&(t.documentMode===n||t.documentMode>7)},y.history=function(){return!!e.history&&!!history.pushState},y.draganddrop=function(){var e=t.createElement("div");return"draggable"in e||"ondragstart"in e&&"ondrop"in e},y.websockets=function(){return"WebSocket"in e||"MozWebSocket"in e},y.rgba=function(){return A("background-color:rgba(150,255,150,.5)"),_(f.backgroundColor,"rgba")},y.hsla=function(){return A("background-color:hsla(120,40%,100%,.5)"),_(f.backgroundColor,"rgba")||_(f.backgroundColor,"hsla")},y.multiplebgs=function(){return A("background:url(https://),url(https://),red url(https://)"),/(url\s*\(.*?){3}/.test(f.background)},y.backgroundsize=function(){return H("backgroundSize")},y.borderimage=function(){return H("borderImage")},y.borderradius=function(){return H("borderRadius")},y.boxshadow=function(){return H("boxShadow")},y.textshadow=function(){return t.createElement("div").style.textShadow===""},y.opacity=function(){return O("opacity:.55"),/^0.55$/.test(f.opacity)},y.cssanimations=function(){return H("animationName")},y.csscolumns=function(){return H("columnCount")},y.cssgradients=function(){var e="background-image:",t="gradient(linear,left top,right bottom,from(#9f9),to(white));",n="linear-gradient(left top,#9f9, white);";return A((e+"-webkit- ".split(" ").join(t+e)+p.join(n+e)).slice(0,-e.length)),_(f.backgroundImage,"gradient")},y.cssreflections=function(){return H("boxReflect")},y.csstransforms=function(){return!!H("transform")},y.csstransforms3d=function(){var e=!!H("perspective");return e&&"webkitPerspective"in o.style&&T("@media (transform-3d),(-webkit-transform-3d){#modernizr{left:9px;position:absolute;height:3px;}}",function(t,n){e=t.offsetLeft===9&&t.offsetHeight===3}),e},y.csstransitions=function(){return H("transition")},y.fontface=function(){var e;return T('@font-face {font-family:"font";src:url("https://")}',function(n,r){var i=t.getElementById("smodernizr"),s=i.sheet||i.styleSheet,o=s?s.cssRules&&s.cssRules[0]?s.cssRules[0].cssText:s.cssText||"":"";e=/src/i.test(o)&&o.indexOf(r.split(" ")[0])===0}),e},y.generatedcontent=function(){var e;return T(["#",u,"{font:0/0 a}#",u,':after{content:"',c,'";visibility:hidden;font:3px/1 a}'].join(""),function(t){e=t.offsetHeight>=3}),e},y.video=function(){var e=t.createElement("video"),n=!1;try{if(n=!!e.canPlayType)n=new Boolean(n),n.ogg=e.canPlayType('video/ogg; codecs="theora"').replace(/^no$/,""),n.h264=e.canPlayType('video/mp4; codecs="avc1.42E01E"').replace(/^no$/,""),n.webm=e.canPlayType('video/webm; codecs="vp8, vorbis"').replace(/^no$/,"")}catch(r){}return n},y.audio=function(){var e=t.createElement("audio"),n=!1;try{if(n=!!e.canPlayType)n=new Boolean(n),n.ogg=e.canPlayType('audio/ogg; codecs="vorbis"').replace(/^no$/,""),n.mp3=e.canPlayType("audio/mpeg;").replace(/^no$/,""),n.wav=e.canPlayType('audio/wav; codecs="1"').replace(/^no$/,""),n.m4a=(e.canPlayType("audio/x-m4a;")||e.canPlayType("audio/aac;")).replace(/^no$/,"")}catch(r){}return n},y.localstorage=function(){try{return localStorage.setItem(u,u),localStorage.removeItem(u),!0}catch(e){return!1}},y.sessionstorage=function(){try{return sessionStorage.setItem(u,u),sessionStorage.removeItem(u),!0}catch(e){return!1}},y.webworkers=function(){return!!e.Worker},y.applicationcache=function(){return!!e.applicationCache},y.svg=function(){return!!t.createElementNS&&!!t.createElementNS(g.svg,"svg").createSVGRect},y.inlinesvg=function(){var e=t.createElement("div");return e.innerHTML="<svg/>",(e.firstChild&&e.firstChild.namespaceURI)==g.svg},y.smil=function(){return!!t.createElementNS&&/SVGAnimate/.test(h.call(t.createElementNS(g.svg,"animate")))},y.svgclippaths=function(){return!!t.createElementNS&&/SVGClipPath/.test(h.call(t.createElementNS(g.svg,"clipPath")))};for(var j in y)L(y,j)&&(x=j.toLowerCase(),i[x]=y[j](),E.push((i[x]?"":"no-")+x));return i.input||B(),i.addTest=function(e,t){if(typeof e=="object")for(var r in e)L(e,r)&&i.addTest(r,e[r]);else{e=e.toLowerCase();if(i[e]!==n)return i;t=typeof t=="function"?t():t,typeof s!="undefined"&&s&&(o.className+=" "+(t?"":"no-")+e),i[e]=t}return i},A(""),a=l=null,function(e,t){function l(e,t){var n=e.createElement("p"),r=e.getElementsByTagName("head")[0]||e.documentElement;return n.innerHTML="x<style>"+t+"</style>",r.insertBefore(n.lastChild,r.firstChild)}function c(){var e=g.elements;return typeof e=="string"?e.split(" "):e}function h(e){var t=a[e[o]];return t||(t={},u++,e[o]=u,a[u]=t),t}function p(e,n,s){n||(n=t);if(f)return n.createElement(e);s||(s=h(n));var o;return s.cache[e]?o=s.cache[e].cloneNode():i.test(e)?o=(s.cache[e]=s.createElem(e)).cloneNode():o=s.createElem(e),o.canHaveChildren&&!r.test(e)?s.frag.appendChild(o):o}function d(e,n){e||(e=t);if(f)return e.createDocumentFragment();n=n||h(e);var r=n.frag.cloneNode(),i=0,s=c(),o=s.length;for(;i<o;i++)r.createElement(s[i]);return r}function v(e,t){t.cache||(t.cache={},t.createElem=e.createElement,t.createFrag=e.createDocumentFragment,t.frag=t.createFrag()),e.createElement=function(n){return g.shivMethods?p(n,e,t):t.createElem(n)},e.createDocumentFragment=Function("h,f","return function(){var n=f.cloneNode(),c=n.createElement;h.shivMethods&&("+c().join().replace(/\w+/g,function(e){return t.createElem(e),t.frag.createElement(e),'c("'+e+'")'})+");return n}")(g,t.frag)}function m(e){e||(e=t);var n=h(e);return g.shivCSS&&!s&&!n.hasCSS&&(n.hasCSS=!!l(e,"article,aside,figcaption,figure,footer,header,hgroup,nav,section{display:block}mark{background:#FF0;color:#000}")),f||v(e,n),e}var n=e.html5||{},r=/^<|^(?:button|map|select|textarea|object|iframe|option|optgroup)$/i,i=/^(?:a|b|code|div|fieldset|h1|h2|h3|h4|h5|h6|i|label|li|ol|p|q|span|strong|style|table|tbody|td|th|tr|ul)$/i,s,o="_html5shiv",u=0,a={},f;(function(){try{var e=t.createElement("a");e.innerHTML="<xyz></xyz>",s="hidden"in e,f=e.childNodes.length==1||function(){t.createElement("a");var e=t.createDocumentFragment();return typeof e.cloneNode=="undefined"||typeof e.createDocumentFragment=="undefined"||typeof e.createElement=="undefined"}()}catch(n){s=!0,f=!0}})();var g={elements:n.elements||"abbr article aside audio bdi canvas data datalist details figcaption figure footer header hgroup mark meter nav output progress section summary time video",shivCSS:n.shivCSS!==!1,supportsUnknownElements:f,shivMethods:n.shivMethods!==!1,type:"default",shivDocument:m,createElement:p,createDocumentFragment:d};e.html5=g,m(t)}(this,t),i._version=r,i._prefixes=p,i._domPrefixes=m,i._cssomPrefixes=v,i.mq=N,i.hasEvent=C,i.testProp=function(e){return D([e])},i.testAllProps=H,i.testStyles=T,i.prefixed=function(e,t,n){return t?H(e,t,n):H(e,"pfx")},o.className=o.className.replace(/(^|\s)no-js(\s|$)/,"$1$2")+(s?" js "+E.join(" "):""),i}(this,this.document),window.url=function(){function e(e){return!isNaN(parseFloat(e))&&isFinite(e)}return function(t,n){var r=n||window.location.toString();if(!t)return r;t=t.toString(),"//"===r.substring(0,2)?r="http:"+r:1===r.split("://").length&&(r="http://"+r),n=r.split("/");var i={auth:""},s=n[2].split("@");1===s.length?s=s[0].split(":"):(i.auth=s[0],s=s[1].split(":")),i.protocol=n[0],i.hostname=s[0],i.port=s[1]||"80",i.pathname="/"+n.slice(3,n.length).join("/").split("?")[0].split("#")[0];var o=i.pathname;1===o.split(".").length&&"/"!==o[o.length-1]&&(o+="/");var u=i.hostname,f=u.split("."),l=o.split("/");if("hostname"===t)return u;if("domain"===t)return f.slice(-2).join(".");if("sub"===t)return f.slice(0,f.length-2).join(".");if("port"===t)return i.port||"80";if("protocol"===t)return i.protocol.split(":")[0];if("auth"===t)return i.auth;if("user"===t)return i.auth.split(":")[0];if("pass"===t)return i.auth.split(":")[1]||"";if("path"===t)return o;if("."===t.charAt(0)){if(t=t.substring(1),e(t))return t=parseInt(t,10),f[0>t?f.length+t:t-1]||""}else{if(e(t))return t=parseInt(t,10),l[0>t?l.length+t:t]||"";if("file"===t)return l.slice(-1)[0];if("filename"===t)return l.slice(-1)[0].split(".")[0];if("fileext"===t)return l.slice(-1)[0].split(".")[1]||"";if("?"===t.charAt(0)||"#"===t.charAt(0)){var c=r,h=null;if("?"===t.charAt(0)?c=(c.split("?")[1]||"").split("#")[0]:"#"===t.charAt(0)&&(c=c.split("#")[1]||""),!t.charAt(1))return c;t=t.substring(1),c=c.split("&");for(var p=0,d=c.length;d>p;p++)if(h=c[p].split("="),h[0]===t)return h[1];return null}}return""}}(),jQuery&&jQuery.extend({url:function(e,t){return window.url(e,t)}}),function(){"use strict";var e="undefined",t="string",n=self.navigator,r=String,i=Object.prototype.hasOwnProperty,s={},o={},u=!1,a=!0,f=/^\s*application\/(?:vnd\.oftn\.|x-)?l10n\+json\s*(?:$|;)/i,l,c="locale",h="defaultLocale",p="toLocaleString",d="toLowerCase",v=Array.prototype.indexOf||function(e){var t=this.length,n=0;for(;n<t;n++)if(n in this&&this[n]===e)return n;return-1},m=function(e){var t=new l;return t.open("GET",e,u),t.send(null),t.status!==200?(setTimeout(function(){var t=new Error("Unable to load localization data: "+e);throw t.name="Localization Error",t},0),{}):JSON.parse(t.responseText)},g=r[p]=function(e){if(arguments.length>0&&typeof e!="number")if(typeof e===t)g(m(e));else if(e===u)o={};else{var n,a,f;for(n in e)if(i.call(e,n)){a=e[n],n=n[d]();if(!(n in o)||a===u)o[n]={};if(a===u)continue;if(typeof a===t){if(r[c][d]().indexOf(n)!==0){n in s||(s[n]=[]),s[n].push(a);continue}a=m(a)}for(f in a)i.call(a,f)&&(o[n][f]=a[f])}}return Function.prototype[p].apply(r,arguments)},y=function(e){var t=s[e],n=0,r=t.length,i;for(;n<r;n++)i={},i[e]=m(t[n]),g(i);delete s[e]},b,w=r.prototype[p]=function(){var e=b,t=r[e?h:c],n=t[d]().split("-"),i=n.length,f=this.valueOf(),l;b=u;do{l=n.slice(0,i).join("-"),l in s&&y(l);if(l in o&&f in o[l])return o[l][f]}while(i-->1);return!e&&r[h]?(b=a,w.call(f)):f};if(typeof XMLHttpRequest===e&&typeof ActiveXObject!==e){var E=ActiveXObject;l=function(){try{return new E("Msxml2.XMLHTTP.6.0")}catch(e){}try{return new E("Msxml2.XMLHTTP.3.0")}catch(t){}try{return new E("Msxml2.XMLHTTP")}catch(n){}throw new Error("XMLHttpRequest not supported by this browser.")}}else l=XMLHttpRequest;r[h]=r[h]||"",r[c]=n&&(n.language||n.userLanguage)||"";if(typeof document!==e){var S=document.getElementsByTagName("link"),x=S.length,T;while(x--){var N=S[x],C=(N.getAttribute("rel")||"")[d]().split(/\s+/);f.test(N.type)&&(v.call(C,"localizations")!==-1?g(N.getAttribute("href")):v.call(C,"localization")!==-1&&(T={},T[(N.getAttribute("hreflang")||"")[d]()]=N.getAttribute("href"),g(T)))}}}(),function(){var e=this,t=e._,n={},r=Array.prototype,i=Object.prototype,s=Function.prototype,o=r.push,u=r.slice,a=r.concat,f=i.toString,l=i.hasOwnProperty,c=r.forEach,h=r.map,p=r.reduce,d=r.reduceRight,v=r.filter,m=r.every,g=r.some,y=r.indexOf,b=r.lastIndexOf,w=Array.isArray,E=Object.keys,S=s.bind,x=function(e){return e instanceof x?e:this instanceof x?(this._wrapped=e,void 0):new x(e)};"undefined"!=typeof exports?("undefined"!=typeof module&&module.exports&&(exports=module.exports=x),exports._=x):e._=x,x.VERSION="1.4.3";var T=x.each=x.forEach=function(e,t,r){if(null!=e)if(c&&e.forEach===c)e.forEach(t,r);else if(e.length===+e.length){for(var i=0,s=e.length;s>i;i++)if(t.call(r,e[i],i,e)===n)return}else for(var o in e)if(x.has(e,o)&&t.call(r,e[o],o,e)===n)return};x.map=x.collect=function(e,t,n){var r=[];return null==e?r:h&&e.map===h?e.map(t,n):(T(e,function(e,i,s){r[r.length]=t.call(n,e,i,s)}),r)};var N="Reduce of empty array with no initial value";x.reduce=x.foldl=x.inject=function(e,t,n,r){var i=arguments.length>2;if(null==e&&(e=[]),p&&e.reduce===p)return r&&(t=x.bind(t,r)),i?e.reduce(t,n):e.reduce(t);if(T(e,function(e,s,o){i?n=t.call(r,n,e,s,o):(n=e,i=!0)}),!i)throw new TypeError(N);return n},x.reduceRight=x.foldr=function(e,t,n,r){var i=arguments.length>2;if(null==e&&(e=[]),d&&e.reduceRight===d)return r&&(t=x.bind(t,r)),i?e.reduceRight(t,n):e.reduceRight(t);var s=e.length;if(s!==+s){var o=x.keys(e);s=o.length}if(T(e,function(u,a,f){a=o?o[--s]:--s,i?n=t.call(r,n,e[a],a,f):(n=e[a],i=!0)}),!i)throw new TypeError(N);return n},x.find=x.detect=function(e,t,n){var r;return C(e,function(e,i,s){return t.call(n,e,i,s)?(r=e,!0):void 0}),r},x.filter=x.select=function(e,t,n){var r=[];return null==e?r:v&&e.filter===v?e.filter(t,n):(T(e,function(e,i,s){t.call(n,e,i,s)&&(r[r.length]=e)}),r)},x.reject=function(e,t,n){return x.filter(e,function(e,r,i){return!t.call(n,e,r,i)},n)},x.every=x.all=function(e,t,r){t||(t=x.identity);var i=!0;return null==e?i:m&&e.every===m?e.every(t,r):(T(e,function(e,s,o){return(i=i&&t.call(r,e,s,o))?void 0:n}),!!i)};var C=x.some=x.any=function(e,t,r){t||(t=x.identity);var i=!1;return null==e?i:g&&e.some===g?e.some(t,r):(T(e,function(e,s,o){return i||(i=t.call(r,e,s,o))?n:void 0}),!!i)};x.contains=x.include=function(e,t){return null==e?!1:y&&e.indexOf===y?-1!=e.indexOf(t):C(e,function(e){return e===t})},x.invoke=function(e,t){var n=u.call(arguments,2);return x.map(e,function(e){return(x.isFunction(t)?t:e[t]).apply(e,n)})},x.pluck=function(e,t){return x.map(e,function(e){return e[t]})},x.where=function(e,t){return x.isEmpty(t)?[]:x.filter(e,function(e){for(var n in t)if(t[n]!==e[n])return!1;return!0})},x.max=function(e,t,n){if(!t&&x.isArray(e)&&e[0]===+e[0]&&65535>e.length)return Math.max.apply(Math,e);if(!t&&x.isEmpty(e))return-1/0;var r={computed:-1/0,value:-1/0};return T(e,function(e,i,s){var o=t?t.call(n,e,i,s):e;o>=r.computed&&(r={value:e,computed:o})}),r.value},x.min=function(e,t,n){if(!t&&x.isArray(e)&&e[0]===+e[0]&&65535>e.length)return Math.min.apply(Math,e);if(!t&&x.isEmpty(e))return 1/0;var r={computed:1/0,value:1/0};return T(e,function(e,i,s){var o=t?t.call(n,e,i,s):e;r.computed>o&&(r={value:e,computed:o})}),r.value},x.shuffle=function(e){var t,n=0,r=[];return T(e,function(e){t=x.random(n++),r[n-1]=r[t],r[t]=e}),r};var k=function(e){return x.isFunction(e)?e:function(t){return t[e]}};x.sortBy=function(e,t,n){var r=k(t);return x.pluck(x.map(e,function(e,t,i){return{value:e,index:t,criteria:r.call(n,e,t,i)}}).sort(function(e,t){var n=e.criteria,r=t.criteria;if(n!==r){if(n>r||void 0===n)return 1;if(r>n||void 0===r)return-1}return e.index<t.index?-1:1}),"value")};var L=function(e,t,n,r){var i={},s=k(t||x.identity);return T(e,function(t,o){var u=s.call(n,t,o,e);r(i,u,t)}),i};x.groupBy=function(e,t,n){return L(e,t,n,function(e,t,n){(x.has(e,t)?e[t]:e[t]=[]).push(n)})},x.countBy=function(e,t,n){return L(e,t,n,function(e,t){x.has(e,t)||(e[t]=0),e[t]++})},x.sortedIndex=function(e,t,n,r){n=null==n?x.identity:k(n);for(var i=n.call(r,t),s=0,o=e.length;o>s;){var u=s+o>>>1;i>n.call(r,e[u])?s=u+1:o=u}return s},x.toArray=function(e){return e?x.isArray(e)?u.call(e):e.length===+e.length?x.map(e,x.identity):x.values(e):[]},x.size=function(e){return null==e?0:e.length===+e.length?e.length:x.keys(e).length},x.first=x.head=x.take=function(e,t,n){return null==e?void 0:null==t||n?e[0]:u.call(e,0,t)},x.initial=function(e,t,n){return u.call(e,0,e.length-(null==t||n?1:t))},x.last=function(e,t,n){return null==e?void 0:null==t||n?e[e.length-1]:u.call(e,Math.max(e.length-t,0))},x.rest=x.tail=x.drop=function(e,t,n){return u.call(e,null==t||n?1:t)},x.compact=function(e){return x.filter(e,x.identity)};var A=function(e,t,n){return T(e,function(e){x.isArray(e)?t?o.apply(n,e):A(e,t,n):n.push(e)}),n};x.flatten=function(e,t){return A(e,t,[])},x.without=function(e){return x.difference(e,u.call(arguments,1))},x.uniq=x.unique=function(e,t,n,r){x.isFunction(t)&&(r=n,n=t,t=!1);var i=n?x.map(e,n,r):e,s=[],o=[];return T(i,function(n,r){(t?r&&o[o.length-1]===n:x.contains(o,n))||(o.push(n),s.push(e[r]))}),s},x.union=function(){return x.uniq(a.apply(r,arguments))},x.intersection=function(e){var t=u.call(arguments,1);return x.filter(x.uniq(e),function(e){return x.every(t,function(t){return x.indexOf(t,e)>=0})})},x.difference=function(e){var t=a.apply(r,u.call(arguments,1));return x.filter(e,function(e){return!x.contains(t,e)})},x.zip=function(){for(var e=u.call(arguments),t=x.max(x.pluck(e,"length")),n=Array(t),r=0;t>r;r++)n[r]=x.pluck(e,""+r);return n},x.object=function(e,t){if(null==e)return{};for(var n={},r=0,i=e.length;i>r;r++)t?n[e[r]]=t[r]:n[e[r][0]]=e[r][1];return n},x.indexOf=function(e,t,n){if(null==e)return-1;var r=0,i=e.length;if(n){if("number"!=typeof n)return r=x.sortedIndex(e,t),e[r]===t?r:-1;r=0>n?Math.max(0,i+n):n}if(y&&e.indexOf===y)return e.indexOf(t,n);for(;i>r;r++)if(e[r]===t)return r;return-1},x.lastIndexOf=function(e,t,n){if(null==e)return-1;var r=null!=n;if(b&&e.lastIndexOf===b)return r?e.lastIndexOf(t,n):e.lastIndexOf(t);for(var i=r?n:e.length;i--;)if(e[i]===t)return i;return-1},x.range=function(e,t,n){1>=arguments.length&&(t=e||0,e=0),n=arguments[2]||1;for(var r=Math.max(Math.ceil((t-e)/n),0),i=0,s=Array(r);r>i;)s[i++]=e,e+=n;return s};var O=function(){};x.bind=function(e,t){var n,r;if(e.bind===S&&S)return S.apply(e,u.call(arguments,1));if(!x.isFunction(e))throw new TypeError;return n=u.call(arguments,2),r=function(){if(this instanceof r){O.prototype=e.prototype;var i=new O;O.prototype=null;var s=e.apply(i,n.concat(u.call(arguments)));return Object(s)===s?s:i}return e.apply(t,n.concat(u.call(arguments)))}},x.bindAll=function(e){var t=u.call(arguments,1);return 0==t.length&&(t=x.functions(e)),T(t,function(t){e[t]=x.bind(e[t],e)}),e},x.memoize=function(e,t){var n={};return t||(t=x.identity),function(){var r=t.apply(this,arguments);return x.has(n,r)?n[r]:n[r]=e.apply(this,arguments)}},x.delay=function(e,t){var n=u.call(arguments,2);return setTimeout(function(){return e.apply(null,n)},t)},x.defer=function(e){return x.delay.apply(x,[e,1].concat(u.call(arguments,1)))},x.throttle=function(e,t){var n,r,i,s,o=0,u=function(){o=new Date,i=null,s=e.apply(n,r)};return function(){var a=new Date,f=t-(a-o);return n=this,r=arguments,0>=f?(clearTimeout(i),i=null,o=a,s=e.apply(n,r)):i||(i=setTimeout(u,f)),s}},x.debounce=function(e,t,n){var r,i;return function(){var s=this,o=arguments,u=function(){r=null,n||(i=e.apply(s,o))},a=n&&!r;return clearTimeout(r),r=setTimeout(u,t),a&&(i=e.apply(s,o)),i}},x.once=function(e){var t,n=!1;return function(){return n?t:(n=!0,t=e.apply(this,arguments),e=null,t)}},x.wrap=function(e,t){return function(){var n=[e];return o.apply(n,arguments),t.apply(this,n)}},x.compose=function(){var e=arguments;return function(){for(var t=arguments,n=e.length-1;n>=0;n--)t=[e[n].apply(this,t)];return t[0]}},x.after=function(e,t){return 0>=e?t():function(){return 1>--e?t.apply(this,arguments):void 0}},x.keys=E||function(e){if(e!==Object(e))throw new TypeError("Invalid object");var t=[];for(var n in e)x.has(e,n)&&(t[t.length]=n);return t},x.values=function(e){var t=[];for(var n in e)x.has(e,n)&&t.push(e[n]);return t},x.pairs=function(e){var t=[];for(var n in e)x.has(e,n)&&t.push([n,e[n]]);return t},x.invert=function(e){var t={};for(var n in e)x.has(e,n)&&(t[e[n]]=n);return t},x.functions=x.methods=function(e){var t=[];for(var n in e)x.isFunction(e[n])&&t.push(n);return t.sort()},x.extend=function(e){return T(u.call(arguments,1),function(t){if(t)for(var n in t)e[n]=t[n]}),e},x.pick=function(e){var t={},n=a.apply(r,u.call(arguments,1));return T(n,function(n){n in e&&(t[n]=e[n])}),t},x.omit=function(e){var t={},n=a.apply(r,u.call(arguments,1));for(var i in e)x.contains(n,i)||(t[i]=e[i]);return t},x.defaults=function(e){return T(u.call(arguments,1),function(t){if(t)for(var n in t)null==e[n]&&(e[n]=t[n])}),e},x.clone=function(e){return x.isObject(e)?x.isArray(e)?e.slice():x.extend({},e):e},x.tap=function(e,t){return t(e),e};var M=function(e,t,n,r){if(e===t)return 0!==e||1/e==1/t;if(null==e||null==t)return e===t;e instanceof x&&(e=e._wrapped),t instanceof x&&(t=t._wrapped);var i=f.call(e);if(i!=f.call(t))return!1;switch(i){case"[object String]":return e==t+"";case"[object Number]":return e!=+e?t!=+t:0==e?1/e==1/t:e==+t;case"[object Date]":case"[object Boolean]":return+e==+t;case"[object RegExp]":return e.source==t.source&&e.global==t.global&&e.multiline==t.multiline&&e.ignoreCase==t.ignoreCase}if("object"!=typeof e||"object"!=typeof t)return!1;for(var s=n.length;s--;)if(n[s]==e)return r[s]==t;n.push(e),r.push(t);var o=0,u=!0;if("[object Array]"==i){if(o=e.length,u=o==t.length)for(;o--&&(u=M(e[o],t[o],n,r)););}else{var a=e.constructor,l=t.constructor;if(a!==l&&!(x.isFunction(a)&&a instanceof a&&x.isFunction(l)&&l instanceof l))return!1;for(var c in e)if(x.has(e,c)&&(o++,!(u=x.has(t,c)&&M(e[c],t[c],n,r))))break;if(u){for(c in t)if(x.has(t,c)&&!(o--))break;u=!o}}return n.pop(),r.pop(),u};x.isEqual=function(e,t){return M(e,t,[],[])},x.isEmpty=function(e){if(null==e)return!0;if(x.isArray(e)||x.isString(e))return 0===e.length;for(var t in e)if(x.has(e,t))return!1;return!0},x.isElement=function(e){return!!e&&1===e.nodeType},x.isArray=w||function(e){return"[object Array]"==f.call(e)},x.isObject=function(e){return e===Object(e)},T(["Arguments","Function","String","Number","Date","RegExp"],function(e){x["is"+e]=function(t){return f.call(t)=="[object "+e+"]"}}),x.isArguments(arguments)||(x.isArguments=function(e){return!!e&&!!x.has(e,"callee")}),x.isFunction=function(e){return"function"==typeof e},x.isFinite=function(e){return isFinite(e)&&!isNaN(parseFloat(e))},x.isNaN=function(e){return x.isNumber(e)&&e!=+e},x.isBoolean=function(e){return e===!0||e===!1||"[object Boolean]"==f.call(e)},x.isNull=function(e){return null===e},x.isUndefined=function(e){return void 0===e},x.has=function(e,t){return l.call(e,t)},x.noConflict=function(){return e._=t,this},x.identity=function(e){return e},x.times=function(e,t,n){for(var r=Array(e),i=0;e>i;i++)r[i]=t.call(n,i);return r},x.random=function(e,t){return null==t&&(t=e,e=0),e+(0|Math.random()*(t-e+1))};var _={escape:{"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","/":"&#x2F;"}};_.unescape=x.invert(_.escape);var D={escape:RegExp("["+x.keys(_.escape).join("")+"]","g"),unescape:RegExp("("+x.keys(_.unescape).join("|")+")","g")};x.each(["escape","unescape"],function(e){x[e]=function(t){return null==t?"":(""+t).replace(D[e],function(t){return _[e][t]})}}),x.result=function(e,t){if(null==e)return null;var n=e[t];return x.isFunction(n)?n.call(e):n},x.mixin=function(e){T(x.functions(e),function(t){var n=x[t]=e[t];x.prototype[t]=function(){var e=[this._wrapped];return o.apply(e,arguments),F.call(this,n.apply(x,e))}})};var P=0;x.uniqueId=function(e){var t=""+ ++P;return e?e+t:t},x.templateSettings={evaluate:/<%([\s\S]+?)%>/g,interpolate:/<%=([\s\S]+?)%>/g,escape:/<%-([\s\S]+?)%>/g};var H=/(.)^/,B={"'":"'","\\":"\\","\r":"r","\n":"n","	":"t","\u2028":"u2028","\u2029":"u2029"},j=/\\|'|\r|\n|\t|\u2028|\u2029/g;x.template=function(e,t,n){n=x.defaults({},n,x.templateSettings);var r=RegExp([(n.escape||H).source,(n.interpolate||H).source,(n.evaluate||H).source].join("|")+"|$","g"),i=0,s="__p+='";e.replace(r,function(t,n,r,o,u){return s+=e.slice(i,u).replace(j,function(e){return"\\"+B[e]}),n&&(s+="'+\n((__t=("+n+"))==null?'':_.escape(__t))+\n'"),r&&(s+="'+\n((__t=("+r+"))==null?'':__t)+\n'"),o&&(s+="';\n"+o+"\n__p+='"),i=u+t.length,t}),s+="';\n",n.variable||(s="with(obj||{}){\n"+s+"}\n"),s="var __t,__p='',__j=Array.prototype.join,print=function(){__p+=__j.call(arguments,'');};\n"+s+"return __p;\n";try{var o=Function(n.variable||"obj","_",s)}catch(u){throw u.source=s,u}if(t)return o(t,x);var a=function(e){return o.call(this,e,x)};return a.source="function("+(n.variable||"obj")+"){\n"+s+"}",a},x.chain=function(e){return x(e).chain()};var F=function(e){return this._chain?x(e).chain():e};x.mixin(x),T(["pop","push","reverse","shift","sort","splice","unshift"],function(e){var t=r[e];x.prototype[e]=function(){var n=this._wrapped;return t.apply(n,arguments),"shift"!=e&&"splice"!=e||0!==n.length||delete n[0],F.call(this,n)}}),T(["concat","join","slice"],function(e){var t=r[e];x.prototype[e]=function(){return F.call(this,t.apply(this._wrapped,arguments))}}),x.extend(x.prototype,{chain:function(){return this._chain=!0,this},value:function(){return this._wrapped}})}.call(this),function(e,t,n){function f(e){var t={},r=/^jQuery\d+$/;return n.each(e.attributes,function(e,n){n.specified&&!r.test(n.name)&&(t[n.name]=n.value)}),t}function l(e,r){var i=this,s=n(i);if(i.value==s.attr("placeholder")&&s.hasClass("placeholder"))if(s.data("placeholder-password")){s=s.hide().next().show().attr("id",s.removeAttr("id").data("placeholder-id"));if(e===!0)return s[0].value=r;s.focus()}else i.value="",s.removeClass("placeholder"),i==t.activeElement&&i.select()}function c(){var e,t=this,r=n(t),i=r,s=this.id;if(t.value==""){if(t.type=="password"){if(!r.data("placeholder-textinput")){try{e=r.clone().attr({type:"text"})}catch(o){e=n("<input>").attr(n.extend(f(this),{type:"text"}))}e.removeAttr("name").data({"placeholder-password":!0,"placeholder-id":s}).bind("focus.placeholder",l),r.data({"placeholder-textinput":e,"placeholder-id":s}).before(e)}r=r.removeAttr("id").hide().prev().attr("id",s).show()}r.addClass("placeholder"),r[0].value=r.attr("placeholder")}else r.removeClass("placeholder")}var r="placeholder"in t.createElement("input"),i="placeholder"in t.createElement("textarea"),s=n.fn,o=n.valHooks,u,a;r&&i?(a=s.placeholder=function(){return this},a.input=a.textarea=!0):(a=s.placeholder=function(){var e=this;return e.filter((r?"textarea":":input")+"[placeholder]").not(".placeholder").bind({"focus.placeholder":l,"blur.placeholder":c}).data("placeholder-enabled",!0).trigger("blur.placeholder"),e},a.input=r,a.textarea=i,u={get:function(e){var t=n(e);return t.data("placeholder-enabled")&&t.hasClass("placeholder")?"":e.value},set:function(e,r){var i=n(e);return i.data("placeholder-enabled")?(r==""?(e.value=r,e!=t.activeElement&&c.call(e)):i.hasClass("placeholder")?l.call(e,!0,r)||(e.value=r):e.value=r,i):e.value=r}},r||(o.input=u),i||(o.textarea=u),n(function(){n(t).delegate("form","submit.placeholder",function(){var e=n(".placeholder",this).each(l);setTimeout(function(){e.each(c)},10)})}),n(e).bind("beforeunload.placeholder",function(){n(".placeholder").each(function(){this.value=""})}))}(this,document,jQuery),function(e,t){function n(t,n){var i,s,o,u=t.nodeName.toLowerCase();return"area"===u?(i=t.parentNode,s=i.name,!t.href||!s||i.nodeName.toLowerCase()!=="map"?!1:(o=e("img[usemap=#"+s+"]")[0],!!o&&r(o))):(/input|select|textarea|button|object/.test(u)?!t.disabled:"a"===u?t.href||n:n)&&r(t)}function r(t){return e.expr.filters.visible(t)&&!e(t).parents().andSelf().filter(function(){return e.css(this,"visibility")==="hidden"}).length}var i=0,s=/^ui-id-\d+$/;e.ui=e.ui||{};if(e.ui.version)return;e.extend(e.ui,{version:"1.9.2",keyCode:{BACKSPACE:8,COMMA:188,DELETE:46,DOWN:40,END:35,ENTER:13,ESCAPE:27,HOME:36,LEFT:37,NUMPAD_ADD:107,NUMPAD_DECIMAL:110,NUMPAD_DIVIDE:111,NUMPAD_ENTER:108,NUMPAD_MULTIPLY:106,NUMPAD_SUBTRACT:109,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,RIGHT:39,SPACE:32,TAB:9,UP:38}}),e.fn.extend({_focus:e.fn.focus,focus:function(t,n){return typeof t=="number"?this.each(function(){var r=this;setTimeout(function(){e(r).focus(),n&&n.call(r)},t)}):this._focus.apply(this,arguments)},scrollParent:function(){var t;return e.ui.ie&&/(static|relative)/.test(this.css("position"))||/absolute/.test(this.css("position"))?t=this.parents().filter(function(){return/(relative|absolute|fixed)/.test(e.css(this,"position"))&&/(auto|scroll)/.test(e.css(this,"overflow")+e.css(this,"overflow-y")+e.css(this,"overflow-x"))}).eq(0):t=this.parents().filter(function(){return/(auto|scroll)/.test(e.css(this,"overflow")+e.css(this,"overflow-y")+e.css(this,"overflow-x"))}).eq(0),/fixed/.test(this.css("position"))||!t.length?e(document):t},zIndex:function(n){if(n!==t)return this.css("zIndex",n);if(this.length){var r=e(this[0]),i,s;while(r.length&&r[0]!==document){i=r.css("position");if(i==="absolute"||i==="relative"||i==="fixed"){s=parseInt(r.css("zIndex"),10);if(!isNaN(s)&&s!==0)return s}r=r.parent()}}return 0},uniqueId:function(){return this.each(function(){this.id||(this.id="ui-id-"+ ++i)})},removeUniqueId:function(){return this.each(function(){s.test(this.id)&&e(this).removeAttr("id")})}}),e.extend(e.expr[":"],{data:e.expr.createPseudo?e.expr.createPseudo(function(t){return function(n){return!!e.data(n,t)}}):function(t,n,r){return!!e.data(t,r[3])},focusable:function(t){return n(t,!isNaN(e.attr(t,"tabindex")))},tabbable:function(t){var r=e.attr(t,"tabindex"),i=isNaN(r);return(i||r>=0)&&n(t,!i)}}),e(function(){var t=document.body,n=t.appendChild(n=document.createElement("div"));n.offsetHeight,e.extend(n.style,{minHeight:"100px",height:"auto",padding:0,borderWidth:0}),e.support.minHeight=n.offsetHeight===100,e.support.selectstart="onselectstart"in n,t.removeChild(n).style.display="none"}),e("<a>").outerWidth(1).jquery||e.each(["Width","Height"],function(n,r){function i(t,n,r,i){return e.each(s,function(){n-=parseFloat(e.css(t,"padding"+this))||0,r&&(n-=parseFloat(e.css(t,"border"+this+"Width"))||0),i&&(n-=parseFloat(e.css(t,"margin"+this))||0)}),n}var s=r==="Width"?["Left","Right"]:["Top","Bottom"],o=r.toLowerCase(),u={innerWidth:e.fn.innerWidth,innerHeight:e.fn.innerHeight,outerWidth:e.fn.outerWidth,outerHeight:e.fn.outerHeight};e.fn["inner"+r]=function(n){return n===t?u["inner"+r].call(this):this.each(function(){e(this).css(o,i(this,n)+"px")})},e.fn["outer"+r]=function(t,n){return typeof t!="number"?u["outer"+r].call(this,t):this.each(function(){e(this).css(o,i(this,t,!0,n)+"px")})}}),e("<a>").data("a-b","a").removeData("a-b").data("a-b")&&(e.fn.removeData=function(t){return function(n){return arguments.length?t.call(this,e.camelCase(n)):t.call(this)}}(e.fn.removeData)),function(){var t=/msie ([\w.]+)/.exec(navigator.userAgent.toLowerCase())||[];e.ui.ie=t.length?!0:!1,e.ui.ie6=parseFloat(t[1],10)===6}(),e.fn.extend({disableSelection:function(){return this.bind((e.support.selectstart?"selectstart":"mousedown")+".ui-disableSelection",function(e){e.preventDefault()})},enableSelection:function(){return this.unbind(".ui-disableSelection")}}),e.extend(e.ui,{plugin:{add:function(t,n,r){var i,s=e.ui[t].prototype;for(i in r)s.plugins[i]=s.plugins[i]||[],s.plugins[i].push([n,r[i]])},call:function(e,t,n){var r,i=e.plugins[t];if(!i||!e.element[0].parentNode||e.element[0].parentNode.nodeType===11)return;for(r=0;r<i.length;r++)e.options[i[r][0]]&&i[r][1].apply(e.element,n)}},contains:e.contains,hasScroll:function(t,n){if(e(t).css("overflow")==="hidden")return!1;var r=n&&n==="left"?"scrollLeft":"scrollTop",i=!1;return t[r]>0?!0:(t[r]=1,i=t[r]>0,t[r]=0,i)},isOverAxis:function(e,t,n){return e>t&&e<t+n},isOver:function(t,n,r,i,s,o){return e.ui.isOverAxis(t,r,s)&&e.ui.isOverAxis(n,i,o)}})}(jQuery),function(e,t){var n=0,r=Array.prototype.slice,i=e.cleanData;e.cleanData=function(t){for(var n=0,r;(r=t[n])!=null;n++)try{e(r).triggerHandler("remove")}catch(s){}i(t)},e.widget=function(t,n,r){var i,s,o,u,a=t.split(".")[0];t=t.split(".")[1],i=a+"-"+t,r||(r=n,n=e.Widget),e.expr[":"][i.toLowerCase()]=function(t){return!!e.data(t,i)},e[a]=e[a]||{},s=e[a][t],o=e[a][t]=function(e,t){if(!this._createWidget)return new o(e,t);arguments.length&&this._createWidget(e,t)},e.extend(o,s,{version:r.version,_proto:e.extend({},r),_childConstructors:[]}),u=new n,u.options=e.widget.extend({},u.options),e.each(r,function(t,i){e.isFunction(i)&&(r[t]=function(){var e=function(){return n.prototype[t].apply(this,arguments)},r=function(e){return n.prototype[t].apply(this,e)};return function(){var t=this._super,n=this._superApply,s;return this._super=e,this._superApply=r,s=i.apply(this,arguments),this._super=t,this._superApply=n,s}}())}),o.prototype=e.widget.extend(u,{widgetEventPrefix:s?u.widgetEventPrefix:t},r,{constructor:o,namespace:a,widgetName:t,widgetBaseClass:i,widgetFullName:i}),s?(e.each(s._childConstructors,function(t,n){var r=n.prototype;e.widget(r.namespace+"."+r.widgetName,o,n._proto)}),delete s._childConstructors):n._childConstructors.push(o),e.widget.bridge(t,o)},e.widget.extend=function(n){var i=r.call(arguments,1),s=0,o=i.length,u,a;for(;s<o;s++)for(u in i[s])a=i[s][u],i[s].hasOwnProperty(u)&&a!==t&&(e.isPlainObject(a)?n[u]=e.isPlainObject(n[u])?e.widget.extend({},n[u],a):e.widget.extend({},a):n[u]=a);return n},e.widget.bridge=function(n,i){var s=i.prototype.widgetFullName||n;e.fn[n]=function(o){var u=typeof o=="string",a=r.call(arguments,1),f=this;return o=!u&&a.length?e.widget.extend.apply(null,[o].concat(a)):o,u?this.each(function(){var r,i=e.data(this,s);if(!i)return e.error("cannot call methods on "+n+" prior to initialization; "+"attempted to call method '"+o+"'");if(!e.isFunction(i[o])||o.charAt(0)==="_")return e.error("no such method '"+o+"' for "+
n+" widget instance");r=i[o].apply(i,a);if(r!==i&&r!==t)return f=r&&r.jquery?f.pushStack(r.get()):r,!1}):this.each(function(){var t=e.data(this,s);t?t.option(o||{})._init():e.data(this,s,new i(o,this))}),f}},e.Widget=function(){},e.Widget._childConstructors=[],e.Widget.prototype={widgetName:"widget",widgetEventPrefix:"",defaultElement:"<div>",options:{disabled:!1,create:null},_createWidget:function(t,r){r=e(r||this.defaultElement||this)[0],this.element=e(r),this.uuid=n++,this.eventNamespace="."+this.widgetName+this.uuid,this.options=e.widget.extend({},this.options,this._getCreateOptions(),t),this.bindings=e(),this.hoverable=e(),this.focusable=e(),r!==this&&(e.data(r,this.widgetName,this),e.data(r,this.widgetFullName,this),this._on(!0,this.element,{remove:function(e){e.target===r&&this.destroy()}}),this.document=e(r.style?r.ownerDocument:r.document||r),this.window=e(this.document[0].defaultView||this.document[0].parentWindow)),this._create(),this._trigger("create",null,this._getCreateEventData()),this._init()},_getCreateOptions:e.noop,_getCreateEventData:e.noop,_create:e.noop,_init:e.noop,destroy:function(){this._destroy(),this.element.unbind(this.eventNamespace).removeData(this.widgetName).removeData(this.widgetFullName).removeData(e.camelCase(this.widgetFullName)),this.widget().unbind(this.eventNamespace).removeAttr("aria-disabled").removeClass(this.widgetFullName+"-disabled "+"ui-state-disabled"),this.bindings.unbind(this.eventNamespace),this.hoverable.removeClass("ui-state-hover"),this.focusable.removeClass("ui-state-focus")},_destroy:e.noop,widget:function(){return this.element},option:function(n,r){var i=n,s,o,u;if(arguments.length===0)return e.widget.extend({},this.options);if(typeof n=="string"){i={},s=n.split("."),n=s.shift();if(s.length){o=i[n]=e.widget.extend({},this.options[n]);for(u=0;u<s.length-1;u++)o[s[u]]=o[s[u]]||{},o=o[s[u]];n=s.pop();if(r===t)return o[n]===t?null:o[n];o[n]=r}else{if(r===t)return this.options[n]===t?null:this.options[n];i[n]=r}}return this._setOptions(i),this},_setOptions:function(e){var t;for(t in e)this._setOption(t,e[t]);return this},_setOption:function(e,t){return this.options[e]=t,e==="disabled"&&(this.widget().toggleClass(this.widgetFullName+"-disabled ui-state-disabled",!!t).attr("aria-disabled",t),this.hoverable.removeClass("ui-state-hover"),this.focusable.removeClass("ui-state-focus")),this},enable:function(){return this._setOption("disabled",!1)},disable:function(){return this._setOption("disabled",!0)},_on:function(t,n,r){var i,s=this;typeof t!="boolean"&&(r=n,n=t,t=!1),r?(n=i=e(n),this.bindings=this.bindings.add(n)):(r=n,n=this.element,i=this.widget()),e.each(r,function(r,o){function u(){if(!t&&(s.options.disabled===!0||e(this).hasClass("ui-state-disabled")))return;return(typeof o=="string"?s[o]:o).apply(s,arguments)}typeof o!="string"&&(u.guid=o.guid=o.guid||u.guid||e.guid++);var a=r.match(/^(\w+)\s*(.*)$/),f=a[1]+s.eventNamespace,l=a[2];l?i.delegate(l,f,u):n.bind(f,u)})},_off:function(e,t){t=(t||"").split(" ").join(this.eventNamespace+" ")+this.eventNamespace,e.unbind(t).undelegate(t)},_delay:function(e,t){function n(){return(typeof e=="string"?r[e]:e).apply(r,arguments)}var r=this;return setTimeout(n,t||0)},_hoverable:function(t){this.hoverable=this.hoverable.add(t),this._on(t,{mouseenter:function(t){e(t.currentTarget).addClass("ui-state-hover")},mouseleave:function(t){e(t.currentTarget).removeClass("ui-state-hover")}})},_focusable:function(t){this.focusable=this.focusable.add(t),this._on(t,{focusin:function(t){e(t.currentTarget).addClass("ui-state-focus")},focusout:function(t){e(t.currentTarget).removeClass("ui-state-focus")}})},_trigger:function(t,n,r){var i,s,o=this.options[t];r=r||{},n=e.Event(n),n.type=(t===this.widgetEventPrefix?t:this.widgetEventPrefix+t).toLowerCase(),n.target=this.element[0],s=n.originalEvent;if(s)for(i in s)i in n||(n[i]=s[i]);return this.element.trigger(n,r),!(e.isFunction(o)&&o.apply(this.element[0],[n].concat(r))===!1||n.isDefaultPrevented())}},e.each({show:"fadeIn",hide:"fadeOut"},function(t,n){e.Widget.prototype["_"+t]=function(r,i,s){typeof i=="string"&&(i={effect:i});var o,u=i?i===!0||typeof i=="number"?n:i.effect||n:t;i=i||{},typeof i=="number"&&(i={duration:i}),o=!e.isEmptyObject(i),i.complete=s,i.delay&&r.delay(i.delay),o&&e.effects&&(e.effects.effect[u]||e.uiBackCompat!==!1&&e.effects[u])?r[t](i):u!==t&&r[u]?r[u](i.duration,i.easing,s):r.queue(function(n){e(this)[t](),s&&s.call(r[0]),n()})}}),e.uiBackCompat!==!1&&(e.Widget.prototype._getCreateOptions=function(){return e.metadata&&e.metadata.get(this.element[0])[this.widgetName]})}(jQuery),function(e,t){var n=!1;e(document).mouseup(function(e){n=!1}),e.widget("ui.mouse",{version:"1.9.2",options:{cancel:"input,textarea,button,select,option",distance:1,delay:0},_mouseInit:function(){var t=this;this.element.bind("mousedown."+this.widgetName,function(e){return t._mouseDown(e)}).bind("click."+this.widgetName,function(n){if(!0===e.data(n.target,t.widgetName+".preventClickEvent"))return e.removeData(n.target,t.widgetName+".preventClickEvent"),n.stopImmediatePropagation(),!1}),this.started=!1},_mouseDestroy:function(){this.element.unbind("."+this.widgetName),this._mouseMoveDelegate&&e(document).unbind("mousemove."+this.widgetName,this._mouseMoveDelegate).unbind("mouseup."+this.widgetName,this._mouseUpDelegate)},_mouseDown:function(t){if(n)return;this._mouseStarted&&this._mouseUp(t),this._mouseDownEvent=t;var r=this,i=t.which===1,s=typeof this.options.cancel=="string"&&t.target.nodeName?e(t.target).closest(this.options.cancel).length:!1;if(!i||s||!this._mouseCapture(t))return!0;this.mouseDelayMet=!this.options.delay,this.mouseDelayMet||(this._mouseDelayTimer=setTimeout(function(){r.mouseDelayMet=!0},this.options.delay));if(this._mouseDistanceMet(t)&&this._mouseDelayMet(t)){this._mouseStarted=this._mouseStart(t)!==!1;if(!this._mouseStarted)return t.preventDefault(),!0}return!0===e.data(t.target,this.widgetName+".preventClickEvent")&&e.removeData(t.target,this.widgetName+".preventClickEvent"),this._mouseMoveDelegate=function(e){return r._mouseMove(e)},this._mouseUpDelegate=function(e){return r._mouseUp(e)},e(document).bind("mousemove."+this.widgetName,this._mouseMoveDelegate).bind("mouseup."+this.widgetName,this._mouseUpDelegate),t.preventDefault(),n=!0,!0},_mouseMove:function(t){return!e.ui.ie||document.documentMode>=9||!!t.button?this._mouseStarted?(this._mouseDrag(t),t.preventDefault()):(this._mouseDistanceMet(t)&&this._mouseDelayMet(t)&&(this._mouseStarted=this._mouseStart(this._mouseDownEvent,t)!==!1,this._mouseStarted?this._mouseDrag(t):this._mouseUp(t)),!this._mouseStarted):this._mouseUp(t)},_mouseUp:function(t){return e(document).unbind("mousemove."+this.widgetName,this._mouseMoveDelegate).unbind("mouseup."+this.widgetName,this._mouseUpDelegate),this._mouseStarted&&(this._mouseStarted=!1,t.target===this._mouseDownEvent.target&&e.data(t.target,this.widgetName+".preventClickEvent",!0),this._mouseStop(t)),!1},_mouseDistanceMet:function(e){return Math.max(Math.abs(this._mouseDownEvent.pageX-e.pageX),Math.abs(this._mouseDownEvent.pageY-e.pageY))>=this.options.distance},_mouseDelayMet:function(e){return this.mouseDelayMet},_mouseStart:function(e){},_mouseDrag:function(e){},_mouseStop:function(e){},_mouseCapture:function(e){return!0}})}(jQuery),function(e,t){e.widget("ui.sortable",e.ui.mouse,{version:"1.9.2",widgetEventPrefix:"sort",ready:!1,options:{appendTo:"parent",axis:!1,connectWith:!1,containment:!1,cursor:"auto",cursorAt:!1,dropOnEmpty:!0,forcePlaceholderSize:!1,forceHelperSize:!1,grid:!1,handle:!1,helper:"original",items:"> *",opacity:!1,placeholder:!1,revert:!1,scroll:!0,scrollSensitivity:20,scrollSpeed:20,scope:"default",tolerance:"intersect",zIndex:1e3},_create:function(){var e=this.options;this.containerCache={},this.element.addClass("ui-sortable"),this.refresh(),this.floating=this.items.length?e.axis==="x"||/left|right/.test(this.items[0].item.css("float"))||/inline|table-cell/.test(this.items[0].item.css("display")):!1,this.offset=this.element.offset(),this._mouseInit(),this.ready=!0},_destroy:function(){this.element.removeClass("ui-sortable ui-sortable-disabled"),this._mouseDestroy();for(var e=this.items.length-1;e>=0;e--)this.items[e].item.removeData(this.widgetName+"-item");return this},_setOption:function(t,n){t==="disabled"?(this.options[t]=n,this.widget().toggleClass("ui-sortable-disabled",!!n)):e.Widget.prototype._setOption.apply(this,arguments)},_mouseCapture:function(t,n){var r=this;if(this.reverting)return!1;if(this.options.disabled||this.options.type=="static")return!1;this._refreshItems(t);var i=null,s=e(t.target).parents().each(function(){if(e.data(this,r.widgetName+"-item")==r)return i=e(this),!1});e.data(t.target,r.widgetName+"-item")==r&&(i=e(t.target));if(!i)return!1;if(this.options.handle&&!n){var o=!1;e(this.options.handle,i).find("*").andSelf().each(function(){this==t.target&&(o=!0)});if(!o)return!1}return this.currentItem=i,this._removeCurrentsFromItems(),!0},_mouseStart:function(t,n,r){var i=this.options;this.currentContainer=this,this.refreshPositions(),this.helper=this._createHelper(t),this._cacheHelperProportions(),this._cacheMargins(),this.scrollParent=this.helper.scrollParent(),this.offset=this.currentItem.offset(),this.offset={top:this.offset.top-this.margins.top,left:this.offset.left-this.margins.left},e.extend(this.offset,{click:{left:t.pageX-this.offset.left,top:t.pageY-this.offset.top},parent:this._getParentOffset(),relative:this._getRelativeOffset()}),this.helper.css("position","absolute"),this.cssPosition=this.helper.css("position"),this.originalPosition=this._generatePosition(t),this.originalPageX=t.pageX,this.originalPageY=t.pageY,i.cursorAt&&this._adjustOffsetFromHelper(i.cursorAt),this.domPosition={prev:this.currentItem.prev()[0],parent:this.currentItem.parent()[0]},this.helper[0]!=this.currentItem[0]&&this.currentItem.hide(),this._createPlaceholder(),i.containment&&this._setContainment(),i.cursor&&(e("body").css("cursor")&&(this._storedCursor=e("body").css("cursor")),e("body").css("cursor",i.cursor)),i.opacity&&(this.helper.css("opacity")&&(this._storedOpacity=this.helper.css("opacity")),this.helper.css("opacity",i.opacity)),i.zIndex&&(this.helper.css("zIndex")&&(this._storedZIndex=this.helper.css("zIndex")),this.helper.css("zIndex",i.zIndex)),this.scrollParent[0]!=document&&this.scrollParent[0].tagName!="HTML"&&(this.overflowOffset=this.scrollParent.offset()),this._trigger("start",t,this._uiHash()),this._preserveHelperProportions||this._cacheHelperProportions();if(!r)for(var s=this.containers.length-1;s>=0;s--)this.containers[s]._trigger("activate",t,this._uiHash(this));return e.ui.ddmanager&&(e.ui.ddmanager.current=this),e.ui.ddmanager&&!i.dropBehaviour&&e.ui.ddmanager.prepareOffsets(this,t),this.dragging=!0,this.helper.addClass("ui-sortable-helper"),this._mouseDrag(t),!0},_mouseDrag:function(t){this.position=this._generatePosition(t),this.positionAbs=this._convertPositionTo("absolute"),this.lastPositionAbs||(this.lastPositionAbs=this.positionAbs);if(this.options.scroll){var n=this.options,r=!1;this.scrollParent[0]!=document&&this.scrollParent[0].tagName!="HTML"?(this.overflowOffset.top+this.scrollParent[0].offsetHeight-t.pageY<n.scrollSensitivity?this.scrollParent[0].scrollTop=r=this.scrollParent[0].scrollTop+n.scrollSpeed:t.pageY-this.overflowOffset.top<n.scrollSensitivity&&(this.scrollParent[0].scrollTop=r=this.scrollParent[0].scrollTop-n.scrollSpeed),this.overflowOffset.left+this.scrollParent[0].offsetWidth-t.pageX<n.scrollSensitivity?this.scrollParent[0].scrollLeft=r=this.scrollParent[0].scrollLeft+n.scrollSpeed:t.pageX-this.overflowOffset.left<n.scrollSensitivity&&(this.scrollParent[0].scrollLeft=r=this.scrollParent[0].scrollLeft-n.scrollSpeed)):(t.pageY-e(document).scrollTop()<n.scrollSensitivity?r=e(document).scrollTop(e(document).scrollTop()-n.scrollSpeed):e(window).height()-(t.pageY-e(document).scrollTop())<n.scrollSensitivity&&(r=e(document).scrollTop(e(document).scrollTop()+n.scrollSpeed)),t.pageX-e(document).scrollLeft()<n.scrollSensitivity?r=e(document).scrollLeft(e(document).scrollLeft()-n.scrollSpeed):e(window).width()-(t.pageX-e(document).scrollLeft())<n.scrollSensitivity&&(r=e(document).scrollLeft(e(document).scrollLeft()+n.scrollSpeed))),r!==!1&&e.ui.ddmanager&&!n.dropBehaviour&&e.ui.ddmanager.prepareOffsets(this,t)}this.positionAbs=this._convertPositionTo("absolute");if(!this.options.axis||this.options.axis!="y")this.helper[0].style.left=this.position.left+"px";if(!this.options.axis||this.options.axis!="x")this.helper[0].style.top=this.position.top+"px";for(var i=this.items.length-1;i>=0;i--){var s=this.items[i],o=s.item[0],u=this._intersectsWithPointer(s);if(!u)continue;if(s.instance!==this.currentContainer)continue;if(o!=this.currentItem[0]&&this.placeholder[u==1?"next":"prev"]()[0]!=o&&!e.contains(this.placeholder[0],o)&&(this.options.type=="semi-dynamic"?!e.contains(this.element[0],o):!0)){this.direction=u==1?"down":"up";if(this.options.tolerance!="pointer"&&!this._intersectsWithSides(s))break;this._rearrange(t,s),this._trigger("change",t,this._uiHash());break}}return this._contactContainers(t),e.ui.ddmanager&&e.ui.ddmanager.drag(this,t),this._trigger("sort",t,this._uiHash()),this.lastPositionAbs=this.positionAbs,!1},_mouseStop:function(t,n){if(!t)return;e.ui.ddmanager&&!this.options.dropBehaviour&&e.ui.ddmanager.drop(this,t);if(this.options.revert){var r=this,i=this.placeholder.offset();this.reverting=!0,e(this.helper).animate({left:i.left-this.offset.parent.left-this.margins.left+(this.offsetParent[0]==document.body?0:this.offsetParent[0].scrollLeft),top:i.top-this.offset.parent.top-this.margins.top+(this.offsetParent[0]==document.body?0:this.offsetParent[0].scrollTop)},parseInt(this.options.revert,10)||500,function(){r._clear(t)})}else this._clear(t,n);return!1},cancel:function(){if(this.dragging){this._mouseUp({target:null}),this.options.helper=="original"?this.currentItem.css(this._storedCSS).removeClass("ui-sortable-helper"):this.currentItem.show();for(var t=this.containers.length-1;t>=0;t--)this.containers[t]._trigger("deactivate",null,this._uiHash(this)),this.containers[t].containerCache.over&&(this.containers[t]._trigger("out",null,this._uiHash(this)),this.containers[t].containerCache.over=0)}return this.placeholder&&(this.placeholder[0].parentNode&&this.placeholder[0].parentNode.removeChild(this.placeholder[0]),this.options.helper!="original"&&this.helper&&this.helper[0].parentNode&&this.helper.remove(),e.extend(this,{helper:null,dragging:!1,reverting:!1,_noFinalSort:null}),this.domPosition.prev?e(this.domPosition.prev).after(this.currentItem):e(this.domPosition.parent).prepend(this.currentItem)),this},serialize:function(t){var n=this._getItemsAsjQuery(t&&t.connected),r=[];return t=t||{},e(n).each(function(){var n=(e(t.item||this).attr(t.attribute||"id")||"").match(t.expression||/(.+)[-=_](.+)/);n&&r.push((t.key||n[1]+"[]")+"="+(t.key&&t.expression?n[1]:n[2]))}),!r.length&&t.key&&r.push(t.key+"="),r.join("&")},toArray:function(t){var n=this._getItemsAsjQuery(t&&t.connected),r=[];return t=t||{},n.each(function(){r.push(e(t.item||this).attr(t.attribute||"id")||"")}),r},_intersectsWith:function(e){var t=this.positionAbs.left,n=t+this.helperProportions.width,r=this.positionAbs.top,i=r+this.helperProportions.height,s=e.left,o=s+e.width,u=e.top,a=u+e.height,f=this.offset.click.top,l=this.offset.click.left,c=r+f>u&&r+f<a&&t+l>s&&t+l<o;return this.options.tolerance=="pointer"||this.options.forcePointerForContainers||this.options.tolerance!="pointer"&&this.helperProportions[this.floating?"width":"height"]>e[this.floating?"width":"height"]?c:s<t+this.helperProportions.width/2&&n-this.helperProportions.width/2<o&&u<r+this.helperProportions.height/2&&i-this.helperProportions.height/2<a},_intersectsWithPointer:function(t){var n=this.options.axis==="x"||e.ui.isOverAxis(this.positionAbs.top+this.offset.click.top,t.top,t.height),r=this.options.axis==="y"||e.ui.isOverAxis(this.positionAbs.left+this.offset.click.left,t.left,t.width),i=n&&r,s=this._getDragVerticalDirection(),o=this._getDragHorizontalDirection();return i?this.floating?o&&o=="right"||s=="down"?2:1:s&&(s=="down"?2:1):!1},_intersectsWithSides:function(t){var n=e.ui.isOverAxis(this.positionAbs.top+this.offset.click.top,t.top+t.height/2,t.height),r=e.ui.isOverAxis(this.positionAbs.left+this.offset.click.left,t.left+t.width/2,t.width),i=this._getDragVerticalDirection(),s=this._getDragHorizontalDirection();return this.floating&&s?s=="right"&&r||s=="left"&&!r:i&&(i=="down"&&n||i=="up"&&!n)},_getDragVerticalDirection:function(){var e=this.positionAbs.top-this.lastPositionAbs.top;return e!=0&&(e>0?"down":"up")},_getDragHorizontalDirection:function(){var e=this.positionAbs.left-this.lastPositionAbs.left;return e!=0&&(e>0?"right":"left")},refresh:function(e){return this._refreshItems(e),this.refreshPositions(),this},_connectWith:function(){var e=this.options;return e.connectWith.constructor==String?[e.connectWith]:e.connectWith},_getItemsAsjQuery:function(t){var n=[],r=[],i=this._connectWith();if(i&&t)for(var s=i.length-1;s>=0;s--){var o=e(i[s]);for(var u=o.length-1;u>=0;u--){var a=e.data(o[u],this.widgetName);a&&a!=this&&!a.options.disabled&&r.push([e.isFunction(a.options.items)?a.options.items.call(a.element):e(a.options.items,a.element).not(".ui-sortable-helper").not(".ui-sortable-placeholder"),a])}}r.push([e.isFunction(this.options.items)?this.options.items.call(this.element,null,{options:this.options,item:this.currentItem}):e(this.options.items,this.element).not(".ui-sortable-helper").not(".ui-sortable-placeholder"),this]);for(var s=r.length-1;s>=0;s--)r[s][0].each(function(){n.push(this)});return e(n)},_removeCurrentsFromItems:function(){var t=this.currentItem.find(":data("+this.widgetName+"-item)");this.items=e.grep(this.items,function(e){for(var n=0;n<t.length;n++)if(t[n]==e.item[0])return!1;return!0})},_refreshItems:function(t){this.items=[],this.containers=[this];var n=this.items,r=[[e.isFunction(this.options.items)?this.options.items.call(this.element[0],t,{item:this.currentItem}):e(this.options.items,this.element),this]],i=this._connectWith();if(i&&this.ready)for(var s=i.length-1;s>=0;s--){var o=e(i[s]);for(var u=o.length-1;u>=0;u--){var a=e.data(o[u],this.widgetName);a&&a!=this&&!a.options.disabled&&(r.push([e.isFunction(a.options.items)?a.options.items.call(a.element[0],t,{item:this.currentItem}):e(a.options.items,a.element),a]),this.containers.push(a))}}for(var s=r.length-1;s>=0;s--){var f=r[s][1],l=r[s][0];for(var u=0,c=l.length;u<c;u++){var h=e(l[u]);h.data(this.widgetName+"-item",f),n.push({item:h,instance:f,width:0,height:0,left:0,top:0})}}},refreshPositions:function(t){this.offsetParent&&this.helper&&(this.offset.parent=this._getParentOffset());for(var n=this.items.length-1;n>=0;n--){var r=this.items[n];if(r.instance!=this.currentContainer&&this.currentContainer&&r.item[0]!=this.currentItem[0])continue;var i=this.options.toleranceElement?e(this.options.toleranceElement,r.item):r.item;t||(r.width=i.outerWidth(),r.height=i.outerHeight());var s=i.offset();r.left=s.left,r.top=s.top}if(this.options.custom&&this.options.custom.refreshContainers)this.options.custom.refreshContainers.call(this);else for(var n=this.containers.length-1;n>=0;n--){var s=this.containers[n].element.offset();this.containers[n].containerCache.left=s.left,this.containers[n].containerCache.top=s.top,this.containers[n].containerCache.width=this.containers[n].element.outerWidth(),this.containers[n].containerCache.height=this.containers[n].element.outerHeight()}return this},_createPlaceholder:function(t){t=t||this;var n=t.options;if(!n.placeholder||n.placeholder.constructor==String){var r=n.placeholder;n.placeholder={element:function(){var n=e(document.createElement(t.currentItem[0].nodeName)).addClass(r||t.currentItem[0].className+" ui-sortable-placeholder").removeClass("ui-sortable-helper")[0];return r||(n.style.visibility="hidden"),n},update:function(e,i){if(r&&!n.forcePlaceholderSize)return;i.height()||i.height(t.currentItem.innerHeight()-parseInt(t.currentItem.css("paddingTop")||0,10)-parseInt(t.currentItem.css("paddingBottom")||0,10)),i.width()||i.width(t.currentItem.innerWidth()-parseInt(t.currentItem.css("paddingLeft")||0,10)-parseInt(t.currentItem.css("paddingRight")||0,10))}}}t.placeholder=e(n.placeholder.element.call(t.element,t.currentItem)),t.currentItem.after(t.placeholder),n.placeholder.update(t,t.placeholder)},_contactContainers:function(t){var n=null,r=null;for(var i=this.containers.length-1;i>=0;i--){if(e.contains(this.currentItem[0],this.containers[i].element[0]))continue;if(this._intersectsWith(this.containers[i].containerCache)){if(n&&e.contains(this.containers[i].element[0],n.element[0]))continue;n=this.containers[i],r=i}else this.containers[i].containerCache.over&&(this.containers[i]._trigger("out",t,this._uiHash(this)),this.containers[i].containerCache.over=0)}if(!n)return;if(this.containers.length===1)this.containers[r]._trigger("over",t,this._uiHash(this)),this.containers[r].containerCache.over=1;else{var s=1e4,o=null,u=this.containers[r].floating?"left":"top",a=this.containers[r].floating?"width":"height",f=this.positionAbs[u]+this.offset.click[u];for(var l=this.items.length-1;l>=0;l--){if(!e.contains(this.containers[r].element[0],this.items[l].item[0]))continue;if(this.items[l].item[0]==this.currentItem[0])continue;var c=this.items[l].item.offset()[u],h=!1;Math.abs(c-f)>Math.abs(c+this.items[l][a]-f)&&(h=!0,c+=this.items[l][a]),Math.abs(c-f)<s&&(s=Math.abs(c-f),o=this.items[l],this.direction=h?"up":"down")}if(!o&&!this.options.dropOnEmpty)return;this.currentContainer=this.containers[r],o?this._rearrange(t,o,null,!0):this._rearrange(t,null,this.containers[r].element,!0),this._trigger("change",t,this._uiHash()),this.containers[r]._trigger("change",t,this._uiHash(this)),this.options.placeholder.update(this.currentContainer,this.placeholder),this.containers[r]._trigger("over",t,this._uiHash(this)),this.containers[r].containerCache.over=1}},_createHelper:function(t){var n=this.options,r=e.isFunction(n.helper)?e(n.helper.apply(this.element[0],[t,this.currentItem])):n.helper=="clone"?this.currentItem.clone():this.currentItem;return r.parents("body").length||e(n.appendTo!="parent"?n.appendTo:this.currentItem[0].parentNode)[0].appendChild(r[0]),r[0]==this.currentItem[0]&&(this._storedCSS={width:this.currentItem[0].style.width,height:this.currentItem[0].style.height,position:this.currentItem.css("position"),top:this.currentItem.css("top"),left:this.currentItem.css("left")}),(r[0].style.width==""||n.forceHelperSize)&&r.width(this.currentItem.width()),(r[0].style.height==""||n.forceHelperSize)&&r.height(this.currentItem.height()),r},_adjustOffsetFromHelper:function(t){typeof t=="string"&&(t=t.split(" ")),e.isArray(t)&&(t={left:+t[0],top:+t[1]||0}),"left"in t&&(this.offset.click.left=t.left+this.margins.left),"right"in t&&(this.offset.click.left=this.helperProportions.width-t.right+this.margins.left),"top"in t&&(this.offset.click.top=t.top+this.margins.top),"bottom"in t&&(this.offset.click.top=this.helperProportions.height-t.bottom+this.margins.top)},_getParentOffset:function(){this.offsetParent=this.helper.offsetParent();var t=this.offsetParent.offset();this.cssPosition=="absolute"&&this.scrollParent[0]!=document&&e.contains(this.scrollParent[0],this.offsetParent[0])&&(t.left+=this.scrollParent.scrollLeft(),t.top+=this.scrollParent.scrollTop());if(this.offsetParent[0]==document.body||this.offsetParent[0].tagName&&this.offsetParent[0].tagName.toLowerCase()=="html"&&e.ui.ie)t={top:0,left:0};return{top:t.top+(parseInt(this.offsetParent.css("borderTopWidth"),10)||0),left:t.left+(parseInt(this.offsetParent.css("borderLeftWidth"),10)||0)}},_getRelativeOffset:function(){if(this.cssPosition=="relative"){var e=this.currentItem.position();return{top:e.top-(parseInt(this.helper.css("top"),10)||0)+this.scrollParent.scrollTop(),left:e.left-(parseInt(this.helper.css("left"),10)||0)+this.scrollParent.scrollLeft()}}return{top:0,left:0}},_cacheMargins:function(){this.margins={left:parseInt(this.currentItem.css("marginLeft"),10)||0,top:parseInt(this.currentItem.css("marginTop"),10)||0}},_cacheHelperProportions:function(){this.helperProportions={width:this.helper.outerWidth(),height:this.helper.outerHeight()}},_setContainment:function(){var t=this.options;t.containment=="parent"&&(t.containment=this.helper[0].parentNode);if(t.containment=="document"||t.containment=="window")this.containment=[0-this.offset.relative.left-this.offset.parent.left,0-this.offset.relative.top-this.offset.parent.top,e(t.containment=="document"?document:window).width()-this.helperProportions.width-this.margins.left,(e(t.containment=="document"?document:window).height()||document.body.parentNode.scrollHeight)-this.helperProportions.height-this.margins.top];if(!/^(document|window|parent)$/.test(t.containment)){var n=e(t.containment)[0],r=e(t.containment).offset(),i=e(n).css("overflow")!="hidden";this.containment=[r.left+(parseInt(e(n).css("borderLeftWidth"),10)||0)+(parseInt(e(n).css("paddingLeft"),10)||0)-this.margins.left,r.top+(parseInt(e(n).css("borderTopWidth"),10)||0)+(parseInt(e(n).css("paddingTop"),10)||0)-this.margins.top,r.left+(i?Math.max(n.scrollWidth,n.offsetWidth):n.offsetWidth)-(parseInt(e(n).css("borderLeftWidth"),10)||0)-(parseInt(e(n).css("paddingRight"),10)||0)-this.helperProportions.width-this.margins.left,r.top+(i?Math.max(n.scrollHeight,n.offsetHeight):n.offsetHeight)-(parseInt(e(n).css("borderTopWidth"),10)||0)-(parseInt(e(n).css("paddingBottom"),10)||0)-this.helperProportions.height-this.margins.top]}},_convertPositionTo:function(t,n){n||(n=this.position);var r=t=="absolute"?1:-1,i=this.options,s=this.cssPosition!="absolute"||this.scrollParent[0]!=document&&!!e.contains(this.scrollParent[0],this.offsetParent[0])?this.scrollParent:this.offsetParent,o=/(html|body)/i.test(s[0].tagName);return{top:n.top+this.offset.relative.top*r+this.offset.parent.top*r-(this.cssPosition=="fixed"?-this.scrollParent.scrollTop():o?0:s.scrollTop())*r,left:n.left+this.offset.relative.left*r+this.offset.parent.left*r-(this.cssPosition=="fixed"?-this.scrollParent.scrollLeft():o?0:s.scrollLeft())*r}},_generatePosition:function(t){var n=this.options,r=this.cssPosition!="absolute"||this.scrollParent[0]!=document&&!!e.contains(this.scrollParent[0],this.offsetParent[0])?this.scrollParent:this.offsetParent,i=/(html|body)/i.test(r[0].tagName);this.cssPosition=="relative"&&(this.scrollParent[0]==document||this.scrollParent[0]==this.offsetParent[0])&&(this.offset.relative=this._getRelativeOffset());var s=t.pageX,o=t.pageY;if(this.originalPosition){this.containment&&(t.pageX-this.offset.click.left<this.containment[0]&&(s=this.containment[0]+this.offset.click.left),t.pageY-this.offset.click.top<this.containment[1]&&(o=this.containment[1]+this.offset.click.top),t.pageX-this.offset.click.left>this.containment[2]&&(s=this.containment[2]+this.offset.click.left),t.pageY-this.offset.click.top>this.containment[3]&&(o=this.containment[3]+this.offset.click.top));if(n.grid){var u=this.originalPageY+Math.round((o-this.originalPageY)/n.grid[1])*n.grid[1];o=this.containment?u-this.offset.click.top<this.containment[1]||u-this.offset.click.top>this.containment[3]?u-this.offset.click.top<this.containment[1]?u+n.grid[1]:u-n.grid[1]:u:u;var a=this.originalPageX+Math.round((s-this.originalPageX)/n.grid[0])*n.grid[0];s=this.containment?a-this.offset.click.left<this.containment[0]||a-this.offset.click.left>this.containment[2]?a-this.offset.click.left<this.containment[0]?a+n.grid[0]:a-n.grid[0]:a:a}}return{top:o-this.offset.click.top-this.offset.relative.top-this.offset.parent.top+(this.cssPosition=="fixed"?-this.scrollParent.scrollTop():i?0:r.scrollTop()),left:s-this.offset.click.left-this.offset.relative.left-this.offset.parent.left+(this.cssPosition=="fixed"?-this.scrollParent.scrollLeft():i?0:r.scrollLeft())}},_rearrange:function(e,t,n,r){n?n[0].appendChild(this.placeholder[0]):t.item[0].parentNode.insertBefore(this.placeholder[0],this.direction=="down"?t.item[0]:t.item[0].nextSibling),this.counter=this.counter?++this.counter:1;var i=this.counter;this._delay(function(){i==this.counter&&this.refreshPositions(!r)})},_clear:function(t,n){this.reverting=!1;var r=[];!this._noFinalSort&&this.currentItem.parent().length&&this.placeholder.before(this.currentItem),this._noFinalSort=null;if(this.helper[0]==this.currentItem[0]){for(var i in this._storedCSS)if(this._storedCSS[i]=="auto"||this._storedCSS[i]=="static")this._storedCSS[i]="";this.currentItem.css(this._storedCSS).removeClass("ui-sortable-helper")}else this.currentItem.show();this.fromOutside&&!n&&r.push(function(e){this._trigger("receive",e,this._uiHash(this.fromOutside))}),(this.fromOutside||this.domPosition.prev!=this.currentItem.prev().not(".ui-sortable-helper")[0]||this.domPosition.parent!=this.currentItem.parent()[0])&&!n&&r.push(function(e){this._trigger("update",e,this._uiHash())}),this!==this.currentContainer&&(n||(r.push(function(e){this._trigger("remove",e,this._uiHash())}),r.push(function(e){return function(t){e._trigger("receive",t,this._uiHash(this))}}.call(this,this.currentContainer)),r.push(function(e){return function(t){e._trigger("update",t,this._uiHash(this))}}.call(this,this.currentContainer))));for(var i=this.containers.length-1;i>=0;i--)n||r.push(function(e){return function(t){e._trigger("deactivate",t,this._uiHash(this))}}.call(this,this.containers[i])),this.containers[i].containerCache.over&&(r.push(function(e){return function(t){e._trigger("out",t,this._uiHash(this))}}.call(this,this.containers[i])),this.containers[i].containerCache.over=0);this._storedCursor&&e("body").css("cursor",this._storedCursor),this._storedOpacity&&this.helper.css("opacity",this._storedOpacity),this._storedZIndex&&this.helper.css("zIndex",this._storedZIndex=="auto"?"":this._storedZIndex),this.dragging=!1;if(this.cancelHelperRemoval){if(!n){this._trigger("beforeStop",t,this._uiHash());for(var i=0;i<r.length;i++)r[i].call(this,t);this._trigger("stop",t,this._uiHash())}return this.fromOutside=!1,!1}n||this._trigger("beforeStop",t,this._uiHash()),this.placeholder[0].parentNode.removeChild(this.placeholder[0]),this.helper[0]!=this.currentItem[0]&&this.helper.remove(),this.helper=null;if(!n){for(var i=0;i<r.length;i++)r[i].call(this,t);this._trigger("stop",t,this._uiHash())}return this.fromOutside=!1,!0},_trigger:function(){e.Widget.prototype._trigger.apply(this,arguments)===!1&&this.cancel()},_uiHash:function(t){var n=t||this;return{helper:n.helper,placeholder:n.placeholder||e([]),position:n.position,originalPosition:n.originalPosition,offset:n.positionAbs,item:n.currentItem,sender:t?t.element:null}}})}(jQuery),jQuery.effects||function(e,t){var n=e.uiBackCompat!==!1,r="ui-effects-";e.effects={effect:{}},function(t,n){function r(e,t,n){var r=c[t.type]||{};return e==null?n||!t.def?null:t.def:(e=r.floor?~~e:parseFloat(e),isNaN(e)?t.def:r.mod?(e+r.mod)%r.mod:0>e?0:r.max<e?r.max:e)}function i(e){var n=f(),r=n._rgba=[];return e=e.toLowerCase(),v(a,function(t,i){var s,o=i.re.exec(e),u=o&&i.parse(o),a=i.space||"rgba";if(u)return s=n[a](u),n[l[a].cache]=s[l[a].cache],r=n._rgba=s._rgba,!1}),r.length?(r.join()==="0,0,0,0"&&t.extend(r,d.transparent),n):d[e]}function s(e,t,n){return n=(n+1)%1,n*6<1?e+(t-e)*n*6:n*2<1?t:n*3<2?e+(t-e)*(2/3-n)*6:e}var o="backgroundColor borderBottomColor borderLeftColor borderRightColor borderTopColor color columnRuleColor outlineColor textDecorationColor textEmphasisColor".split(" "),u=/^([\-+])=\s*(\d+\.?\d*)/,a=[{re:/rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(?:,\s*(\d+(?:\.\d+)?)\s*)?\)/,parse:function(e){return[e[1],e[2],e[3],e[4]]}},{re:/rgba?\(\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*(?:,\s*(\d+(?:\.\d+)?)\s*)?\)/,parse:function(e){return[e[1]*2.55,e[2]*2.55,e[3]*2.55,e[4]]}},{re:/#([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})/,parse:function(e){return[parseInt(e[1],16),parseInt(e[2],16),parseInt(e[3],16)]}},{re:/#([a-f0-9])([a-f0-9])([a-f0-9])/,parse:function(e){return[parseInt(e[1]+e[1],16),parseInt(e[2]+e[2],16),parseInt(e[3]+e[3],16)]}},{re:/hsla?\(\s*(\d+(?:\.\d+)?)\s*,\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*(?:,\s*(\d+(?:\.\d+)?)\s*)?\)/,space:"hsla",parse:function(e){return[e[1],e[2]/100,e[3]/100,e[4]]}}],f=t.Color=function(e,n,r,i){return new t.Color.fn.parse(e,n,r,i)},l={rgba:{props:{red:{idx:0,type:"byte"},green:{idx:1,type:"byte"},blue:{idx:2,type:"byte"}}},hsla:{props:{hue:{idx:0,type:"degrees"},saturation:{idx:1,type:"percent"},lightness:{idx:2,type:"percent"}}}},c={"byte":{floor:!0,max:255},percent:{max:1},degrees:{mod:360,floor:!0}},h=f.support={},p=t("<p>")[0],d,v=t.each;p.style.cssText="background-color:rgba(1,1,1,.5)",h.rgba=p.style.backgroundColor
.indexOf("rgba")>-1,v(l,function(e,t){t.cache="_"+e,t.props.alpha={idx:3,type:"percent",def:1}}),f.fn=t.extend(f.prototype,{parse:function(s,o,u,a){if(s===n)return this._rgba=[null,null,null,null],this;if(s.jquery||s.nodeType)s=t(s).css(o),o=n;var c=this,h=t.type(s),p=this._rgba=[];o!==n&&(s=[s,o,u,a],h="array");if(h==="string")return this.parse(i(s)||d._default);if(h==="array")return v(l.rgba.props,function(e,t){p[t.idx]=r(s[t.idx],t)}),this;if(h==="object")return s instanceof f?v(l,function(e,t){s[t.cache]&&(c[t.cache]=s[t.cache].slice())}):v(l,function(t,n){var i=n.cache;v(n.props,function(e,t){if(!c[i]&&n.to){if(e==="alpha"||s[e]==null)return;c[i]=n.to(c._rgba)}c[i][t.idx]=r(s[e],t,!0)}),c[i]&&e.inArray(null,c[i].slice(0,3))<0&&(c[i][3]=1,n.from&&(c._rgba=n.from(c[i])))}),this},is:function(e){var t=f(e),n=!0,r=this;return v(l,function(e,i){var s,o=t[i.cache];return o&&(s=r[i.cache]||i.to&&i.to(r._rgba)||[],v(i.props,function(e,t){if(o[t.idx]!=null)return n=o[t.idx]===s[t.idx],n})),n}),n},_space:function(){var e=[],t=this;return v(l,function(n,r){t[r.cache]&&e.push(n)}),e.pop()},transition:function(e,t){var n=f(e),i=n._space(),s=l[i],o=this.alpha()===0?f("transparent"):this,u=o[s.cache]||s.to(o._rgba),a=u.slice();return n=n[s.cache],v(s.props,function(e,i){var s=i.idx,o=u[s],f=n[s],l=c[i.type]||{};if(f===null)return;o===null?a[s]=f:(l.mod&&(f-o>l.mod/2?o+=l.mod:o-f>l.mod/2&&(o-=l.mod)),a[s]=r((f-o)*t+o,i))}),this[i](a)},blend:function(e){if(this._rgba[3]===1)return this;var n=this._rgba.slice(),r=n.pop(),i=f(e)._rgba;return f(t.map(n,function(e,t){return(1-r)*i[t]+r*e}))},toRgbaString:function(){var e="rgba(",n=t.map(this._rgba,function(e,t){return e==null?t>2?1:0:e});return n[3]===1&&(n.pop(),e="rgb("),e+n.join()+")"},toHslaString:function(){var e="hsla(",n=t.map(this.hsla(),function(e,t){return e==null&&(e=t>2?1:0),t&&t<3&&(e=Math.round(e*100)+"%"),e});return n[3]===1&&(n.pop(),e="hsl("),e+n.join()+")"},toHexString:function(e){var n=this._rgba.slice(),r=n.pop();return e&&n.push(~~(r*255)),"#"+t.map(n,function(e){return e=(e||0).toString(16),e.length===1?"0"+e:e}).join("")},toString:function(){return this._rgba[3]===0?"transparent":this.toRgbaString()}}),f.fn.parse.prototype=f.fn,l.hsla.to=function(e){if(e[0]==null||e[1]==null||e[2]==null)return[null,null,null,e[3]];var t=e[0]/255,n=e[1]/255,r=e[2]/255,i=e[3],s=Math.max(t,n,r),o=Math.min(t,n,r),u=s-o,a=s+o,f=a*.5,l,c;return o===s?l=0:t===s?l=60*(n-r)/u+360:n===s?l=60*(r-t)/u+120:l=60*(t-n)/u+240,f===0||f===1?c=f:f<=.5?c=u/a:c=u/(2-a),[Math.round(l)%360,c,f,i==null?1:i]},l.hsla.from=function(e){if(e[0]==null||e[1]==null||e[2]==null)return[null,null,null,e[3]];var t=e[0]/360,n=e[1],r=e[2],i=e[3],o=r<=.5?r*(1+n):r+n-r*n,u=2*r-o;return[Math.round(s(u,o,t+1/3)*255),Math.round(s(u,o,t)*255),Math.round(s(u,o,t-1/3)*255),i]},v(l,function(e,i){var s=i.props,o=i.cache,a=i.to,l=i.from;f.fn[e]=function(e){a&&!this[o]&&(this[o]=a(this._rgba));if(e===n)return this[o].slice();var i,u=t.type(e),c=u==="array"||u==="object"?e:arguments,h=this[o].slice();return v(s,function(e,t){var n=c[u==="object"?e:t.idx];n==null&&(n=h[t.idx]),h[t.idx]=r(n,t)}),l?(i=f(l(h)),i[o]=h,i):f(h)},v(s,function(n,r){if(f.fn[n])return;f.fn[n]=function(i){var s=t.type(i),o=n==="alpha"?this._hsla?"hsla":"rgba":e,a=this[o](),f=a[r.idx],l;return s==="undefined"?f:(s==="function"&&(i=i.call(this,f),s=t.type(i)),i==null&&r.empty?this:(s==="string"&&(l=u.exec(i),l&&(i=f+parseFloat(l[2])*(l[1]==="+"?1:-1))),a[r.idx]=i,this[o](a)))}})}),v(o,function(e,n){t.cssHooks[n]={set:function(e,r){var s,o,u="";if(t.type(r)!=="string"||(s=i(r))){r=f(s||r);if(!h.rgba&&r._rgba[3]!==1){o=n==="backgroundColor"?e.parentNode:e;while((u===""||u==="transparent")&&o&&o.style)try{u=t.css(o,"backgroundColor"),o=o.parentNode}catch(a){}r=r.blend(u&&u!=="transparent"?u:"_default")}r=r.toRgbaString()}try{e.style[n]=r}catch(l){}}},t.fx.step[n]=function(e){e.colorInit||(e.start=f(e.elem,n),e.end=f(e.end),e.colorInit=!0),t.cssHooks[n].set(e.elem,e.start.transition(e.end,e.pos))}}),t.cssHooks.borderColor={expand:function(e){var t={};return v(["Top","Right","Bottom","Left"],function(n,r){t["border"+r+"Color"]=e}),t}},d=t.Color.names={aqua:"#00ffff",black:"#000000",blue:"#0000ff",fuchsia:"#ff00ff",gray:"#808080",green:"#008000",lime:"#00ff00",maroon:"#800000",navy:"#000080",olive:"#808000",purple:"#800080",red:"#ff0000",silver:"#c0c0c0",teal:"#008080",white:"#ffffff",yellow:"#ffff00",transparent:[null,null,null,0],_default:"#ffffff"}}(jQuery),function(){function n(){var t=this.ownerDocument.defaultView?this.ownerDocument.defaultView.getComputedStyle(this,null):this.currentStyle,n={},r,i;if(t&&t.length&&t[0]&&t[t[0]]){i=t.length;while(i--)r=t[i],typeof t[r]=="string"&&(n[e.camelCase(r)]=t[r])}else for(r in t)typeof t[r]=="string"&&(n[r]=t[r]);return n}function r(t,n){var r={},i,o;for(i in n)o=n[i],t[i]!==o&&!s[i]&&(e.fx.step[i]||!isNaN(parseFloat(o)))&&(r[i]=o);return r}var i=["add","remove","toggle"],s={border:1,borderBottom:1,borderColor:1,borderLeft:1,borderRight:1,borderTop:1,borderWidth:1,margin:1,padding:1};e.each(["borderLeftStyle","borderRightStyle","borderBottomStyle","borderTopStyle"],function(t,n){e.fx.step[n]=function(e){if(e.end!=="none"&&!e.setAttr||e.pos===1&&!e.setAttr)jQuery.style(e.elem,n,e.end),e.setAttr=!0}}),e.effects.animateClass=function(t,s,o,u){var a=e.speed(s,o,u);return this.queue(function(){var s=e(this),o=s.attr("class")||"",u,f=a.children?s.find("*").andSelf():s;f=f.map(function(){var t=e(this);return{el:t,start:n.call(this)}}),u=function(){e.each(i,function(e,n){t[n]&&s[n+"Class"](t[n])})},u(),f=f.map(function(){return this.end=n.call(this.el[0]),this.diff=r(this.start,this.end),this}),s.attr("class",o),f=f.map(function(){var t=this,n=e.Deferred(),r=jQuery.extend({},a,{queue:!1,complete:function(){n.resolve(t)}});return this.el.animate(this.diff,r),n.promise()}),e.when.apply(e,f.get()).done(function(){u(),e.each(arguments,function(){var t=this.el;e.each(this.diff,function(e){t.css(e,"")})}),a.complete.call(s[0])})})},e.fn.extend({_addClass:e.fn.addClass,addClass:function(t,n,r,i){return n?e.effects.animateClass.call(this,{add:t},n,r,i):this._addClass(t)},_removeClass:e.fn.removeClass,removeClass:function(t,n,r,i){return n?e.effects.animateClass.call(this,{remove:t},n,r,i):this._removeClass(t)},_toggleClass:e.fn.toggleClass,toggleClass:function(n,r,i,s,o){return typeof r=="boolean"||r===t?i?e.effects.animateClass.call(this,r?{add:n}:{remove:n},i,s,o):this._toggleClass(n,r):e.effects.animateClass.call(this,{toggle:n},r,i,s)},switchClass:function(t,n,r,i,s){return e.effects.animateClass.call(this,{add:n,remove:t},r,i,s)}})}(),function(){function i(t,n,r,i){e.isPlainObject(t)&&(n=t,t=t.effect),t={effect:t},n==null&&(n={}),e.isFunction(n)&&(i=n,r=null,n={});if(typeof n=="number"||e.fx.speeds[n])i=r,r=n,n={};return e.isFunction(r)&&(i=r,r=null),n&&e.extend(t,n),r=r||n.duration,t.duration=e.fx.off?0:typeof r=="number"?r:r in e.fx.speeds?e.fx.speeds[r]:e.fx.speeds._default,t.complete=i||n.complete,t}function s(t){return!t||typeof t=="number"||e.fx.speeds[t]?!0:typeof t=="string"&&!e.effects.effect[t]?n&&e.effects[t]?!1:!0:!1}e.extend(e.effects,{version:"1.9.2",save:function(e,t){for(var n=0;n<t.length;n++)t[n]!==null&&e.data(r+t[n],e[0].style[t[n]])},restore:function(e,n){var i,s;for(s=0;s<n.length;s++)n[s]!==null&&(i=e.data(r+n[s]),i===t&&(i=""),e.css(n[s],i))},setMode:function(e,t){return t==="toggle"&&(t=e.is(":hidden")?"show":"hide"),t},getBaseline:function(e,t){var n,r;switch(e[0]){case"top":n=0;break;case"middle":n=.5;break;case"bottom":n=1;break;default:n=e[0]/t.height}switch(e[1]){case"left":r=0;break;case"center":r=.5;break;case"right":r=1;break;default:r=e[1]/t.width}return{x:r,y:n}},createWrapper:function(t){if(t.parent().is(".ui-effects-wrapper"))return t.parent();var n={width:t.outerWidth(!0),height:t.outerHeight(!0),"float":t.css("float")},r=e("<div></div>").addClass("ui-effects-wrapper").css({fontSize:"100%",background:"transparent",border:"none",margin:0,padding:0}),i={width:t.width(),height:t.height()},s=document.activeElement;try{s.id}catch(o){s=document.body}return t.wrap(r),(t[0]===s||e.contains(t[0],s))&&e(s).focus(),r=t.parent(),t.css("position")==="static"?(r.css({position:"relative"}),t.css({position:"relative"})):(e.extend(n,{position:t.css("position"),zIndex:t.css("z-index")}),e.each(["top","left","bottom","right"],function(e,r){n[r]=t.css(r),isNaN(parseInt(n[r],10))&&(n[r]="auto")}),t.css({position:"relative",top:0,left:0,right:"auto",bottom:"auto"})),t.css(i),r.css(n).show()},removeWrapper:function(t){var n=document.activeElement;return t.parent().is(".ui-effects-wrapper")&&(t.parent().replaceWith(t),(t[0]===n||e.contains(t[0],n))&&e(n).focus()),t},setTransition:function(t,n,r,i){return i=i||{},e.each(n,function(e,n){var s=t.cssUnit(n);s[0]>0&&(i[n]=s[0]*r+s[1])}),i}}),e.fn.extend({effect:function(){function t(t){function n(){e.isFunction(s)&&s.call(i[0]),e.isFunction(t)&&t()}var i=e(this),s=r.complete,o=r.mode;(i.is(":hidden")?o==="hide":o==="show")?n():u.call(i[0],r,n)}var r=i.apply(this,arguments),s=r.mode,o=r.queue,u=e.effects.effect[r.effect],a=!u&&n&&e.effects[r.effect];return e.fx.off||!u&&!a?s?this[s](r.duration,r.complete):this.each(function(){r.complete&&r.complete.call(this)}):u?o===!1?this.each(t):this.queue(o||"fx",t):a.call(this,{options:r,duration:r.duration,callback:r.complete,mode:r.mode})},_show:e.fn.show,show:function(e){if(s(e))return this._show.apply(this,arguments);var t=i.apply(this,arguments);return t.mode="show",this.effect.call(this,t)},_hide:e.fn.hide,hide:function(e){if(s(e))return this._hide.apply(this,arguments);var t=i.apply(this,arguments);return t.mode="hide",this.effect.call(this,t)},__toggle:e.fn.toggle,toggle:function(t){if(s(t)||typeof t=="boolean"||e.isFunction(t))return this.__toggle.apply(this,arguments);var n=i.apply(this,arguments);return n.mode="toggle",this.effect.call(this,n)},cssUnit:function(t){var n=this.css(t),r=[];return e.each(["em","px","%","pt"],function(e,t){n.indexOf(t)>0&&(r=[parseFloat(n),t])}),r}})}(),function(){var t={};e.each(["Quad","Cubic","Quart","Quint","Expo"],function(e,n){t[n]=function(t){return Math.pow(t,e+2)}}),e.extend(t,{Sine:function(e){return 1-Math.cos(e*Math.PI/2)},Circ:function(e){return 1-Math.sqrt(1-e*e)},Elastic:function(e){return e===0||e===1?e:-Math.pow(2,8*(e-1))*Math.sin(((e-1)*80-7.5)*Math.PI/15)},Back:function(e){return e*e*(3*e-2)},Bounce:function(e){var t,n=4;while(e<((t=Math.pow(2,--n))-1)/11);return 1/Math.pow(4,3-n)-7.5625*Math.pow((t*3-2)/22-e,2)}}),e.each(t,function(t,n){e.easing["easeIn"+t]=n,e.easing["easeOut"+t]=function(e){return 1-n(1-e)},e.easing["easeInOut"+t]=function(e){return e<.5?n(e*2)/2:1-n(e*-2+2)/2}})}()}(jQuery),function(e,t){var n=/up|down|vertical/,r=/up|left|vertical|horizontal/;e.effects.effect.blind=function(t,i){var s=e(this),o=["position","top","bottom","left","right","height","width"],u=e.effects.setMode(s,t.mode||"hide"),a=t.direction||"up",f=n.test(a),l=f?"height":"width",c=f?"top":"left",h=r.test(a),p={},d=u==="show",v,m,g;s.parent().is(".ui-effects-wrapper")?e.effects.save(s.parent(),o):e.effects.save(s,o),s.show(),v=e.effects.createWrapper(s).css({overflow:"hidden"}),m=v[l](),g=parseFloat(v.css(c))||0,p[l]=d?m:0,h||(s.css(f?"bottom":"right",0).css(f?"top":"left","auto").css({position:"absolute"}),p[c]=d?g:m+g),d&&(v.css(l,0),h||v.css(c,g+m)),v.animate(p,{duration:t.duration,easing:t.easing,queue:!1,complete:function(){u==="hide"&&s.hide(),e.effects.restore(s,o),e.effects.removeWrapper(s),i()}})}}(jQuery),function(e,t){e.effects.effect.bounce=function(t,n){var r=e(this),i=["position","top","bottom","left","right","height","width"],s=e.effects.setMode(r,t.mode||"effect"),o=s==="hide",u=s==="show",a=t.direction||"up",f=t.distance,l=t.times||5,c=l*2+(u||o?1:0),h=t.duration/c,p=t.easing,d=a==="up"||a==="down"?"top":"left",v=a==="up"||a==="left",m,g,y,b=r.queue(),w=b.length;(u||o)&&i.push("opacity"),e.effects.save(r,i),r.show(),e.effects.createWrapper(r),f||(f=r[d==="top"?"outerHeight":"outerWidth"]()/3),u&&(y={opacity:1},y[d]=0,r.css("opacity",0).css(d,v?-f*2:f*2).animate(y,h,p)),o&&(f/=Math.pow(2,l-1)),y={},y[d]=0;for(m=0;m<l;m++)g={},g[d]=(v?"-=":"+=")+f,r.animate(g,h,p).animate(y,h,p),f=o?f*2:f/2;o&&(g={opacity:0},g[d]=(v?"-=":"+=")+f,r.animate(g,h,p)),r.queue(function(){o&&r.hide(),e.effects.restore(r,i),e.effects.removeWrapper(r),n()}),w>1&&b.splice.apply(b,[1,0].concat(b.splice(w,c+1))),r.dequeue()}}(jQuery),function(e,t){e.effects.effect.clip=function(t,n){var r=e(this),i=["position","top","bottom","left","right","height","width"],s=e.effects.setMode(r,t.mode||"hide"),o=s==="show",u=t.direction||"vertical",a=u==="vertical",f=a?"height":"width",l=a?"top":"left",c={},h,p,d;e.effects.save(r,i),r.show(),h=e.effects.createWrapper(r).css({overflow:"hidden"}),p=r[0].tagName==="IMG"?h:r,d=p[f](),o&&(p.css(f,0),p.css(l,d/2)),c[f]=o?d:0,c[l]=o?0:d/2,p.animate(c,{queue:!1,duration:t.duration,easing:t.easing,complete:function(){o||r.hide(),e.effects.restore(r,i),e.effects.removeWrapper(r),n()}})}}(jQuery),function(e,t){e.effects.effect.drop=function(t,n){var r=e(this),i=["position","top","bottom","left","right","opacity","height","width"],s=e.effects.setMode(r,t.mode||"hide"),o=s==="show",u=t.direction||"left",a=u==="up"||u==="down"?"top":"left",f=u==="up"||u==="left"?"pos":"neg",l={opacity:o?1:0},c;e.effects.save(r,i),r.show(),e.effects.createWrapper(r),c=t.distance||r[a==="top"?"outerHeight":"outerWidth"](!0)/2,o&&r.css("opacity",0).css(a,f==="pos"?-c:c),l[a]=(o?f==="pos"?"+=":"-=":f==="pos"?"-=":"+=")+c,r.animate(l,{queue:!1,duration:t.duration,easing:t.easing,complete:function(){s==="hide"&&r.hide(),e.effects.restore(r,i),e.effects.removeWrapper(r),n()}})}}(jQuery),function(e,t){e.effects.effect.explode=function(t,n){function r(){p.push(this),p.length===s*o&&i()}function i(){u.css({visibility:"visible"}),e(p).remove(),f||u.hide(),n()}var s=t.pieces?Math.round(Math.sqrt(t.pieces)):3,o=s,u=e(this),a=e.effects.setMode(u,t.mode||"hide"),f=a==="show",l=u.show().css("visibility","hidden").offset(),c=Math.ceil(u.outerWidth()/o),h=Math.ceil(u.outerHeight()/s),p=[],d,v,m,g,y,b;for(d=0;d<s;d++){g=l.top+d*h,b=d-(s-1)/2;for(v=0;v<o;v++)m=l.left+v*c,y=v-(o-1)/2,u.clone().appendTo("body").wrap("<div></div>").css({position:"absolute",visibility:"visible",left:-v*c,top:-d*h}).parent().addClass("ui-effects-explode").css({position:"absolute",overflow:"hidden",width:c,height:h,left:m+(f?y*c:0),top:g+(f?b*h:0),opacity:f?0:1}).animate({left:m+(f?0:y*c),top:g+(f?0:b*h),opacity:f?1:0},t.duration||500,t.easing,r)}}}(jQuery),function(e,t){e.effects.effect.fade=function(t,n){var r=e(this),i=e.effects.setMode(r,t.mode||"toggle");r.animate({opacity:i},{queue:!1,duration:t.duration,easing:t.easing,complete:n})}}(jQuery),function(e,t){e.effects.effect.fold=function(t,n){var r=e(this),i=["position","top","bottom","left","right","height","width"],s=e.effects.setMode(r,t.mode||"hide"),o=s==="show",u=s==="hide",a=t.size||15,f=/([0-9]+)%/.exec(a),l=!!t.horizFirst,c=o!==l,h=c?["width","height"]:["height","width"],p=t.duration/2,d,v,m={},g={};e.effects.save(r,i),r.show(),d=e.effects.createWrapper(r).css({overflow:"hidden"}),v=c?[d.width(),d.height()]:[d.height(),d.width()],f&&(a=parseInt(f[1],10)/100*v[u?0:1]),o&&d.css(l?{height:0,width:a}:{height:a,width:0}),m[h[0]]=o?v[0]:a,g[h[1]]=o?v[1]:0,d.animate(m,p,t.easing).animate(g,p,t.easing,function(){u&&r.hide(),e.effects.restore(r,i),e.effects.removeWrapper(r),n()})}}(jQuery),function(e,t){e.effects.effect.highlight=function(t,n){var r=e(this),i=["backgroundImage","backgroundColor","opacity"],s=e.effects.setMode(r,t.mode||"show"),o={backgroundColor:r.css("backgroundColor")};s==="hide"&&(o.opacity=0),e.effects.save(r,i),r.show().css({backgroundImage:"none",backgroundColor:t.color||"#ffff99"}).animate(o,{queue:!1,duration:t.duration,easing:t.easing,complete:function(){s==="hide"&&r.hide(),e.effects.restore(r,i),n()}})}}(jQuery),function(e,t){e.effects.effect.pulsate=function(t,n){var r=e(this),i=e.effects.setMode(r,t.mode||"show"),s=i==="show",o=i==="hide",u=s||i==="hide",a=(t.times||5)*2+(u?1:0),f=t.duration/a,l=0,c=r.queue(),h=c.length,p;if(s||!r.is(":visible"))r.css("opacity",0).show(),l=1;for(p=1;p<a;p++)r.animate({opacity:l},f,t.easing),l=1-l;r.animate({opacity:l},f,t.easing),r.queue(function(){o&&r.hide(),n()}),h>1&&c.splice.apply(c,[1,0].concat(c.splice(h,a+1))),r.dequeue()}}(jQuery),function(e,t){e.effects.effect.puff=function(t,n){var r=e(this),i=e.effects.setMode(r,t.mode||"hide"),s=i==="hide",o=parseInt(t.percent,10)||150,u=o/100,a={height:r.height(),width:r.width(),outerHeight:r.outerHeight(),outerWidth:r.outerWidth()};e.extend(t,{effect:"scale",queue:!1,fade:!0,mode:i,complete:n,percent:s?o:100,from:s?a:{height:a.height*u,width:a.width*u,outerHeight:a.outerHeight*u,outerWidth:a.outerWidth*u}}),r.effect(t)},e.effects.effect.scale=function(t,n){var r=e(this),i=e.extend(!0,{},t),s=e.effects.setMode(r,t.mode||"effect"),o=parseInt(t.percent,10)||(parseInt(t.percent,10)===0?0:s==="hide"?0:100),u=t.direction||"both",a=t.origin,f={height:r.height(),width:r.width(),outerHeight:r.outerHeight(),outerWidth:r.outerWidth()},l={y:u!=="horizontal"?o/100:1,x:u!=="vertical"?o/100:1};i.effect="size",i.queue=!1,i.complete=n,s!=="effect"&&(i.origin=a||["middle","center"],i.restore=!0),i.from=t.from||(s==="show"?{height:0,width:0,outerHeight:0,outerWidth:0}:f),i.to={height:f.height*l.y,width:f.width*l.x,outerHeight:f.outerHeight*l.y,outerWidth:f.outerWidth*l.x},i.fade&&(s==="show"&&(i.from.opacity=0,i.to.opacity=1),s==="hide"&&(i.from.opacity=1,i.to.opacity=0)),r.effect(i)},e.effects.effect.size=function(t,n){var r,i,s,o=e(this),u=["position","top","bottom","left","right","width","height","overflow","opacity"],a=["position","top","bottom","left","right","overflow","opacity"],f=["width","height","overflow"],l=["fontSize"],c=["borderTopWidth","borderBottomWidth","paddingTop","paddingBottom"],h=["borderLeftWidth","borderRightWidth","paddingLeft","paddingRight"],p=e.effects.setMode(o,t.mode||"effect"),d=t.restore||p!=="effect",v=t.scale||"both",m=t.origin||["middle","center"],g=o.css("position"),y=d?u:a,b={height:0,width:0,outerHeight:0,outerWidth:0};p==="show"&&o.show(),r={height:o.height(),width:o.width(),outerHeight:o.outerHeight(),outerWidth:o.outerWidth()},t.mode==="toggle"&&p==="show"?(o.from=t.to||b,o.to=t.from||r):(o.from=t.from||(p==="show"?b:r),o.to=t.to||(p==="hide"?b:r)),s={from:{y:o.from.height/r.height,x:o.from.width/r.width},to:{y:o.to.height/r.height,x:o.to.width/r.width}};if(v==="box"||v==="both")s.from.y!==s.to.y&&(y=y.concat(c),o.from=e.effects.setTransition(o,c,s.from.y,o.from),o.to=e.effects.setTransition(o,c,s.to.y,o.to)),s.from.x!==s.to.x&&(y=y.concat(h),o.from=e.effects.setTransition(o,h,s.from.x,o.from),o.to=e.effects.setTransition(o,h,s.to.x,o.to));(v==="content"||v==="both")&&s.from.y!==s.to.y&&(y=y.concat(l).concat(f),o.from=e.effects.setTransition(o,l,s.from.y,o.from),o.to=e.effects.setTransition(o,l,s.to.y,o.to)),e.effects.save(o,y),o.show(),e.effects.createWrapper(o),o.css("overflow","hidden").css(o.from),m&&(i=e.effects.getBaseline(m,r),o.from.top=(r.outerHeight-o.outerHeight())*i.y,o.from.left=(r.outerWidth-o.outerWidth())*i.x,o.to.top=(r.outerHeight-o.to.outerHeight)*i.y,o.to.left=(r.outerWidth-o.to.outerWidth)*i.x),o.css(o.from);if(v==="content"||v==="both")c=c.concat(["marginTop","marginBottom"]).concat(l),h=h.concat(["marginLeft","marginRight"]),f=u.concat(c).concat(h),o.find("*[width]").each(function(){var n=e(this),r={height:n.height(),width:n.width(),outerHeight:n.outerHeight(),outerWidth:n.outerWidth()};d&&e.effects.save(n,f),n.from={height:r.height*s.from.y,width:r.width*s.from.x,outerHeight:r.outerHeight*s.from.y,outerWidth:r.outerWidth*s.from.x},n.to={height:r.height*s.to.y,width:r.width*s.to.x,outerHeight:r.height*s.to.y,outerWidth:r.width*s.to.x},s.from.y!==s.to.y&&(n.from=e.effects.setTransition(n,c,s.from.y,n.from),n.to=e.effects.setTransition(n,c,s.to.y,n.to)),s.from.x!==s.to.x&&(n.from=e.effects.setTransition(n,h,s.from.x,n.from),n.to=e.effects.setTransition(n,h,s.to.x,n.to)),n.css(n.from),n.animate(n.to,t.duration,t.easing,function(){d&&e.effects.restore(n,f)})});o.animate(o.to,{queue:!1,duration:t.duration,easing:t.easing,complete:function(){o.to.opacity===0&&o.css("opacity",o.from.opacity),p==="hide"&&o.hide(),e.effects.restore(o,y),d||(g==="static"?o.css({position:"relative",top:o.to.top,left:o.to.left}):e.each(["top","left"],function(e,t){o.css(t,function(t,n){var r=parseInt(n,10),i=e?o.to.left:o.to.top;return n==="auto"?i+"px":r+i+"px"})})),e.effects.removeWrapper(o),n()}})}}(jQuery),function(e,t){e.effects.effect.shake=function(t,n){var r=e(this),i=["position","top","bottom","left","right","height","width"],s=e.effects.setMode(r,t.mode||"effect"),o=t.direction||"left",u=t.distance||20,a=t.times||3,f=a*2+1,l=Math.round(t.duration/f),c=o==="up"||o==="down"?"top":"left",h=o==="up"||o==="left",p={},d={},v={},m,g=r.queue(),y=g.length;e.effects.save(r,i),r.show(),e.effects.createWrapper(r),p[c]=(h?"-=":"+=")+u,d[c]=(h?"+=":"-=")+u*2,v[c]=(h?"-=":"+=")+u*2,r.animate(p,l,t.easing);for(m=1;m<a;m++)r.animate(d,l,t.easing).animate(v,l,t.easing);r.animate(d,l,t.easing).animate(p,l/2,t.easing).queue(function(){s==="hide"&&r.hide(),e.effects.restore(r,i),e.effects.removeWrapper(r),n()}),y>1&&g.splice.apply(g,[1,0].concat(g.splice(y,f+1))),r.dequeue()}}(jQuery),function(e,t){e.effects.effect.slide=function(t,n){var r=e(this),i=["position","top","bottom","left","right","width","height"],s=e.effects.setMode(r,t.mode||"show"),o=s==="show",u=t.direction||"left",a=u==="up"||u==="down"?"top":"left",f=u==="up"||u==="left",l,c={};e.effects.save(r,i),r.show(),l=t.distance||r[a==="top"?"outerHeight":"outerWidth"](!0),e.effects.createWrapper(r).css({overflow:"hidden"}),o&&r.css(a,f?isNaN(l)?"-"+l:-l:l),c[a]=(o?f?"+=":"-=":f?"-=":"+=")+l,r.animate(c,{queue:!1,duration:t.duration,easing:t.easing,complete:function(){s==="hide"&&r.hide(),e.effects.restore(r,i),e.effects.removeWrapper(r),n()}})}}(jQuery),function(e,t){e.effects.effect.transfer=function(t,n){var r=e(this),i=e(t.to),s=i.css("position")==="fixed",o=e("body"),u=s?o.scrollTop():0,a=s?o.scrollLeft():0,f=i.offset(),l={top:f.top-u,left:f.left-a,height:i.innerHeight(),width:i.innerWidth()},c=r.offset(),h=e('<div class="ui-effects-transfer"></div>').appendTo(document.body).addClass(t.className).css({top:c.top-u,left:c.left-a,height:r.innerHeight(),width:r.innerWidth(),position:s?"fixed":"absolute"}).animate(l,t.duration,t.easing,function(){h.remove(),n()})}}(jQuery),function(e){"use strict";typeof define=="function"&&define.amd?define(["jquery"],e):e(window.jQuery)}(function(e){"use strict";var t=0;e.ajaxTransport("iframe",function(n){if(n.async){var r=n.initialIframeSrc||"javascript:false;",i,s,o;return{send:function(u,a){i=e('<form style="display:none;"></form>'),i.attr("accept-charset",n.formAcceptCharset),o=/\?/.test(n.url)?"&":"?",n.type==="DELETE"?(n.url=n.url+o+"_method=DELETE",n.type="POST"):n.type==="PUT"?(n.url=n.url+o+"_method=PUT",n.type="POST"):n.type==="PATCH"&&(n.url=n.url+o+"_method=PATCH",n.type="POST"),t+=1,s=e('<iframe src="'+r+'" name="iframe-transport-'+t+'"></iframe>').bind("load",function(){var t,o=e.isArray(n.paramName)?n.paramName:[n.paramName];s.unbind("load").bind("load",function(){var t;try{t=s.contents();if(!t.length||!t[0].firstChild)throw new Error}catch(n){t=undefined}a(200,"success",{iframe:t}),e('<iframe src="'+r+'"></iframe>').appendTo(i),window.setTimeout(function(){i.remove()},0)}),i.prop("target",s.prop("name")).prop("action",n.url).prop("method",n.type),n.formData&&e.each(n.formData,function(t,n){e('<input type="hidden"/>').prop("name",n.name).val(n.value).appendTo(i)}),n.fileInput&&n.fileInput.length&&n.type==="POST"&&(t=n.fileInput.clone(),n.fileInput.after(function(e){return t[e]}),n.paramName&&n.fileInput.each(function(t){e(this).prop("name",o[t]||n.paramName)}),i.append(n.fileInput).prop("enctype","multipart/form-data").prop("encoding","multipart/form-data"),n.fileInput.removeAttr("form")),i.submit(),t&&t.length&&n.fileInput.each(function(n,r){var i=e(t[n]);e(r).prop("name",i.prop("name")).attr("form",i.attr("form")),i.replaceWith(r)})}),i.append(s).appendTo(document.body)},abort:function(){s&&s.unbind("load").prop("src",r),i&&i.remove()}}}}),e.ajaxSetup({converters:{"iframe text":function(t){return t&&e(t[0].body).text()},"iframe json":function(t){return t&&e.parseJSON(e(t[0].body).text())},"iframe html":function(t){return t&&e(t[0].body).html()},"iframe xml":function(t){var n=t&&t[0];return n&&e.isXMLDoc(n)?n:e.parseXML(n.XMLDocument&&n.XMLDocument.xml||e(n.body).html())},"iframe script":function(t){return t&&e.globalEval(e(t[0].body).text())}}})}),function(e){"use strict";typeof define=="function"&&define.amd?define(["jquery","jquery.ui.widget"],e):e(window.jQuery)}(function(e){"use strict";e.support.fileInput=!(new RegExp("(Android (1\\.[0156]|2\\.[01]))|(Windows Phone (OS 7|8\\.0))|(XBLWP)|(ZuneWP)|(WPDesktop)|(w(eb)?OSBrowser)|(webOS)|(Kindle/(1\\.0|2\\.[05]|3\\.0))")).test(window.navigator.userAgent)&&!e('<input type="file">').prop("disabled"),e.support.xhrFileUpload=!!window.ProgressEvent&&!!window.FileReader,e.support.xhrFormDataFileUpload=!!window.FormData,e.support.blobSlice=window.Blob&&(Blob.prototype.slice||Blob.prototype.webkitSlice||Blob.prototype.mozSlice),e.widget("blueimp.fileupload",{options:{dropZone:e(document),pasteZone:e(document),fileInput:undefined,replaceFileInput:!0,paramName:undefined,singleFileUploads:!0,limitMultiFileUploads:undefined,limitMultiFileUploadSize:undefined,limitMultiFileUploadSizeOverhead:512,sequentialUploads:!1,limitConcurrentUploads:undefined,forceIframeTransport:!1,redirect:undefined,redirectParamName:undefined,postMessage:undefined,multipart:!0,maxChunkSize:undefined,uploadedBytes:undefined,recalculateProgress:!0,progressInterval:100,bitrateInterval:500,autoUpload:!0,messages:{uploadedBytes:"Uploaded bytes exceed file size"},i18n:function(t,n){return t=this.messages[t]||t.toString(),n&&e.each(n,function(e,n){t=t.replace("{"+e+"}",n)}),t},formData:function(e){return e.serializeArray()},add:function(t,n){if(t.isDefaultPrevented())return!1;(n.autoUpload||n.autoUpload!==!1&&e(this).fileupload("option","autoUpload"))&&n.process().done(function(){n.submit()})},processData:!1,contentType:!1,cache:!1},_specialOptions:["fileInput","dropZone","pasteZone","multipart","forceIframeTransport"],_blobSlice:e.support.blobSlice&&function(){var e=this.slice||this.webkitSlice||this.mozSlice;return e.apply(this,arguments)},_BitrateTimer:function(){this.timestamp=Date.now?Date.now():(new Date).getTime(),this.loaded=0,this.bitrate=0,this.getBitrate=function(e,t,n){var r=e-this.timestamp;if(!this.bitrate||!n||r>n)this.bitrate=(t-this.loaded)*(1e3/r)*8,this.loaded=t,this.timestamp=e;return this.bitrate}},_isXHRUpload:function(t){return!t.forceIframeTransport&&(!t.multipart&&e.support.xhrFileUpload||e.support.xhrFormDataFileUpload)},_getFormData:function(t){var n;return e.type(t.formData)==="function"?t.formData(t.form):e.isArray(t.formData)?t.formData:e.type(t.formData)==="object"?(n=[],e.each(t.formData,function(e,t){n.push({name:e,value:t})}),n):[]},_getTotal:function(t){var n=0;return e.each(t,function(e,t){n+=t.size||1}),n},_initProgressObject:function(t){var n={loaded:0,total:0,bitrate:0};t._progress?e.extend(t._progress,n):t._progress=n},_initResponseObject:function(e){var t;if(e._response)for(t in e._response)e._response.hasOwnProperty(t)&&delete e._response[t];else e._response={}},_onProgress:function(t,n){if(t.lengthComputable){var r=Date.now?Date.now():(new Date).getTime(),i;if(n._time&&n.progressInterval&&r-n._time<n.progressInterval&&t.loaded!==t.total)return;n._time=r,i=Math.floor(t.loaded/t.total*(n.chunkSize||n._progress.total))+(n.uploadedBytes||0),this._progress.loaded+=i-n._progress.loaded,this._progress.bitrate=this._bitrateTimer.getBitrate(r,this._progress.loaded,n.bitrateInterval),n._progress.loaded=n.loaded=i,n._progress.bitrate=n.bitrate=n._bitrateTimer.getBitrate(r,i,n.bitrateInterval),this._trigger("progress",e.Event("progress",{delegatedEvent:t}),n),this._trigger("progressall",e.Event("progressall",{delegatedEvent:t}),this._progress)}},_initProgressListener:function(t){var n=this,r=t.xhr?t.xhr():e.ajaxSettings.xhr();r.upload&&(e(r.upload).bind("progress",function(e){var r=e.originalEvent;e.lengthComputable=r.lengthComputable,e.loaded=r.loaded,e.total=r.total,n._onProgress(e,t)}),t.xhr=function(){return r})},_isInstanceOf:function(e,t){return Object.prototype.toString.call(t)==="[object "+e+"]"},_initXHRData:function(t){var n=this,r,i=t.files[0],s=t.multipart||!e.support.xhrFileUpload,o=e.type(t.paramName)==="array"?t.paramName[0]:t.paramName;t.headers=e.extend({},t.headers),t.contentRange&&(t.headers["Content-Range"]=t.contentRange);if(!s||t.blob||!this._isInstanceOf("File",i))t.headers["Content-Disposition"]='attachment; filename="'+encodeURI(i.name)+'"';s?e.support.xhrFormDataFileUpload&&(t.postMessage?(r=this._getFormData(t),t.blob?r.push({name:o,value:t.blob}):e.each(t.files,function(n,i){r.push({name:e.type(t.paramName)==="array"&&t.paramName[n]||o,value:i})})):(n._isInstanceOf("FormData",t.formData)?r=t.formData:(r=new FormData,e.each(this._getFormData(t),function(e,t){r.append(t.name,t.value)})),t.blob?r.append(o,t.blob,i.name):e.each(t.files,function(i,s){(n._isInstanceOf("File",s)||n._isInstanceOf("Blob",s))&&r.append(e.type(t.paramName)==="array"&&t.paramName[i]||o,s,s.uploadName||s.name)})),t.data=r):(t.contentType=i.type||"application/octet-stream",t.data=t.blob||i),t.blob=null},_initIframeSettings:function(t){var n=e("<a></a>").prop("href",t.url).prop("host");t.dataType="iframe "+(t.dataType||""),t.formData=this._getFormData(t),t.redirect&&n&&n!==location.host&&t.formData.push({name:t.redirectParamName||"redirect",value:t.redirect})},_initDataSettings:function(e){this._isXHRUpload(e)?(this._chunkedUpload(e,!0)||(e.data||this._initXHRData(e),this._initProgressListener(e)),e.postMessage&&(e.dataType="postmessage "+(e.dataType||""))):this._initIframeSettings(e)},_getParamName:function(t){var n=e(t.fileInput),r=t.paramName;return r?e.isArray(r)||(r=[r]):(r=[],n.each(function(){var t=e(this),n=t.prop("name")||"files[]",i=(t.prop("files")||[1]).length;while(i)r.push(n),i-=1}),r.length||(r=[n.prop("name")||"files[]"])),r},_initFormSettings:function(t){if(!t.form||!t.form.length)t.form=e(t.fileInput.prop("form")),t.form.length||(t.form=e(this.options.fileInput.prop("form")));t.paramName=this._getParamName(t),t.url||(t.url=t.form.prop("action")||location.href),t.type=(t.type||e.type(t.form.prop("method"))==="string"&&t.form.prop("method")||"").toUpperCase(),t.type!=="POST"&&t.type!=="PUT"&&t.type!=="PATCH"&&(t.type="POST"),t.formAcceptCharset||(t.formAcceptCharset=t.form.attr("accept-charset"))},_getAJAXSettings:function(t){var n=e.extend({},this.options,t);return this._initFormSettings(n),this._initDataSettings(n),n},_getDeferredState:function(e){return e.state?e.state():e.isResolved()?"resolved":e.isRejected()?"rejected":"pending"},_enhancePromise:function(e){return e.success=e.done,e.error=e.fail,e.complete=e.always,e},_getXHRPromise:function(t,n,r){var i=e.Deferred(),s=i.promise();return n=n||this.options.context||s,t===!0?i.resolveWith(n,r):t===!1&&i.rejectWith(n,r),s.abort=i.promise,this._enhancePromise(s)},_addConvenienceMethods:function(t,n){var r=this,i=function(t){return e.Deferred().resolveWith(r,t).promise()};n.process=function(t,s){if(t||s)n._processQueue=this._processQueue=(this._processQueue||i([this])).pipe(function(){return n.errorThrown?e.Deferred().rejectWith(r,[n]).promise():i(arguments)}).pipe(t,s);return this._processQueue||i([this])},n.submit=function(){return this.state()!=="pending"&&(n.jqXHR=this.jqXHR=r._trigger("submit",e.Event("submit",{delegatedEvent:t}),this)!==!1&&r._onSend(t,this)),this.jqXHR||r._getXHRPromise()},n.abort=function(){return this.jqXHR?this.jqXHR.abort():(this.errorThrown="abort",r._trigger("fail",null,this),r._getXHRPromise(!1))},n.state=function(){if(this.jqXHR)return r._getDeferredState(this.jqXHR);if(this._processQueue)return r._getDeferredState(this._processQueue)},n.processing=function(){return!this.jqXHR&&this._processQueue&&r._getDeferredState(this._processQueue)==="pending"},n.progress=function(){return this._progress},n.response=function(){return this._response}},_getUploadedBytes:function(e){var t=e.getResponseHeader("Range"),n=t&&t.split("-"),r=n&&n.length>1&&parseInt(n[1],10);return r&&r+1},_chunkedUpload:function(t,n){t.uploadedBytes=t.uploadedBytes||0;var r=this,i=t.files[0],s=i.size,o=t.uploadedBytes,u=t.maxChunkSize||s,a=this._blobSlice,f=e.Deferred(),l=f.promise
(),c,h;return!(this._isXHRUpload(t)&&a&&(o||u<s))||t.data?!1:n?!0:o>=s?(i.error=t.i18n("uploadedBytes"),this._getXHRPromise(!1,t.context,[null,"error",i.error])):(h=function(){var n=e.extend({},t),l=n._progress.loaded;n.blob=a.call(i,o,o+u,i.type),n.chunkSize=n.blob.size,n.contentRange="bytes "+o+"-"+(o+n.chunkSize-1)+"/"+s,r._initXHRData(n),r._initProgressListener(n),c=(r._trigger("chunksend",null,n)!==!1&&e.ajax(n)||r._getXHRPromise(!1,n.context)).done(function(i,u,a){o=r._getUploadedBytes(a)||o+n.chunkSize,l+n.chunkSize-n._progress.loaded&&r._onProgress(e.Event("progress",{lengthComputable:!0,loaded:o-n.uploadedBytes,total:o-n.uploadedBytes}),n),t.uploadedBytes=n.uploadedBytes=o,n.result=i,n.textStatus=u,n.jqXHR=a,r._trigger("chunkdone",null,n),r._trigger("chunkalways",null,n),o<s?h():f.resolveWith(n.context,[i,u,a])}).fail(function(e,t,i){n.jqXHR=e,n.textStatus=t,n.errorThrown=i,r._trigger("chunkfail",null,n),r._trigger("chunkalways",null,n),f.rejectWith(n.context,[e,t,i])})},this._enhancePromise(l),l.abort=function(){return c.abort()},h(),l)},_beforeSend:function(e,t){this._active===0&&(this._trigger("start"),this._bitrateTimer=new this._BitrateTimer,this._progress.loaded=this._progress.total=0,this._progress.bitrate=0),this._initResponseObject(t),this._initProgressObject(t),t._progress.loaded=t.loaded=t.uploadedBytes||0,t._progress.total=t.total=this._getTotal(t.files)||1,t._progress.bitrate=t.bitrate=0,this._active+=1,this._progress.loaded+=t.loaded,this._progress.total+=t.total},_onDone:function(t,n,r,i){var s=i._progress.total,o=i._response;i._progress.loaded<s&&this._onProgress(e.Event("progress",{lengthComputable:!0,loaded:s,total:s}),i),o.result=i.result=t,o.textStatus=i.textStatus=n,o.jqXHR=i.jqXHR=r,this._trigger("done",null,i)},_onFail:function(e,t,n,r){var i=r._response;r.recalculateProgress&&(this._progress.loaded-=r._progress.loaded,this._progress.total-=r._progress.total),i.jqXHR=r.jqXHR=e,i.textStatus=r.textStatus=t,i.errorThrown=r.errorThrown=n,this._trigger("fail",null,r)},_onAlways:function(e,t,n,r){this._trigger("always",null,r)},_onSend:function(t,n){n.submit||this._addConvenienceMethods(t,n);var r=this,i,s,o,u,a=r._getAJAXSettings(n),f=function(){return r._sending+=1,a._bitrateTimer=new r._BitrateTimer,i=i||((s||r._trigger("send",e.Event("send",{delegatedEvent:t}),a)===!1)&&r._getXHRPromise(!1,a.context,s)||r._chunkedUpload(a)||e.ajax(a)).done(function(e,t,n){r._onDone(e,t,n,a)}).fail(function(e,t,n){r._onFail(e,t,n,a)}).always(function(e,t,n){r._onAlways(e,t,n,a),r._sending-=1,r._active-=1;if(a.limitConcurrentUploads&&a.limitConcurrentUploads>r._sending){var i=r._slots.shift();while(i){if(r._getDeferredState(i)==="pending"){i.resolve();break}i=r._slots.shift()}}r._active===0&&r._trigger("stop")}),i};return this._beforeSend(t,a),this.options.sequentialUploads||this.options.limitConcurrentUploads&&this.options.limitConcurrentUploads<=this._sending?(this.options.limitConcurrentUploads>1?(o=e.Deferred(),this._slots.push(o),u=o.pipe(f)):(this._sequence=this._sequence.pipe(f,f),u=this._sequence),u.abort=function(){return s=[undefined,"abort","abort"],i?i.abort():(o&&o.rejectWith(a.context,s),f())},this._enhancePromise(u)):f()},_onAdd:function(t,n){var r=this,i=!0,s=e.extend({},this.options,n),o=n.files,u=o.length,a=s.limitMultiFileUploads,f=s.limitMultiFileUploadSize,l=s.limitMultiFileUploadSizeOverhead,c=0,h=this._getParamName(s),p,d,v,m,g=0;f&&(!u||o[0].size===undefined)&&(f=undefined);if(!(s.singleFileUploads||a||f)||!this._isXHRUpload(s))v=[o],p=[h];else if(!s.singleFileUploads&&!f&&a){v=[],p=[];for(m=0;m<u;m+=a)v.push(o.slice(m,m+a)),d=h.slice(m,m+a),d.length||(d=h),p.push(d)}else if(!s.singleFileUploads&&f){v=[],p=[];for(m=0;m<u;m+=1){c+=o[m].size+l;if(m+1===u||c+o[m+1].size+l>f||a&&m+1-g>=a)v.push(o.slice(g,m+1)),d=h.slice(g,m+1),d.length||(d=h),p.push(d),g=m+1,c=0}}else p=h;return n.originalFiles=o,e.each(v||o,function(s,o){var u=e.extend({},n);return u.files=v?o:[o],u.paramName=p[s],r._initResponseObject(u),r._initProgressObject(u),r._addConvenienceMethods(t,u),i=r._trigger("add",e.Event("add",{delegatedEvent:t}),u),i}),i},_replaceFileInput:function(t){var n=t.clone(!0);e("<form></form>").append(n)[0].reset(),t.after(n).detach(),e.cleanData(t.unbind("remove")),this.options.fileInput=this.options.fileInput.map(function(e,r){return r===t[0]?n[0]:r}),t[0]===this.element[0]&&(this.element=n)},_handleFileTreeEntry:function(t,n){var r=this,i=e.Deferred(),s=function(e){e&&!e.entry&&(e.entry=t),i.resolve([e])},o;return n=n||"",t.isFile?t._file?(t._file.relativePath=n,i.resolve(t._file)):t.file(function(e){e.relativePath=n,i.resolve(e)},s):t.isDirectory?(o=t.createReader(),o.readEntries(function(e){r._handleFileTreeEntries(e,n+t.name+"/").done(function(e){i.resolve(e)}).fail(s)},s)):i.resolve([]),i.promise()},_handleFileTreeEntries:function(t,n){var r=this;return e.when.apply(e,e.map(t,function(e){return r._handleFileTreeEntry(e,n)})).pipe(function(){return Array.prototype.concat.apply([],arguments)})},_getDroppedFiles:function(t){t=t||{};var n=t.items;return n&&n.length&&(n[0].webkitGetAsEntry||n[0].getAsEntry)?this._handleFileTreeEntries(e.map(n,function(e){var t;return e.webkitGetAsEntry?(t=e.webkitGetAsEntry(),t&&(t._file=e.getAsFile()),t):e.getAsEntry()})):e.Deferred().resolve(e.makeArray(t.files)).promise()},_getSingleFileInputFiles:function(t){t=e(t);var n=t.prop("webkitEntries")||t.prop("entries"),r,i;if(n&&n.length)return this._handleFileTreeEntries(n);r=e.makeArray(t.prop("files"));if(!r.length){i=t.prop("value");if(!i)return e.Deferred().resolve([]).promise();r=[{name:i.replace(/^.*\\/,"")}]}else r[0].name===undefined&&r[0].fileName&&e.each(r,function(e,t){t.name=t.fileName,t.size=t.fileSize});return e.Deferred().resolve(r).promise()},_getFileInputFiles:function(t){return t instanceof e&&t.length!==1?e.when.apply(e,e.map(t,this._getSingleFileInputFiles)).pipe(function(){return Array.prototype.concat.apply([],arguments)}):this._getSingleFileInputFiles(t)},_onChange:function(t){var n=this,r={fileInput:e(t.target),form:e(t.target.form)};this._getFileInputFiles(r.fileInput).always(function(i){r.files=i,n.options.replaceFileInput&&n._replaceFileInput(r.fileInput),n._trigger("change",e.Event("change",{delegatedEvent:t}),r)!==!1&&n._onAdd(t,r)})},_onPaste:function(t){var n=t.originalEvent&&t.originalEvent.clipboardData&&t.originalEvent.clipboardData.items,r={files:[]};n&&n.length&&(e.each(n,function(e,t){var n=t.getAsFile&&t.getAsFile();n&&r.files.push(n)}),this._trigger("paste",e.Event("paste",{delegatedEvent:t}),r)!==!1&&this._onAdd(t,r))},_onDrop:function(t){t.dataTransfer=t.originalEvent&&t.originalEvent.dataTransfer;var n=this,r=t.dataTransfer,i={};r&&r.files&&r.files.length&&(t.preventDefault(),this._getDroppedFiles(r).always(function(r){i.files=r,n._trigger("drop",e.Event("drop",{delegatedEvent:t}),i)!==!1&&n._onAdd(t,i)}))},_onDragOver:function(t){t.dataTransfer=t.originalEvent&&t.originalEvent.dataTransfer;var n=t.dataTransfer;n&&e.inArray("Files",n.types)!==-1&&this._trigger("dragover",e.Event("dragover",{delegatedEvent:t}))!==!1&&(t.preventDefault(),n.dropEffect="copy")},_initEventHandlers:function(){this._isXHRUpload(this.options)&&(this._on(this.options.dropZone,{dragover:this._onDragOver,drop:this._onDrop}),this._on(this.options.pasteZone,{paste:this._onPaste})),e.support.fileInput&&this._on(this.options.fileInput,{change:this._onChange})},_destroyEventHandlers:function(){this._off(this.options.dropZone,"dragover drop"),this._off(this.options.pasteZone,"paste"),this._off(this.options.fileInput,"change")},_setOption:function(t,n){var r=e.inArray(t,this._specialOptions)!==-1;r&&this._destroyEventHandlers(),this._super(t,n),r&&(this._initSpecialOptions(),this._initEventHandlers())},_initSpecialOptions:function(){var t=this.options;t.fileInput===undefined?t.fileInput=this.element.is('input[type="file"]')?this.element:this.element.find('input[type="file"]'):t.fileInput instanceof e||(t.fileInput=e(t.fileInput)),t.dropZone instanceof e||(t.dropZone=e(t.dropZone)),t.pasteZone instanceof e||(t.pasteZone=e(t.pasteZone))},_getRegExp:function(e){var t=e.split("/"),n=t.pop();return t.shift(),new RegExp(t.join("/"),n)},_isRegExpOption:function(t,n){return t!=="url"&&e.type(n)==="string"&&/^\/.*\/[igm]{0,3}$/.test(n)},_initDataAttributes:function(){var t=this,n=this.options,r=e(this.element[0].cloneNode(!1));e.each(r.data(),function(e,i){var s="data-"+e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase();r.attr(s)&&(t._isRegExpOption(e,i)&&(i=t._getRegExp(i)),n[e]=i)})},_create:function(){this._initDataAttributes(),this._initSpecialOptions(),this._slots=[],this._sequence=this._getXHRPromise(!0),this._sending=this._active=0,this._initProgressObject(this),this._initEventHandlers()},active:function(){return this._active},progress:function(){return this._progress},add:function(t){var n=this;if(!t||this.options.disabled)return;t.fileInput&&!t.files?this._getFileInputFiles(t.fileInput).always(function(e){t.files=e,n._onAdd(null,t)}):(t.files=e.makeArray(t.files),this._onAdd(null,t))},send:function(t){if(t&&!this.options.disabled){if(t.fileInput&&!t.files){var n=this,r=e.Deferred(),i=r.promise(),s,o;return i.abort=function(){return o=!0,s?s.abort():(r.reject(null,"abort","abort"),i)},this._getFileInputFiles(t.fileInput).always(function(e){if(o)return;if(!e.length){r.reject();return}t.files=e,s=n._onSend(null,t).then(function(e,t,n){r.resolve(e,t,n)},function(e,t,n){r.reject(e,t,n)})}),this._enhancePromise(i)}t.files=e.makeArray(t.files);if(t.files.length)return this._onSend(null,t)}return this._getXHRPromise(!1,t&&t.context)}})}),!function(e){"use strict";var t=function(t,n){this.options=n,this.$element=e(t).delegate('[data-dismiss="modal"]',"click.dismiss.modal",e.proxy(this.hide,this)),this.options.remote&&this.$element.find(".modal-body").load(this.options.remote)};t.prototype={constructor:t,toggle:function(){return this[this.isShown?"hide":"show"]()},show:function(){var t=this,n=e.Event("show");this.$element.trigger(n);if(this.isShown||n.isDefaultPrevented())return;this.isShown=!0,this.escape(),this.backdrop(function(){var n=e.support.transition&&t.$element.hasClass("fade");t.$element.parent().length||t.$element.appendTo(document.body),t.$element.show(),n&&t.$element[0].offsetWidth,t.$element.addClass("in").attr("aria-hidden",!1),t.enforceFocus(),n?t.$element.one(e.support.transition.end,function(){t.$element.focus().trigger("shown")}):t.$element.focus().trigger("shown")})},hide:function(t){t&&t.preventDefault();var n=this;t=e.Event("hide"),this.$element.trigger(t);if(!this.isShown||t.isDefaultPrevented())return;this.isShown=!1,this.escape(),e(document).off("focusin.modal"),this.$element.removeClass("in").attr("aria-hidden",!0),e.support.transition&&this.$element.hasClass("fade")?this.hideWithTransition():this.hideModal()},enforceFocus:function(){var t=this;e(document).on("focusin.modal",function(e){t.$element[0]!==e.target&&!t.$element.has(e.target).length&&t.$element.focus()})},escape:function(){var e=this;this.isShown&&this.options.keyboard?this.$element.on("keyup.dismiss.modal",function(t){t.which==27&&e.hide()}):this.isShown||this.$element.off("keyup.dismiss.modal")},hideWithTransition:function(){var t=this,n=setTimeout(function(){t.$element.off(e.support.transition.end),t.hideModal()},500);this.$element.one(e.support.transition.end,function(){clearTimeout(n),t.hideModal()})},hideModal:function(){var e=this;this.$element.hide(),this.backdrop(function(){e.removeBackdrop(),e.$element.trigger("hidden")})},removeBackdrop:function(){this.$backdrop&&this.$backdrop.remove(),this.$backdrop=null},backdrop:function(t){var n=this,r=this.$element.hasClass("fade")?"fade":"";if(this.isShown&&this.options.backdrop){var i=e.support.transition&&r;this.$backdrop=e('<div class="modal-backdrop '+r+'" />').appendTo(document.body),this.$backdrop.click(this.options.backdrop=="static"?e.proxy(this.$element[0].focus,this.$element[0]):e.proxy(this.hide,this)),i&&this.$backdrop[0].offsetWidth,this.$backdrop.addClass("in");if(!t)return;i?this.$backdrop.one(e.support.transition.end,t):t()}else!this.isShown&&this.$backdrop?(this.$backdrop.removeClass("in"),e.support.transition&&this.$element.hasClass("fade")?this.$backdrop.one(e.support.transition.end,t):t()):t&&t()}};var n=e.fn.modal;e.fn.modal=function(n){return this.each(function(){var r=e(this),i=r.data("modal"),s=e.extend({},e.fn.modal.defaults,r.data(),typeof n=="object"&&n);i||r.data("modal",i=new t(this,s)),typeof n=="string"?i[n]():s.show&&i.show()})},e.fn.modal.defaults={backdrop:!0,keyboard:!0,show:!0},e.fn.modal.Constructor=t,e.fn.modal.noConflict=function(){return e.fn.modal=n,this},e(document).on("click.modal.data-api",'[data-toggle="modal"]',function(t){var n=e(this),r=n.attr("href"),i=e(n.attr("data-target")||r&&r.replace(/.*(?=#[^\s]+$)/,"")),s=i.data("modal")?"toggle":e.extend({remote:!/#/.test(r)&&r},i.data(),n.data());t.preventDefault(),i.modal(s).one("hide",function(){n.focus()})})}(window.jQuery),!function(e){"use strict";function r(){e(".dropdown-backdrop").remove(),e(t).each(function(){i(e(this)).removeClass("open")})}function i(t){var n=t.attr("data-target"),r;n||(n=t.attr("href"),n=n&&/#/.test(n)&&n.replace(/.*(?=#[^\s]*$)/,"")),r=n&&e(n);if(!r||!r.length)r=t.parent();return r}var t="[data-toggle=dropdown]",n=function(t){var n=e(t).on("click.dropdown.data-api",this.toggle);e("html").on("click.dropdown.data-api",function(){n.parent().removeClass("open")})};n.prototype={constructor:n,toggle:function(t){var n=e(this),s,o;if(n.is(".disabled, :disabled"))return;return s=i(n),o=s.hasClass("open"),r(),o||("ontouchstart"in document.documentElement&&e('<div class="dropdown-backdrop"/>').insertBefore(e(this)).on("click",r),s.toggleClass("open")),n.focus(),!1},keydown:function(n){var r,s,o,u,a,f;if(!/(38|40|27)/.test(n.keyCode))return;r=e(this),n.preventDefault(),n.stopPropagation();if(r.is(".disabled, :disabled"))return;u=i(r),a=u.hasClass("open");if(!a||a&&n.keyCode==27)return n.which==27&&u.find(t).focus(),r.click();s=e("[role=menu] li:not(.divider):visible a",u);if(!s.length)return;f=s.index(s.filter(":focus")),n.keyCode==38&&f>0&&f--,n.keyCode==40&&f<s.length-1&&f++,~f||(f=0),s.eq(f).focus()}};var s=e.fn.dropdown;e.fn.dropdown=function(t){return this.each(function(){var r=e(this),i=r.data("dropdown");i||r.data("dropdown",i=new n(this)),typeof t=="string"&&i[t].call(r)})},e.fn.dropdown.Constructor=n,e.fn.dropdown.noConflict=function(){return e.fn.dropdown=s,this},e(document).on("click.dropdown.data-api",r).on("click.dropdown.data-api",".dropdown form",function(e){e.stopPropagation()}).on("click.dropdown.data-api",t,n.prototype.toggle).on("keydown.dropdown.data-api",t+", [role=menu]",n.prototype.keydown)}(window.jQuery),!function(e){"use strict";var t=function(t){this.element=e(t)};t.prototype={constructor:t,show:function(){var t=this.element,n=t.closest("ul:not(.dropdown-menu)"),r=t.attr("data-target"),i,s,o;r||(r=t.attr("href"),r=r&&r.replace(/.*(?=#[^\s]*$)/,""));if(t.parent("li").hasClass("active"))return;i=n.find(".active:last a")[0],o=e.Event("show",{relatedTarget:i}),t.trigger(o);if(o.isDefaultPrevented())return;s=e(r),this.activate(t.parent("li"),n),this.activate(s,s.parent(),function(){t.trigger({type:"shown",relatedTarget:i})})},activate:function(t,n,r){function o(){i.removeClass("active").find("> .dropdown-menu > .active").removeClass("active"),t.addClass("active"),s?(t[0].offsetWidth,t.addClass("in")):t.removeClass("fade"),t.parent(".dropdown-menu")&&t.closest("li.dropdown").addClass("active"),r&&r()}var i=n.find("> .active"),s=r&&e.support.transition&&i.hasClass("fade");s?i.one(e.support.transition.end,o):o(),i.removeClass("in")}};var n=e.fn.tab;e.fn.tab=function(n){return this.each(function(){var r=e(this),i=r.data("tab");i||r.data("tab",i=new t(this)),typeof n=="string"&&i[n]()})},e.fn.tab.Constructor=t,e.fn.tab.noConflict=function(){return e.fn.tab=n,this},e(document).on("click.tab.data-api",'[data-toggle="tab"], [data-toggle="pill"]',function(t){t.preventDefault(),e(this).tab("show")})}(window.jQuery),!function(e){"use strict";var t=function(t,n){this.$element=e(t),this.options=e.extend({},e.fn.button.defaults,n)};t.prototype.setState=function(e){var t="disabled",n=this.$element,r=n.data(),i=n.is("input")?"val":"html";e+="Text",r.resetText||n.data("resetText",n[i]()),n[i](r[e]||this.options[e]),setTimeout(function(){e=="loadingText"?n.addClass(t).attr(t,t):n.removeClass(t).removeAttr(t)},0)},t.prototype.toggle=function(){var e=this.$element.closest('[data-toggle="buttons-radio"]');e&&e.find(".active").removeClass("active"),this.$element.toggleClass("active")};var n=e.fn.button;e.fn.button=function(n){return this.each(function(){var r=e(this),i=r.data("button"),s=typeof n=="object"&&n;i||r.data("button",i=new t(this,s)),n=="toggle"?i.toggle():n&&i.setState(n)})},e.fn.button.defaults={loadingText:"loading..."},e.fn.button.Constructor=t,e.fn.button.noConflict=function(){return e.fn.button=n,this},e(document).on("click.button.data-api","[data-toggle^=button]",function(t){var n=e(t.target);n.hasClass("btn")||(n=n.closest(".btn")),n.button("toggle")})}(window.jQuery),!function(e){"use strict";var t=function(e,t){this.init("tooltip",e,t)};t.prototype={constructor:t,init:function(t,n,r){var i,s,o,u,a;this.type=t,this.$element=e(n),this.options=this.getOptions(r),this.enabled=!0,o=this.options.trigger.split(" ");for(a=o.length;a--;)u=o[a],u=="click"?this.$element.on("click."+this.type,this.options.selector,e.proxy(this.toggle,this)):u!="manual"&&(i=u=="hover"?"mouseenter":"focus",s=u=="hover"?"mouseleave":"blur",this.$element.on(i+"."+this.type,this.options.selector,e.proxy(this.enter,this)),this.$element.on(s+"."+this.type,this.options.selector,e.proxy(this.leave,this)));this.options.selector?this._options=e.extend({},this.options,{trigger:"manual",selector:""}):this.fixTitle()},getOptions:function(t){return t=e.extend({},e.fn[this.type].defaults,this.$element.data(),t),t.delay&&typeof t.delay=="number"&&(t.delay={show:t.delay,hide:t.delay}),t},enter:function(t){var n=e.fn[this.type].defaults,r={},i;this._options&&e.each(this._options,function(e,t){n[e]!=t&&(r[e]=t)},this),i=e(t.currentTarget)[this.type](r).data(this.type);if(!i.options.delay||!i.options.delay.show)return i.show();clearTimeout(this.timeout),i.hoverState="in",this.timeout=setTimeout(function(){i.hoverState=="in"&&i.show()},i.options.delay.show)},leave:function(t){var n=e(t.currentTarget)[this.type](this._options).data(this.type);this.timeout&&clearTimeout(this.timeout);if(!n.options.delay||!n.options.delay.hide)return n.hide();n.hoverState="out",this.timeout=setTimeout(function(){n.hoverState=="out"&&n.hide()},n.options.delay.hide)},show:function(){var t,n,r,i,s,o,u=e.Event("show");if(this.hasContent()&&this.enabled){this.$element.trigger(u);if(u.isDefaultPrevented())return;t=this.tip(),this.setContent(),this.options.animation&&t.addClass("fade"),s=typeof this.options.placement=="function"?this.options.placement.call(this,t[0],this.$element[0]):this.options.placement,t.detach().css({top:0,left:0,display:"block"}),this.options.container?t.appendTo(this.options.container):t.insertAfter(this.$element),n=this.getPosition(),r=t[0].offsetWidth,i=t[0].offsetHeight;switch(s){case"bottom":o={top:n.top+n.height,left:n.left+n.width/2-r/2};break;case"top":o={top:n.top-i,left:n.left+n.width/2-r/2};break;case"left":o={top:n.top+n.height/2-i/2,left:n.left-r};break;case"right":o={top:n.top+n.height/2-i/2,left:n.left+n.width}}this.applyPlacement(o,s),this.$element.trigger("shown")}},applyPlacement:function(e,t){var n=this.tip(),r=n[0].offsetWidth,i=n[0].offsetHeight,s,o,u,a;n.offset(e).addClass(t).addClass("in"),s=n[0].offsetWidth,o=n[0].offsetHeight,t=="top"&&o!=i&&(e.top=e.top+i-o,a=!0),t=="bottom"||t=="top"?(u=0,e.left<0&&(u=e.left*-2,e.left=0,n.offset(e),s=n[0].offsetWidth,o=n[0].offsetHeight),this.replaceArrow(u-r+s,s,"left")):this.replaceArrow(o-i,o,"top"),a&&n.offset(e)},replaceArrow:function(e,t,n){this.arrow().css(n,e?50*(1-e/t)+"%":"")},setContent:function(){var e=this.tip(),t=this.getTitle();e.find(".tooltip-inner")[this.options.html?"html":"text"](t),e.removeClass("fade in top bottom left right")},hide:function(){function i(){var t=setTimeout(function(){n.off(e.support.transition.end).detach()},500);n.one(e.support.transition.end,function(){clearTimeout(t),n.detach()})}var t=this,n=this.tip(),r=e.Event("hide");this.$element.trigger(r);if(r.isDefaultPrevented())return;return n.removeClass("in"),e.support.transition&&this.$tip.hasClass("fade")?i():n.detach(),this.$element.trigger("hidden"),this},fixTitle:function(){var e=this.$element;(e.attr("title")||typeof e.attr("data-original-title")!="string")&&e.attr("data-original-title",e.attr("title")||"").attr("title","")},hasContent:function(){return this.getTitle()},getPosition:function(){var t=this.$element[0];return e.extend({},typeof t.getBoundingClientRect=="function"?t.getBoundingClientRect():{width:t.offsetWidth,height:t.offsetHeight},this.$element.offset())},getTitle:function(){var e,t=this.$element,n=this.options;return e=t.attr("data-original-title")||(typeof n.title=="function"?n.title.call(t[0]):n.title),e},tip:function(){return this.$tip=this.$tip||e(this.options.template)},arrow:function(){return this.$arrow=this.$arrow||this.tip().find(".tooltip-arrow")},validate:function(){this.$element[0].parentNode||(this.hide(),this.$element=null,this.options=null)},enable:function(){this.enabled=!0},disable:function(){this.enabled=!1},toggleEnabled:function(){this.enabled=!this.enabled},toggle:function(t){var n=t?e(t.currentTarget)[this.type](this._options).data(this.type):this;n.tip().hasClass("in")?n.hide():n.show()},destroy:function(){this.hide().$element.off("."+this.type).removeData(this.type)}};var n=e.fn.tooltip;e.fn.tooltip=function(n){return this.each(function(){var r=e(this),i=r.data("tooltip"),s=typeof n=="object"&&n;i||r.data("tooltip",i=new t(this,s)),typeof n=="string"&&i[n]()})},e.fn.tooltip.Constructor=t,e.fn.tooltip.defaults={animation:!0,placement:"top",selector:!1,template:'<div class="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,container:!1},e.fn.tooltip.noConflict=function(){return e.fn.tooltip=n,this}}(window.jQuery),!function(e){"use strict";var t=function(e,t){this.init("popover",e,t)};t.prototype=e.extend({},e.fn.tooltip.Constructor.prototype,{constructor:t,setContent:function(){var e=this.tip(),t=this.getTitle(),n=this.getContent();e.find(".popover-title")[this.options.html?"html":"text"](t),e.find(".popover-content")[this.options.html?"html":"text"](n),e.removeClass("fade top bottom left right in")},hasContent:function(){return this.getTitle()||this.getContent()},getContent:function(){var e,t=this.$element,n=this.options;return e=(typeof n.content=="function"?n.content.call(t[0]):n.content)||t.attr("data-content"),e},tip:function(){return this.$tip||(this.$tip=e(this.options.template)),this.$tip},destroy:function(){this.hide().$element.off("."+this.type).removeData(this.type)}});var n=e.fn.popover;e.fn.popover=function(n){return this.each(function(){var r=e(this),i=r.data("popover"),s=typeof n=="object"&&n;i||r.data("popover",i=new t(this,s)),typeof n=="string"&&i[n]()})},e.fn.popover.Constructor=t,e.fn.popover.defaults=e.extend({},e.fn.tooltip.defaults,{placement:"right",trigger:"click",content:"",template:'<div class="popover"><div class="arrow"></div><h3 class="popover-title"></h3><div class="popover-content"></div></div>'}),e.fn.popover.noConflict=function(){return e.fn.popover=n,this}}(window.jQuery),!function(e){"use strict";var t=function(e,t){this.cinit("clickover",e,t)};t.prototype=e.extend({},e.fn.popover.Constructor.prototype,{constructor:t,cinit:function(t,n,r){this.attr={},this.attr.me=(Math.random()*10+"").replace(/\D/g,""),this.attr.click_event_ns="click."+this.attr.me,r||(r={}),r.trigger="manual",this.init(t,n,r),this.$element.on("click",this.options.selector,e.proxy(this.clickery,this))},clickery:function(t){t&&(t.preventDefault(),t.stopPropagation()),this.options.width&&this.tip().find(".popover-inner").width(this.options.width),this.options.height&&this.tip().find(".popover-inner").height(this.options.height),this.options.tip_id&&this.tip().attr("id",this.options.tip_id),this.options.class_name&&this.tip().addClass(this.options.class_name),this.toggle();if(this.isShown()){var n=this;this.options.global_close&&e("body").on(this.attr.click_event_ns,function(e){n.tip().has(e.target).length||n.clickery()}),this.options.esc_close&&e(document).bind("keyup.clickery",function(e){e.keyCode==27&&n.clickery();return}),!this.options.allow_multiple&&e("[data-clickover-open=1]").each(function(){e(this).data("clickover")&&e(this).data("clickover").clickery()}),this.$element.attr("data-clickover-open",1),this.tip().on("click",'[data-dismiss="clickover"]',e.proxy(this.clickery,this)),this.options.auto_close&&this.options.auto_close>0&&(this.attr.tid=setTimeout(e.proxy(this.clickery,this),this.options.auto_close)),typeof this.options.onShown=="function"&&this.options.onShown.call(this),this.$element.trigger("shown")}else this.$element.removeAttr("data-clickover-open"),this.options.esc_close&&e(document).unbind("keyup.clickery"),this.tip().off("click",'[data-dismiss="clickover"]',e.proxy(this.clickery,this)),e("body").off(this.attr.click_event_ns),typeof this.attr.tid=="number"&&(clearTimeout(this.attr.tid),delete this.attr.tid),typeof this.options.onHidden=="function"&&this.options.onHidden.call(this),this.$element.trigger("hidden")},isShown:function(){return this.tip().hasClass("in")},resetPosition:function(){var e,t,n,r,i,s,o;if(this.hasContent()&&this.enabled){e=this.tip(),s=typeof this.options.placement=="function"?this.options.placement.call(this,e[0],this.$element[0]):this.options.placement,t=/in/.test(s),n=this.getPosition(t),r=e[0].offsetWidth,i=e[0].offsetHeight;switch(t?s.split(" ")[1]:s){case"bottom":o={top:n.top+n.height,left:n.left+n.width/2-r/2};break;case"top":o={top:n.top-i,left:n.left+n.width/2-r/2};break;case"left":o={top:n.top+n.height/2-i/2,left:n.left-r};break;case"right":o={top:n.top+n.height/2-i/2,left:n.left+n.width}}e.css(o)}},debughide:function(){var e=(new Date).toString();console.log(e+": clickover hide"),this.hide()}}),e.fn.clickover=function(n){return this.each(function(){var r=e(this),i=r.data("clickover"),s=typeof n=="object"&&n;i||r.data("clickover",i=new t(this,s)),typeof n=="string"&&i[n]()})},e.fn.clickover.Constructor=t,e.fn.clickover.defaults=e.extend({},e.fn.popover.defaults,{trigger:"manual",auto_close:0,global_close:1,esc_close:1,onShown:null,onHidden:null,width:null,height:null,tip_id:null,class_name:"clickover",allow_multiple:0})}(window.jQuery),function(){window.GoldenData||(window.GoldenData={}),GoldenData.recalcFormHeight=function(){if(!($.browser.msie&&$.browser.version<7))return $(window).trigger("customLoad")},GoldenData.numberToHumanSize=function(e){var t,n,r;return e?(n=e/1024,n<1024?""+n.toFixed(0)+" KB":(r=n/1024,r<1024?""+r.toFixed(1)+" MB":(t=r/1024,""+t.toFixed(1)+" GB"))):null},GoldenData.updateSubmitBtn=function(){var e,t,n,r,i;return t=$(".submit-field input.submit"),n=(r=t.data("fileUploading"))!=null?r:0,n<0&&t.data("fileUploading",0),e=(i=n>0||t.data("no-goods-selected"))!=null?i:!1,t.attr("disabled",e)},$(document).on("page:load ready",function(){var e;return typeof (e=$("input, textarea")).placeholder=="function"&&e.placeholder(),$("#login_modal .invitation").on("click",function(){return $("#login_modal .alert").hide(),$(this).parents("section").addClass("hide"),$("."+$(this).data("for")).removeClass("hide")}),$("form#new_entry input").on("keypress",function(e){if(e.keyCode===13)return e.preventDefault()}),$(document).on("click",".captcha-container img",function(){return $(this).attr("src","/captcha?action=captcha&i="+(new Date).getTime())}),$(document).on("click",".submit-field .submit",function(e){var t;t=$(this).parents("form"),t.find("input:text").each(function(){return $(this).val($.trim($(this).val()))});if($(".submit-field .captcha-container").length>0)return e.preventDefault(),$.get("/captcha/verify?captcha="+$("#captcha").val(),function(e){return e==="false"?($("#captcha").addClass("error"),$(".captcha-container .error-message").show()):t.submit()})}),$(document).on("click",".user-info .logout-link",function(e){return e.preventDefault(),$.ajax({url:$(this).attr("href"),complete:function(e){if(e.status===200)return window.location.reload()}})}),$(".field_content textarea:visible").autosize().blur()})}.call(this),_.templateSettings={interpolate:/\{\{(.+?)\}\}/g},String.toLocaleString({en:{"%rechoose":"rechoose","%delete":"delete","%warn_system_file":"system file is not allowed","%warn_exec_file":"EXE/BAT format file is not allowed","%file_name_too_long":"file name is too long","%warn_oversize":"file size is over {{maxSize}}","%warn_wrong_mobile_number":"please enter correct mobile number of mainland China","%total":"total","%download":"download","%sold_out":"sold out","%common_separator":", ","%resend":"resend SMS","%sendingSMS":"sending SMS","%warn_not_get_location":"Could not locate your position!","%geo_coord":"Longitude:{{long}},Latitude:{{lat}}","%geo_address":"{{address}}","%geo_choose":"Choose location","%geo_locate":"Get current location","%geo_locating":"Locating...","%geo_no_address":"No address information. ","%select_prompt":"Please select","%no_geo_data":"There is no data for this field currently.","%bracket":" ({{content}})","%upload_failed":"failed {{status}}, please contact system admin","%uploading":"Uploading...","%upload_done":"Uploading finished!"},"zh-CN":{"%rechoose":"重新选择","%delete":"刪除","%warn_system_file":"禁止上传系统文件","%warn_exec_file":"禁止上传EXE、BAT格式的文件","%file_name_too_long":"文件名过长","%warn_oversize":"文件超过了{{maxSize}}","%warn_wrong_mobile_number":"请输入正确的中国大陆手机号码","%total":"合计","%download":"下载","%sold_out":"已售空","%common_separator":"、","%resend":"重新发送","%sendingSMS":"正在发送短信","%warn_not_get_location":"无法定位您的位置！请检查您的网络状况，以及浏览器的位置共享权限。","%geo_coord":"经度:{{long}}，纬度:{{lat}}","%geo_address":"{{address}}","%geo_choose":"获取地理位置","%geo_locate":"获取当前位置","%geo_locating":"获取位置中...","%geo_no_address":"无法获取地址。","%select_prompt":"请选择","%no_geo_data":"该地理位置字段暂无数据。","%bracket":"（{{content}}）","%upload_failed":"上传失败{{status}}，请联系管理员","%uploading":"上传中...","%upload_done":"上传成功！"}});var l=function(e,t){return t==null&&(t={}),_.template(e.toLocaleString(),t)};String.locale="zh-CN",function(){$(function(){return GoldenData.DatePicker=function(){function e(e){$(e).find(".day_of_week").click(function(){return $(this).prev(".input_date").focus().select()}),$(e).find(".input_date").datepicker({language:String.locale||"zh-CN",format:"yyyy-mm-dd",autoclose:!0})}return e}(),GoldenData.initDatePicker=function(e){var t,n,r,i,s;i=$(e).find("[data-role=date]"),s=[];for(n=0,r=i.length;n<r;n++)t=i[n],$(t).attr("type")==="text"||!Modernizr.inputtypes.date?s.push(new GoldenData.DatePicker($(t).parents(".controls"))):s.push($(t).next(".day_of_week").remove());return s},$(document).on("ready page:load ajax:complete",function(){return GoldenData.initDatePicker(document)})})}.call(this),function(){window.GoldenData||(window.GoldenData={}),$(function(){return GoldenData.AddressSelector=function(){function e(e){var t;this.data=e,this.provinces=function(){var e;e=[];for(t in this.data)e.push(t);return e}.call(this)}return e.prototype.render=function(e,t,n,r){var i,s,o,u;n==null&&(n=""),r==null&&(r=""),e.html("<option value=''>- 省/自治区/直辖市 -</option>"),u=this.provinces;for(s=0,o=u.length;s<o;s++)i=u[s],e.append("<option value='"+i+"'>"+i+"</option>");return e.val(n),$.mobile&&e.selectmenu("refresh",!0),this.renderCityOptions(n,t,r)},e.prototype.renderCityOptions=function(e,t,n){var r,i,s,o;n==null&&(n=""),t.html("<option value=''>- 市 -</option>");if(this.data.hasOwnProperty(e)){o=this.data[e];for(i=0,s=o.length;i<s;i++)r=o[i],t.append("<option value='"+r+"'>"+r+"</option>")}t.val(n);if($.mobile)return t.selectmenu("refresh",!0)},e}(),GoldenData.initAddressSelector=function(e){var t,n,r,i,s,o,u;o=$(e).find("[data-role=address]"),u=[];for(i=0,s=o.length;i<s;i++)t=o[i],r=$(t).find("[data-role=province]"),n=$(t).find("[data-role=city]"),GoldenData.addressSelector.render(r,n,r.data("value"),n.data("value")),u.push(r.on("change",function(){return n=$(this).parents("[data-role=address]").find("[data-role=city]"),GoldenData.addressSelector.renderCityOptions($(this).val(),n)}));return u},$(document).on("ready page:load ajax:complete",function(){return GoldenData.initAddressSelector(document)})})}.call(this),function(){var e,t;t=!0,e=function(e,t,n){return $(".rating-group i").removeClass("icon-heart-light icon-star-light"
),$(".rating-group[data-field-id="+e+"] i").removeClass("fa fa-"+t+" fa-"+t+"-o"),n===""?$(".rating-group[data-field-id="+e+"] i").addClass("fa fa-"+t+"-o"):($(".rating-group[data-field-id="+e+"] i:lt("+n+")").addClass("fa fa-"+t),$(".rating-group[data-field-id="+e+"] i:gt("+(n-1)+")").addClass("fa fa-"+t+"-o"))},$(document).on("click",".rating-group i",function(){var n,r,i;return t=!1,n=$(this).parents(".rating-group").data("field-id"),r=$(this).parents(".rating-group").data("rating-type"),i=$(this).data("value"),$("#entry_"+n).val($(this).data("value")),e(n,r,i)}),$(document).on("hover",".rating-group i",function(){var e,n,r;if(t&&!$.browser.msie)return e=$(this).parents(".rating-group").data("field-id"),n=$(this).parents(".rating-group").data("rating-type"),r=$(this).data("value"),$(".rating-group[data-field-id="+e+"] i:lt("+r+")").removeClass("fa fa-"+n+"-o"),$(".rating-group[data-field-id="+e+"] i:lt("+r+")").addClass("fa fa-"+n+" icon-"+n+"-light"),$(".rating-group[data-field-id="+e+"] i:gt("+(r-1)+")").addClass("fa fa-"+n+"-o"),$(".rating-group[data-field-id="+e+"] i:gt("+(r-1)+")").removeClass("icon-"+n+"-light")}),$(document).on("mouseleave",".rating-group",function(){var n;return n=$("#entry_"+$(this).data("field-id")).val(),e($(this).data("field-id"),$(this).data("rating-type"),n),t=!0})}.call(this),function(){$(function(){var e;return e=function(e,t){var n;return n=$(e).parents(".controls").find("input.other-choice"),$(e).find(".other-choice-item").is(":selected")?(n.show(),n.removeClass("hide"),t&&n.focus(),n.parents(".ui-input-text").show()):(n.hide(),n.addClass("hide"),n.parents(".ui-input-text").hide())},GoldenData.initialDropdownOtherChoice=function(t,n){return $(t).each(function(){return e(this,n)})},$(document).on("ready page:load",function(){var t,n=this;return t=function(e,t){return $(t).parents(".other-choice-area").find("input."+e)},$(document).on("click",".other-choice-input",function(){var e;e=t("other_choice",this);if(!e.is(":checked")){e.click();if(e.checkboxradio!=null)return $("input[type=radio], input[type=checkbox]").checkboxradio("refresh")}}),$(document).on("click","input.other_choice",function(){$(this).checkboxradio!=null&&$(this).checkboxradio("refresh");if($(this).is(":checked"))return t("other-choice-input",this).focus()}),GoldenData.initialDropdownOtherChoice(".controls select.with-other-choice",!1),$(document).on("change","form .field .controls select.with-other-choice",function(){return e($(this),!0)}),$(".entry form, #new_entry").on("submit",function(){return $(this).find("input.other_choice").each(function(){if(!$(this).is(":checked"))return t("other-choice-input",this).val("")}),$(this).find("option.other-choice-item").each(function(){if(!$(this).is(":selected"))return $(this).parents(".controls").find("input.other-choice").val("")})})})})}.call(this),function(){$(function(){return $(".image-attachment-preview").livequery(function(){return $(this).one("load",function(){return $(this).prev(".preview-loading").hide(),$(this).css("visibility","visible"),$(this).next("a").show()}).each(function(){if(this.complete)return $(this).load()})})})}.call(this),function(){window.GoldenData||(window.GoldenData={}),GoldenData.validateAttachment=function(e,t){if(e.name.substr(0,1)===".")return l("%warn_system_file");if(e.name.length>200)return l("%file_name_too_long");if(/.*\.(exe|bat)$/gi.test(e.name))return l("%warn_exec_file");if(e.size&&e.size>t)return l("%warn_oversize",{maxSize:GoldenData.numberToHumanSize(t)})},$(function(){var e,t,n,r,i,s,o;return n=function(e){var t;return t=e.find("input:file"),t.replaceWith(t.val("").clone(!0))},i=function(e,t,n){var r,i;return t==null&&(t=""),n==null&&(n=null),(i=e.find("[data-role=cancel]").data("jqXHR"))!=null&&i.abort(),r=e.find(".status"),r.find(".file-name").removeClass("error").text(t),r.find(".file-name + .error").remove(),$.support.xhrFormDataFileUpload?(r.find(".progress-bar .bar").addClass("initial").text("0%").css("width","auto"),r.find(".progress-statistics .total").text(n!=null?n:"0 KB")):(r.find(".text-status, .progress-bar").remove(),r.find(".progress-container").prepend("<div class='text-status'>"+l("%uploading")+"</div>")),e.find(".select-area .preview").empty(),e.find("input[data-role=attachment_id]").val(""),o(-1)},r=function(e){if(e)return n(e),i(e),e.find(".status").hide()},s=function(e,t){return e.find("input[data-role=attachment_id]").val(""),$.support.xhrFormDataFileUpload?e.find(".status .progress-bar .bar").addClass("initial").text("0%").css("width","auto"):e.find(".status .text-status").remove(),e.find(".status .file-name").addClass("error").after("<span class='error'>"+l("%bracket",{content:t})+"</span>")},o=function(e){var t,n,r;return t=$(".submit-field input.submit"),n=((r=t.data("fileUploading"))!=null?r:0)+e,t.data("fileUploading",n),GoldenData.updateSubmitBtn()},t=function(e){return e.find("[data-role=cancel]").on("click",function(){return r(e)}),e.find(".select-area label").on("dragleave",function(){return $(this).removeClass("drag-over")}),e.find("input:file").fileupload({dataType:"json",dropZone:e.find(".select-area label"),paramName:"file",url:"https://up.qbox.me/",dragover:function(){return e.find(".select-area label").addClass("drag-over")},drop:function(){return e.find(".select-area label").removeClass("drag-over")},add:function(t,n){var r,u,a;n.context=e,u=n.files[0],i(n.context,u.name,GoldenData.numberToHumanSize(u.size)),a=n.context.find(".status").show(),a.find(".progress-bar.invisible").removeClass("invisible"),r=GoldenData.validateAttachment(u,$(this).data("max-size"));if(r)return s(n.context,r);if(!n.context.hasClass("preview"))return n.formData={accept:"text/plain; charset=utf-8",token:GoldenData.xhrUploadToken,"x:field_api_code":n.context.closest(".field[data-api-code]").data("api-code")},$.support.xhrFormDataFileUpload||(n.formData.token=GoldenData.iframeUploadToken,n.formData["x:auth_token"]=n.context.closest("form").find("[name=authenticity_token]").val()),a.find("[data-role=cancel]").data({jqXHR:n.submit()}),o(1)},progress:function(e,t){var n,r,i;return i=t.context.find(".status"),n=parseInt(t.loaded/t.total*100,10),r=Math.min(n,99)+"%",i.find(".progress-bar .bar").removeClass("initial").text(r).css("width",r)},done:function(e,t){var r,i,o,u;return n(t.context),o=t.result,o.errors?(r=_.isArray(o.errors)?o.errors.join(l("%common_separator")):o.errors,s(t.context,r)):($.support.xhrFormDataFileUpload?t.context.find(".status .progress-bar .bar").text("100%").css("width","100%"):t.context.find(".status .progress-container .text-status").text(l("%upload_done")),t.context.find("input[data-role=attachment_id]").val(o.attachment_id),i=(u=o.image_url)!=null?u:GoldenData.attachmentImage,t.context.find(".select-area .preview").html("<img src='"+i+"'>"))},fail:function(e,t){var n;return n=l("%upload_failed",{status:t.jqXHR.status}),s(t.context,n)},always:function(){return o(-1)}})},e=function(e){var t,n;if(e.find("[data-role=attachment]").length>0)return GoldenData.isMobile?(t=$(window).width()-30,n=t-e.find("[data-role=attachment] .select-area").outerWidth()-10):n=e.find("[data-role=attachment]").outerWidth()-112,e.find("[data-role=attachment] .status").width(n)},GoldenData.initializeFileUploads=function(n){return e($(n)),$.each($(n).find("[data-role=attachment]"),function(e,n){return t($(n))})},$(document).on("ready page:load ajax:complete",function(){return GoldenData.initializeFileUploads(document)}),$(window).on("resize",function(){return e($(document))})})}.call(this),!function(e){var t,n;GoldenData.LightboxOptions=function(){this.fileLoadingImage="/assets/images/loading.gif",this.fileCloseImage="/assets/images/close.png",this.resizeDuration=0,this.fadeDuration=0,this.labelImage="图片",this.labelOf="/"},GoldenData.Lightbox=function(t){this.options=t,this.album=[],this.currentImageIndex=void 0},GoldenData.Lightbox.prototype.enable=function(){var t=this;return e("body").on("click","a[rel^=lightbox], area[rel^=lightbox]",function(n){return t.start(e(n.currentTarget)),!1})},GoldenData.Lightbox.prototype.build=function(){var t,n=this;e("<div>",{id:"lightboxOverlay"}).after(e("<div/>",{id:"lightbox"}).append(e("<div/>",{"class":"lb-outerContainer"}).append(e("<div/>",{"class":"lb-container"}).append(e("<img/>",{"class":"lb-image"}),e("<div/>",{"class":"lb-nav"}).append(e("<a/>",{"class":"lb-prev"}),e("<a/>",{"class":"lb-next"})),e("<div/>",{"class":"lb-loader"}).append(e("<a/>",{"class":"lb-cancel"}).append(e("<img/>",{src:this.options.fileLoadingImage}))))),e("<div/>",{"class":"lb-dataContainer"}).append(e("<div/>",{"class":"lb-data"}).append(e("<div/>",{"class":"lb-details"}).append(e("<span/>",{"class":"lb-caption"}),e("<span/>",{"class":"lb-number"})),e("<div/>",{"class":"lb-closeContainer"}).append(e("<a/>",{"class":"lb-close"}).append(e("<img/>",{src:this.options.fileCloseImage}))))))).appendTo(e("body")),e("#lightboxOverlay").hide().on("click",function(e){return n.end(),!1}),t=e("#lightbox"),t.hide().on("click",function(t){return e(t.target).attr("id")==="lightbox"&&n.end(),!1}),t.find(".lb-outerContainer").on("click",function(t){return e(t.target).attr("id")==="lightbox"&&n.end(),!1}),t.find(".lb-prev").on("click",function(e){return n.changeImage(n.currentImageIndex-1),!1}),t.find(".lb-next").on("click",function(e){return n.changeImage(n.currentImageIndex+1),!1}),t.find(".lb-loader, .lb-close").on("click",function(e){return n.end(),!1})},GoldenData.Lightbox.prototype.start=function(t){var n,r,i,s,o,u,a,f,l;e(window).on("resize",this.sizeOverlay),e("select, object, embed").css({visibility:"hidden"}),e("#lightboxOverlay").width(e(document).width()).height(e(document).height()).fadeIn(this.options.fadeDuration),this.album=[],o=0;if(t.attr("rel")==="lightbox")this.album.push({link:t.attr("href"),title:t.attr("title")});else{l=e(t.prop("tagName")+'[rel="'+t.attr("rel")+'"]');for(s=0,f=l.length;s<f;s++)i=l[s],this.album.push({link:e(i).attr("href"),title:e(i).attr("title")}),e(i).attr("href")===t.attr("href")&&(o=s)}r=e(window),a=r.scrollTop()+r.height()/10,u=r.scrollLeft(),n=e("#lightbox"),n.css({top:a+"px",left:u+"px"}).fadeIn(this.options.fadeDuration),this.changeImage(o)},GoldenData.Lightbox.prototype.changeImage=function(t){var n,r,i,s=this;this.disableKeyboardNav(),r=e("#lightbox"),n=r.find(".lb-image"),this.sizeOverlay(),e("#lightboxOverlay").fadeIn(this.options.fadeDuration),e(".loader").fadeIn("slow"),r.find(".lb-image, .lb-nav, .lb-prev, .lb-next, .lb-dataContainer, .lb-numbers, .lb-caption").hide(),r.find(".lb-outerContainer").addClass("animating"),i=new Image,i.onload=function(){return n.attr("src",s.album[t].link),n.width=i.width,n.height=i.height,s.sizeContainer(i.width,i.height)},i.src=this.album[t].link,this.currentImageIndex=t},GoldenData.Lightbox.prototype.sizeOverlay=function(){return e("#lightboxOverlay").width(e(document).width()).height(e(document).height())},GoldenData.Lightbox.prototype.sizeContainer=function(t,n){var r,i,s,o,u,a,f,l,c,h,p,d=this;i=e("#lightbox"),s=i.find(".lb-outerContainer"),p=s.outerWidth(),h=s.outerHeight(),r=i.find(".lb-container"),f=parseInt(r.css("padding-top"),10),a=parseInt(r.css("padding-right"),10),o=parseInt(r.css("padding-bottom"),10),u=parseInt(r.css("padding-left"),10),c=t+u+a,l=n+f+o,c!==p&&l!==h?s.animate({width:c,height:l},this.options.resizeDuration,"swing"):c!==p?s.animate({width:c},this.options.resizeDuration,"swing"):l!==h&&s.animate({height:l},this.options.resizeDuration,"swing"),setTimeout(function(){i.find(".lb-dataContainer").width(c),i.find(".lb-prevLink").height(l),i.find(".lb-nextLink").height(l),d.showImage()},this.options.resizeDuration)},GoldenData.Lightbox.prototype.showImage=function(){var t;t=e("#lightbox"),t.find(".lb-loader").hide(),t.find(".lb-image").fadeIn("slow"),this.updateNav(),this.updateDetails(),this.preloadNeighboringImages(),this.enableKeyboardNav()},GoldenData.Lightbox.prototype.updateNav=function(){var t;t=e("#lightbox"),t.find(".lb-nav").show(),this.currentImageIndex>0&&t.find(".lb-prev").show(),this.currentImageIndex<this.album.length-1&&t.find(".lb-next").show()},GoldenData.Lightbox.prototype.updateDetails=function(){var t,n=this;t=e("#lightbox"),typeof this.album[this.currentImageIndex].title!="undefined"&&this.album[this.currentImageIndex].title!==""&&t.find(".lb-caption").html(this.album[this.currentImageIndex].title).fadeIn("fast"),t.find(".lb-outerContainer").removeClass("animating"),t.find(".lb-dataContainer").fadeIn(this.resizeDuration,function(){return n.sizeOverlay()})},GoldenData.Lightbox.prototype.preloadNeighboringImages=function(){var e,t;this.album.length>this.currentImageIndex+1&&(e=new Image,e.src=this.album[this.currentImageIndex+1].link),this.currentImageIndex>0&&(t=new Image,t.src=this.album[this.currentImageIndex-1].link)},GoldenData.Lightbox.prototype.enableKeyboardNav=function(){e(document).on("keyup.keyboard",e.proxy(this.keyboardAction,this))},GoldenData.Lightbox.prototype.disableKeyboardNav=function(){e(document).off(".keyboard")},GoldenData.Lightbox.prototype.keyboardAction=function(e){var t,n,r,i,s;t=27,n=37,r=39,s=e.keyCode,i=String.fromCharCode(s).toLowerCase(),s===t||i.match(/x|o|c/)?this.end():i==="p"||s===n?this.currentImageIndex!==0&&this.changeImage(this.currentImageIndex-1):(i==="n"||s===r)&&this.currentImageIndex!==this.album.length-1&&this.changeImage(this.currentImageIndex+1)},GoldenData.Lightbox.prototype.end=function(){return this.disableKeyboardNav(),e(window).off("resize",this.sizeOverlay),e("#lightbox").fadeOut(this.options.fadeDuration),e("#lightboxOverlay").fadeOut(this.options.fadeDuration),e("select, object, embed").css({visibility:"visible"})}}(window.jQuery),function(){$(document).on("page:load ready",function(){var e;return e={fileLoadingImage:GoldenData.fileLoadingImage,fileCloseImage:GoldenData.fileCloseImage},e=_.extend({},new GoldenData.LightboxOptions,e),GoldenData.lightbox=new GoldenData.Lightbox(e),GoldenData.lightbox.enable(),GoldenData.lightbox.build(),$(document).on("change",".choices.image-choices input",function(){return $(this).closest(".image-choices").children(".radio.active").removeClass("active"),$(this).closest("label").toggleClass("active")})})}.call(this),function(){var e=[].indexOf||function(e){for(var t=0,n=this.length;t<n;t++)if(t in this&&this[t]===e)return t;return-1};window.GoldenData||(window.GoldenData={}),$(function(){var t,n,r;return r=function(e){var t,n;return t=$("[name='entry["+e+"]']:visible").filter(":radio:checked, :not(:radio)"),((n=t.data("value"))!=null?n.toString():void 0)||t.val()},n=function(){function e(e){this.apiCode=e,this.targets=[],this.element=$("[name='entry["+this.apiCode+"]']"),this.elementContainer=$(".field[data-api-code='"+this.apiCode+"']")}return e.prototype._informTargets=function(e){var t,n,r,i,s;i=this.targets,s=[];for(n=0,r=i.length;n<r;n++)t=i[n],s.push(t.set(this.apiCode,e));return s},e.prototype.run=function(){var e=this;return this.element.on("change",function(t){e._informTargets(r(e.apiCode));if(!$(t.target).hasClass("with-other-choice"))return t.stopPropagation()}),this.elementContainer.on("change.formLogic",function(){return e._informTargets(r(e.apiCode))})},e}(),t=function(){function t(e,t){var n;this.apiCode=e,this.triggerConditions=t,this.element=$("[name^='entry["+this.apiCode+"]']"),this.elementContainer=$(".field[data-api-code='"+this.apiCode+"']"),this.triggersStatus={};for(n in this.triggerConditions)this.set(n,r(n))}return t.prototype._onElementContainerShowHide=function(){return this.elementContainer.trigger("change.formLogic"),GoldenData.recalcFormHeight()},t.prototype.set=function(t,n){var r,i,s;n?(this.triggersStatus[t]=e.call(this.triggerConditions[t],n)>=0,i=function(){var e,n;e=this.triggersStatus,n=[];for(t in e)s=e[t],s&&n.push(t);return n}.call(this).length>0):i=!1,r=this.elementContainer.is(":visible"),i&&!r&&(this.elementContainer.show(),this.elementContainer.trigger("shown"),this.elementContainer.find("textarea").autosize().blur(),this.element.attr("disabled",!1),this._onElementContainerShowHide());if(!i&&r)return this.elementContainer.hide(),this.elementContainer.trigger("hidden"),this.element.attr("disabled",!0),this._onElementContainerShowHide()},t}(),GoldenData.FormLogic=function(){function e(e){var r,i,s,o,u;this.triggers={},this.targets={};for(r in e){s=e[r],(o=this.targets)[r]||(o[r]=new t(r,s));for(i in s)(u=this.triggers)[i]||(u[i]=new n(i)),this.triggers[i].targets.push(this.targets[r])}}return e.prototype.run=function(){var e,t,n,r;n=this.triggers,r=[];for(e in n)t=n[e],r.push(t.run());return r},e}()})}.call(this),function(){$(function(){var e,t,n;return e=function(){function e(e){var t,n,r,i,s;n=e.sku,this.fieldApiCode=e.fieldApiCode,this.goodsValue=e.goodsValue,n==null&&(n={}),this.specification=(r=n.specification)!=null?r:{},this.price=parseFloat((i=n.price)!=null?i:e.price),t=(s=n.inventory)!=null?s:e.inventory,this.inventory=t!=null?parseInt(t):null}return e.prototype.priceDisplay=function(){return"&yen;"+this.price.toFixed(2)},e.prototype.inventoryDisplay=function(e,t){return this.inventory==null?"":this.inventory>0?""+e+" "+this.inventory+" "+t:l("%sold_out")},e}(),t=function(){function t(e,t){this.$el=e,this.shoppingCart=t,this.initialPriceDisplay=this.$el.find(".price").html(),this.initialInventoryDisplay=this.$el.find(".inventory").html(),this._initializeCurrentSpecification(),this._initializeGoodsSKUs(),this._bindOnDimensionChangeEvent(),this._bindOnIncreaseDecreaseClickEvent(),this._bindOnNumberChangeEvent(),this._bindShowHideEvent(),(this.$el.is(":visible")||this._mobileGoodsVisible())&&this._updateCurrentGoodsSKU()}return t.prototype._addGoodsSKU=function(t,n,r,i,s){var o;return r==null&&(r=null),i==null&&(i=null),s==null&&(s=null),o=new e({fieldApiCode:t,goodsValue:n,sku:r,price:i,inventory:s}),this.goodsSKUs.push(o)},t.prototype._initializeGoodsSKUs=function(){var e,t,n,r,i,s,o,u,a;this.goodsSKUs=[],r=this.$el.find("input.number"),e=r.data("field-api-code"),t=r.data("goods-value"),o=this.$el.find(".dimensions").data("skus");if(!_.isEmpty(o))for(u=0,a=o.length;u<a;u++)s=o[u],this._addGoodsSKU(e,t,s);if(this.goodsSKUs.length===0)return i=r.data("goods-price"),n=r.data("inventory"),this._addGoodsSKU(e,t,null,i,n),this._updateNumberInput(),this._updateDecreaseIncreaseBtn(0)},t.prototype._matchedGoodsSKU=function(){var e=this;return _.find(this.goodsSKUs,function(t){return _.isEqual(t.specification,e.currentSpecification)})},t.prototype._updateNumberInput=function(){var e,t;return t=this.$el.find("input.number"),e=this._matchedGoodsSKU(),t.attr("disabled",e==null||e.inventory!=null&&e.inventory===0)},t.prototype._updateDecreaseIncreaseBtn=function(e){var t,n,r;r=this._matchedGoodsSKU(),n=$(this.$el.find(".increase")),t=$(this.$el.find(".decrease")),r==null||r.inventory!=null&&r.inventory<=e?n.addClass("disabled"):n.removeClass("disabled"),e>0?t.removeClass("disabled"):t.addClass("disabled");if($.browser.msie)return n.html(n.html()),t.html(t.html())},t.prototype._updatePriceAndInventoryDisplay=function(){var e;return e=this._matchedGoodsSKU(),e!=null?(this.$el.find(".price").html(e.priceDisplay()),this.$el.find(".inventory").html(e.inventoryDisplay(this.$el.data("inventory-label"),this.$el.data("unit")))):(this.$el.find(".price").html(this.initialPriceDisplay),this.$el.find(".inventory").html(this.initialInventoryDisplay))},t.prototype._bindOnDimensionChangeEvent=function(){var e;return e=this,this.$el.find(".dimension-options input:radio").change(function(){var t,n;return t=e.$el.find("input.number"),t.val(0).change(),$(this).closest(".dimension-options").find("label.selected").removeClass("selected"),n=$(this).closest(".dimension-options").find("input:radio:checked"),n.next("label").addClass("selected"),e.currentSpecification[n.data("dimension")]=n.val(),e._updatePriceAndInventoryDisplay(),e._updateNumberInput(),e._updateDecreaseIncreaseBtn(0)}),this.$el.find(".dimension-options label").click(function(){return $("#"+$(this).attr("for")).attr("checked",!0).change()})},t.prototype._bindOnIncreaseDecreaseClickEvent=function(){var e;return e=this,this.$el.find(".number-container a").click(function(t){var n,r,i;if($(this).hasClass("disabled"))return!1;t.preventDefault(),r=e._matchedGoodsSKU();if(r!=null){i=e._currentNumber(),n=$(this).is(".increase")?i+1:i-1;if(n>=0&&!(r.inventory!=null&&n>r.inventory))return e.$el.find("input.number").val(n).trigger("change")}})},t.prototype._bindOnNumberChangeEvent=function(){var e;return e=this,this.$el.find("input.number").on("keydown",function(e){if($.inArray(e.keyCode,[46,8,9,27,13])!==-1||e.keyCode>=35&&e.keyCode<=40)return;if((e.shiftKey||e.keyCode<48||e.keyCode>57)&&(e.keyCode<96||e.keyCode>105))return e.preventDefault()}),this.$el.find("input.number").change(function(){var t,n;return t=$(this).val(),t&&$.isNumeric(t)&&t>0?$(this).val(parseInt(t)):$(this).val(0),n=e._matchedGoodsSKU(),n!=null&&n.inventory!=null&&t>n.inventory&&$(this).val(n.inventory),e._updateDecreaseIncreaseBtn(t),e._addCurrentGoodsSKUsToShoppingCart()})},t.prototype._bindShowHideEvent=function(){var e=this;return this.$el.on("shown",function(){return e._addCurrentGoodsSKUsToShoppingCart()}),this.$el.on("hidden",function(){return e._updateShoppingCart(0)})},t.prototype._addCurrentGoodsSKUsToShoppingCart=function(e){return e==null&&(e=!1),this._updateShoppingCart(this._currentNumber(),e)},t.prototype._updateShoppingCart=function(e,t){var n;n=this._matchedGoodsSKU();if(n!=null)return this.shoppingCart.updateGoodsSKUNumber(n,e,t),e>0?this.$el.addClass("active"):this.$el.removeClass("active")},t.prototype._updateCurrentGoodsSKU=function(){return this._addCurrentGoodsSKUsToShoppingCart(!0),this._updatePriceAndInventoryDisplay(),this._updateNumberInput(),this._updateDecreaseIncreaseBtn(this._currentNumber())},t.prototype._initializeCurrentSpecification=function(){var e,t,n,r,i;this.currentSpecification={},r=this.$el.find(".dimensions input:radio:checked"),i=[];for(t=0,n=r.length;t<n;t++)e=r[t],i.push(this.currentSpecification[$(e).data("dimension")]=$(e).val());return i},t.prototype._currentNumber=function(){return parseInt(this.$el.find("input.number").val())},t.prototype._mobileGoodsVisible=function(){return GoldenData.isMobile&&this.$el.parents(".field [data-role=collapsible]").is(":visible")},t}(),n=function(){function e(){this.goodsList=[],this.eventHandlers={}}return e.prototype.on=function(e,t){return this.eventHandlers[e]=t},e.prototype.trigger=function(e){var t;t=this.eventHandlers[e];if(t!=null&&typeof t=="function")return t(this)},e.prototype.updateGoodsSKUNumber=function(e,t,n){var r;n==null&&(n=!1),r=_.find(this.goodsList,function(t){return _.isEqual(t.goodsSKU,e)}),r!=null&&this.goodsList.splice(_.indexOf(this.goodsList,r),1),t>0&&this.goodsList.push({goodsSKU:e,number:t});if(!n)return this.trigger("change")},e.prototype.totalPrice=function(){var e,t,n,r,i;t=0,i=this.goodsList;for(n=0,r=i.length;n<r;n++)e=i[n],t+=e.goodsSKU.price*e.number;return t},e.prototype._findGoodsByFieldApiCode=function(e){return _.filter(this.goodsList,function(t){return t.goodsSKU.fieldApiCode===e})},e.prototype.clearOnField=function(e){var t,n,r,i;i=this._findGoodsByFieldApiCode(e);for(n=0,r=i.length;n<r;n++)t=i[n],this.goodsList.splice(this.goodsList.indexOf(t),1);return this.trigger("change")},e}(),GoldenData.initGoods=function(e){var r,i,s,o,u,a,f,c;o=new n,r=function(e){var t;return t=$(".field[data-api-code='"+e+"'] .control-label"),$.trim(t.clone().children().remove().end().text())},i=function(e){var t,n,r;return t=$(".field[data-api-code='"+e.fieldApiCode+"'] [data-goods-value='"+e.goodsValue+"']").parents("[data-role=goods]"),n=$.trim(t.find(".name").text()),r=t.find(".dimensions input:radio:checked").map(function(e,t){return $.trim($(t).next("label").text())}).get().join(l("%common_separator")),r===""?""+n+"：":""+n+"（"+r+"）："},u=function(e,t){var n,s,o,u,a,f,c,h,p,d,v,m;t==null&&(t="update");if(_.isEmpty(e.goodsList))$("#shopping_cart").empty();else{p=$("<div class='content'></div>"),m=e.goodsList;for(d=0,v=m.length;d<v;d++)u=m[d],f=u.goodsSKU,o=f.fieldApiCode,n=p.find(".field[data-api-code='"+o+"']"),n.length>0?(s=n,a=s.find("ul")):(s=$("<div class='field' data-api-code='"+o+"'></div>"),s.append("<div class='field-label'>"+r(o)+"：</div> "),a=$("<ul></ul>"),s.append(a),p.append(s)),c=$("<li></li>"),c.append("<span class='goods-name'>"+i(f)+"</span>"),c.append("<span class='price-number'>&yen;"+f.price.toFixed(2)+" &times; "+u.number+"</span>"),a.append(c);p.append("<div class='summary'>"+l("%total")+"&yen; <span class='total-price'>"+e.totalPrice().toFixed(2)+"</span></div>"),$("#shopping_cart").html(p)}return h=_.isEmpty(e.goodsList)&&$("[data-role=goods]").closest(".control-group").find(".control-label:visible").length>0,$(".submit-field.alipay input.submit").data("no-goods-selected",h),GoldenData.updateSubmitBtn(),GoldenData.recalcFormHeight()};if($(e).find("[data-role=goods]").length>0){o.on("change",u),c=$(e).find("[data-role=goods]");for(a=0,f=c.length;a<f;a++)s=c[a],new t($(s),o);return u(o,"init"),$(e).on("hidden",".field[data-api-code]",function(){return o.clearOnField($(this).data("api-code"))}),$(e).on("shown",".field[data-api-code]",function(){return $(this).find("[data-role=goods] input.number[value!=0]").trigger("change")})}},$(document).on("ready page:load",function(){return GoldenData.initGoods(document)}),$(document).on("ajax:complete","form:not(#submission_password_form)",function(){return GoldenData.initGoods(document)})})}.call(this),function(){$(function(){var e,t,n,r,i;return i=function(e){var t;return t=/^1\d{10}$/,t.test(e)},t=function(e,t){return e.addClass("disabled"),e.text(t)},n=function(e){return e.removeClass("disabled"),e.text(l("%resend"))},e=function(r,i){return i>0?(t(r,l("%resend")+(" ("+i+")")),i--,setTimeout(function(){return e(r,i)},1e3)):n(r)},r=function(e){var t,n;return n=$.trim(e.val()),t=e.attr("name").match(/\[(.+)\]$/)[1],[n,t]},$(document).on("click","[data-role='verification_sender'] a.btn:not(.disabled):not(.preview)",function(){var s,o,u,a,f,c,h=this;return $(this).prev(".field_with_errors").find("input").unwrap(),$(this).parent().nextAll().remove(),c=r($(this).parents("[data-role='verification_sender']").find(".mobile")),u=c[0],a=c[1],i(u)?(t($(this),l("%sendingSMS")),f={mobile:u,field_api_code:a},s=$(this).closest("form").find("[name='authenticity_token']").val(),o={field_verification:f,authenticity_token:s,form_id:$(this).data("form-id")},$.post($(this).data("url"),o,function(t){return e($(h),60),$(h).parent().after(t)}).fail(function(e){return $(h).prev("input").wrap("<div class='field_with_errors'></div>"),$(h).parent().after("<div class='error-message'>"+e.responseText+"</div>"),n($(h))})):($(this).prev("input").wrap("<div class='field_with_errors'></div>"),$(this).parent().after("<div class='error-message'>"+l("%warn_wrong_mobile_number")+"</div>"))})})}.call(this),function(){var e=function(e,t){return function(){return e.apply(t,arguments)}};window.GoldenData||(window.GoldenData={}),$(function(){var t,n,r;return t=function(){function e(e){this.auth_token=e}return e.prototype.geocode=function(e,t){if(!e.latLng)return;return $.get("/u/address_from_geo",{lat:e.latLng.lat,lng:e.latLng.lng,authenticity_token:this.auth_token}).done(function(e){return t(e,"success")}).fail(function(e,n){return t(e.responseText,n)})},e}(),n=function(){function n(n,r){this.$el=n,this.options=r!=null?r:{},this.initialGoogleMap=e(this.initialGoogleMap,this),this.initGoogleMapEvent=e(this.initGoogleMapEvent,this),this.updateGoogleMap=e(this.updateGoogleMap,this),this.searchLocation=e(this.searchLocation,this),this.showAddress=e(this.showAddress,this),this.updateMap=e(this.updateMap,this),this.defaultLocation=e(this.defaultLocation,this),this.currentLocation=e(this.currentLocation,this),this.options=$.extend({staticMap:!1,withGoogleMap:!0,useBaidu:!1},this.options),this.geocoder=this.options.useBaidu?new t(this.$el.closest("form").find("[name=authenticity_token]").val()):new google.maps.Geocoder,this.options.withGoogleMap&&(this.initialGoogleMap(this.$el.find(".google-map-container")[0]),this.options.staticMap||this.initGoogleMapEvent())}return n.prototype.currentLocation=function(e,t){var n,r=this;return e==null&&(e=null),t==null&&(t={}),navigator.geolocation?(n={timeout:5e3,maximumAge:36e5},navigator.geolocation.getCurrentPosition(function(e){var n;return n={lat:e.coords.latitude,lng:e.coords.longitude},r.updateMap(n,14,t)},function(n){return r.defaultLocation(e,t)},n)):this.defaultLocation(e,t)},n.prototype.defaultLocation=function(e,t){var n;return t==null&&(t={}),e?(n={lat:39.90403,lng:116.40752599999996},this.updateMap(n,4,t)):alert(l("%warn_not_get_location"))},n.prototype.updateMap=function(e,t,n){return t==null&&(t=null),n==null&&(n={}),this.googleMap&&this.updateGoogleMap(e,t),this.showAddress(e,n),this.$el.find(".geo-map-coord").text(l("%geo_coord",{lat:e.lat.toFixed(8),"long":e.lng.toFixed(8)})),this.$el.find("input[name$='[latitude]']").val(e.lat),this.$el.find("input[name$='[longitude]']").val(e.lng)},n.prototype._getAddress=function(e,n){if(this.geocoder instanceof t){if(n==="success"&&e)return e}else if(n===google.maps.GeocoderStatus.OK&&e[0])return $.trim(e[0].formatted_address.replace(/邮政编码.*/,""))},n.prototype._googleLatLng=function(e){return new google.maps.LatLng(e.lat,e.lng)},n.prototype._latLngFromGoogleLatLng=function(e){return{lat:e.lat(),lng:e.lng()}},n.prototype.showAddress=function(e,t){var n,r=this;return t==null&&(t={}),n=this.options.useBaidu?e:this._googleLatLng(e),this.geocoder.geocode({latLng:n},function(n,i){var s;s=r._getAddress(n,i),s?(r.$el.find(".geo-map-address").text(l("%geo_address",{address:s})),r.$el.find("input[name$='[address]']").val(s)):r.$el.find(".geo-map-address").text(l("%geo_no_address")+l("%geo_coord",{"long":e.lng,lat:e.lat}));if(t.onComplete)return t.onComplete()})},n.prototype.searchLocation=function(e){var t=this;return this.geocoder.geocode({address:e},function(e,n){if(n===google.maps.GeocoderStatus.OK)return t.updateMap(t._latLngFromGoogleLatLng(e[0].geometry.location))})},n.prototype.updateGoogleMap=function(e,t){var n;return n=this._googleLatLng(e),t&&this.googleMap.setZoom(t),this.googleMap.setCenter(n),this.marker.setPosition(n)},n.prototype.initGoogleMapEvent=function(){var e=this;return google.maps.event.addListener(this.googleMap,"click",function(t){return e.updateMap(e._latLngFromGoogleLatLng(t.latLng))}),google.maps.event.addListener(this.marker,"dragend",function(t){return e.updateMap(e._latLngFromGoogleLatLng(t.latLng))})},n.prototype.initialGoogleMap=function(e){var t;return t={zoom:4,mapTypeId:google.maps.MapTypeId.ROADMAP,streetViewControl:!1,zoomControlOptions:{position:google.maps.ControlPosition.LEFT_TOP}},this.googleMap=new google.maps.Map(e,t),this.marker=new google.maps.Marker({position:this.googleMap.getCenter(),map:this.googleMap,animation:google.maps.Animation.Drop,draggable:!this.options.staticMap})},n}(),r=function(){function t(t){this.$container=t,this.initialEvents=e(this.initialEvents,this),this.initialExistedPosition=e(this.initialExistedPosition,this),this.initialMap=e(this.initialMap,this),this.mobile=this.$container.hasClass("mobile"),this.initialMap(),this.initialEvents()}return t.prototype.initialMap=function(){return this.$container.find(".geo-field-chooser").removeClass("disabled"),this.$container.find(".geo-field-chooser span").text(l("%geo_choose")),this.mobile&&this.$container.find(".btn.geo-field-chooser").val(l("%geo_locate")),this.initialExistedPosition()},t.prototype.initialExistedPosition=function(){var e,t;e=this.$container.find("input[name$='[latitude]']").val(),t=this.$container.find("input[name$='[longitude]']").val();if(e&&t)return this.geo===void 0&&(this.geo=new n(this.$container,{useBaidu:this.mobile})),this.geo.updateMap({lat:parseFloat(e),lng:parseFloat(t)},14)},t.prototype.initialEvents=function(){var e=this;return this.$container.find(".geo-field-chooser").click(function(){return e.mobile&&(e.$container.find(".geo-field-chooser").addClass("disabled"),e.$container.find(".geo-field-chooser").val(l("%geo_locating"))),e.geo===void 0&&(e.geo=new n(e.$container,{withGoogleMap:!e.mobile,useBaidu:e.mobile})),e.mobile?e.geo.currentLocation(!0,{onComplete:function(){return e.$container.find(".geo-map-container").show(),e.$container.find(".geo-field-chooser").removeClass("disabled").val(l("%geo_locate")).hide(),GoldenData.recalcFormHeight()}}):(e.geo.currentLocation(!0),e.$container.find(".geo-field-chooser").hide(),e.$container.find(".geo-map-container").show(),GoldenData.recalcFormHeight())}),this.$container.find(".clear-location-btn").click(function(){return e.$container.find(".geo-field-chooser").show(),e.$container.find(".geo-map-container"
).hide(),e.$container.find("input[name$='[latitude]']").val(""),e.$container.find("input[name$='[longitude]']").val(""),e.$container.find("input[name$='[address]']").val(""),GoldenData.recalcFormHeight()}),this.$container.find(".modify-location-btn").click(function(){return e.$container.find(".clear-location-btn").click(),e.$container.find(".geo-field-chooser").click()}),this.$container.find(".map-search-btn").click(function(){var t;t=e.$container.find(".map-search").val();if(t)return e.geo.searchLocation(t)}),this.$container.find(".current-location-btn").click(function(){return e.geo.currentLocation()})},t}(),GoldenData.initGeoView=function(){var e,t,n,i,s;i=$("[data-role=geo]"),s=[];for(t=0,n=i.length;t<n;t++)e=i[t],s.push(new r($(e)));return s},GoldenData.initStaticGeoView=function(){var e,t,r,i,s,o,u,a;u=$("[data-role=static-geomap]"),a=[];for(s=0,o=u.length;s<o;s++)e=u[s],i=new n($(e),{staticMap:!0}),t=$(e).find(".google-map-container").data("latitude"),r=$(e).find(".google-map-container").data("longitude"),a.push(i.updateGoogleMap({lat:parseFloat(t),lng:parseFloat(r)},14));return a},$(".map-search").on("keypress",function(e){if(e.keyCode===13)return $(this).parent(".geo-map-action").find(".map-search-btn").click()})})}.call(this),function(){window.GoldenData||(window.GoldenData={}),$(function(){var e;return e=function(e,t){var n,r,i,s,o,u;r=e.find("option:selected"),t.html("<option value=''>"+l("%select_prompt")+"</option>"),u=(o=r.data("choices"))!=null?o:[];for(i=0,s=u.length;i<s;i++)n=u[i],t.append("<option value='"+n+"'>"+n+"</option>");t.val(t.data("value"));if($.mobile)return t.selectmenu("refresh",!0)},GoldenData.initCascadeSelector=function(t){var n,r,i,s,o,u;o=$(t).find("[data-role=cascade]"),u=[];for(i=0,s=o.length;i<s;i++)n=o[i],r=$(n).find("[data-role=level_1]"),e(r,$(n).find("[data-role=level_2]")),u.push(r.on("change",function(){var t;return t=$(this).parents("[data-role=cascade]").find("[data-role=level_2]"),e($(this),t)}));return u},$(document).on("ready page:load ajax:complete",function(){return GoldenData.initCascadeSelector(document)})})}.call(this),function(){}.call(this),function(){function xt(e,t){var n;e||(e={});for(n in t)e[n]=t[n];return e}function Tt(){var e,t=arguments.length,n={},r=function(e,t){var n,i;for(i in t)t.hasOwnProperty(i)&&(n=t[i],typeof e!="object"&&(e={}),n&&typeof n=="object"&&Object.prototype.toString.call(n)!=="[object Array]"&&typeof n.nodeType!="number"?e[i]=r(e[i]||{},n):e[i]=t[i]);return e};for(e=0;e<t;e++)n=r(n,arguments[e]);return n}function Nt(){var e=0,t=arguments,n=t.length,r={};for(;e<n;e++)r[t[e++]]=t[e];return r}function Ct(e,t){return parseInt(e,t||10)}function kt(e){return typeof e=="string"}function Lt(e){return typeof e=="object"}function At(e){return Object.prototype.toString.call(e)==="[object Array]"}function Ot(e){return typeof e=="number"}function Mt(e){return r.log(e)/r.LN10}function _t(e){return r.pow(10,e)}function Dt(e,t){var n=e.length;while(n--)if(e[n]===t){e.splice(n,1);break}}function Pt(t){return t!==e&&t!==null}function Ht(e,t,n){var r,i="setAttribute",s;if(kt(t))Pt(n)?e[i](t,n):e&&e.getAttribute&&(s=e.getAttribute(t));else if(Pt(t)&&Lt(t))for(r in t)e[i](r,t[r]);return s}function Bt(e){return At(e)?e:[e]}function jt(){var e=arguments,t,n,r=e.length;for(t=0;t<r;t++){n=e[t];if(typeof n!="undefined"&&n!==null)return n}}function Ft(t,n){m&&n&&n.opacity!==e&&(n.filter="alpha(opacity="+n.opacity*100+")"),xt(t.style,n)}function It(e,n,r,i,s){var o=t.createElement(e);return n&&xt(o,n),s&&Ft(o,{padding:0,border:V,margin:0}),r&&Ft(o,r),i&&i.appendChild(o),o}function qt(e,t){var n=function(){};return n.prototype=new e,xt(n.prototype,t),n}function Rt(e){return e=(e||0).toString(),e.indexOf(".")>-1?e.split(".")[1].length:0}function Ut(e,t,n,r){var i=O.lang,s=e,o=t===-1?Rt(e):isNaN(t=f(t))?2:t,u=n===undefined?i.decimalPoint:n,a=r===undefined?i.thousandsSep:r,l=s<0?"-":"",c=String(Ct(s=f(+s||0).toFixed(o))),h=c.length>3?c.length%3:0;return l+(h?c.substr(0,h)+a:"")+c.substr(h).replace(/(\d{3})(?=\d)/g,"$1"+a)+(o?u+f(s-c).toFixed(o).slice(2):"")}function zt(e,t){return(new Array((t||2)+1-String(e).length)).join(0)+e}function Wt(e,t,n){var r=e[t];e[t]=function(){var e=Array.prototype.slice.call(arguments);return e.unshift(r),n.apply(this,e)}}function Xt(e,t){var n=/f$/,r=/\.([0-9])/,i=O.lang,s;return n.test(e)?(s=e.match(r),s=s?s[1]:6,t=Ut(t,s,i.decimalPoint,e.indexOf(",")>-1?i.thousandsSep:"")):t=M(e,t),t}function Vt(e,t){var n="{",r=!1,i,s,o,u,a,f=[],l,c;while((c=e.indexOf(n))!==-1){i=e.slice(0,c);if(r){s=i.split(":"),o=s.shift().split("."),a=o.length,l=t;for(u=0;u<a;u++)l=l[o[u]];s.length&&(l=Xt(s.join(":"),l)),f.push(l)}else f.push(i);e=e.slice(c+1),r=!r,n=r?"}":"{"}return f.push(e),f.join("")}function $t(e,t,n,r){var i,s;n=jt(n,1),i=e/n,t||(t=[1,2,2.5,5,10],r&&r.allowDecimals===!1&&(n===1?t=[1,2,5,10]:n<=.1&&(t=[1/n])));for(s=0;s<t.length;s++){e=t[s];if(i<=(t[s]+(t[s+1]||t[s]))/2)break}return e*=n,e}function Jt(e,t){var n=t||[[Z,[1,2,5,10,20,25,50,100,200,500]],[et,[1,2,5,10,15,30]],[tt,[1,2,5,10,15,30]],[nt,[1,2,3,4,6,8,12]],[rt,[1,2]],[it,[1,2]],[st,[1,2,3,4,6]],[ot,null]],r=n[n.length-1],i=P[r[0]],s=r[1],o,u;for(u=0;u<n.length;u++){r=n[u],i=P[r[0]],s=r[1];if(n[u+1]){var a=(i*s[s.length-1]+P[n[u+1][0]])/2;if(e<=a)break}}return i===P[ot]&&e<5*i&&(s=[1,2,5]),i===P[ot]&&e<5*i&&(s=[1,2,5]),o=$t(e/i,s),{unitRange:i,count:o,unitName:r[0]}}function Kt(e,t,n,r){var i=[],o,u={},a=O.global.useUTC,f,l=new Date(t),c=e.unitRange,h=e.count;if(Pt(t)){c>=P[et]&&(l.setMilliseconds(0),l.setSeconds(c>=P[tt]?0:h*s(l.getSeconds()/h))),c>=P[tt]&&l[gt](c>=P[nt]?0:h*s(l[ct]()/h)),c>=P[nt]&&l[yt](c>=P[rt]?0:h*s(l[ht]()/h)),c>=P[rt]&&l[bt](c>=P[st]?1:h*s(l[dt]()/h)),c>=P[st]&&(l[wt](c>=P[ot]?0:h*s(l[vt]()/h)),f=l[mt]()),c>=P[ot]&&(f-=f%h,l[Et](f)),c===P[it]&&l[bt](l[dt]()-l[pt]()+jt(r,1)),o=1,f=l[mt]();var p=l.getTime(),d=l[vt](),v=l[dt](),m=a?0:(864e5+l.getTimezoneOffset()*60*1e3)%864e5;while(p<n)i.push(p),c===P[ot]?p=lt(f+o*h,0):c===P[st]?p=lt(f,d+o*h):!!a||c!==P[rt]&&c!==P[it]?(p+=c*h,c<=P[nt]&&p%P[rt]===m&&(u[p]=rt)):p=lt(f,d,v+o*h*(c===P[rt]?1:7)),o++;i.push(p)}return i.info=xt(e,{higherRanks:u,totalRange:c*h}),i}function Qt(){this.color=0,this.symbol=0}function Gt(e,t){var n=e.length,r,i;for(i=0;i<n;i++)e[i].ss_i=i;e.sort(function(e,n){return r=t(e,n),r===0?e.ss_i-n.ss_i:r});for(i=0;i<n;i++)delete e[i].ss_i}function Yt(e){var t=e.length,n=e[0];while(t--)e[t]<n&&(n=e[t]);return n}function Zt(e){var t=e.length,n=e[0];while(t--)e[t]>n&&(n=e[t]);return n}function en(e,t){var n;for(n in e)e[n]&&e[n]!==t&&e[n].destroy&&e[n].destroy(),delete e[n]}function tn(e){A||(A=It(I)),e&&A.appendChild(e),A.innerHTML=""}function nn(e,t){var r="Highcharts error #"+e+": www.highcharts.com/errors/"+e;if(t)throw r;n.console&&console.log(r)}function rn(e){return parseFloat(e.toPrecision(14))}function sn(e,t){_=jt(e,t.animation)}function Tn(){var e=O.global.useUTC,t=e?"getUTC":"get",n=e?"setUTC":"set";lt=e?Date.UTC:function(e,t,n,r,i,s){return(new Date(e,t,jt(n,1),jt(r,0),jt(i,0),jt(s,0))).getTime()},ct=t+"Minutes",ht=t+"Hours",pt=t+"Day",dt=t+"Date",vt=t+"Month",mt=t+"FullYear",gt=n+"Minutes",yt=n+"Hours",bt=n+"Date",wt=n+"Month",Et=n+"FullYear"}function Nn(e){return O=Tt(O,e),Tn(),O}function Cn(){return O}function Ln(){}function Hn(e,t,n,r){this.axis=e,this.pos=t,this.type=n||"",this.isNew=!0,!n&&!r&&this.addLabel()}function Bn(e,t){this.axis=e,t&&(this.options=t,this.id=t.id)}function jn(e,t,n,r,i,s){var o=e.chart.inverted;this.axis=e,this.isNegative=n,this.options=t,this.x=r,this.stack=i,this.percent=s==="percent",this.alignOptions={align:t.align||(o?n?"left":"right":"center"),verticalAlign:t.verticalAlign||(o?"middle":n?"bottom":"top"),y:jt(t.y,o?4:n?14:-6),x:jt(t.x,o?n?-6:6:0)},this.textAlign=t.textAlign||(o?n?"right":"left":"center")}function Fn(){this.init.apply(this,arguments)}function In(){this.init.apply(this,arguments)}function qn(e,t){this.init(e,t)}function Rn(e,t){this.init(e,t)}function Un(){this.init.apply(this,arguments)}var e,t=document,n=window,r=Math,i=r.round,s=r.floor,o=r.ceil,u=r.max,a=r.min,f=r.abs,l=r.cos,c=r.sin,h=r.PI,p=h*2/360,d=navigator.userAgent,v=n.opera,m=/msie/i.test(d)&&!v,g=t.documentMode===8,y=/AppleWebKit/.test(d),b=/Firefox/.test(d),w=/(Mobile|Android|Windows Phone)/.test(d),E="http://www.w3.org/2000/svg",S=!!t.createElementNS&&!!t.createElementNS(E,"svg").createSVGRect,x=b&&parseInt(d.split("Firefox/")[1],10)<4,T=!S&&!m&&!!t.createElement("canvas").getContext,N,C=t.documentElement.ontouchstart!==e,k={},L=0,A,O,M,_,D,P,H=function(){},B=[],j="Highcharts",F="3.0.0",I="div",q="absolute",R="relative",U="hidden",z="highcharts-",W="visible",X="px",V="none",$="M",J="L",K="rgba(192,192,192,"+(S?1e-4:.002)+")",Q="",G="hover",Y="select",Z="millisecond",et="second",tt="minute",nt="hour",rt="day",it="week",st="month",ot="year",ut="linearGradient",at="stops",ft="stroke-width",lt,ct,ht,pt,dt,vt,mt,gt,yt,bt,wt,Et,St={};n.Highcharts=n.Highcharts?nn(16,!0):{},M=function(e,t,n){if(!Pt(t)||isNaN(t))return"Invalid date";e=jt(e,"%Y-%m-%d %H:%M:%S");var r=new Date(t),s,o=r[ht](),u=r[pt](),a=r[dt](),f=r[vt](),l=r[mt](),c=O.lang,h=c.weekdays,p=xt({a:h[u].substr(0,3),A:h[u],d:zt(a),e:a,b:c.shortMonths[f],B:c.months[f],m:zt(f+1),y:l.toString().substr(2,2),Y:l,H:zt(o),I:zt(o%12||12),l:o%12||12,M:zt(r[ct]()),p:o<12?"AM":"PM",P:o<12?"am":"pm",S:zt(r.getSeconds()),L:zt(i(t%1e3),3)},Highcharts.dateFormats);for(s in p)while(e.indexOf("%"+s)!==-1)e=e.replace("%"+s,typeof p[s]=="function"?p[s](t):p[s]);return n?e.substr(0,1).toUpperCase()+e.substr(1):e},Qt.prototype={wrapColor:function(e){this.color>=e&&(this.color=0)},wrapSymbol:function(e){this.symbol>=e&&(this.symbol=0)}},P=Nt(Z,1,et,1e3,tt,6e4,nt,36e5,rt,864e5,it,6048e5,st,26784e5,ot,31556952e3),D={init:function(e,t,n){t=t||"";var r=e.shift,i=t.indexOf("C")>-1,s=i?7:3,o,u,a,f=t.split(" "),l=[].concat(n),c,h,p=function(e){a=e.length;while(a--)e[a]===$&&e.splice(a+1,0,e[a+1],e[a+2],e[a+1],e[a+2])};i&&(p(f),p(l)),e.isArea&&(c=f.splice(f.length-6,6),h=l.splice(l.length-6,6));if(r<=l.length/s)while(r--)l=[].concat(l).splice(0,s).concat(l);e.shift=0;if(f.length){o=l.length;while(f.length<o)u=[].concat(f).splice(f.length-s,s),i&&(u[s-6]=u[s-2],u[s-5]=u[s-1]),f=f.concat(u)}return c&&(f=f.concat(c),l=l.concat(h)),[f,l]},step:function(e,t,n,r){var i=[],s=e.length,o;if(n===1)i=r;else if(s===t.length&&n<1)while(s--)o=parseFloat(e[s]),i[s]=isNaN(o)?e[s]:n*parseFloat(t[s]-o)+o;else i=t;return i}},function(r){n.HighchartsAdapter=n.HighchartsAdapter||r&&{init:function(t){var n=r.fx,i=n.step,s,o=r.Tween,u=o&&o.propHooks;r.extend(r.easing,{easeOutQuad:function(e,t,n,r,i){return-r*(t/=i)*(t-2)+n}}),r.each(["cur","_default","width","height","opacity"],function(t,r){var s=i,a,f;r==="cur"?s=n.prototype:r==="_default"&&o&&(s=u[r],r="set"),a=s[r],a&&(s[r]=function(n){return n=t?n:this,f=n.elem,f.attr?f.attr(n.prop,r==="cur"?e:n.now):a.apply(this,arguments)})}),s=function(e){var n=e.elem,r;e.started||(r=t.init(n,n.d,n.toD),e.start=r[0],e.end=r[1],e.started=!0),n.attr("d",t.step(e.start,e.end,e.pos,n.toD))},o?u.d={set:s}:i.d=s,this.each=Array.prototype.forEach?function(e,t){return Array.prototype.forEach.call(e,t)}:function(e,t){var n=0,r=e.length;for(;n<r;n++)if(t.call(e[n],e[n],n,e)===!1)return n},r.fn.highcharts=function(){var t="Chart",n=arguments,r,i,s;return kt(n[0])&&(t=n[0],n=Array.prototype.slice.call(n,1)),r=n[0],r!==e&&(r.chart=r.chart||{},r.chart.renderTo=this[0],s=new Highcharts[t](r,n[1]),i=this),r===e&&(i=B[Ht(this[0],"data-highcharts-chart")]),i}},getScript:r.getScript,inArray:r.inArray,adapterRun:function(e,t){return r(e)[t]()},grep:r.grep,map:function(e,t){var n=[],r=0,i=e.length;for(;r<i;r++)n[r]=t.call(e[r],e[r],r,e);return n},offset:function(e){return r(e).offset()},addEvent:function(e,t,n){r(e).bind(t,n)},removeEvent:function(e,n,i){var s=t.removeEventListener?"removeEventListener":"detachEvent";t[s]&&!e[s]&&(e[s]=function(){}),r(e).unbind(n,i)},fireEvent:function(e,t,n,i){var s=r.Event(t),o="detached"+t,u;!m&&n&&(delete n.layerX,delete n.layerY),xt(s,n),e[t]&&(e[o]=e[t],e[t]=null),r.each(["preventDefault","stopPropagation"],function(e,t){var n=s[t];s[t]=function(){try{n.call(s)}catch(e){t==="preventDefault"&&(u=!0)}}}),r(e).trigger(s),e[o]&&(e[t]=e[o],e[o]=null),i&&!s.isDefaultPrevented()&&!u&&i(s)},washMouseEvent:function(t){var n=t.originalEvent||t;return n.pageX===e&&(n.pageX=t.pageX,n.pageY=t.pageY),n},animate:function(t,n,i){var s=r(t);n.d&&(t.toD=n.d,n.d=1),s.stop(),n.opacity!==e&&t.attr&&(n.opacity+="px"),s.animate(n,i)},stop:function(e){r(e).stop()}}}(n.jQuery);var on=n.HighchartsAdapter,un=on||{};on&&on.init.call(on,D);var an=un.adapterRun,fn=un.getScript,ln=un.inArray,cn=un.each,hn=un.grep,pn=un.offset,dn=un.map,vn=un.addEvent,mn=un.removeEvent,gn=un.fireEvent,yn=un.washMouseEvent,bn=un.animate,wn=un.stop,En={enabled:!0,align:"center",x:0,y:15,style:{color:"#666",cursor:"default",fontSize:"11px",lineHeight:"14px"}};O={colors:["#2f7ed8","#0d233a","#8bbc21","#910000","#1aadce","#492970","#f28f43","#77a1e5","#c42525","#a6c96a"],symbols:["circle","diamond","square","triangle","triangle-down"],lang:{loading:"Loading...",months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],weekdays:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],decimalPoint:".",numericSymbols:["k","M","G","T","P","E"],resetZoom:"Reset zoom",resetZoomTitle:"Reset zoom level 1:1",thousandsSep:","},global:{useUTC:!0,canvasToolsURL:"http://code.highcharts.com/3.0.0/modules/canvas-tools.js",VMLRadialGradientURL:"http://code.highcharts.com/3.0.0/gfx/vml-radial-gradient.png"},chart:{borderColor:"#4572A7",borderRadius:5,defaultSeriesType:"line",ignoreHiddenSeries:!0,spacingTop:10,spacingRight:10,spacingBottom:15,spacingLeft:10,style:{fontFamily:'"Lucida Grande", "Lucida Sans Unicode", Verdana, Arial, Helvetica, sans-serif',fontSize:"12px"},backgroundColor:"#FFFFFF",plotBorderColor:"#C0C0C0",resetZoomButton:{theme:{zIndex:20},position:{align:"right",x:-10,y:10}}},title:{text:"Chart title",align:"center",y:15,style:{color:"#274b6d",fontSize:"16px"}},subtitle:{text:"",align:"center",y:30,style:{color:"#4d759e"}},plotOptions:{line:{allowPointSelect:!1,showCheckbox:!1,animation:{duration:1e3},events:{},lineWidth:2,marker:{enabled:!0,lineWidth:0,radius:4,lineColor:"#FFFFFF",states:{hover:{enabled:!0},select:{fillColor:"#FFFFFF",lineColor:"#000000",lineWidth:2}}},point:{events:{}},dataLabels:Tt(En,{enabled:!1,formatter:function(){return this.y},verticalAlign:"bottom",y:0}),cropThreshold:300,pointRange:0,showInLegend:!0,states:{hover:{marker:{}},select:{marker:{}}},stickyTracking:!0}},labels:{style:{position:q,color:"#3E576F"}},legend:{enabled:!0,align:"center",layout:"horizontal",labelFormatter:function(){return this.name},borderWidth:1,borderColor:"#909090",borderRadius:5,navigation:{activeColor:"#274b6d",inactiveColor:"#CCC"},shadow:!1,itemStyle:{cursor:"pointer",color:"#274b6d",fontSize:"12px"},itemHoverStyle:{color:"#000"},itemHiddenStyle:{color:"#CCC"},itemCheckboxStyle:{position:q,width:"13px",height:"13px"},symbolWidth:16,symbolPadding:5,verticalAlign:"bottom",x:0,y:0,title:{style:{fontWeight:"bold"}}},loading:{labelStyle:{fontWeight:"bold",position:R,top:"1em"},style:{position:q,backgroundColor:"white",opacity:.5,textAlign:"center"}},tooltip:{enabled:!0,animation:S,backgroundColor:"rgba(255, 255, 255, .85)",borderWidth:1,borderRadius:3,dateTimeLabelFormats:{millisecond:"%A, %b %e, %H:%M:%S.%L",second:"%A, %b %e, %H:%M:%S",minute:"%A, %b %e, %H:%M",hour:"%A, %b %e, %H:%M",day:"%A, %b %e, %Y",week:"Week from %A, %b %e, %Y",month:"%B %Y",year:"%Y"},headerFormat:'<span style="font-size: 10px">{point.key}</span><br/>',pointFormat:'<span style="color:{series.color}">{series.name}</span>: <b>{point.y}</b><br/>',shadow:!0,snap:w?25:10,style:{color:"#333333",cursor:"default",fontSize:"12px",padding:"8px",whiteSpace:"nowrap"}},credits:{enabled:!0,text:"Highcharts.com",href:"http://www.highcharts.com",position:{align:"right",x:-10,verticalAlign:"bottom",y:-5},style:{cursor:"pointer",color:"#909090",fontSize:"9px"}}};var Sn=O.plotOptions,xn=Sn.line;Tn();var kn=function(e){function i(e){e&&e.stops?r=dn(e.stops,function(e){return kn(e[1])}):(n=/rgba\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]?(?:\.[0-9]+)?)\s*\)/.exec(e),n?t=[Ct(n[1]),Ct(n[2]),Ct(n[3]),parseFloat(n[4],10)]:(n=/#([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})/.exec(e),n?t=[Ct(n[1],16),Ct(n[2],16),Ct(n[3],16),1]:(n=/rgb\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*\)/.exec(e),n&&(t=[Ct(n[1]),Ct(n[2]),Ct(n[3]),1]))))}function s(n){var i;return r?(i=Tt(e),i.stops=[].concat(i.stops),cn(r,function(e,t){i.stops[t]=[i.stops[t][0],e.get(n)]})):t&&!isNaN(t[0])?n==="rgb"?i="rgb("+t[0]+","+t[1]+","+t[2]+")":n==="a"?i=t[3]:i="rgba("+t.join(",")+")":i=e,i}function o(e){if(r)cn(r,function(t){t.brighten(e)});else if(Ot(e)&&e!==0){var n;for(n=0;n<3;n++)t[n]+=Ct(e*255),t[n]<0&&(t[n]=0),t[n]>255&&(t[n]=255)}return this}function u(e){return t[3]=e,this}var t=[],n,r;return i(e),{get:s,brighten:o,rgba:t,setOpacity:u}};Ln.prototype={init:function(e,n){var r=this;r.element=n==="span"?It(n):t.createElementNS(E,n),r.renderer=e,r.attrSetters={}},opacity:1,animate:function(e,t,n){var r=jt(t,_,!0);wn(this),r?(r=Tt(r),n&&(r.complete=n),bn(this,e,r)):(this.attr(e),n&&n())},attr:function(n,r){var i=this,s,o,a,f,l,c=i.element,h=c.nodeName.toLowerCase(),p=i.renderer,d,v,m=i.attrSetters,g=i.shadows,y,b,w=i;kt(n)&&Pt(r)&&(s=n,n={},n[s]=r);if(kt(n))s=n,h==="circle"?s={x:"cx",y:"cy"}[s]||s:s==="strokeWidth"&&(s="stroke-width"),w=Ht(c,s)||i[s]||0,s!=="d"&&s!=="visibility"&&(w=parseFloat(w));else{for(s in n){d=!1,o=n[s],a=m[s]&&m[s].call(i,o,s);if(a!==!1){a!==e&&(o=a);if(s==="d")o&&o.join&&(o=o.join(" ")),/(NaN| {2}|^$)/.test(o)&&(o="M 0 0");else if(s==="x"&&h==="text")for(f=0;f<c.childNodes.length;f++)l=c.childNodes[f],Ht(l,"x")===Ht(c,"x")&&Ht(l,"x",o);else if(!i.rotation||s!=="x"&&s!=="y")if(s==="fill")o=p.color(o,c,s);else if(h!=="circle"||s!=="x"&&s!=="y")if(h==="rect"&&s==="r")Ht(c,{rx:o,ry:o}),d=!0;else if(s==="translateX"||s==="translateY"||s==="rotation"||s==="verticalAlign"||s==="scaleX"||s==="scaleY")b=!0,d=!0;else if(s==="stroke")o=p.color(o,c,s);else if(s==="dashstyle"){s="stroke-dasharray",o=o&&o.toLowerCase();if(o==="solid")o=V;else if(o){o=o.replace("shortdashdotdot","3,1,1,1,1,1,").replace("shortdashdot","3,1,1,1").replace("shortdot","1,1,").replace("shortdash","3,1,").replace("longdash","8,3,").replace(/dot/g,"1,3,").replace("dash","4,3,").replace(/,$/,"").split(","),f=o.length;while(f--)o[f]=Ct(o[f])*n["stroke-width"];o=o.join(",")}}else s==="width"?o=Ct(o):s==="align"?(s="text-anchor",o={left:"start",center:"middle",right:"end"}[o]):s==="title"&&(v=c.getElementsByTagName("title")[0],v||(v=t.createElementNS(E,"title"),c.appendChild(v)),v.textContent=o);else s={x:"cx",y:"cy"}[s]||s;else b=!0;s==="strokeWidth"&&(s="stroke-width");if(s==="stroke-width"||s==="stroke")i[s]=o,i.stroke&&i["stroke-width"]?(Ht(c,"stroke",i.stroke),Ht(c,"stroke-width",i["stroke-width"]),i.hasStroke=!0):s==="stroke-width"&&o===0&&i.hasStroke&&(c.removeAttribute("stroke"),i.hasStroke=!1),d=!0;i.symbolName&&/^(x|y|width|height|r|start|end|innerR|anchorX|anchorY)/.test(s)&&(y||(i.symbolAttr(n),y=!0),d=!0);if(g&&/^(width|height|visibility|x|y|d|transform)$/.test(s)){f=g.length;while(f--)Ht(g[f],s,s==="height"?u(o-(g[f].cutHeight||0),0):o)}(s==="width"||s==="height")&&h==="rect"&&o<0&&(o=0),i[s]=o,s==="text"?(o!==i.textStr&&delete i.bBox,i.textStr=o,i.added&&p.buildText(i)):d||Ht(c,s,o)}}b&&i.updateTransform()}return w},addClass:function(e){return Ht(this.element,"class",Ht(this.element,"class")+" "+e),this},symbolAttr:function(e){var t=this;cn(["x","y","r","start","end","width","height","innerR","anchorX","anchorY"],function(n){t[n]=jt(e[n],t[n])}),t.attr({d:t.renderer.symbols[t.symbolName](t.x,t.y,t.width,t.height,t)})},clip:function(e){return this.attr("clip-path",e?"url("+this.renderer.url+"#"+e.id+")":V)},crisp:function(e,t,n,r,o){var u=this,a,f={},l={},c;e=e||u.strokeWidth||u.attr&&u.attr("stroke-width")||0,c=i(e)%2/2,l.x=s(t||u.x||0)+c,l.y=s(n||u.y||0)+c,l.width=s((r||u.width||0)-2*c),l.height=s((o||u.height||0)-2*c),l.strokeWidth=e;for(a in l)u[a]!==l[a]&&(u[a]=f[a]=l[a]);return f},css:function(e){var t=this,n=t.element,r=e&&e.width&&n.nodeName.toLowerCase()==="text",i,s="",o=function(e,t){return"-"+t.toLowerCase()};e&&e.color&&(e.fill=e.color),e=xt(t.styles,e),t.styles=e,T&&r&&delete e.width;if(m&&!S)r&&delete e.width,Ft(t.element,e);else{for(i in e)s+=i.replace(/([A-Z])/g,o)+":"+e[i]+";";t.attr({style:s})}return r&&t.added&&t.renderer.buildText(t),t},on:function(e,t){return C&&e==="click"&&(this.element.ontouchstart=function(e){e.preventDefault(),t()}),this.element["on"+e]=t,this},setRadialReference:function(e){return this.element.radialReference=e,this},translate:function(e,t){return this.attr({translateX:e,translateY:t})},invert:function(){var e=this;return e.inverted=!0,e.updateTransform(),e},htmlCss:function(e){var t=this,n=t.element,r=e&&n.tagName==="SPAN"&&e.width;return r&&(delete e.width,t.textWidth=r,t.updateTransform()),t.styles=xt(t.styles,e),Ft(t.element,e),t},htmlGetBBox:function(){var e=this,t=e.element,n=e.bBox;return n||(t.nodeName==="text"&&(t.style.position=q),n=e.bBox={x:t.offsetLeft,y:t.offsetTop,width:t.offsetWidth,height:t.offsetHeight}),n},htmlUpdateTransform:function(){if(!this.added){this.alignOnAdd=!0;return}var e=this,t=e.renderer,n=e.element,r=e.translateX||0,i=e.translateY||0,s=e.x||0,o=e.y||0,u=e.textAlign||"left",a={left:0,center:.5,right:1}[u],f=u&&u!=="left",h=e.shadows;if(r||i)Ft(n,{marginLeft:r,marginTop:i}),h&&cn(h,function(e){Ft(e,{marginLeft:r+1,marginTop:i+1})});e.inverted&&cn(n.childNodes,function(e){t.invertChild(e,n)});if(n.tagName==="SPAN"){var d,g,w=e.rotation,E,S=0,x=1,T=0,N,C=Ct(e.textWidth),k=e.xCorr||0,L=e.yCorr||0,A=[w,u,n.innerHTML,e.textWidth].join(","),O={},M;A!==e.cTT&&(Pt(w)&&(t.isSVG?(M=m?"-ms-transform":y?"-webkit-transform":b?"MozTransform":v?"-o-transform":"",O[M]=O.transform="rotate("+w+"deg)"):(S=w*p,x=l(S),T=c(S),O.filter=w?["progid:DXImageTransform.Microsoft.Matrix(M11=",x,", M12=",-T,", M21=",T,", M22=",x,", sizingMethod='auto expand')"].join(""):V),Ft(n,O)),d=jt(e.elemWidth,n.offsetWidth),g=jt(e.elemHeight,n.offsetHeight),d>C&&/[ \-]/.test(n.textContent||n.innerText)&&(Ft(n,{width:C+X,display:"block",whiteSpace:"normal"}),d=C),E=t.fontMetrics(n.style.fontSize).b,k=x<0&&-d,L=T<0&&-g,N=x*T<0,k+=T*E*(N?1-a:a),L-=x*E*(w?N?a:1-a:1),f&&(k-=d*a*(x<0?-1:1),w&&(L-=g*a*(T<0?-1:1)),Ft(n,{textAlign:u})),e.xCorr=k,e.yCorr=L),Ft(n,{left:s+k+X,top:o+L+X}),y&&(g=n.offsetHeight),e.cTT=A}},updateTransform:function(){var e=this,t=e.translateX||0,n=e.translateY||0,r=e.scaleX,i=e.scaleY,s=e.inverted,o=e.rotation,u=[];s&&(t+=e.attr("width"),n+=e.attr("height")),(t||n)&&u.push("translate("+t+","+n+")"),s?u.push("rotate(90) scale(-1,1)"):o&&u.push("rotate("+o+" "+(e.x||0)+" "+(e.y||0)+")"),(Pt(r)||Pt(i))&&u.push("scale("+jt(r,1)+" "+jt(i,1)+")"),u.length&&Ht(e.element,"transform",u.join(" "))},toFront:function(){var e=this.element;return e.parentNode.appendChild(e),this},align:function(e,t,n){var r,s,o,u,a={},f,l=this.renderer,c=l.alignedObjects;if(e){this.alignOptions=e,this.alignByTranslate=t;if(!n||kt(n))this.alignTo=f=n||"renderer",Dt(c,this),c.push(this),n=null}else e=this.alignOptions,t=this.alignByTranslate,f=this.alignTo;n=jt(n,l[f],l),r=e.align,s=e.verticalAlign,o=(n.x||0)+(e.x||0),u=(n.y||0)+(e.y||0);if(r==="right"||r==="center")o+=(n.width-(e.width||0))/{right:1,center:2}[r];a[t?"translateX":"x"]=i(o);if(s==="bottom"||s==="middle")u+=(n.height-(e.height||0))/({bottom:1,middle:2}[s]||1);return a[t?"translateY":"y"]=i(u),this[this.placed?"animate":"attr"](a),this.placed=!0,this.alignAttr=a,this},getBBox:function(){var e=this,t=e.bBox,n=e.renderer,r,i,s=e.rotation,o=e.element,u=e.styles,a=s*p;if(!t){if(o.namespaceURI===E||n.forExport){try{t=o.getBBox?xt({},o.getBBox()):{width:o.offsetWidth,height:o.offsetHeight}}catch(h){}if(!t||t.width<0)t={width:0,height:0}}else t=e.htmlGetBBox();n.isSVG&&(r=t.width,i=t.height,m&&u&&u.fontSize==="11px"&&i.toPrecision(3)===22.7&&(t.height=i=14),s&&(t.width=f(i*c(a))+f(r*l(a)),t.height=f(i*l(a))+f(r*c(a)))),e.bBox=t}return t},show:function(){return this.attr({visibility:W})},hide:function(){return this.attr({visibility:U})},fadeOut:function(e){var t=this;t.animate({opacity:0},{duration:e||150,complete:function(){t.hide()}})},add:function(e){var t=this.renderer,n=e||t,r=n.element||t.box,i=r.childNodes,s=this.element,o=Ht(s,"zIndex"),u,a,f,l;e&&(this.parentGroup=e),this.parentInverted=e&&e.inverted,this.textStr!==undefined&&t.buildText(this),o&&(n.handleZ=!0,o=Ct(o));if(n.handleZ)for(f=0;f<i.length;f++){u=i[f],a=Ht(u,"zIndex");if(u!==s&&(Ct(a)>o||!Pt(o)&&Pt(a))){r.insertBefore(s,u),l=!0;break}}return l||r.appendChild(s),this.added=!0,gn(this,"add"),this},safeRemoveChild:function(e){var t=e.parentNode;t&&t.removeChild(e)},destroy:function(){var e=this,t=e.element||{},n=e.shadows,r,i;t.onclick=t.onmouseout=t.onmouseover=t.onmousemove=t.point=null,wn(e),e.clipPath&&(e.clipPath=e.clipPath.destroy());if(e.stops){for(i=0;i<e.stops.length;i++)e.stops[i]=e.stops[i].destroy();e.stops=null}e.safeRemoveChild(t),n&&cn(n,function(t){e.safeRemoveChild(t)}),e.alignTo&&Dt(e.renderer.alignedObjects,e);for(r in e)delete e[r];return null},shadow:function(e,t,n){var r=[],i,s,o=this.element,a,f,l,c;if(e){f=jt(e.width,3),l=(e.opacity||.15)/f,c=this.parentInverted?"(-1,-1)":"("+jt(e.offsetX,1)+", "+jt(e.offsetY,1)+")";for(i=1;i<=f;i++)s=o.cloneNode(0),a=f*2+1-2*i,Ht(s,{isShadow:"true",stroke:e.color||"black","stroke-opacity":l*i,"stroke-width":a,transform:"translate"+c,fill:V}),n&&(Ht(s,"height",u(Ht(s,"height")-a,0)),s.cutHeight=a),t?t.element.appendChild(s):o.parentNode.insertBefore(s,o),r.push(s);this.shadows=r}return this}};var An=function(){this.init.apply(this,arguments)};An.prototype={Element:Ln,init:function(e,r,i,s){var u=this,a=location,f,l;f=u.createElement("svg").attr({xmlns:E,version:"1.1"}),e.appendChild(f.element),u.isSVG=!0,u.box=f.element,u.boxWrapper=f,u.alignedObjects=[],u.url=(b||y)&&t.getElementsByTagName("base").length?a.href.replace(/#.*?$/,"").replace(/([\('\)])/g,"\\$1").replace(/ /g,"%20"):"",l=this.createElement("desc").add(),l.element.appendChild(t.createTextNode("Created with "+j+" "+F)),u.defs=this.createElement("defs").add(),u.forExport=s,u.gradients={},u.setSize(r,i,!1);var c,h;b&&e.getBoundingClientRect&&(u.subPixelFix=c=function(){Ft(e,{left:0,top:0}),h=e.getBoundingClientRect(),Ft(e,{left:o(h.left)-h.left+X,top:o(h.top)-h.top+X})},c(),vn(n,"resize",c))},isHidden:function(){return!this.boxWrapper.getBBox().width},destroy:function(){var e=this,t=e.defs;return e.box=null,e.boxWrapper=e.boxWrapper.destroy(),en(e.gradients||{}),e.gradients=null,t&&(e.defs=t.destroy()),e.subPixelFix&&mn(n,"resize",e.subPixelFix),e.alignedObjects=null,null},createElement:function(e){var t=new this.Element;return t.init(this,e),t},draw:function(){},buildText:function(e){var n=e.element,r=this,i=r.forExport,s=jt(e.textStr,"").toString().replace(/<(b|strong)>/g,'<span style="font-weight:bold">').replace(/<(i|em)>/g,'<span style="font-style:italic">').replace(/<a/g,"<span").replace(/<\/(b|strong|i|em|a)>/g,"</span>").split(/<br.*?>/g),o=n.childNodes,u=/style="([^"]+)"/,a=/href="([^"]+)"/,f=Ht(n,"x"),l=e.styles,c=l&&l.width&&Ct(l.width),h=l&&l.lineHeight,p=o.length;while(p--)n.removeChild(o[p]);c&&!e.added&&this.box.appendChild(n),s[s.length-1]===""&&s.pop(),cn(s,function(s,o){var p,d=0;s=s.replace(/<span/g,"|||<span").replace(/<\/span>/g,"</span>|||"),p=s.split("|||"),cn(p,function(s){if(s!==""||p.length===1){var v={},m=t.createElementNS(E,"tspan"),g;u.test(s)&&(g=s.match(u)[1].replace(/(;| |^)color([ :])/,"$1fill$2"),Ht(m,"style",g)),a.test(s)&&!i&&(Ht(m,"onclick",'location.href="'+s.match(a)[1]+'"'),Ft(m,{cursor:"pointer"})),s=(s.replace(/<(.|\n)*?>/g,"")||" ").replace(/&lt;/g,"<").replace(/&gt;/g,">"),m.appendChild(t.createTextNode(s)),d?v.dx=0:v.x=f,Ht(m,v),!d&&o&&(!S&&i&&Ft(m,{display:"block"}),Ht(m,"dy",h||r.fontMetrics(/px$/.test(m.style.fontSize)?m.style.fontSize:l.fontSize).h,y&&m.offsetHeight)),n.appendChild(m),d++;if(c){var b=s.replace(/([^\^])-/g,"$1- ").split(" "),w,x,T=[];while(b.length||T.length)delete e.bBox,x=e.getBBox().width,w=x>c,!w||b.length===1?(b=T,T=[],b.length&&(m=t.createElementNS(E,"tspan"),Ht(m,{dy:h||16,x:f}),g&&Ht(m,"style",g),n.appendChild(m),x>c&&(c=x))):(m.removeChild(m.firstChild),T.unshift(b.pop())),b.length&&m.appendChild(t.createTextNode(b.join(" ").replace(/- /g,"-")))}}})})},button:function(e,t,n,r,i,s,o){var u=this.label(e,t,n,null,null,null,null,null,"button"),a=0,f,l,c,h,p,d="style",v={x1:0,y1:0,x2:0,y2:1};return i=Tt({"stroke-width":1,stroke:"#CCCCCC",fill:{linearGradient:v,stops:[[0,"#FEFEFE"],[1,"#F6F6F6"]]},r:2,padding:5,style:{color:"black"}},i),c=i[d],delete i[d],s=Tt(i,{stroke:"#68A",fill:{linearGradient:v,stops:[[0,"#FFF"],[1,"#ACF"]]}},s),h=s[d],delete s[d],o=Tt(i,{stroke:"#68A",fill:{linearGradient:v,stops:[[0,"#9BD"],[1,"#CDF"]]}},o),p=o[d],delete o[d],vn(u.element,"mouseenter",function(){u.attr(s).css(h)}),vn(u.element,"mouseleave",function(){f=[i,s,o][a],l=[c,h,p][a],u.attr(f).css(l)}),u.setState=function(e){a=e,e?e===2&&u.attr(o).css(p):u.attr(i).css(c)},u.on("click",function(){r.call(u)}).attr(i).css(xt({cursor:"default"},c))},crispLine:function(e,t){return e[1]===e[4]&&(e[1]=e[4]=i(e[1])-t%2/2),e[2]===e[5]&&(e[2]=e[5]=i(e[2])+t%2/2),e},path:function(e){var t={fill:V};return At(e)?t.d=e:Lt(e)&&xt(t,e),this.createElement("path").attr(t)},circle:function(e,t,n){var r=Lt(e)?e:{x:e,y:t,r:n};return this.createElement("circle").attr(r)},arc:function(e,t,n,r,i,s){return Lt(e)&&(t=e.y,n=e.r,r=e.innerR,i=e.start,s=e.end,e=e.x),this.symbol("arc",e||0,t||0,n||0,n||0,{innerR:r||0,start:i||0,end:s||0})},rect:function(e,t,n,r,i,s){i=Lt(e)?e.r:i;var o=this.createElement("rect").attr({rx:i,ry:i,fill:V});return o.attr(Lt(e)?e:o.crisp(s,e,t,u(n,0),u(r,0)))},setSize:function(e,t,n){var r=this,i=r.alignedObjects,s=i.length;r.width=e,r.height=t,r.boxWrapper[jt(n,!0)?"animate":"attr"]({width:e,height:t});while(s--)i[s].align()},g:function(e){var t=this.createElement("g");return Pt(e)?t.attr({"class":z+e}):t},image:function(e,t,n,r,i){var s={preserveAspectRatio:V},o;return arguments.length>1&&xt(s,{x:t,y:n,width:r,height:i}),o=this.createElement("image").attr(s),o.element.setAttributeNS?o.element.setAttributeNS("http://www.w3.org/1999/xlink","href",e):o.element.setAttribute("hc-svg-href",e),o},symbol:function(e,t,n,r,s,o){var u,a=this.symbols[e],f=a&&a(i(t),i(n),r,s,o),l,c=/^url\((.*?)\)$/,h,p,d;return f?(u=this.path(f),xt(u,{symbolName:e,x:t,y:n,width:r,height:s}),o&&xt(u,o)):c.test(e)&&(d=function(e,t){e.element&&(e.attr({width:t[0],height:t[1]}),e.alignByTranslate||e.translate(i((r-t[0])/2),i((s-t[1])/2)))},h=e.match(c)[1],p=k[h],u=this.image(h).attr({x:t,y:n}),p?d(u,p):(u.attr({width:0,height:0}),l=It("img",{onload:function(){d(u,k[h]=[this.width,this.height])},src:h}))),u},symbols:{circle:function(e,t,n,r){var i=.166*n;return[$,e+n/2,t,"C",e+n+i,t,e+n+i,t+r,e+n/2,t+r,"C",e-i,t+r,e-i,t,e+n/2,t,"Z"]},square:function(e,t,n,r){return[$,e,t,J,e+n,t,e+n,t+r,e,t+r,"Z"]},triangle:function(e,t,n,r){return[$,e+n/2,t,J,e+n,t+r,e,t+r,"Z"]},"triangle-down":function(e,t,n,r){return[$,e,t,J,e+n,t,e+n/2,t+r,"Z"]},diamond:function(e,t,n,r){return[$,e+n/2,t,J,e+n,t+r/2,e+n/2,t+r,e,t+r/2,"Z"]},arc:function(e,t,n,r,i){var s=i.start,o=i.r||n||r,u=i.end-.001,a=i.innerR,f=i.open,p=l(s),d=c(s),v=l(u),m=c(u),g=i.end-s<h?0:1;return[$,e+o*p,t+o*d,"A",o,o,0,g,1,e+o*v,t+o*m,f?$:J,e+a*v,t+a*m,"A",a,a,0,g,0,e+a*p,t+a*d,f?"":"Z"]}},clipRect:function(e,t,n,r){var i,s=z+L++,o=this.createElement("clipPath").attr({id:s}).add(this.defs);return i=this.rect(e,t,n,r,0).add(o),i.id=s,i.clipPath=o,i},color:function(e,t,n){var r=this,i,s=/^rgba/,o,u,a,f,l,c,h,p,d,v,m=[];e&&e.linearGradient?o="linearGradient":e&&e.radialGradient&&(o="radialGradient");if(o){u=e[o],a=r.gradients,l=e.stops,p=t.radialReference,At(u)&&(e[o]=u={x1:u[0],y1:u[1],x2:u[2],y2:u[3],gradientUnits:"userSpaceOnUse"}),o==="radialGradient"&&p&&!Pt(u.gradientUnits)&&(u=Tt(u,{cx:p[0]-p[2]/2+u.cx*p[2],cy:p[1]-p[2]/2+u.cy*p[2],r:u.r*p[2],gradientUnits:"userSpaceOnUse"}));for(d in u)d!=="id"&&m.push(d,u[d]);for(d in l)m.push(l[d]);return m=m.join(","),a[m]?v=a[m].id:(u.id=v=z+L++,a[m]=f=r.createElement(o).attr(u).add(r.defs),f.stops=[],cn(l,function(e){var t;s.test(e[1])?(i=kn(e[1]),c=i.get("rgb"),h=i.get("a")):(c=e[1],h=1),t=r.createElement("stop"
).attr({offset:e[0],"stop-color":c,"stop-opacity":h}).add(f),f.stops.push(t)})),"url("+r.url+"#"+v+")"}return s.test(e)?(i=kn(e),Ht(t,n+"-opacity",i.get("a")),i.get("rgb")):(t.removeAttribute(n+"-opacity"),e)},text:function(e,t,n,r){var s=this,o=O.chart.style,u=T||!S&&s.forExport,a;return r&&!s.forExport?s.html(e,t,n):(t=i(jt(t,0)),n=i(jt(n,0)),a=s.createElement("text").attr({x:t,y:n,text:e}).css({fontFamily:o.fontFamily,fontSize:o.fontSize}),u&&a.css({position:q}),a.x=t,a.y=n,a)},html:function(e,t,n){var r=O.chart.style,s=this.createElement("span"),o=s.attrSetters,u=s.element,a=s.renderer;return o.text=function(e){return e!==u.innerHTML&&delete this.bBox,u.innerHTML=e,!1},o.x=o.y=o.align=function(e,t){return t==="align"&&(t="textAlign"),s[t]=e,s.htmlUpdateTransform(),!1},s.attr({text:e,x:i(t),y:i(n)}).css({position:q,whiteSpace:"nowrap",fontFamily:r.fontFamily,fontSize:r.fontSize}),s.css=s.htmlCss,a.isSVG&&(s.add=function(e){var t,n=a.box.parentNode,r,i=[];if(e){t=e.div;if(!t){r=e;while(r)i.push(r),r=r.parentGroup;cn(i.reverse(),function(e){var r;t=e.div=e.div||It(I,{className:Ht(e.element,"class")},{position:q,left:(e.translateX||0)+X,top:(e.translateY||0)+X},t||n),r=t.style,xt(e.attrSetters,{translateX:function(e){r.left=e+X},translateY:function(e){r.top=e+X},visibility:function(e,t){r[t]=e}})})}}else t=n;return t.appendChild(u),s.added=!0,s.alignOnAdd&&s.htmlUpdateTransform(),s}),s},fontMetrics:function(e){e=Ct(e||11);var t=e<24?e+4:i(e*1.2),n=i(t*.8);return{h:t,b:n}},label:function(t,n,r,s,o,u,a,f,l){function L(){var e,t,n=p.element.style;v=(b===undefined||w===undefined||h.styles.textAlign)&&p.getBBox(),h.width=(b||v.width||0)+2*g+y,h.height=(w||v.height||0)+2*g,N=g+c.fontMetrics(n&&n.fontSize).b,k&&(d||(e=i(-m*g),t=f?-N:0,h.box=d=s?c.symbol(s,e,t,h.width,h.height):c.rect(e,t,h.width,h.height,0,T[ft]),d.add(h)),d.attr(Tt({width:h.width,height:h.height},T)),T=null)}function A(){var e=h.styles,t=e&&e.textAlign,n=y+g*(1-m),r;r=f?0:N,Pt(b)&&(t==="center"||t==="right")&&(n+={center:.5,right:1}[t]*(b-v.width)),(n!==p.x||r!==p.y)&&p.attr({x:n,y:r}),p.x=n,p.y=r}function O(e,t){d?d.attr(e,t):T[e]=t}function M(){p.add(h),h.attr({text:t,x:n,y:r}),d&&Pt(o)&&h.attr({anchorX:o,anchorY:u})}var c=this,h=c.g(l),p=c.text("",0,0,a).attr({zIndex:1}),d,v,m=0,g=3,y=0,b,w,E,S,x=0,T={},N,C=h.attrSetters,k;vn(h,"add",M),C.width=function(e){return b=e,!1},C.height=function(e){return w=e,!1},C.padding=function(e){return Pt(e)&&e!==g&&(g=e,A()),!1},C.paddingLeft=function(e){return Pt(e)&&e!==y&&(y=e,A()),!1},C.align=function(e){return m={left:0,center:.5,right:1}[e],!1},C.text=function(e,t){return p.attr(t,e),L(),A(),!1},C[ft]=function(e,t){return k=!0,x=e%2/2,O(t,e),!1},C.stroke=C.fill=C.r=function(e,t){return t==="fill"&&(k=!0),O(t,e),!1},C.anchorX=function(e,t){return o=e,O(t,e+x-E),!1},C.anchorY=function(e,t){return u=e,O(t,e-S),!1},C.x=function(e){return h.x=e,e-=m*((b||v.width)+g),E=i(e),h.attr("translateX",E),!1},C.y=function(e){return S=h.y=i(e),h.attr("translateY",S),!1};var _=h.css;return xt(h,{css:function(t){if(t){var n={};t=Tt(t),cn(["fontSize","fontWeight","fontFamily","color","lineHeight","width"],function(r){t[r]!==e&&(n[r]=t[r],delete t[r])}),p.css(n)}return _.call(h,t)},getBBox:function(){return{width:v.width+2*g,height:v.height+2*g,x:v.x-g,y:v.y-g}},shadow:function(e){return d&&d.shadow(e),h},destroy:function(){mn(h,"add",M),mn(h.element,"mouseenter"),mn(h.element,"mouseleave"),p&&(p=p.destroy()),d&&(d=d.destroy()),Ln.prototype.destroy.call(h),h=c=L=A=O=M=null}})}},N=An;var On,Mn;if(!S&&!T){Highcharts.VMLElement=Mn={init:function(e,t){var n=this,r=["<",t,' filled="f" stroked="f"'],i=["position: ",q,";"],s=t===I;(t==="shape"||s)&&i.push("left:0;top:0;width:1px;height:1px;"),i.push("visibility: ",s?U:W),r.push(' style="',i.join(""),'"/>'),t&&(r=s||t==="span"||t==="img"?r.join(""):e.prepVML(r),n.element=It(r)),n.renderer=e,n.attrSetters={}},add:function(e){var t=this,n=t.renderer,r=t.element,i=n.box,s=e&&e.inverted,o=e?e.element||e:i;return s&&n.invertChild(r,o),o.appendChild(r),t.added=!0,t.alignOnAdd&&!t.deferUpdateTransform&&t.updateTransform(),gn(t,"add"),t},updateTransform:Ln.prototype.htmlUpdateTransform,attr:function(t,n){var r=this,s,o,a,f,h=r.element||{},d=h.style,v=h.nodeName,m=r.renderer,y=r.symbolName,b,w=r.shadows,E,S=r.attrSetters,x=r;kt(t)&&Pt(n)&&(s=t,t={},t[s]=n);if(kt(t))s=t,s==="strokeWidth"||s==="stroke-width"?x=r.strokeweight:x=r[s];else for(s in t){o=t[s],E=!1,f=S[s]&&S[s].call(r,o,s);if(f!==!1&&o!==null){f!==e&&(o=f);if(y&&/^(x|y|r|start|end|width|height|innerR|anchorX|anchorY)/.test(s))b||(r.symbolAttr(t),b=!0),E=!0;else if(s==="d"){o=o||[],r.d=o.join(" "),a=o.length;var T=[];while(a--)if(Ot(o[a]))T[a]=i(o[a]*10)-5;else if(o[a]==="Z")T[a]="x";else{T[a]=o[a];if(o[a]==="wa"||o[a]==="at")T[a+5]===T[a+7]&&(T[a+7]-=1),T[a+6]===T[a+8]&&(T[a+8]-=1)}o=T.join(" ")||"x",h.path=o;if(w){a=w.length;while(a--)w[a].path=w[a].cutOff?this.cutOffPath(o,w[a].cutOff):o}E=!0}else if(s==="visibility"){if(w){a=w.length;while(a--)w[a].style[s]=o}v==="DIV"&&(o=o===U?"-999em":0,g||(d[s]=o?U:W),s="top"),d[s]=o,E=!0}else if(s==="zIndex")o&&(d[s]=o),E=!0;else if(ln(s,["x","y","width","height"])!==-1)r[s]=o,s==="x"||s==="y"?s={x:"left",y:"top"}[s]:o=u(0,o),r.updateClipping?(r[s]=o,r.updateClipping()):d[s]=o,E=!0;else if(s==="class"&&v==="DIV")h.className=o;else if(s==="stroke")o=m.color(o,h,s),s="strokecolor";else if(s==="stroke-width"||s==="strokeWidth")h.stroked=o?!0:!1,s="strokeweight",r[s]=o,Ot(o)&&(o+=X);else if(s==="dashstyle"){var N=h.getElementsByTagName("stroke")[0]||It(m.prepVML(["<stroke/>"]),null,null,h);N[s]=o||"solid",r.dashstyle=o,E=!0}else s==="fill"?v==="SPAN"?d.color=o:v!=="IMG"&&(h.filled=o!==V?!0:!1,o=m.color(o,h,s,r),s="fillcolor"):s==="opacity"?E=!0:v==="shape"&&s==="rotation"?(r[s]=o,h.style.left=-i(c(o*p)+1)+X,h.style.top=i(l(o*p))+X):s==="translateX"||s==="translateY"||s==="rotation"?(r[s]=o,r.updateTransform(),E=!0):s==="text"&&(this.bBox=null,h.innerHTML=o,E=!0);E||(g?h[s]=o:Ht(h,s,o))}}return x},clip:function(e){var t=this,n,r;return e?(n=e.members,Dt(n,t),n.push(t),t.destroyClip=function(){Dt(n,t)},r=e.getCSS(t)):(t.destroyClip&&t.destroyClip(),r={clip:g?"inherit":"rect(auto)"}),t.css(r)},css:Ln.prototype.htmlCss,safeRemoveChild:function(e){e.parentNode&&tn(e)},destroy:function(){return this.destroyClip&&this.destroyClip(),Ln.prototype.destroy.apply(this)},on:function(e,t){return this.element["on"+e]=function(){var e=n.event;e.target=e.srcElement,t(e)},this},cutOffPath:function(e,t){var n;e=e.split(/[ ,]/),n=e.length;if(n===9||n===11)e[n-4]=e[n-2]=Ct(e[n-2])-10*t;return e.join(" ")},shadow:function(e,t,n){var r=[],i,s=this.element,o=this.renderer,u,a=s.style,f,l=s.path,c,h,p,d;l&&typeof l.value!="string"&&(l="x"),h=l;if(e){p=jt(e.width,3),d=(e.opacity||.15)/p;for(i=1;i<=3;i++)c=p*2+1-2*i,n&&(h=this.cutOffPath(l.value,c+.5)),f=['<shape isShadow="true" strokeweight="',c,'" filled="false" path="',h,'" coordsize="10 10" style="',s.style.cssText,'" />'],u=It(o.prepVML(f),null,{left:Ct(a.left)+jt(e.offsetX,1),top:Ct(a.top)+jt(e.offsetY,1)}),n&&(u.cutOff=c+1),f=['<stroke color="',e.color||"black",'" opacity="',d*i,'"/>'],It(o.prepVML(f),null,null,u),t?t.element.appendChild(u):s.parentNode.insertBefore(u,s),r.push(u);this.shadows=r}return this}},Mn=qt(Ln,Mn);var _n={Element:Mn,isIE8:d.indexOf("MSIE 8.0")>-1,init:function(e,n,r){var i=this,s,o;i.alignedObjects=[],s=i.createElement(I),o=s.element,o.style.position=R,e.appendChild(s.element),i.isVML=!0,i.box=o,i.boxWrapper=s,i.setSize(n,r,!1),t.namespaces.hcv||(t.namespaces.add("hcv","urn:schemas-microsoft-com:vml"),t.createStyleSheet().cssText="hcv\\:fill, hcv\\:path, hcv\\:shape, hcv\\:stroke{ behavior:url(#default#VML); display: inline-block; } ")},isHidden:function(){return!this.box.offsetWidth},clipRect:function(e,t,n,r){var s=this.createElement(),o=Lt(e);return xt(s,{members:[],left:o?e.x:e,top:o?e.y:t,width:o?e.width:n,height:o?e.height:r,getCSS:function(e){var t=e.element,n=t.nodeName,r=n==="shape",s=e.inverted,o=this,u=o.top-(r?t.offsetTop:0),a=o.left,f=a+o.width,l=u+o.height,c={clip:"rect("+i(s?a:u)+"px,"+i(s?l:f)+"px,"+i(s?f:l)+"px,"+i(s?u:a)+"px)"};return!s&&g&&n==="DIV"&&xt(c,{width:f+X,height:l+X}),c},updateClipping:function(){cn(s.members,function(e){e.css(s.getCSS(e))})}})},color:function(e,t,n,i){var s=this,o,u=/^rgba/,a,f,l=V;e&&e.linearGradient?f="gradient":e&&e.radialGradient&&(f="pattern");if(f){var c,p,d=e.linearGradient||e.radialGradient,v,m,g,y,b,w,E,S,x="",T=e.stops,N,C,k=[],L=function(){a=['<fill colors="'+k.join(",")+'" opacity="',w,'" o:opacity2="',b,'" type="',f,'" ',x,'focus="100%" method="any" />'],It(s.prepVML(a),null,null,t)};N=T[0],C=T[T.length-1],N[0]>0&&T.unshift([0,N[1]]),C[0]<1&&T.push([1,C[1]]),cn(T,function(e,t){u.test(e[1])?(o=kn(e[1]),c=o.get("rgb"),p=o.get("a")):(c=e[1],p=1),k.push(e[0]*100+"% "+c),t?(w=p,E=c):(b=p,S=c)});if(n==="fill")if(f==="gradient")v=d.x1||d[0]||0,m=d.y1||d[1]||0,g=d.x2||d[2]||0,y=d.y2||d[3]||0,x='angle="'+(90-r.atan((y-m)/(g-v))*180/h)+'"',L();else{var A=d.r,M=A*2,_=A*2,D=d.cx,P=d.cy,H=t.radialReference,B,j=function(){H&&(B=i.getBBox(),D+=(H[0]-B.x)/B.width-.5,P+=(H[1]-B.y)/B.height-.5,M*=H[2]/B.width,_*=H[2]/B.height),x='src="'+O.global.VMLRadialGradientURL+'" '+'size="'+M+","+_+'" '+'origin="0.5,0.5" '+'position="'+D+","+P+'" '+'color2="'+S+'" ',L()};i.added?j():vn(i,"add",j),l=E}else l=c}else if(u.test(e)&&t.tagName!=="IMG")o=kn(e),a=["<",n,' opacity="',o.get("a"),'"/>'],It(this.prepVML(a),null,null,t),l=o.get("rgb");else{var F=t.getElementsByTagName(n);F.length&&(F[0].opacity=1,F[0].type="solid"),l=e}return l},prepVML:function(e){var t="display:inline-block;behavior:url(#default#VML);",n=this.isIE8;return e=e.join(""),n?(e=e.replace("/>",' xmlns="urn:schemas-microsoft-com:vml" />'),e.indexOf('style="')===-1?e=e.replace("/>",' style="'+t+'" />'):e=e.replace('style="','style="'+t)):e=e.replace("<","<hcv:"),e},text:An.prototype.html,path:function(e){var t={coordsize:"10 10"};return At(e)?t.d=e:Lt(e)&&xt(t,e),this.createElement("shape").attr(t)},circle:function(e,t,n){return Lt(e)&&(n=e.r,t=e.y,e=e.x),this.symbol("circle").attr({x:e-n,y:t-n,width:2*n,height:2*n})},g:function(e){var t,n;return e&&(n={className:z+e,"class":z+e}),t=this.createElement(I).attr(n),t},image:function(e,t,n,r,i){var s=this.createElement("img").attr({src:e});return arguments.length>1&&s.attr({x:t,y:n,width:r,height:i}),s},rect:function(e,t,n,r,i,s){Lt(e)&&(t=e.y,n=e.width,r=e.height,s=e.strokeWidth,e=e.x);var o=this.symbol("rect");return o.r=i,o.attr(o.crisp(s,e,t,u(n,0),u(r,0)))},invertChild:function(e,t){var n=t.style;Ft(e,{flip:"x",left:Ct(n.width)-1,top:Ct(n.height)-1,rotation:-90})},symbols:{arc:function(e,t,n,r,i){var s=i.start,o=i.end,u=i.r||n||r,a=i.innerR,f=l(s),h=c(s),p=l(o),d=c(o),v;return o-s===0?["x"]:(v=["wa",e-u,t-u,e+u,t+u,e+u*f,t+u*h,e+u*p,t+u*d],i.open&&!a&&v.push("e",$,e,t),v.push("at",e-a,t-a,e+a,t+a,e+a*p,t+a*d,e+a*f,t+a*h,"x","e"),v)},circle:function(e,t,n,r){return["wa",e,t,e+n,t+r,e+n,t+r/2,e+n,t+r/2,"e"]},rect:function(e,t,n,r,i){var s=e+n,o=t+r,u,f;return!Pt(i)||!i.r?u=An.prototype.symbols.square.apply(0,arguments):(f=a(i.r,n,r),u=[$,e+f,t,J,s-f,t,"wa",s-2*f,t,s,t+2*f,s-f,t,s,t+f,J,s,o-f,"wa",s-2*f,o-2*f,s,o,s,o-f,s-f,o,J,e+f,o,"wa",e,o-2*f,e+2*f,o,e+f,o,e,o-f,J,e,t+f,"wa",e,t,e+2*f,t+2*f,e,t+f,e+f,t,"x","e"]),u}}};Highcharts.VMLRenderer=On=function(){this.init.apply(this,arguments)},On.prototype=Tt(An.prototype,_n),N=On}var Dn,Pn;T&&(Highcharts.CanVGRenderer=Dn=function(){E="http://www.w3.org/1999/xhtml"},Dn.prototype.symbols={},Pn=function(){function t(){var t=e.length,n;for(n=0;n<t;n++)e[n]();e=[]}var e=[];return{push:function(n,r){e.length===0&&fn(r,t),e.push(n)}}}(),N=Dn),Hn.prototype={addLabel:function(){var e=this,t=e.axis,n=t.options,r=t.chart,s=t.horiz,o=t.categories,a=t.series[0]&&t.series[0].names,f=e.pos,l=n.labels,c,h=t.tickPositions,p=s&&o&&!l.step&&!l.staggerLines&&!l.rotation&&r.plotWidth/h.length||!s&&r.plotWidth/2,d=f===h[0],v=f===h[h.length-1],m,g,y=o?jt(o[f],a&&a[f],f):f,b=e.label,w=h.info,E;t.isDatetimeAxis&&w&&(E=n.dateTimeLabelFormats[w.higherRanks[f]||w.unitName]),e.isFirst=d,e.isLast=v,c=t.labelFormatter.call({axis:t,chart:r,isFirst:d,isLast:v,dateTimeLabelFormat:E,value:t.isLog?rn(_t(y)):y}),m=p&&{width:u(1,i(p-2*(l.padding||10)))+X},m=xt(m,l.style),Pt(b)?b&&b.attr({text:c}).css(m):(g={align:l.align},Ot(l.rotation)&&(g.rotation=l.rotation),e.label=Pt(c)&&l.enabled?r.renderer.text(c,0,0,l.useHTML).attr(g).css(m).add(t.labelGroup):null)},getLabelSize:function(){var e=this.label,t=this.axis;return e?(this.labelBBox=e.getBBox())[t.horiz?"height":"width"]:0},getLabelSides:function(){var e=this.labelBBox,t=this.axis,n=t.options,r=n.labels,i=e.width,s=i*{left:0,center:.5,right:1}[r.align]-r.x;return[-s,i-s]},handleOverflow:function(e,t){var n=!0,r=this.axis,i=r.chart,s=this.isFirst,o=this.isLast,u=t.x,a=r.reversed,f=r.tickPositions;if(s||o){var l=this.getLabelSides(),c=l[0],h=l[1],p=i.plotLeft,d=p+r.len,v=r.ticks[f[e+(s?1:-1)]],m=v&&v.label.xy&&v.label.xy.x+v.getLabelSides()[s?0:1];s&&!a||o&&a?u+c<p&&(u=p-c,v&&u+h>m&&(n=!1)):u+h>d&&(u=d-h,v&&u+c<m&&(n=!1)),t.x=u}return n},getPosition:function(e,t,n,r){var i=this.axis,s=i.chart,o=r&&s.oldChartHeight||s.chartHeight;return{x:e?i.translate(t+n,null,null,r)+i.transB:i.left+i.offset+(i.opposite?(r&&s.oldChartWidth||s.chartWidth)-i.right-i.left:0),y:e?o-i.bottom+i.offset-(i.opposite?i.height:0):o-i.translate(t+n,null,null,r)-i.transB}},getLabelPosition:function(e,t,n,r,i,s,o,u){var a=this.axis,f=a.transA,l=a.reversed,c=a.staggerLines;return e=e+i.x-(s&&r?s*f*(l?-1:1):0),t=t+i.y-(s&&!r?s*f*(l?1:-1):0),Pt(i.y)||(t+=Ct(n.styles.lineHeight)*.9-n.getBBox().height/2),c&&(t+=o/(u||1)%c*16),{x:e,y:t}},getMarkPath:function(e,t,n,r,i,s){return s.crispLine([$,e,t,J,e+(i?0:-n),t+(i?n:0)],r)},render:function(t,n,r){var i=this,s=i.axis,o=s.options,u=s.chart,a=u.renderer,f=s.horiz,l=i.type,c=i.label,h=i.pos,p=o.labels,d=i.gridLine,v=l?l+"Grid":"grid",m=l?l+"Tick":"tick",g=o[v+"LineWidth"],y=o[v+"LineColor"],b=o[v+"LineDashStyle"],w=o[m+"Length"],E=o[m+"Width"]||0,S=o[m+"Color"],x=o[m+"Position"],T,N=i.mark,C,k=p.step,L,A=!0,O=s.tickmarkOffset,M=i.getPosition(f,h,O,n),_=M.x,D=M.y,P=f&&_===s.pos||!f&&D===s.pos+s.len?-1:1,H=s.staggerLines;this.isActive=!0,g&&(T=s.getPlotLinePath(h+O,g*P,n,!0),d===e&&(L={stroke:y,"stroke-width":g},b&&(L.dashstyle=b),l||(L.zIndex=1),n&&(L.opacity=0),i.gridLine=d=g?a.path(T).attr(L).add(s.gridGroup):null),!n&&d&&T&&d[i.isNew?"attr":"animate"]({d:T,opacity:r})),E&&w&&(x==="inside"&&(w=-w),s.opposite&&(w=-w),C=i.getMarkPath(_,D,w,E*P,f,a),N?N.animate({d:C,opacity:r}):i.mark=a.path(C).attr({stroke:S,"stroke-width":E,opacity:r}).add(s.axisGroup)),c&&!isNaN(_)&&(c.xy=M=i.getLabelPosition(_,D,c,f,p,O,t,k),i.isFirst&&!jt(o.showFirstLabel,1)||i.isLast&&!jt(o.showLastLabel,1)?A=!1:!H&&f&&p.overflow==="justify"&&!i.handleOverflow(t,M)&&(A=!1),k&&t%k&&(A=!1),A&&!isNaN(M.y)?(M.opacity=r,c[i.isNew?"attr":"animate"](M),i.isNew=!1):c.attr("y",-9999))},destroy:function(){en(this,this.axis)}},Bn.prototype={render:function(){var e=this,t=e.axis,n=t.horiz,r=(t.pointRange||0)/2,i=e.options,s=i.label,o=e.label,f=i.width,l=i.to,c=i.from,h=Pt(c)&&Pt(l),p=i.value,d=i.dashStyle,v=e.svgElem,m=[],g,y,b,w,E,S,x=i.color,T=i.zIndex,N=i.events,C,k=t.chart.renderer;t.isLog&&(c=Mt(c),l=Mt(l),p=Mt(p));if(f)m=t.getPlotLinePath(p,f),C={stroke:x,"stroke-width":f},d&&(C.dashstyle=d);else{if(!h)return;c=u(c,t.min-r),l=a(l,t.max+r),m=t.getPlotBandPath(c,l,i),C={fill:x},i.borderWidth&&(C.stroke=i.borderColor,C["stroke-width"]=i.borderWidth)}Pt(T)&&(C.zIndex=T);if(v)m?v.animate({d:m},null,v.onGetPath):(v.hide(),v.onGetPath=function(){v.show()});else if(m&&m.length){e.svgElem=v=k.path(m).attr(C).add();if(N){g=function(t){v.on(t,function(n){N[t].apply(e,[n])})};for(y in N)g(y)}}return s&&Pt(s.text)&&m&&m.length&&t.width>0&&t.height>0?(s=Tt({align:n&&h&&"center",x:n?!h&&4:10,verticalAlign:!n&&h&&"middle",y:n?h?16:10:h?6:-4,rotation:n&&!h&&90},s),o||(e.label=o=k.text(s.text,0,0).attr({align:s.textAlign||s.align,rotation:s.rotation,zIndex:T}).css(s.style).add()),b=[m[1],m[4],jt(m[6],m[1])],w=[m[2],m[5],jt(m[7],m[2])],E=Yt(b),S=Yt(w),o.align(s,!1,{x:E,y:S,width:Zt(b)-E,height:Zt(w)-S}),o.show()):o&&o.hide(),e},destroy:function(){var e=this,t=e.axis;Dt(t.plotLinesAndBands,e),en(e,this.axis)}},jn.prototype={destroy:function(){en(this,this.axis)},setTotal:function(e){this.total=e,this.cum=e},render:function(e){var t=this.options,n=t.formatter.call(this);this.label?this.label.attr({text:n,visibility:U}):this.label=this.axis.chart.renderer.text(n,0,0,t.useHTML).css(t.style).attr({align:this.textAlign,rotation:t.rotation,visibility:U}).add(e)},setOffset:function(e,t){var n=this,r=n.axis,i=r.chart,s=i.inverted,o=this.isNegative,u=r.translate(this.percent?100:this.total,0,0,0,1),a=r.translate(0),l=f(u-a),c=i.xAxis[0].translate(this.x)+e,h=i.plotHeight,p={x:s?o?u:u-l:c,y:s?h-c-t:o?h-u-l:h-u,width:s?l:t,height:s?t:l},d=this.label,v;d&&(d.align(this.alignOptions,null,p),v=d.alignAttr,d.attr({visibility:this.options.crop===!1||i.isInsidePlot(v.x,v.y)?S?"inherit":W:U}))}},Fn.prototype={defaultOptions:{dateTimeLabelFormats:{millisecond:"%H:%M:%S.%L",second:"%H:%M:%S",minute:"%H:%M",hour:"%H:%M",day:"%e. %b",week:"%e. %b",month:"%b '%y",year:"%Y"},endOnTick:!1,gridLineColor:"#C0C0C0",labels:En,lineColor:"#C0D0E0",lineWidth:1,minPadding:.01,maxPadding:.01,minorGridLineColor:"#E0E0E0",minorGridLineWidth:1,minorTickColor:"#A0A0A0",minorTickLength:2,minorTickPosition:"outside",startOfWeek:1,startOnTick:!1,tickColor:"#C0D0E0",tickLength:5,tickmarkPlacement:"between",tickPixelInterval:100,tickPosition:"outside",tickWidth:1,title:{align:"middle",style:{color:"#4d759e",fontWeight:"bold"}},type:"linear"},defaultYAxisOptions:{endOnTick:!0,gridLineWidth:1,tickPixelInterval:72,showLastLabel:!0,labels:{align:"right",x:-8,y:3},lineWidth:0,maxPadding:.05,minPadding:.05,startOnTick:!0,tickWidth:0,title:{rotation:270,text:"Values"},stackLabels:{enabled:!1,formatter:function(){return this.total},style:En.style}},defaultLeftAxisOptions:{labels:{align:"right",x:-8,y:null},title:{rotation:270}},defaultRightAxisOptions:{labels:{align:"left",x:8,y:null},title:{rotation:90}},defaultBottomAxisOptions:{labels:{align:"center",x:0,y:14},title:{rotation:0}},defaultTopAxisOptions:{labels:{align:"center",x:0,y:-5},title:{rotation:0}},init:function(t,n){var r=n.isX,i=this;i.horiz=t.inverted?!r:r,i.isXAxis=r,i.xOrY=r?"x":"y",i.opposite=n.opposite,i.side=i.horiz?i.opposite?0:2:i.opposite?1:3,i.setOptions(n);var s=this.options,o=s.type,u=o==="datetime";i.labelFormatter=s.labels.formatter||i.defaultLabelFormatter,i.staggerLines=i.horiz&&s.labels.staggerLines,i.userOptions=n,i.minPixelPadding=0,i.chart=t,i.reversed=s.reversed,i.zoomEnabled=s.zoomEnabled!==!1,i.categories=s.categories||o==="category",i.isLog=o==="logarithmic",i.isDatetimeAxis=u,i.isLinked=Pt(s.linkedTo),i.tickmarkOffset=i.categories&&s.tickmarkPlacement==="between"?.5:0,i.ticks={},i.minorTicks={},i.plotLinesAndBands=[],i.alternateBands={},i.len=0,i.minRange=i.userMinRange=s.minRange||s.maxZoom,i.range=s.range,i.offset=s.offset||0,i.stacks={},i._stacksTouched=0,i.max=null,i.min=null;var a,f=i.options.events;ln(i,t.axes)===-1&&(t.axes.push(i),t[r?"xAxis":"yAxis"].push(i)),i.series=i.series||[],t.inverted&&r&&i.reversed===e&&(i.reversed=!0),i.removePlotBand=i.removePlotBandOrLine,i.removePlotLine=i.removePlotBandOrLine;for(a in f)vn(i,a,f[a]);i.isLog&&(i.val2lin=Mt,i.lin2val=_t)},setOptions:function(e){this.options=Tt(this.defaultOptions,this.isXAxis?{}:this.defaultYAxisOptions,[this.defaultTopAxisOptions,this.defaultRightAxisOptions,this.defaultBottomAxisOptions,this.defaultLeftAxisOptions][this.side],Tt(O[this.isXAxis?"xAxis":"yAxis"],e))},update:function(e,t){var n=this.chart;e=n.options[this.xOrY+"Axis"][this.options.index]=Tt(this.userOptions,e),this.destroy(),this._addedPlotLB=!1,this.init(n,e),n.isDirtyBox=!0,jt(t,!0)&&n.redraw()},remove:function(e){var t=this.chart,n=this.xOrY+"Axis";cn(this.series,function(e){e.remove(!1)}),Dt(t.axes,this),Dt(t[n],this),t.options[n].splice(this.options.index,1),this.destroy(),t.isDirtyBox=!0,jt(e,!0)&&t.redraw()},defaultLabelFormatter:function(){var t=this.axis,n=this.value,r=t.categories,i=this.dateTimeLabelFormat,s=O.lang.numericSymbols,o=s&&s.length,u,a,f=t.options.labels.format,l=t.isLog?n:t.tickInterval;if(f)a=Vt(f,this);else if(r)a=n;else if(i)a=M(i,n);else if(o&&l>=1e3)while(o--&&a===e)u=Math.pow(1e3,o+1),l>=u&&s[o]!==null&&(a=Ut(n/u,-1)+s[o]);return a===e&&(n>=1e3?a=Ut(n,0):a=Ut(n,-1)),a},getSeriesExtremes:function(){var t=this,n=t.chart,r=t.stacks,i=[],s=[],o=t._stacksTouched=t._stacksTouched+1,f,l;t.hasVisibleSeries=!1,t.dataMin=t.dataMax=null,cn(t.series,function(f){if(f.visible||!n.options.chart.ignoreHiddenSeries){var c=f.options,h,p,d,v,m,g,y,b,w,E,S=c.threshold,x,T=[],N,C,k=0;t.hasVisibleSeries=!0,t.isLog&&S<=0&&(S=c.threshold=null);if(t.isXAxis)y=f.xData,y.length&&(t.dataMin=a(jt(t.dataMin,y[0]),Yt(y)),t.dataMax=u(jt(t.dataMax,y[0]),Zt(y)));else{var L,A,O,M=f.cropped,_=f.xAxis.getExtremes(),D,P=!!f.modifyValue;h=c.stacking,t.usePercentage=h==="percent",h&&(m=c.stack,v=f.type+jt(m,""),g="-"+v,f.stackKey=v,p=i[v]||[],i[v]=p,d=s[g]||[],s[g]=d),t.usePercentage&&(t.dataMin=0,t.dataMax=99),y=f.processedXData,b=f.processedYData,x=b.length;for(l=0;l<x;l++){w=y[l],E=b[l],h&&(L=E<S,A=L?d:p,O=L?g:v,Pt(A[w])?(A[w]=rn(A[w]+E),E=[E,A[w]]):A[w]=E,r[O]||(r[O]={}),r[O][w]||(r[O][w]=new jn(t,t.options.stackLabels,L,w,m,h)),r[O][w].setTotal(A[w]),r[O][w].touched=o);if(E!==null&&E!==e&&(!t.isLog||E>0)){P&&(E=f.modifyValue(E));if(f.getExtremesFromAll||M||(y[l+1]||w)>=_.min&&(y[l-1]||w)<=_.max){D=E.length;if(D)while(D--)E[D]!==null&&(T[k++]=E[D]);else T[k++]=E}}}!t.usePercentage&&T.length&&(f.dataMin=N=Yt(T),f.dataMax=C=Zt(T),t.dataMin=a(jt(t.dataMin,N),N),t.dataMax=u(jt(t.dataMax,C),C)),Pt(S)&&(t.dataMin>=S?(t.dataMin=S,t.ignoreMinPadding=!0):t.dataMax<S&&(t.dataMax=S,t.ignoreMaxPadding=!0))}}});for(f in r)for(l in r[f])r[f][l].touched<o&&(r[f][l].destroy(),delete r[f][l])},translate:function(e,t,n,r,i,s){var o=this,u=o.len,a=1,f=0,l=r?o.oldTransA:o.transA,c=r?o.oldMin:o.min,h,p=o.minPixelPadding,d=(o.options.ordinal||o.isLog&&i)&&o.lin2val;return l||(l=o.transA),n&&(a*=-1,f=u),o.reversed&&(a*=-1,f-=a*u),t?(e=e*a+f,e-=p,h=e/l+c,d&&(h=o.lin2val(h))):(d&&(e=o.val2lin(e)),h=a*(e-c)*l+f+a*p+(s?l*o.pointRange/2:0)),h},toPixels:function(e,t){return this.translate(e,!1,!this.horiz,null,!0)+(t?0:this.pos)},toValue:function(e,t){return this.translate(e-(t?0:this.pos),!0,!this.horiz,null,!0)},getPlotLinePath:function(e,t,n,r){var s=this,o=s.chart,u=s.left,a=s.top,f,l,c,h,p=s.translate(e,null,null,n),d=n&&o.oldChartHeight||o.chartHeight,v=n&&o.oldChartWidth||o.chartWidth,m,g=s.transB;f=c=i(p+g),l=h=i(d-p-g);if(isNaN(p))m=!0;else if(s.horiz){l=a,h=d-s.bottom;if(f<u||f>u+s.width)m=!0}else{f=u,c=v-s.right;if(l<a||l>a+s.height)m=!0}return m&&!r?null:o.renderer.crispLine([$,f,l,J,c,h],t||0)},getPlotBandPath:function(e,t){var n=this.getPlotLinePath(t),r=this.getPlotLinePath(e);return r&&n?r.push(n[4],n[5],n[1],n[2]):r=null,r},getLinearTickPositions:function(e,t,n){var r,i,u=rn(s(t/e)*e),a=rn(o(n/e)*e),f=[];r=u;while(r<=a){f.push(r),r=rn(r+e);if(r===i)break;i=r}return f},getLogTickPositions:function(e,t,n,o){var u=this,a=u.options,f=u.len,l=[];o||(u._minorAutoInterval=null);if(e>=.5)e=i(e),l=u.getLinearTickPositions(e,t,n);else if(e>=.08){var c=s(t),h,p,d,v,m,g,y;e>.3?h=[1,2,4]:e>.15?h=[1,2,4,6,8]:h=[1,2,3,4,5,6,7,8,9];for(p=c;p<n+1&&!y;p++){v=h.length;for(d=0;d<v&&!y;d++)m=Mt(_t(p)*h[d]),m>t&&g<=n&&l.push(g),g>n&&(y=!0),g=m}}else{var b=_t(t),w=_t(n),E=a[o?"minorTickInterval":"tickInterval"],S=E==="auto"?null:E,x=a.tickPixelInterval/(o?5:1),T=o?f/u.tickPositions.length:f;e=jt(S,u._minorAutoInterval,(w-b)*x/(T||1)),e=$t(e,null,r.pow(10,s(r.log(e)/r.LN10))),l=dn(u.getLinearTickPositions(e,b,w),Mt),o||(u._minorAutoInterval=e/5)}return o||(u.tickInterval=e),l},getMinorTickPositions:function(){var e=this,t=e.options,n=e.tickPositions,r=e.minorTickInterval,i=[],s,o,u;if(e.isLog){u=n.length;for(o=1;o<u;o++)i=i.concat(e.getLogTickPositions(r,n[o-1],n[o],!0))}else if(e.isDatetimeAxis&&t.minorTickInterval==="auto")i=i.concat(Kt(Jt(r),e.min,e.max,t.startOfWeek)),i[0]<e.min&&i.shift();else for(s=e.min+(n[0]-e.min)%r;s<=e.max;s+=r)i.push(s);return i},adjustForMinRange:function(){var t=this,n=t.options,r=t.min,i=t.max,s,o=t.dataMax-t.dataMin>=t.minRange,u,f,l,c,h,p,d;t.isXAxis&&t.minRange===e&&!t.isLog&&(Pt(n.min)||Pt(n.max)?t.minRange=null:(cn(t.series,function(t){c=t.xData,h=t.xIncrement?1:c.length-1;for(f=h;f>0;f--){l=c[f]-c[f-1];if(u===e||l<u)u=l}}),t.minRange=a(u*5,t.dataMax-t.dataMin)));if(i-r<t.minRange){var v=t.minRange;s=(v-i+r)/2,p=[r-s,jt(n.min,r-s)],o&&(p[2]=t.dataMin),r=Zt(p),d=[r+v,jt(n.max,r+v)],o&&(d[2]=t.dataMax),i=Yt(d),i-r<v&&(p[0]=i-v,p[1]=jt(n.min,i-v),r=Zt(p))}t.min=r,t.max=i},setAxisTranslation:function(e){var t=this,n=t.max-t.min,r=0,i,s=0,o=0,f=t.linkedParent,l=t.transA;t.isXAxis&&(f?(s=f.minPointOffset,o=f.pointRangePadding):cn(t.series,function(e){var t=e.pointRange,f=e.options.pointPlacement,l=e.closestPointRange;t>n&&(t=0),r=u(r,t),s=u(s,f?0:t/2),o=u(o,f==="on"?0:t),!e.noSharedTooltip&&Pt(l)&&(i=Pt(i)?a(i,l):l)}),t.minPointOffset=s,t.pointRangePadding=o,t.pointRange=a(r,n),t.closestPointRange=i),e&&(t.oldTransA=l),t.translationSlope=t.transA=l=t.len/(n+o||1),t.transB=t.horiz?t.left:t.bottom,t.minPixelPadding=l*s},setTickPositions:function(e){var t=this,n=t.chart,i=t.options,o=t.isLog,f=t.isDatetimeAxis,l=t.isXAxis,c=t.isLinked,h=t.options.tickPositioner,p,d=i.maxPadding,v=i.minPadding,m,g,y=i.tickInterval,b=i.minTickInterval,w=i.tickPixelInterval,E,S=t.categories;c?(t.linkedParent=n[l?"xAxis":"yAxis"][i.linkedTo],g=t.linkedParent.getExtremes(),t.min=jt(g.min,g.dataMin),t.max=jt(g.max,g.dataMax),i.type!==t.linkedParent.options.type&&nn(11,1)):(t.min=jt(t.userMin,i.min,t.dataMin),t.max=jt(t.userMax,i.max,t.dataMax)),o&&(!e&&a(t.min,jt(t.dataMin,t.min))<=0&&nn(10,1),t.min=rn(Mt(t.min)),t.max=rn(Mt(t.max))),t.range&&(t.userMin=t.min=u(t.min,t.max-t.range),t.userMax=t.max,e&&(t.range=null)),t.beforePadding&&t.beforePadding(),t.adjustForMinRange(),!S&&!t.usePercentage&&!c&&Pt(t.min)&&Pt(t.max)&&(m=t.max-t.min,m&&(!Pt(i.min)&&!Pt(t.userMin)&&v&&(t.dataMin<0||!t.ignoreMinPadding)&&(t.min-=m*v),!Pt(i.max)&&!Pt(t.userMax)&&d&&(t.dataMax>0||!t.ignoreMaxPadding)&&(t.max+=m*d))),t.min===t.max||t.min===undefined||t.max===undefined?t.tickInterval=1:c&&!y&&w===t.linkedParent.options.tickPixelInterval?t.tickInterval=t.linkedParent.tickInterval:t.tickInterval=jt(y,S?1:(t.max-t.min)*w/(t.len||1)),l&&!e&&cn(t.series,function(e){e.processData(t.min!==t.oldMin||t.max!==t.oldMax)}),t.setAxisTranslation(!0),t.beforeSetTickPositions&&t.beforeSetTickPositions(),t.postProcessTickInterval&&(t.tickInterval=t.postProcessTickInterval(t.tickInterval)),!y&&t.tickInterval<b&&(t.tickInterval=b),!f&&!o&&(p=r.pow(10,s(r.log(t.tickInterval)/r.LN10)),y||(t.tickInterval=$t(t.tickInterval,null,p,i))),t.minorTickInterval=i.minorTickInterval==="auto"&&t.tickInterval?t.tickInterval/5:i.minorTickInterval,t.tickPositions=E=i.tickPositions?[].concat(i.tickPositions):h&&h.apply(t,[t.min,t.max]),E||(f?E=(t.getNonLinearTimeTicks||Kt)(Jt(t.tickInterval,i.units),t.min,t.max,i.startOfWeek,t.ordinalPositions,t.closestPointRange,!0):o?E=t.getLogTickPositions(t.tickInterval,t.min,t.max):E=t.getLinearTickPositions(t.tickInterval,t.min,t.max),t.tickPositions=E);if(!c){var x=E[0],T=E[E.length-1],N=t.minPointOffset||0,C;i.startOnTick?t.min=x:t.min-N>x&&E.shift(),i.endOnTick?t.max=T:t.max+N<T&&E.pop(),E.length===1&&(C=.001,t.min-=C,t.max+=C)}},setMaxTicks:function(){var e=this.chart,t=e.maxTicks||{},n=this.tickPositions,r=this._maxTicksKey=[this.xOrY,this.pos,this.len].join("-");!this.isLinked&&!this.isDatetimeAxis&&n&&n.length>(t[r]||0)&&this.options.alignTicks!==!1&&(t[r]=n.length),e.maxTicks=t},adjustTickAmount:function(){var e=this,t=e.chart,n=e._maxTicksKey,r=e.tickPositions,i=t.maxTicks;if(i&&i[n]&&!e.isDatetimeAxis&&!e.categories&&!e.isLinked&&e.options.alignTicks!==!1){var s=e.tickAmount,o=r.length,u;e.tickAmount=u=i[n];if(o<u){while(r.length<u)r.push(rn(r[r.length-1]+e.tickInterval));e.transA*=(o-1)/(u-1),e.max=r[r.length-1]}Pt(s)&&u!==s&&(e.isDirty=!0)}},setScale:function(){var e=this,t=e.stacks,n,r,i,s;e.oldMin=e.min,e.oldMax=e.max,e.oldAxisLength=e.len,e.setAxisSize(),s=e.len!==e.oldAxisLength,cn(e.series,function(e){if(e.isDirtyData||e.isDirty||e.xAxis.isDirty)i=!0});if(s||i||e.isLinked||e.forceRedraw||e.userMin!==e.oldUserMin||e.userMax!==e.oldUserMax)e.forceRedraw=!1,e.getSeriesExtremes(),e.setTickPositions(),e.oldUserMin=e.userMin,e.oldUserMax=e.userMax,e.isDirty||(e.isDirty=s||e.min!==e.oldMin||e.max!==e.oldMax);if(!e.isXAxis)for(n in t)for(r in t[n])t[n][r].cum=t[n][r].total;e.setMaxTicks()},setExtremes:function(e,t,n,r,i){var s=this,o=s.chart;n=jt(n,!0),i=xt(i,{min:e,max:t}),gn(s,"setExtremes",i,function(){s.userMin=e,s.userMax=t,s.isDirtyExtremes=!0,n&&o.redraw(r)})},zoom:function(t,n){return t<=this.dataMin&&(t=e),n>=this.dataMax&&(n=e),this.displayBtn=t!==e||n!==e,this.setExtremes(t,n,!1,e,{trigger:"zoom"}),!0},setAxisSize:function(){var e=this.chart,t=this.options,n=t.offsetLeft||0,r=t.offsetRight||0,i=this.horiz,s,o,a,f;this.left=f=jt(t.left,e.plotLeft+n),this.top=a=jt(t.top,e.plotTop),this.width=s=jt(t.width,e.plotWidth-n+r),this.height=o=jt(t.height,e.plotHeight),this.bottom=e.chartHeight-o-a,this.right=e.chartWidth-s-f,this.len=u(i?s:o,0),this.pos=i?f:a},getExtremes:function(){var e=this,t=e.isLog;return{min:t?rn(_t(e.min)):e.min,max:t?rn(_t(e.max)):e.max,dataMin:e.dataMin,dataMax:e.dataMax,userMin:e.userMin,userMax:e.userMax}},getThreshold:function(e){var t=this,n=t.isLog,r=n?_t(t.min):t.min,i=n?_t(t.max):t.max;return r>e||e===null?e=r:i<e&&(e=i),t.translate(e,0,1,0,1)},addPlotBand:function(e){this.addPlotBandOrLine(e,"plotBands")},addPlotLine:function(e){this.addPlotBandOrLine(e,"plotLines")},addPlotBandOrLine:function(e,t){var n=(new Bn(this,e)).render(),r=this.userOptions;return t&&(r[t]=r[t]||[],r[t].push(e)),this.plotLinesAndBands.push(n),n},getOffset:function(){var e=this,t=e.chart,n=t.renderer,r=e.options,i=e.tickPositions,s=e.ticks,o=e.horiz,a=e.side,f=t.inverted?[1,0,3,2][a]:a,l,c,h=0,p,d=0,v=r.title,m=r.labels,g=0,y=t.axisOffset,b=t.clipOffset,w=[-1,1,1,-1][a],E;e.hasData=l=e.hasVisibleSeries||Pt(e.min)&&Pt(e.max)&&!!i,e.showAxis=c=l||jt(r.showEmpty,!0),e.axisGroup||(e.gridGroup=n.g("grid").attr({zIndex:r.gridZIndex||1}).add(),e.axisGroup=n.g("axis").attr({zIndex:r.zIndex||2}).add(),e.labelGroup=n.g("axis-labels").attr({zIndex:m.zIndex||7}).add());if(l||e.isLinked)cn(i,function(t){s[t]?s[t].addLabel():s[t]=new Hn(e,t)}),cn(i,function(e){if(a===0||a===2||{1:"left",3:"right"}[a]===m.align)g=u(s[e].getLabelSize(),g)}),e.staggerLines&&(g+=(e.staggerLines-1)*16);else for(E in s)s[E].destroy(),delete s[E];v&&v.text&&v.enabled!==!1&&(e.axisTitle||(e.axisTitle=n.text(v.text,0,0,v.useHTML).attr({zIndex:7,rotation:v.rotation||0,align:v.textAlign||{low:"left",middle:"center",high:"right"}[v.align]}).css(v.style).add(e.axisGroup),e.axisTitle.isNew=!0),c&&(h=e.axisTitle.getBBox()[o?"height":"width"],d=jt(v.margin,o?5:10),p=v.offset),e.axisTitle[c?"show":"hide"]()),e.offset=w*jt(r.offset,y[a]),e.axisTitleMargin=jt(p,g+d+(a!==2&&g&&w*r.labels[o?"y":"x"])),y[a]=u(y[a],e.axisTitleMargin+h+w*e.offset),b[f]=u(b[f],r.lineWidth)},getLinePath:function(e){var t=this.chart,n=this.opposite,r=this.offset,i=this.horiz,s=this.left+(n?this.width:0)+r,o=t.chartHeight-this.bottom-(n?this.height:0)+r;return this.lineTop=o,n||(e*=-1),t.renderer.crispLine([$,i?this.left:s,i?o:this.top,J,i?t.chartWidth-this.right:s,i?o:t.chartHeight-this.bottom],e)},getTitlePosition:function(){var e=this.horiz,t=this.left,n=this.top,r=this.len,i=this.options.title,s=e?t:n,o=this.opposite,u=this.offset,a=Ct(i.style.fontSize||12),f={low:s+(e?0:r),middle:s+r/2,high:s+(e?r:0)}[i.align],l=(e?n+this.height:t)+(e?1:-1)*(o?-1:1)*this.axisTitleMargin+(this.side===2?a:0);return{x:e?f:l+(o?this.width:0)+u+(i.x||0),y:e?l-(o?this.height:0)+u:f+(i.y||0)}},render:function(){var t=this,n=t.chart,r=n.renderer,i=t.options,s=t.isLog,o=t.isLinked,u=t.tickPositions,a=t.axisTitle,f=t.stacks,l=t.ticks,c=t.minorTicks,h=t.alternateBands,p=i.stackLabels,d=i.alternateGridColor,v=t.tickmarkOffset,m=i.lineWidth,g,y=n.hasRendered,b=y&&Pt(t.oldMin)&&!isNaN(t.oldMin),w=t.hasData,E=t.showAxis,S,x;if(w||o)cn([l,c,h],function(e){var t;for(t in e)e[t].isActive=!1}),t.minorTickInterval&&!t.categories&&cn(t.getMinorTickPositions(),function(e){c[e]||(c[e]=new Hn(t,e,"minor")),b&&c[e].isNew&&c[e].render(null,!0),c[e].render(null,!1,1)}),u.length&&(cn(u.
slice(1).concat([u[0]]),function(e,n){n=n===u.length-1?0:n+1;if(!o||e>=t.min&&e<=t.max)l[e]||(l[e]=new Hn(t,e)),b&&l[e].isNew&&l[e].render(n,!0),l[e].render(n,!1,1)}),v&&t.min===0&&(l[-1]||(l[-1]=new Hn(t,-1,null,!0)),l[-1].render(-1))),d&&cn(u,function(n,r){r%2===0&&n<t.max&&(h[n]||(h[n]=new Bn(t)),S=n+v,x=u[r+1]!==e?u[r+1]+v:t.max,h[n].options={from:s?_t(S):S,to:s?_t(x):x,color:d},h[n].render(),h[n].isActive=!0)}),t._addedPlotLB||(cn((i.plotLines||[]).concat(i.plotBands||[]),function(e){t.addPlotBandOrLine(e)}),t._addedPlotLB=!0);cn([l,c,h],function(e){var t,r,i=[],s=_?_.duration||500:0,o=function(){r=i.length;while(r--)e[i[r]]&&!e[i[r]].isActive&&(e[i[r]].destroy(),delete e[i[r]])};for(t in e)e[t].isActive||(e[t].render(t,!1,0),e[t].isActive=!1,i.push(t));e===h||!n.hasRendered||!s?o():s&&setTimeout(o,s)}),m&&(g=t.getLinePath(m),t.axisLine?t.axisLine.animate({d:g}):t.axisLine=r.path(g).attr({stroke:i.lineColor,"stroke-width":m,zIndex:7}).add(t.axisGroup),t.axisLine[E?"show":"hide"]()),a&&E&&(a[a.isNew?"attr":"animate"](t.getTitlePosition()),a.isNew=!1);if(p&&p.enabled){var T,N,C,k=t.stackTotalGroup;k||(t.stackTotalGroup=k=r.g("stack-labels").attr({visibility:W,zIndex:6}).add()),k.translate(n.plotLeft,n.plotTop);for(T in f){N=f[T];for(C in N)N[C].render(k)}}t.isDirty=!1},removePlotBandOrLine:function(e){var t=this.plotLinesAndBands,n=t.length;while(n--)t[n].id===e&&t[n].destroy()},setTitle:function(e,t){this.update({title:e},t)},redraw:function(){var e=this,t=e.chart,n=t.pointer;n.reset&&n.reset(!0),e.render(),cn(e.plotLinesAndBands,function(e){e.render()}),cn(e.series,function(e){e.isDirty=!0})},setCategories:function(e,t){this.update({categories:e},t)},destroy:function(){var e=this,t=e.stacks,n;mn(e);for(n in t)en(t[n]),t[n]=null;cn([e.ticks,e.minorTicks,e.alternateBands,e.plotLinesAndBands],function(e){en(e)}),cn(["stackTotalGroup","axisLine","axisGroup","gridGroup","labelGroup","axisTitle"],function(t){e[t]&&(e[t]=e[t].destroy())})}},In.prototype={init:function(e,t){var n=t.borderWidth,r=t.style,i=Ct(r.padding);this.chart=e,this.options=t,this.crosshairs=[],this.now={x:0,y:0},this.isHidden=!0,this.label=e.renderer.label("",0,0,t.shape,null,null,t.useHTML,null,"tooltip").attr({padding:i,fill:t.backgroundColor,"stroke-width":n,r:t.borderRadius,zIndex:8}).css(r).css({padding:0}).hide().add(),T||this.label.shadow(t.shadow),this.shared=t.shared},destroy:function(){cn(this.crosshairs,function(e){e&&e.destroy()}),this.label&&(this.label=this.label.destroy())},move:function(e,t,n,r){var i=this,s=i.now,o=i.options.animation!==!1&&!i.isHidden;xt(s,{x:o?(2*s.x+e)/3:e,y:o?(s.y+t)/2:t,anchorX:o?(2*s.anchorX+n)/3:n,anchorY:o?(s.anchorY+r)/2:r}),i.label.attr(s),o&&(f(e-s.x)>1||f(t-s.y)>1)&&(clearTimeout(this.tooltipTimeout),this.tooltipTimeout=setTimeout(function(){i&&i.move(e,t,n,r)},32))},hide:function(){var e=this,t;this.isHidden||(t=this.chart.hoverPoints,this.hideTimer=setTimeout(function(){e.label.fadeOut(),e.isHidden=!0},jt(this.options.hideDelay,500)),t&&cn(t,function(e){e.setState()}),this.chart.hoverPoints=null)},hideCrosshairs:function(){cn(this.crosshairs,function(e){e&&e.hide()})},getAnchor:function(t,n){var r,s=this.chart,o=s.inverted,u=s.plotTop,a=0,f=0,l;return t=Bt(t),r=t[0].tooltipPos,this.followPointer&&n&&(n.chartX===e&&(n=s.pointer.normalize(n)),r=[n.chartX-s.plotLeft,n.chartY-u]),r||(cn(t,function(e){l=e.series.yAxis,a+=e.plotX,f+=(e.plotLow?(e.plotLow+e.plotHigh)/2:e.plotY)+(!o&&l?l.top-u:0)}),a/=t.length,f/=t.length,r=[o?s.plotWidth-f:a,this.shared&&!o&&t.length>1&&n?n.chartY-u:o?s.plotHeight-a:f]),dn(r,i)},getPosition:function(e,t,n){var r=this.chart,i=r.plotLeft,s=r.plotTop,o=r.plotWidth,a=r.plotHeight,f=jt(this.options.distance,12),l=n.plotX,c=n.plotY,h=l+i+(r.inverted?f:-e-f),p=c-t+s+15,d;return h<7&&(h=i+u(l,0)+f),h+e>i+o&&(h-=h+e-(i+o),p=c-t+s-f,d=!0),p<s+5&&(p=s+5,d&&c>=p&&c<=p+t&&(p=c+s+f)),p+t>s+a&&(p=u(s,s+a-t-f)),{x:h,y:p}},defaultFormatter:function(e){var t=this.points||Bt(this),n=t[0].series,r;return r=[n.tooltipHeaderFormatter(t[0])],cn(t,function(e){n=e.series,r.push(n.tooltipFormatter&&n.tooltipFormatter(e)||e.point.tooltipFormatter(n.tooltipOptions.pointFormat))}),r.push(e.options.footerFormat||""),r.join("")},refresh:function(e,t){var n=this,r=n.chart,i=n.label,s=n.options,o,u,a,f,l={},c,h=[],p=s.formatter||n.defaultFormatter,d=r.hoverPoints,v,m=s.crosshairs,g=n.shared,y;clearTimeout(this.hideTimer),n.followPointer=Bt(e)[0].series.tooltipOptions.followPointer,f=n.getAnchor(e,t),o=f[0],u=f[1],g&&(!e.series||!e.series.noSharedTooltip)?(r.hoverPoints=e,d&&cn(d,function(e){e.setState()}),cn(e,function(e){e.setState(G),h.push(e.getLabelConfig())}),l={x:e[0].category,y:e[0].y},l.points=h,e=e[0]):l=e.getLabelConfig(),c=p.call(l,n),y=e.series,a=g||!y.isCartesian||y.tooltipOutsidePlot||r.isInsidePlot(o,u),c===!1||!a?this.hide():(n.isHidden&&(wn(i),i.attr("opacity",1).show()),i.attr({text:c}),v=s.borderColor||e.color||y.color||"#606060",i.attr({stroke:v}),n.updatePosition({plotX:o,plotY:u}),this.isHidden=!1);if(m){m=Bt(m);var b,w=m.length,E,S;while(w--)S=e.series[w?"yAxis":"xAxis"],m[w]&&S&&(b=S.getPlotLinePath(w?jt(e.stackY,e.y):e.x,1),n.crosshairs[w]?n.crosshairs[w].attr({d:b,visibility:W}):(E={"stroke-width":m[w].width||1,stroke:m[w].color||"#C0C0C0",zIndex:m[w].zIndex||2},m[w].dashStyle&&(E.dashstyle=m[w].dashStyle),n.crosshairs[w]=r.renderer.path(b).attr(E).add()))}gn(r,"tooltipRefresh",{text:c,x:o+r.plotLeft,y:u+r.plotTop,borderColor:v})},updatePosition:function(e){var t=this.chart,n=this.label,r=(this.options.positioner||this.getPosition).call(this,n.width,n.height,e);this.move(i(r.x),i(r.y),e.plotX+t.plotLeft,e.plotY+t.plotTop)}},qn.prototype={init:function(e,t){var n=T?"":t.chart.zoomType,r=e.inverted,i,s;this.options=t,this.chart=e,this.zoomX=i=/x/.test(n),this.zoomY=s=/y/.test(n),this.zoomHor=i&&!r||s&&r,this.zoomVert=s&&!r||i&&r,this.pinchDown=[],this.lastValidTouch={},t.tooltip.enabled&&(e.tooltip=new In(e,t.tooltip)),this.setDOMEvents()},normalize:function(t){var r,s,o,u;return t=t||n.event,t.target||(t.target=t.srcElement),t=yn(t),u=t.touches?t.touches.item(0):t,this.chartPosition=r=pn(this.chart.container),u.pageX===e?(s=t.x,o=t.y):(s=u.pageX-r.left,o=u.pageY-r.top),xt(t,{chartX:i(s),chartY:i(o)})},getCoordinates:function(e){var t={xAxis:[],yAxis:[]};return cn(this.chart.axes,function(n){t[n.isXAxis?"xAxis":"yAxis"].push({axis:n,value:n.toValue(e[n.horiz?"chartX":"chartY"])})}),t},getIndex:function(e){var t=this.chart;return t.inverted?t.plotHeight+t.plotTop-e.chartY:e.chartX-t.plotLeft},runPointActions:function(e){var t=this,n=t.chart,r=n.series,i=n.tooltip,s,o,u=n.hoverPoint,l=n.hoverSeries,c,h,p=n.chartWidth,d=t.getIndex(e),v;if(i&&t.options.tooltip.shared&&(!l||!l.noSharedTooltip)){o=[],c=r.length;for(h=0;h<c;h++)r[h].visible&&r[h].options.enableMouseTracking!==!1&&!r[h].noSharedTooltip&&r[h].tooltipPoints.length&&(s=r[h].tooltipPoints[d],s.series&&(s._dist=f(d-s.clientX),p=a(p,s._dist),o.push(s)));c=o.length;while(c--)o[c]._dist>p&&o.splice(c,1);o.length&&o[0].clientX!==t.hoverX&&(i.refresh(o,e),t.hoverX=o[0].clientX)}l&&l.tracker?(s=l.tooltipPoints[d],s&&s!==u&&s.onMouseOver(e)):i&&i.followPointer&&!i.isHidden&&(v=i.getAnchor([{}],e),i.updatePosition({plotX:v[0],plotY:v[1]}))},reset:function(t){var n=this,r=n.chart,i=r.hoverSeries,s=r.hoverPoint,o=r.tooltip,u=o&&o.shared?r.hoverPoints:s;t=t&&o&&u,t&&Bt(u)[0].plotX===e&&(t=!1),t?o.refresh(u):(s&&s.onMouseOut(),i&&i.onMouseOut(),o&&(o.hide(),o.hideCrosshairs()),n.hoverX=null)},scaleGroups:function(e,t){var n=this.chart;cn(n.series,function(r){r.xAxis.zoomEnabled&&(r.group.attr(e),r.markerGroup&&(r.markerGroup.attr(e),r.markerGroup.clip(t?n.clipRect:null)),r.dataLabelsGroup&&r.dataLabelsGroup.attr(e))}),n.clipRect.attr(t||n.clipBox)},pinchTranslateDirection:function(e,t,n,r,i,s,o){var u=this.chart,a=e?"x":"y",l=e?"X":"Y",c="chart"+l,h=e?"width":"height",p=u["plot"+(e?"Left":"Top")],d,v,m,g=1,y=u.inverted,b=u.bounds[e?"h":"v"],w=t.length===1,E=t[0][c],S=n[0][c],x=!w&&t[1][c],T=!w&&n[1][c],N,C,k,L=function(){!w&&f(E-x)>20&&(g=f(S-T)/f(E-x)),m=(p-S)/g+E,d=u["plot"+(e?"Width":"Height")]/g};L(),v=m,v<b.min?(v=b.min,N=!0):v+d>b.max&&(v=b.max-d,N=!0),N?(S-=.8*(S-o[a][0]),w||(T-=.8*(T-o[a][1])),L()):o[a]=[S,T],y||(s[a]=m-p,s[h]=d),k=y?e?"scaleY":"scaleX":"scale"+l,C=y?1/g:g,i[h]=d,i[a]=v,r[k]=g,r["translate"+l]=C*p+(S-C*E)},pinch:function(e){var t=this,n=t.chart,r=t.pinchDown,i=e.touches,s=t.lastValidTouch,o=t.zoomHor||t.pinchHor,f=t.zoomVert||t.pinchVert,l=t.selectionMarker,c={},h={};e.type==="touchstart"&&(t.inClass(e.target,z+"tracker")?n.runTrackerClick||e.preventDefault():n.runChartClick||e.preventDefault()),dn(i,function(e){return t.normalize(e)}),e.type==="touchstart"?(cn(i,function(e,t){r[t]={chartX:e.chartX,chartY:e.chartY}}),s.x=[r[0].chartX,r[1]&&r[1].chartX],s.y=[r[0].chartY,r[1]&&r[1].chartY],cn(n.axes,function(e){if(e.zoomEnabled){var t=n.bounds[e.horiz?"h":"v"],r=e.minPixelPadding,i=e.toPixels(e.dataMin),s=e.toPixels(e.dataMax),o=a(i,s),f=u(i,s);t.min=a(e.pos,o-r),t.max=u(e.pos+e.len,f+r)}})):r.length&&(l||(t.selectionMarker=l=xt({destroy:H},n.plotBox)),o&&t.pinchTranslateDirection(!0,r,i,c,l,h,s),f&&t.pinchTranslateDirection(!1,r,i,c,l,h,s),t.hasPinched=o||f,t.scaleGroups(c,h))},dragStart:function(e){var t=this.chart;t.mouseIsDown=e.type,t.cancelClick=!1,t.mouseDownX=this.mouseDownX=e.chartX,this.mouseDownY=e.chartY},drag:function(e){var t=this.chart,n=t.options.chart,r=e.chartX,i=e.chartY,s=this.zoomHor,o=this.zoomVert,u=t.plotLeft,a=t.plotTop,l=t.plotWidth,c=t.plotHeight,h,p,d=this.mouseDownX,v=this.mouseDownY;r<u?r=u:r>u+l&&(r=u+l),i<a?i=a:i>a+c&&(i=a+c),this.hasDragged=Math.sqrt(Math.pow(d-r,2)+Math.pow(v-i,2)),this.hasDragged>10&&(h=t.isInsidePlot(d-u,v-a),t.hasCartesianSeries&&(this.zoomX||this.zoomY)&&h&&(this.selectionMarker||(this.selectionMarker=t.renderer.rect(u,a,s?1:l,o?1:c,0).attr({fill:n.selectionMarkerFill||"rgba(69,114,167,0.25)",zIndex:7}).add())),this.selectionMarker&&s&&(p=r-d,this.selectionMarker.attr({width:f(p),x:(p>0?0:p)+d})),this.selectionMarker&&o&&(p=i-v,this.selectionMarker.attr({height:f(p),y:(p>0?0:p)+v})),h&&!this.selectionMarker&&n.panning&&t.pan(r))},drop:function(e){var t=this.chart,n=this.hasPinched;if(this.selectionMarker){var r={xAxis:[],yAxis:[],originalEvent:e.originalEvent||e},i=this.selectionMarker,s=i.x,o=i.y,f;if(this.hasDragged||n)cn(t.axes,function(e){if(e.zoomEnabled){var t=e.horiz,n=e.minPixelPadding,l=e.toValue((t?s:o)+n),c=e.toValue((t?s+i.width:o+i.height)-n);!isNaN(l)&&!isNaN(c)&&(r[e.xOrY+"Axis"].push({axis:e,min:a(l,c),max:u(l,c)}),f=!0)}}),f&&gn(t,"selection",r,function(e){t.zoom(xt(e,n?{animation:!1}:null))});this.selectionMarker=this.selectionMarker.destroy(),n&&this.scaleGroups({translateX:t.plotLeft,translateY:t.plotTop,scaleX:1,scaleY:1})}t&&(Ft(t.container,{cursor:"auto"}),t.cancelClick=this.hasDragged,t.mouseIsDown=this.hasDragged=this.hasPinched=!1,this.pinchDown=[])},onContainerMouseDown:function(e){e=this.normalize(e),e.preventDefault&&e.preventDefault(),this.dragStart(e)},onDocumentMouseUp:function(e){this.drop(e)},onDocumentMouseMove:function(e){var t=this.chart,n=this.chartPosition,r=t.hoverSeries;e=yn(e),n&&r&&r.isCartesian&&!t.isInsidePlot(e.pageX-n.left-t.plotLeft,e.pageY-n.top-t.plotTop)&&this.reset()},onContainerMouseLeave:function(){this.reset(),this.chartPosition=null},onContainerMouseMove:function(e){var t=this.chart;e=this.normalize(e),e.returnValue=!1,t.mouseIsDown==="mousedown"&&this.drag(e),t.isInsidePlot(e.chartX-t.plotLeft,e.chartY-t.plotTop)&&this.runPointActions(e)},inClass:function(e,t){var n;while(e){n=Ht(e,"class");if(n){if(n.indexOf(t)!==-1)return!0;if(n.indexOf(z+"container")!==-1)return!1}e=e.parentNode}},onTrackerMouseOut:function(e){var t=this.chart.hoverSeries;t&&!t.options.stickyTracking&&!this.inClass(e.toElement||e.relatedTarget,z+"tooltip")&&t.onMouseOut()},onContainerClick:function(e){var t=this.chart,n=t.hoverPoint,r=t.plotLeft,i=t.plotTop,s=t.inverted,o,u,a;e=this.normalize(e),e.cancelBubble=!0,t.cancelClick||(n&&this.inClass(e.target,z+"tracker")?(o=this.chartPosition,u=n.plotX,a=n.plotY,xt(n,{pageX:o.left+r+(s?t.plotWidth-a:u),pageY:o.top+i+(s?t.plotHeight-u:a)}),gn(n.series,"click",xt(e,{point:n})),n.firePointEvent("click",e)):(xt(e,this.getCoordinates(e)),t.isInsidePlot(e.chartX-r,e.chartY-i)&&gn(t,"click",e)))},onContainerTouchStart:function(e){var t=this.chart;e.touches.length===1?(e=this.normalize(e),t.isInsidePlot(e.chartX-t.plotLeft,e.chartY-t.plotTop)&&(this.runPointActions(e),this.pinch(e))):e.touches.length===2&&this.pinch(e)},onContainerTouchMove:function(e){(e.touches.length===1||e.touches.length===2)&&this.pinch(e)},onDocumentTouchEnd:function(e){this.drop(e)},setDOMEvents:function(){var e=this,n=e.chart.container,r;this._events=r=[[n,"onmousedown","onContainerMouseDown"],[n,"onmousemove","onContainerMouseMove"],[n,"onclick","onContainerClick"],[n,"mouseleave","onContainerMouseLeave"],[t,"mousemove","onDocumentMouseMove"],[t,"mouseup","onDocumentMouseUp"]],C&&r.push([n,"ontouchstart","onContainerTouchStart"],[n,"ontouchmove","onContainerTouchMove"],[t,"touchend","onDocumentTouchEnd"]),cn(r,function(t){e["_"+t[2]]=function(n){e[t[2]](n)},t[1].indexOf("on")===0?t[0][t[1]]=e["_"+t[2]]:vn(t[0],t[1],e["_"+t[2]])})},destroy:function(){var e=this;cn(e._events,function(t){t[1].indexOf("on")===0?t[0][t[1]]=null:mn(t[0],t[1],e["_"+t[2]])}),delete e._events,clearInterval(e.tooltipTimeout)}},Rn.prototype={init:function(e,t){var n=this,r=t.itemStyle,i=jt(t.padding,8),s=t.itemMarginTop||0;this.options=t;if(!t.enabled)return;n.baseline=Ct(r.fontSize)+3+s,n.itemStyle=r,n.itemHiddenStyle=Tt(r,t.itemHiddenStyle),n.itemMarginTop=s,n.padding=i,n.initialItemX=i,n.initialItemY=i-5,n.maxItemWidth=0,n.chart=e,n.itemHeight=0,n.lastLineHeight=0,n.render(),vn(n.chart,"endResize",function(){n.positionCheckboxes()})},colorizeItem:function(t,n){var r=this,i=r.options,s=t.legendItem,o=t.legendLine,u=t.legendSymbol,a=r.itemHiddenStyle.color,f=n?i.itemStyle.color:a,l=n?t.color:a,c=t.options&&t.options.marker,h={stroke:l,fill:l},p,d;s&&s.css({fill:f,color:f}),o&&o.attr({stroke:l});if(u){if(c){c=t.convertAttribs(c);for(p in c)d=c[p],d!==e&&(h[p]=d)}u.attr(h)}},positionItem:function(e){var t=this,n=t.options,r=n.symbolPadding,i=!n.rtl,s=e._legendItemPos,o=s[0],u=s[1],a=e.checkbox;e.legendGroup&&e.legendGroup.translate(i?o:t.legendWidth-o-2*r-4,u),a&&(a.x=o,a.y=u)},destroyItem:function(e){var t=e.checkbox;cn(["legendItem","legendLine","legendSymbol","legendGroup"],function(t){e[t]&&e[t].destroy()}),t&&tn(e.checkbox)},destroy:function(){var e=this,t=e.group,n=e.box;n&&(e.box=n.destroy()),t&&(e.group=t.destroy())},positionCheckboxes:function(e){var t=this.group.alignAttr,n,r=this.clipHeight||this.legendHeight;t&&(n=t.translateY,cn(this.allItems,function(i){var s=i.checkbox,o;s&&(o=n+s.y+(e||0)+3,Ft(s,{left:t.translateX+i.legendItemWidth+s.x-20+X,top:o+X,display:o>n-6&&o<n+r-6?"":V}))}))},renderTitle:function(){var e=this.options,t=this.padding,n=e.title,r=0;n.text&&(this.title||(this.title=this.chart.renderer.label(n.text,t-3,t-4,null,null,null,null,null,"legend-title").attr({zIndex:1}).css(n.style).add(this.group)),r=this.title.getBBox().height,this.contentGroup.attr({translateY:r})),this.titleHeight=r},renderItem:function(e){var t=this,n=t.chart,r=n.renderer,i=t.options,s=i.layout==="horizontal",o=i.symbolWidth,a=i.symbolPadding,f=t.itemStyle,l=t.itemHiddenStyle,c=t.padding,h=!i.rtl,p,d=i.width,v=i.itemMarginBottom||0,m=t.itemMarginTop,g=t.initialItemX,y,b,w=e.legendItem,E=e.series||e,S=E.options,x=S.showCheckbox,T=i.useHTML;w||(e.legendGroup=r.g("legend-item").attr({zIndex:1}).add(t.scrollGroup),E.drawLegendSymbol(t,e),e.legendItem=w=r.text(i.labelFormat?Vt(i.labelFormat,e):i.labelFormatter.call(e),h?o+a:-a,t.baseline,T).css(Tt(e.visible?f:l)).attr({align:h?"left":"right",zIndex:2}).add(e.legendGroup),(T?w:e.legendGroup).on("mouseover",function(){e.setState(G),w.css(t.options.itemHoverStyle)}).on("mouseout",function(){w.css(e.visible?f:l),e.setState()}).on("click",function(t){var n="legendItemClick",r=function(){e.setVisible()};t={browserEvent:t},e.firePointEvent?e.firePointEvent(n,t,r):gn(e,n,t,r)}),t.colorizeItem(e,e.visible),S&&x&&(e.checkbox=It("input",{type:"checkbox",checked:e.selected,defaultChecked:e.selected},i.itemCheckboxStyle,n.container),vn(e.checkbox,"click",function(t){var n=t.target;gn(e,"checkboxClick",{checked:n.checked},function(){e.select()})}))),y=w.getBBox(),b=e.legendItemWidth=i.itemWidth||o+a+y.width+c+(x?20:0),t.itemHeight=p=y.height,s&&t.itemX-g+b>(d||n.chartWidth-2*c-g)&&(t.itemX=g,t.itemY+=m+t.lastLineHeight+v,t.lastLineHeight=0),t.maxItemWidth=u(t.maxItemWidth,b),t.lastItemY=m+t.itemY+v,t.lastLineHeight=u(p,t.lastLineHeight),e._legendItemPos=[t.itemX,t.itemY],s?t.itemX+=b:(t.itemY+=m+p+v,t.lastLineHeight=p),t.offsetWidth=d||u(s?t.itemX-g:b,t.offsetWidth)},render:function(){var e=this,t=e.chart,n=t.renderer,r=e.group,i,s,o,u,a=e.box,f=e.options,l=e.padding,c=f.borderWidth,h=f.backgroundColor;e.itemX=e.initialItemX,e.itemY=e.initialItemY,e.offsetWidth=0,e.lastItemY=0,r||(e.group=r=n.g("legend").attr({zIndex:7}).add(),e.contentGroup=n.g().attr({zIndex:1}).add(r),e.scrollGroup=n.g().add(e.contentGroup),e.clipRect=n.clipRect(0,0,9999,t.chartHeight),e.contentGroup.clip(e.clipRect)),e.renderTitle(),i=[],cn(t.series,function(e){var t=e.options;if(!t.showInLegend||Pt(t.linkedTo))return;i=i.concat(e.legendItems||(t.legendType==="point"?e.data:e))}),Gt(i,function(e,t){return(e.options&&e.options.legendIndex||0)-(t.options&&t.options.legendIndex||0)}),f.reversed&&i.reverse(),e.allItems=i,e.display=s=!!i.length,cn(i,function(t){e.renderItem(t)}),o=f.width||e.offsetWidth,u=e.lastItemY+e.lastLineHeight+e.titleHeight,u=e.handleOverflow(u);if(c||h)o+=l,u+=l,a?o>0&&u>0&&(a[a.isNew?"attr":"animate"](a.crisp(null,null,null,o,u)),a.isNew=!1):(e.box=a=n.rect(0,0,o,u,f.borderRadius,c||0).attr({stroke:f.borderColor,"stroke-width":c||0,fill:h||V}).add(r).shadow(f.shadow),a.isNew=!0),a[s?"show":"hide"]();e.legendWidth=o,e.legendHeight=u,cn(i,function(t){e.positionItem(t)}),s&&r.align(xt({width:o,height:u},f),!0,"spacingBox"),t.isResizing||this.positionCheckboxes()},handleOverflow:function(e){var t=this,n=this.chart,r=n.renderer,i,s=this.options,u=s.y,f=s.verticalAlign==="top",l=n.spacingBox.height+(f?-u:u)-this.padding,c=s.maxHeight,h,p=this.clipRect,d=s.navigation,v=jt(d.animation,!0),m=d.arrowSize||12,g=this.nav;return s.layout==="horizontal"&&(l/=2),c&&(l=a(l,c)),e>l&&!s.useHTML?(this.clipHeight=h=l-20-this.titleHeight,this.pageCount=i=o(e/h),this.currentPage=jt(this.currentPage,1),this.fullHeight=e,p.attr({height:h}),g||(this.nav=g=r.g().attr({zIndex:1}).add(this.group),this.up=r.symbol("triangle",0,0,m,m).on("click",function(){t.scroll(-1,v)}).add(g),this.pager=r.text("",15,10).css(d.style).add(g),this.down=r.symbol("triangle-down",0,0,m,m).on("click",function(){t.scroll(1,v)}).add(g)),t.scroll(0),e=l):g&&(p.attr({height:n.chartHeight}),g.hide(),this.scrollGroup.attr({translateY:1}),this.clipHeight=0),e},scroll:function(t,n){var r=this.pageCount,i=this.currentPage+t,s=this.clipHeight,o=this.options.navigation,u=o.activeColor,f=o.inactiveColor,l=this.pager,c=this.padding,h;i>r&&(i=r),i>0&&(n!==e&&sn(n,this.chart),this.nav.attr({translateX:c,translateY:s+7+this.titleHeight,visibility:W}),this.up.attr({fill:i===1?f:u}).css({cursor:i===1?"default":"pointer"}),l.attr({text:i+"/"+this.pageCount}),this.down.attr({x:18+this.pager.getBBox().width,fill:i===r?f:u}).css({cursor:i===r?"default":"pointer"}),h=-a(s*(i-1),this.fullHeight-s+c)+1,this.scrollGroup.animate({translateY:h}),l.attr({text:i+"/"+r}),this.currentPage=i,this.positionCheckboxes(h))}},Un.prototype={init:function(e,t){var n,r=e.series;e.series=null,n=Tt(O,e),n.series=e.series=r;var i=n.chart,s=i.margin,o=Lt(s)?s:[s,s,s,s];this.optionsMarginTop=jt(i.marginTop,o[0]),this.optionsMarginRight=jt(i.marginRight,o[1]),this.optionsMarginBottom=jt(i.marginBottom,o[2]),this.optionsMarginLeft=jt(i.marginLeft,o[3]);var u=i.events;this.runChartClick=u&&!!u.click,this.bounds={h:{},v:{}},this.callback=t,this.isResizing=0,this.options=n,this.axes=[],this.series=[],this.hasCartesianSeries=i.showAxes;var a=this,f;a.index=B.length,B.push(a),i.reflow!==!1&&vn(a,"load",function(){a.initReflow()});if(u)for(f in u)vn(a,f,u[f]);a.xAxis=[],a.yAxis=[],a.animation=T?!1:jt(i.animation,!0),a.pointCount=0,a.counters=new Qt,a.firstRender()},initSeries:function(e){var t=this,n=t.options.chart,r=e.type||n.type||n.defaultSeriesType,i,s=St[r];return s||nn(17,!0),i=new s,i.init(this,e),i},addSeries:function(e,t,n){var r,i=this;return e&&(t=jt(t,!0),gn(i,"addSeries",{options:e},function(){r=i.initSeries(e),i.isDirtyLegend=!0,t&&i.redraw(n)})),r},addAxis:function(e,t,n,r){var i=t?"xAxis":"yAxis",s=this.options,o;o=new Fn(this,Tt(e,{index:this[i].length})),s[i]=Bt(s[i]||{}),s[i].push(e),jt(n,!0)&&this.redraw(r)},isInsidePlot:function(e,t,n){var r=n?t:e,i=n?e:t;return r>=0&&r<=this.plotWidth&&i>=0&&i<=this.plotHeight},adjustTickAmounts:function(){this.options.chart.alignTicks!==!1&&cn(this.axes,function(e){e.adjustTickAmount()}),this.maxTicks=null},redraw:function(e){var t=this,n=t.axes,r=t.series,i=t.pointer,s=t.legend,o=t.isDirtyLegend,u,a=t.isDirtyBox,f=r.length,l=f,c,h=t.renderer,p=h.isHidden(),d=[];sn(e,t),p&&t.cloneRenderTo();while(l--){c=r[l];if(c.isDirty&&c.options.stacking){u=!0;break}}if(u){l=f;while(l--)c=r[l],c.options.stacking&&(c.isDirty=!0)}cn(r,function(e){e.isDirty&&e.options.legendType==="point"&&(o=!0)}),o&&s.options.enabled&&(s.render(),t.isDirtyLegend=!1),t.hasCartesianSeries&&(t.isResizing||(t.maxTicks=null,cn(n,function(e){e.setScale()})),t.adjustTickAmounts(),t.getMargins(),cn(n,function(e){e.isDirtyExtremes&&(e.isDirtyExtremes=!1,d.push(function(){gn(e,"afterSetExtremes",e.getExtremes())}));if(e.isDirty||a||u)e.redraw(),a=!0})),a&&t.drawChartBox(),cn(r,function(e){e.isDirty&&e.visible&&(!e.isCartesian||e.xAxis)&&e.redraw()}),i&&i.reset&&i.reset(!0),h.draw(),gn(t,"redraw"),p&&t.cloneRenderTo(!0),cn(d,function(e){e.call()})},showLoading:function(e){var t=this,n=t.options,r=t.loadingDiv,i=n.loading;r||(t.loadingDiv=r=It(I,{className:z+"loading"},xt(i.style,{left:t.plotLeft+X,top:t.plotTop+X,width:t.plotWidth+X,height:t.plotHeight+X,zIndex:10,display:V}),t.container),t.loadingSpan=It("span",null,i.labelStyle,r)),t.loadingSpan.innerHTML=e||n.lang.loading,t.loadingShown||(Ft(r,{opacity:0,display:""}),bn(r,{opacity:i.style.opacity},{duration:i.showDuration||0}),t.loadingShown=!0)},hideLoading:function(){var e=this.options,t=this.loadingDiv;t&&bn(t,{opacity:0},{duration:e.loading.hideDuration||100,complete:function(){Ft(t,{display:V})}}),this.loadingShown=!1},get:function(e){var t=this,n=t.axes,r=t.series,i,s,o;for(i=0;i<n.length;i++)if(n[i].options.id===e)return n[i];for(i=0;i<r.length;i++)if(r[i].options.id===e)return r[i];for(i=0;i<r.length;i++){o=r[i].points||[];for(s=0;s<o.length;s++)if(o[s].id===e)return o[s]}return null},getAxes:function(){var e=this,t=this.options,n=t.xAxis=Bt(t.xAxis||{}),r=t.yAxis=Bt(t.yAxis||{}),i,s;cn(n,function(e,t){e.index=t,e.isX=!0}),cn(r,function(e,t){e.index=t}),i=n.concat(r),cn(i,function(t){s=new Fn(e,t)}),e.adjustTickAmounts()},getSelectedPoints:function(){var e=[];return cn(this.series,function(t){e=e.concat(hn(t.points,function(e){return e.selected}))}),e},getSelectedSeries:function(){return hn(this.series,function(e){return e.selected})},showResetZoom:function(){var e=this,t=O.lang,n=e.options.chart.resetZoomButton,r=n.theme,i=r.states,s=n.relativeTo==="chart"?null:"plotBox";this.resetZoomButton=e.renderer.button(t.resetZoom,null,null,function(){e.zoomOut()},r,i&&i.hover).attr({align:n.position.align,title:t.resetZoomTitle}).add().align(n.position,!1,s)},zoomOut:function(){var e=this;gn(e,"selection",{resetSelection:!0},function(){e.zoom()})},zoom:function(e){var t=this,n,r=t.pointer,i=!1,s;!e||e.resetSelection?cn(t.axes,function(e){n=e.zoom()}):cn(e.xAxis.concat(e.yAxis),function(e){var t=e.axis,s=t.isXAxis;if(r[s?"zoomX":"zoomY"]||r[s?"pinchX":"pinchY"])n=t.zoom(e.min,e.max),t.displayBtn&&(i=!0)}),s=t.resetZoomButton,i&&!s?t.showResetZoom():!i&&Lt(s)&&(t.resetZoomButton=s.destroy()),n&&t.redraw(jt(t.options.chart.animation,e&&e.animation,t.pointCount<100))},pan:function(e){var t=this,n=t.xAxis[0],r=t.mouseDownX,i=n.pointRange/2,s=n.getExtremes(),o=n.translate(r-e,!0)+i,f=n.translate(r+t.plotWidth-e,!0)-i,l=t.hoverPoints;l&&cn(l,function(e){e.setState()}),n.series.length&&o>a(s.dataMin,s.min)&&f<u(s.dataMax,s.max)&&n.setExtremes(o,f,!0,!1,{trigger:"pan"}),t.mouseDownX=e,Ft(t.container,{cursor:"move"})},setTitle:function(e,t){var n=this,r=n.options,i,s;i=r.title=Tt(r.title,e),s=r.subtitle=Tt(r.subtitle,t),cn([["title",e,i],["subtitle",t,s]],function(e){var t=e[0],r=n[t],i=e[1],s=e[2];r&&i&&(n[t]=r=r.destroy()),s&&s.text&&!r&&(n[t]=n.renderer.text(s.text,0,0,s.useHTML).attr({align:s.align,"class":z+t,zIndex:s.zIndex||4}).css(s.style).add().align(s,!1,"spacingBox"))})},getChartSize:function(){var e=this,t=e.options.chart,n=e.renderToClone||e.renderTo;e.containerWidth=an(n,"width"),e.containerHeight=an(n,"height"),e.chartWidth=u(0,t.width||e.containerWidth||600),e.chartHeight=u(0,jt(t.height,e.containerHeight>19?e.containerHeight:400))},cloneRenderTo:function(e){var n=this.renderToClone,r=this.container;e?n&&(this.renderTo.appendChild(r),tn(n),delete this.renderToClone):(r&&this.renderTo.removeChild(r),this.renderToClone=n=this.renderTo.cloneNode(0),Ft(n,{position:q,top:"-9999px",display:"block"}),t.body.appendChild(n),r&&n.appendChild(r))},getContainer:function(){var e=this,n,r=e.options.chart,i,s,o,u="data-highcharts-chart",a,f;e.renderTo=o=r.renderTo,f=z+L++,kt(o)&&(e.renderTo=o=t.getElementById(o)),o||nn(13,!0),a=Ct(Ht(o,u)),!isNaN(a)&&B[a]&&B[a].destroy(),Ht(o,u,e.index),o.innerHTML="",o.offsetWidth||e.cloneRenderTo(),e.getChartSize(),i=e.chartWidth,s=e.chartHeight,e.container=n=It(I,{className:z+"container"+(r.className?" "+r.className:""),id:f},xt({position:R,overflow:U,width:i+X,height:s+X,textAlign:"left",lineHeight:"normal",zIndex:0},r.style),e.renderToClone||o),e.renderer=r.forExport?new An(n,i,s,!0):new N(n,i,s),T&&e.renderer.create(e,n,i,s)},getMargins:function(){var e=this,t=e.options.chart,n=t.spacingTop,r=t.spacingRight,i=t.spacingBottom,s=t.spacingLeft,o,a=e.legend,f=e.optionsMarginTop,l=e.optionsMarginLeft,c=e.optionsMarginRight,h=e.optionsMarginBottom,p=e.options.title,d=e.options.subtitle,v=e.options.legend,m=jt(v.margin,10),g=v.x,y=v.y,b=v.align,w=v.verticalAlign,E;e.resetMargins(),o=e.axisOffset,(e.title||e.subtitle)&&!Pt(e.optionsMarginTop)&&(E=u(e.title&&!p.floating&&!p.verticalAlign&&p.y||0,e.subtitle&&!d.floating&&!d.verticalAlign&&d.y||0),E&&(e.plotTop=u(e.plotTop,E+jt(p.margin,15)+n))),a.display&&!v.floating&&(b==="right"?Pt(c)||(e.marginRight=u(e.marginRight,a.legendWidth-g+m+r)):b==="left"?Pt(l)||(e.plotLeft=u(e.plotLeft,a.legendWidth+g+m+s)):w==="top"?Pt(f)||(e.plotTop=u(e.plotTop,a.legendHeight+y+m+n)):w==="bottom"&&(Pt(h)||(e.marginBottom=u(e.marginBottom,a.legendHeight-y+m+i)))),e.extraBottomMargin&&(e.marginBottom+=e.extraBottomMargin),e.extraTopMargin&&(e.plotTop+=e.extraTopMargin),e.hasCartesianSeries&&cn(e.axes,function(e){e.getOffset()}),Pt(l)||(e.plotLeft+=o[3]),Pt(f)||(e.plotTop+=o[0]),Pt(h)||(e.marginBottom+=o[2]),Pt(c)||(e.marginRight+=o[1]),e.setChartSize()},initReflow:function(){function o(o){var u=r.width||an(i,"width"),a=r.height||an(i,"height"),f=o?o.target:n;if(!e.hasUserSize&&u&&a&&(f===n||f===t)){if(u!==e.containerWidth||a!==e.containerHeight)clearTimeout(s),e.reflowTimeout=s=setTimeout(function(){e.container&&(e.setSize(u,a,!1),e.hasUserSize=null)},100);e.containerWidth=u,e.containerHeight=a}}var e=this,r=e.options.chart,i=e.renderTo,s;vn(n,"resize",o),vn(e,"destroy",function(){mn(n,"resize",o)})},setSize:function(e,t,n){var r=this,s,o,a;r.isResizing+=1,a=function(){r&&gn(r,"endResize",null,function(){r.isResizing-=1})},sn(n,r),r.oldChartHeight=r.chartHeight,r.oldChartWidth=r.chartWidth,Pt(e)&&(r.chartWidth=s=u(0,i(e)),r.hasUserSize=!!s),Pt(t)&&(r.chartHeight=o=u(0,i(t))),Ft(r.container,{width:s+X,height:o+X}),r.setChartSize(!0),r.renderer.setSize(s,o,n),r.maxTicks=null,cn(r.axes,function(e){e.isDirty=!0,e.setScale()}),cn(r.series,function(e){e.isDirty=!0}),r.isDirtyLegend=!0,r.isDirtyBox=!0,r.getMargins(),r.redraw(n),r.oldChartHeight=null,gn(r,"resize"),_===!1?a():setTimeout(a,_&&_.duration||500)},setChartSize:function(e){var t=this,n=t.inverted,r=t.renderer,a=t.chartWidth,f=t.chartHeight,l=t.options.chart,c=l.spacingTop,h=l.spacingRight,p=l.spacingBottom,d=l.spacingLeft,v=t.clipOffset,m,g,y,b,w,E,S;t.plotLeft=y=i(t.plotLeft),t.plotTop=b=i(t.plotTop),t.plotWidth=w=u(0,i(a-y-t.marginRight)),t.plotHeight=E=u(0,i(f-b-t.marginBottom)),t.plotSizeX=n?E:w,t.plotSizeY=n?w:E,t.plotBorderWidth=S=l.plotBorderWidth||0,t.spacingBox=r.spacingBox={x:d,y:c,width:a-d-h,height:f-c-p},t.plotBox=r.plotBox={x:y,y:b,width:w,height:E},m=o(u(S,v[3])/2),g=o(u(S,v[0])/2),t.clipBox={x:m,y:g,width:s(t.plotSizeX-u(S,v[1])/2-m),height:s(t.plotSizeY-u(S,v[2])/2-g)},e||cn(t.axes,function(e){e.setAxisSize(),e.setAxisTranslation()})},resetMargins:function(){var e=this,t=e.options.chart,n=t.spacingTop,r=t.spacingRight,i=t.spacingBottom,s=t.spacingLeft;e.plotTop=jt(e.optionsMarginTop,n),e.marginRight=jt(e.optionsMarginRight,r),e.marginBottom=jt(e.optionsMarginBottom,i),e.plotLeft=jt(e.optionsMarginLeft,s),e.axisOffset=[0,0,0,0],e.clipOffset=[0,0,0,0]},drawChartBox:function(){var e=this,t=e.options.chart,n=e.renderer,r=e.chartWidth,i=e.chartHeight,s=e.chartBackground,o=e.plotBackground,u=e.plotBorder,a=e.plotBGImage,f=t.borderWidth||0,l=t.backgroundColor,c=t.plotBackgroundColor,h=t.plotBackgroundImage,p=t.plotBorderWidth||0,d,v,m=e.plotLeft,g=e.plotTop,y=e.plotWidth,b=e.plotHeight,w=e.plotBox,E=e.clipRect,S=e.clipBox;d=f+(t.shadow?8:0);if(f||l)s?s.animate(s.crisp(null,null,null,r-d,i-d)):(v={fill:l||V},f&&(v.stroke=t.borderColor,v["stroke-width"]=f),e.chartBackground=n.rect(d/2,d/2,r-d,i-d,t.borderRadius,f).attr(v).add().shadow(t.shadow));c&&(o?o.animate(w):e.plotBackground=n.rect(m,g,y,b,0).attr({fill:c}).add().shadow(t.plotShadow)),h&&(a?a.animate(w):e.plotBGImage=n.image(h,m,g,y,b).add()),E?E.animate({width:S.width,height:S.height}):e.clipRect=n.clipRect(S),p&&(u?u.animate(u.crisp(null,m,g,y,b)):e.plotBorder=n.rect(m,g,y,b,0,p).attr({stroke:t.plotBorderColor,"stroke-width":p,zIndex:1}).add()),e.isDirtyBox=!1},propFromSeries:function(){var e=this,t=e.options.chart,n,r=e.options.series,i,s;cn(["inverted","angular","polar"],function(o){n=St[t.type||t.defaultSeriesType],s=e[o]||t[o]||n&&n.prototype[o],i=r&&r.length;while(!s&&i--)n=St[r[i].type],n&&n.prototype[o]&&(s=!0);e[o]=s})},render:function(){var e=this,t=e.axes,n=e.renderer,r=e.options,i=r.labels,s=r.credits,o;e.setTitle(),e.legend=new Rn(e,r.legend),cn(t,function(e){e.setScale()}),e.getMargins(),e.maxTicks=null,cn(t,function(e){e.setTickPositions(!0),e.setMaxTicks()}),e.adjustTickAmounts(),e.getMargins(),e.drawChartBox(),e.hasCartesianSeries&&cn(t,function(e){e.render()}),e.seriesGroup||(e.seriesGroup=n.g("series-group").attr({zIndex:3}).add()),cn(e.series,function(e){e.translate(),e.setTooltipPoints(),e.render()}),i.items&&cn(i.items,function(t){var r=xt(i.style,t.style),s=Ct(r.left)+e.plotLeft,o=Ct(r.top)+e.plotTop+12;delete r.left,delete r.top,n.text(t.html,s,o).attr({zIndex:2}).css(r).add()}),s.enabled&&!e.credits&&(o=s.href,e.credits=n.text(s.text,0,0).on("click",function(){o&&(location.href=o)}).attr({align:s.position.align,zIndex:8}).css(s.style).add().align(s.position)),e.hasRendered=!0},destroy:function(){var t=this,n=t.axes,r=t.series,i=t.container,s,o=i&&i.parentNode;gn(t,"destroy"),B[t.index]=e,t.renderTo.removeAttribute("data-highcharts-chart"),mn(t),s=n.length;while(s--)n[s]=n[s].destroy();s=r.length;while(s--)r[s]=r[s].destroy();cn(["title","subtitle","chartBackground","plotBackground","plotBGImage","plotBorder","seriesGroup","clipRect","credits","pointer","scroller","rangeSelector","legend","resetZoomButton","tooltip","renderer"],function(e){var n=t[e];n&&n.destroy&&(t[e]=n.destroy())}),i&&(i.innerHTML="",mn(i),o&&tn(i));for(s in t)delete t[s]},isReadyToRender:function(){var e=this;return!S&&n==n.top&&t.readyState!=="complete"||T&&!n.canvg?(T?Pn.push(function(){e.firstRender()},e.options.global.canvasToolsURL):t.attachEvent("onreadystatechange",function(){t.detachEvent("onreadystatechange",e.firstRender),t.readyState==="complete"&&e.firstRender()}),!1):!0},firstRender:function(){var e=this,t=e.options,n=e.callback;if(!e.isReadyToRender())return;e.getContainer(),gn(e,"init"),e.resetMargins(),e.setChartSize(),e.propFromSeries(),e.getAxes(),cn(t.series||[],function(t){e.initSeries(t)}),gn(e,"beforeRender"
),e.pointer=new qn(e,t),e.render(),e.renderer.draw(),n&&n.apply(e,[e]),cn(e.callbacks,function(t){t.apply(e,[e])}),e.cloneRenderTo(!0),gn(e,"load")}},Un.prototype.callbacks=[];var zn=function(){};zn.prototype={init:function(e,t,n){var r=this,i;return r.series=e,r.applyOptions(t,n),r.pointAttr={},e.options.colorByPoint&&(i=e.options.colors||e.chart.options.colors,r.color=r.color||i[e.colorCounter++],e.colorCounter===i.length&&(e.colorCounter=0)),e.chart.pointCount++,r},applyOptions:function(t,n){var r=this,i=r.series,s=i.pointValKey;return t=zn.prototype.optionsToObject.call(this,t),xt(r,t),r.options=r.options?xt(r.options,t):t,s&&(r.y=r[s]),r.x===e&&i&&(r.x=n===e?i.autoIncrement():n),r},optionsToObject:function(e){var t,n=this.series,r=n.pointArrayMap||["y"],i=r.length,s,o=0,u=0;if(typeof e=="number"||e===null)t={y:e};else if(At(e)){t={},e.length>i&&(s=typeof e[0],s==="string"?t.name=e[0]:s==="number"&&(t.x=e[0]),o++);while(u<i)t[r[u++]]=e[o++]}else typeof e=="object"&&(t=e,e.dataLabels&&(n._hasPointLabels=!0),e.marker&&(n._hasPointMarkers=!0));return t},destroy:function(){var e=this,t=e.series,n=t.chart,r=n.hoverPoints,i;n.pointCount--,r&&(e.setState(),Dt(r,e),r.length||(n.hoverPoints=null)),e===n.hoverPoint&&e.onMouseOut();if(e.graphic||e.dataLabel)mn(e),e.destroyElements();e.legendItem&&n.legend.destroyItem(e);for(i in e)e[i]=null},destroyElements:function(){var e=this,t=["graphic","dataLabel","dataLabelUpper","group","connector","shadowGroup"],n,r=6;while(r--)n=t[r],e[n]&&(e[n]=e[n].destroy())},getLabelConfig:function(){var e=this;return{x:e.category,y:e.y,key:e.name||e.category,series:e.series,point:e,percentage:e.percentage,total:e.total||e.stackTotal}},select:function(e,t){var n=this,r=n.series,i=r.chart;e=jt(e,!n.selected),n.firePointEvent(e?"select":"unselect",{accumulate:t},function(){n.selected=n.options.selected=e,r.options.data[ln(n,r.data)]=n.options,n.setState(e&&Y),t||cn(i.getSelectedPoints(),function(e){e.selected&&e!==n&&(e.selected=e.options.selected=!1,r.options.data[ln(e,r.data)]=e.options,e.setState(Q),e.firePointEvent("unselect"))})})},onMouseOver:function(e){var t=this,n=t.series,r=n.chart,i=r.tooltip,s=r.hoverPoint;s&&s!==t&&s.onMouseOut(),t.firePointEvent("mouseOver"),i&&(!i.shared||n.noSharedTooltip)&&i.refresh(t,e),t.setState(G),r.hoverPoint=t},onMouseOut:function(){var e=this.series.chart,t=e.hoverPoints;if(!t||ln(this,t)===-1)this.firePointEvent("mouseOut"),this.setState(),e.hoverPoint=null},tooltipFormatter:function(e){var t=this.series,n=t.tooltipOptions,r=n.valueDecimals,i=n.valuePrefix||"",s=n.valueSuffix||"";return cn(t.pointArrayMap||["y"],function(t){t="{point."+t;if(i||s)e=e.replace(t+"}",i+t+"}"+s);Ot(r)&&(e=e.replace(t+"}",t+":,."+r+"f}"))}),Vt(e,{point:this,series:this.series})},update:function(e,t,n){var r=this,i=r.series,s=r.graphic,o,u=i.data,a=i.chart;t=jt(t,!0),r.firePointEvent("update",{options:e},function(){r.applyOptions(e),Lt(e)&&(i.getAttribs(),s&&s.attr(r.pointAttr[i.state])),o=ln(r,u),i.xData[o]=r.x,i.yData[o]=i.toYData?i.toYData(r):r.y,i.zData[o]=r.z,i.options.data[o]=r.options,i.isDirty=!0,i.isDirtyData=!0,t&&a.redraw(n)})},remove:function(e,t){var n=this,r=n.series,i=r.chart,s,o=r.data;sn(t,i),e=jt(e,!0),n.firePointEvent("remove",null,function(){s=ln(n,o),o.splice(s,1),r.options.data.splice(s,1),r.xData.splice(s,1),r.yData.splice(s,1),r.zData.splice(s,1),n.destroy(),r.isDirty=!0,r.isDirtyData=!0,e&&i.redraw()})},firePointEvent:function(e,t,n){var r=this,i=this.series,s=i.options;(s.point.events[e]||r.options&&r.options.events&&r.options.events[e])&&this.importEvents(),e==="click"&&s.allowPointSelect&&(n=function(e){r.select(null,e.ctrlKey||e.metaKey||e.shiftKey)}),gn(this,e,t,n)},importEvents:function(){if(!this.hasImportedEvents){var e=this,t=Tt(e.series.options.point,e.options),n=t.events,r;e.events=n;for(r in n)vn(e,r,n[r]);this.hasImportedEvents=!0}},setState:function(e){var t=this,n=t.plotX,r=t.plotY,i=t.series,s=i.options.states,o=Sn[i.type].marker&&i.options.marker,u=o&&!o.enabled,a=o&&o.states[e],f=a&&a.enabled===!1,l=i.stateMarkerGraphic,c=t.marker||{},h=i.chart,p,d,v=t.pointAttr;e=e||Q;if(e===t.state||t.selected&&e!==Y||s[e]&&s[e].enabled===!1||e&&(f||u&&!a.enabled))return;t.graphic?(p=o&&t.graphic.symbolName&&v[e].r,t.graphic.attr(Tt(v[e],p?{x:n-p,y:r-p,width:2*p,height:2*p}:{}))):(e&&a&&(p=a.radius,d=c.symbol||i.symbol,l&&l.currentSymbol!==d&&(l=l.destroy()),l?l.attr({x:n-p,y:r-p}):(i.stateMarkerGraphic=l=h.renderer.symbol(d,n-p,r-p,2*p,2*p).attr(v[e]).add(i.markerGroup),l.currentSymbol=d)),l&&l[e&&h.isInsidePlot(n,r)?"show":"hide"]()),t.state=e}};var Wn=function(){};Wn.prototype={isCartesian:!0,type:"line",pointClass:zn,sorted:!0,requireSorting:!0,pointAttrToOptions:{stroke:"lineColor","stroke-width":"lineWidth",fill:"fillColor",r:"radius"},colorCounter:0,init:function(e,t){var n=this,r,i,s,o=e.series;n.chart=e,n.options=t=n.setOptions(t),n.bindAxes(),xt(n,{name:t.name,state:Q,pointAttr:{},visible:t.visible!==!1,selected:t.selected===!0}),T&&(t.animation=!1),i=t.events;for(r in i)vn(n,r,i[r]);if(i&&i.click||t.point&&t.point.events&&t.point.events.click||t.allowPointSelect)e.runTrackerClick=!0;n.getColor(),n.getSymbol(),n.setData(t.data,!1),n.isCartesian&&(e.hasCartesianSeries=!0),o.push(n),n._i=o.length-1,Gt(o,function(e,t){return jt(e.options.index,e._i)-jt(t.options.index,e._i)}),cn(o,function(e,t){e.index=t,e.name=e.name||"Series "+(t+1)}),s=t.linkedTo,n.linkedSeries=[],kt(s)&&(s===":previous"?s=o[n.index-1]:s=e.get(s),s&&(s.linkedSeries.push(n),n.linkedParent=s))},bindAxes:function(){var t=this,n=t.options,r=t.chart,i;t.isCartesian&&cn(["xAxis","yAxis"],function(s){cn(r[s],function(r){i=r.options;if(n[s]===i.index||n[s]!==e&&n[s]===i.id||n[s]===e&&i.index===0)r.series.push(t),t[s]=r,r.isDirty=!0}),t[s]||nn(17,!0)})},autoIncrement:function(){var e=this,t=e.options,n=e.xIncrement;return n=jt(n,t.pointStart,0),e.pointInterval=jt(e.pointInterval,t.pointInterval,1),e.xIncrement=n+e.pointInterval,n},getSegments:function(){var e=this,t=-1,n=[],r,i=e.points,s=i.length;if(s)if(e.options.connectNulls){r=s;while(r--)i[r].y===null&&i.splice(r,1);i.length&&(n=[i])}else cn(i,function(e,r){e.y===null?(r>t+1&&n.push(i.slice(t+1,r)),t=r):r===s-1&&n.push(i.slice(t+1,r+1))});e.segments=n},setOptions:function(e){var t=this.chart,n=t.options,r=n.plotOptions,i=r[this.type],s;return this.userOptions=e,s=Tt(i,r.series,e),this.tooltipOptions=Tt(n.tooltip,s.tooltip),i.marker===null&&delete s.marker,s},getColor:function(){var e=this.options,t=this.userOptions,n=this.chart.options.colors,r=this.chart.counters,i,s;i=e.color||Sn[this.type].color,!i&&!e.colorByPoint&&(Pt(t._colorIndex)?s=t._colorIndex:(t._colorIndex=r.color,s=r.color++),i=n[s]),this.color=i,r.wrapColor(n.length)},getSymbol:function(){var e=this,t=e.userOptions,n=e.options.marker,r=e.chart,i=r.options.symbols,s=r.counters,o;e.symbol=n.symbol,e.symbol||(Pt(t._symbolIndex)?o=t._symbolIndex:(t._symbolIndex=s.symbol,o=s.symbol++),e.symbol=i[o]),/^url/.test(e.symbol)&&(n.radius=0),s.wrapSymbol(i.length)},drawLegendSymbol:function(e){var t=this.options,n=t.marker,r,i=e.options,s,o=i.symbolWidth,u=this.chart.renderer,a=this.legendGroup,f=e.baseline,l;t.lineWidth&&(l={"stroke-width":t.lineWidth},t.dashStyle&&(l.dashstyle=t.dashStyle),this.legendLine=u.path([$,0,f-4,J,o,f-4]).attr(l).add(a)),n&&n.enabled&&(r=n.radius,this.legendSymbol=s=u.symbol(this.symbol,o/2-r,f-4-r,2*r,2*r).add(a))},addPoint:function(e,t,n,r){var i=this,s=i.options,o=i.data,u=i.graph,a=i.area,f=i.chart,l=i.xData,c=i.yData,h=i.zData,p=i.names,d=u&&u.shift||0,v=s.data,m;sn(r,f),u&&n&&(u.shift=d+1),a&&(n&&(a.shift=d+1),a.isArea=!0),t=jt(t,!0),m={series:i},i.pointClass.prototype.applyOptions.apply(m,[e]),l.push(m.x),c.push(i.toYData?i.toYData(m):m.y),h.push(m.z),p&&(p[m.x]=m.name),v.push(e),s.legendType==="point"&&i.generatePoints(),n&&(o[0]&&o[0].remove?o[0].remove(!1):(o.shift(),l.shift(),c.shift(),h.shift(),v.shift())),i.getAttribs(),i.isDirty=!0,i.isDirtyData=!0,t&&f.redraw()},setData:function(t,n){var r=this,i=r.points,s=r.options,o=r.chart,u=null,a=r.xAxis,f=a&&a.categories&&!a.categories.length?[]:null,l;r.xIncrement=null,r.pointRange=a&&a.categories?1:s.pointRange,r.colorCounter=0;var c=[],h=[],p=[],d=t?t.length:[],v=s.turboThreshold||1e3,m,g=r.pointArrayMap,y=g&&g.length,b=!!r.toYData;if(d>v){l=0;while(u===null&&l<d)u=t[l],l++;if(Ot(u)){var w=jt(s.pointStart,0),E=jt(s.pointInterval,1);for(l=0;l<d;l++)c[l]=w,h[l]=t[l],w+=E;r.xIncrement=w}else if(At(u))if(y)for(l=0;l<d;l++)m=t[l],c[l]=m[0],h[l]=m.slice(1,y+1);else for(l=0;l<d;l++)m=t[l],c[l]=m[0],h[l]=m[1]}else for(l=0;l<d;l++)t[l]!==e&&(m={series:r},r.pointClass.prototype.applyOptions.apply(m,[t[l]]),c[l]=m.x,h[l]=b?r.toYData(m):m.y,p[l]=m.z,f&&m.name&&(f[l]=m.name));r.requireSorting&&c.length>1&&c[1]<c[0]&&nn(15),kt(h[0])&&nn(14,!0),r.data=[],r.options.data=t,r.xData=c,r.yData=h,r.zData=p,r.names=f,l=i&&i.length||0;while(l--)i[l]&&i[l].destroy&&i[l].destroy();a&&(a.minRange=a.userMinRange),r.isDirty=r.isDirtyData=o.isDirtyBox=!0,jt(n,!0)&&o.redraw(!1)},remove:function(e,t){var n=this,r=n.chart;e=jt(e,!0),n.isRemoving||(n.isRemoving=!0,gn(n,"remove",null,function(){n.destroy(),r.isDirtyLegend=r.isDirtyBox=!0,e&&r.redraw(t)})),n.isRemoving=!1},processData:function(t){var n=this,r=n.xData,i=n.yData,s=r.length,o=0,a=s,f,l,c,h=n.xAxis,p,d=n.options,v=d.cropThreshold,m=n.isCartesian;if(m&&!n.isDirty&&!h.isDirty&&!n.yAxis.isDirty&&!t)return!1;if(m&&n.sorted&&(!v||s>v||n.forceCrop)){var g=h.getExtremes(),y=g.min,b=g.max;if(r[s-1]<y||r[0]>b)r=[],i=[];else if(r[0]<y||r[s-1]>b){for(p=0;p<s;p++)if(r[p]>=y){o=u(0,p-1);break}for(;p<s;p++)if(r[p]>b){a=p+1;break}r=r.slice(o,a),i=i.slice(o,a),f=!0}}for(p=r.length-1;p>0;p--)l=r[p]-r[p-1],l>0&&(c===e||l<c)&&(c=l);n.cropped=f,n.cropStart=o,n.processedXData=r,n.processedYData=i,d.pointRange===null&&(n.pointRange=c||1),n.closestPointRange=c},generatePoints:function(){var t=this,n=t.options,r=n.data,i=t.data,s,o=t.processedXData,u=t.processedYData,a=t.pointClass,f=o.length,l=t.cropStart||0,c,h=t.hasGroupedData,p,d=[],v;if(!i&&!h){var m=[];m.length=r.length,i=t.data=m}for(v=0;v<f;v++)c=l+v,h?d[v]=(new a).init(t,[o[v]].concat(Bt(u[v]))):(i[c]?p=i[c]:r[c]!==e&&(i[c]=p=(new a).init(t,r[c],o[v])),d[v]=p);if(i&&(f!==(s=i.length)||h))for(v=0;v<s;v++)v===l&&!h&&(v+=f),i[v]&&(i[v].destroyElements(),i[v].plotX=e);t.data=i,t.points=d},translate:function(){this.processedXData||this.processData(),this.generatePoints();var t=this,n=t.options,r=n.stacking,s=t.xAxis,o=s.categories,u=t.yAxis,a=t.points,f=a.length,l=!!t.modifyValue,c,h=u.series,p=h.length,d=n.pointPlacement==="between",v=n.threshold;while(p--)if(h[p].visible){h[p]===t&&(c=!0);break}for(p=0;p<f;p++){var m=a[p],g=m.x,y=m.y,b=m.low,w=u.stacks[(y<v?"-":"")+t.stackKey],E,S;u.isLog&&y<=0&&(m.y=y=null),m.plotX=s.translate(g,0,0,0,1,d),r&&t.visible&&w&&w[g]&&(E=w[g],S=E.total,E.cum=b=E.cum-y,y=b+y,c&&(b=jt(v,u.min)),u.isLog&&b<=0&&(b=null),r==="percent"&&(b=S?b*100/S:0,y=S?y*100/S:0),m.percentage=S?m.y*100/S:0,m.total=m.stackTotal=S,m.stackY=y),m.yBottom=Pt(b)?u.translate(b,0,1,0,1):null,l&&(y=t.modifyValue(y,m)),m.plotY=typeof y=="number"&&y!==Infinity?i(u.translate(y,0,1,0,1)*10)/10:e,m.clientX=d?s.translate(g,0,0,0,1):m.plotX,m.negative=m.y<(v||0),m.category=o&&o[m.x]!==e?o[m.x]:m.x}t.getSegments()},setTooltipPoints:function(e){var t=this,n=[],r,i,o,a=t.xAxis,f=a?a.tooltipLen||a.len:t.chart.plotSizeX,l,c,h=[];if(t.options.enableMouseTracking===!1)return;e&&(t.tooltipPoints=null),cn(t.segments||t.points,function(e){n=n.concat(e)}),a&&a.reversed&&(n=n.reverse()),r=n.length;for(c=0;c<r;c++){l=n[c],i=n[c-1]?o+1:0,o=n[c+1]?u(0,s((l.clientX+(n[c+1]?n[c+1].clientX:f))/2)):f;while(i>=0&&i<=o)h[i++]=l}t.tooltipPoints=h},tooltipHeaderFormatter:function(e){var t=this,n=t.tooltipOptions,r=n.xDateFormat,i=t.xAxis,s=i&&i.options.type==="datetime",o=n.headerFormat,u;if(s&&!r)for(u in P)if(P[u]>=i.closestPointRange){r=n.dateTimeLabelFormats[u];break}return s&&r&&Ot(e.key)&&(o=o.replace("{point.key}","{point.key:"+r+"}")),Vt(o,{point:e,series:t})},onMouseOver:function(){var e=this,t=e.chart,n=t.hoverSeries;n&&n!==e&&n.onMouseOut(),e.options.events.mouseOver&&gn(e,"mouseOver"),e.setState(G),t.hoverSeries=e},onMouseOut:function(){var e=this,t=e.options,n=e.chart,r=n.tooltip,i=n.hoverPoint;i&&i.onMouseOut(),e&&t.events.mouseOut&&gn(e,"mouseOut"),r&&!t.stickyTracking&&(!r.shared||e.noSharedTooltip)&&r.hide(),e.setState(),n.hoverSeries=null},animate:function(e){var t=this,n=t.chart,r=n.renderer,i,s,o=t.options.animation,u=n.clipBox,a=n.inverted,f;o&&!Lt(o)&&(o=Sn[t.type].animation),f="_sharedClip"+o.duration+o.easing,e?(i=n[f],s=n[f+"m"],i||(n[f]=i=r.clipRect(xt(u,{width:0})),n[f+"m"]=s=r.clipRect(-99,a?-n.plotLeft:-n.plotTop,99,a?n.chartWidth:n.chartHeight)),t.group.clip(i),t.markerGroup.clip(s),t.sharedClipKey=f):(i=n[f],i&&(i.animate({width:n.plotSizeX},o),n[f+"m"].animate({width:n.plotSizeX+99},o)),t.animate=null,t.animationTimeout=setTimeout(function(){t.afterAnimate()},o.duration))},afterAnimate:function(){var e=this.chart,t=this.sharedClipKey,n=this.group;n&&this.options.clip!==!1&&(n.clip(e.clipRect),this.markerGroup.clip()),setTimeout(function(){t&&e[t]&&(e[t]=e[t].destroy(),e[t+"m"]=e[t+"m"].destroy())},100)},drawPoints:function(){var t=this,n,r=t.points,i=t.chart,s,o,u,a,f,l,c,h,p=t.options,d=p.marker,v,m,g,y=t.markerGroup;if(d.enabled||t._hasPointMarkers){u=r.length;while(u--)a=r[u],s=a.plotX,o=a.plotY,h=a.graphic,v=a.marker||{},m=d.enabled&&v.enabled===e||v.enabled,g=i.isInsidePlot(s,o,i.inverted),m&&o!==e&&!isNaN(o)&&a.y!==null?(n=a.pointAttr[a.selected?Y:Q],f=n.r,l=jt(v.symbol,t.symbol),c=l.indexOf("url")===0,h?h.attr({visibility:g?S?"inherit":W:U}).animate(xt({x:s-f,y:o-f},h.symbolName?{width:2*f,height:2*f}:{})):g&&(f>0||c)&&(a.graphic=h=i.renderer.symbol(l,s-f,o-f,2*f,2*f).attr(n).add(y))):h&&(a.graphic=h.destroy())}},convertAttribs:function(e,t,n,r){var i=this.pointAttrToOptions,s,o,u={};e=e||{},t=t||{},n=n||{},r=r||{};for(s in i)o=i[s],u[s]=jt(e[o],t[s],n[s],r[s]);return u},getAttribs:function(){var e=this,t=e.options,n=Sn[e.type].marker?t.marker:t,r=n.states,i=r[G],s,o=e.color,u={stroke:o,fill:o},a=e.points||[],f,l,c=[],h,p=e.pointAttrToOptions,d,v=t.negativeColor,m;t.marker?(i.radius=i.radius||n.radius+2,i.lineWidth=i.lineWidth||n.lineWidth+1):i.color=i.color||kn(i.color||o).brighten(i.brightness).get(),c[Q]=e.convertAttribs(n,u),cn([G,Y],function(t){c[t]=e.convertAttribs(r[t],c[Q])}),e.pointAttr=c,f=a.length;while(f--){l=a[f],n=l.options&&l.options.marker||l.options,n&&n.enabled===!1&&(n.radius=0),l.negative&&(l.color=l.fillColor=v),d=t.colorByPoint||l.color;if(l.options)for(m in p)Pt(n[p[m]])&&(d=!0);d?(n=n||{},h=[],r=n.states||{},s=r[G]=r[G]||{},t.marker||(s.color=kn(s.color||l.color).brighten(s.brightness||i.brightness).get()),h[Q]=e.convertAttribs(xt({color:l.color},n),c[Q]),h[G]=e.convertAttribs(r[G],c[G],h[Q]),h[Y]=e.convertAttribs(r[Y],c[Y],h[Q]),l.negative&&t.marker&&(h[Q].fill=h[G].fill=h[Y].fill=e.convertAttribs({fillColor:v}).fill)):h=c,l.pointAttr=h}},update:function(e,t){var n=this.chart,r=this.userOptions,i=this.type;e=Tt(r,{animation:!1,index:this.index,pointStart:this.xData[0]},e),this.remove(!1),xt(this,St[e.type||i].prototype),this.init(n,e),jt(t,!0)&&n.redraw(!1)},destroy:function(){var e=this,t=e.chart,n=/AppleWebKit\/533/.test(d),r,i,s=e.data||[],o,u,a;gn(e,"destroy"),mn(e),cn(["xAxis","yAxis"],function(t){a=e[t],a&&(Dt(a.series,e),a.isDirty=a.forceRedraw=!0)}),e.legendItem&&e.chart.legend.destroyItem(e),i=s.length;while(i--)o=s[i],o&&o.destroy&&o.destroy();e.points=null,clearTimeout(e.animationTimeout),cn(["area","graph","dataLabelsGroup","group","markerGroup","tracker","graphNeg","areaNeg","posClip","negClip"],function(t){e[t]&&(r=n&&t==="group"?"hide":"destroy",e[t][r]())}),t.hoverSeries===e&&(t.hoverSeries=null),Dt(t.series,e);for(u in e)delete e[u]},drawDataLabels:function(){var t=this,n=t.options,r=n.dataLabels,i=t.points,s,o,u,a;if(r.enabled||t._hasPointLabels)t.dlProcessOptions&&t.dlProcessOptions(r),a=t.plotGroup("dataLabelsGroup","data-labels",t.visible?W:U,r.zIndex||6),o=r,cn(i,function(n){var i,f=n.dataLabel,l,c,h,p,d=n.connector,v=!0;s=n.options&&n.options.dataLabels,i=o.enabled||s&&s.enabled;if(f&&!i)n.dataLabel=f.destroy();else if(i){p=r.rotation,r=Tt(o,s),l=n.getLabelConfig(),u=r.format?Vt(r.format,l):r.formatter.call(l,r),r.style.color=jt(r.color,r.style.color,t.color,"black");if(f)Pt(u)?(f.attr({text:u}),v=!1):(n.dataLabel=f=f.destroy(),d&&(n.connector=d.destroy()));else if(Pt(u)){c={fill:r.backgroundColor,stroke:r.borderColor,"stroke-width":r.borderWidth,r:r.borderRadius||0,rotation:p,padding:r.padding,zIndex:1};for(h in c)c[h]===e&&delete c[h];f=n.dataLabel=t.chart.renderer[p?"text":"label"](u,0,-999,null,null,null,r.useHTML).attr(c).css(r.style).add(a).shadow(r.shadow)}f&&t.alignDataLabel(n,f,r,null,v)}})},alignDataLabel:function(e,t,n,r,s){var o=this.chart,u=o.inverted,a=jt(e.plotX,-999),f=jt(e.plotY,-999),l=t.getBBox(),c;r=xt({x:u?o.plotWidth-f:a,y:i(u?o.plotHeight-a:f),width:0,height:0},r),xt(n,{width:l.width,height:l.height}),n.rotation?(c={align:n.align,x:r.x+n.x+r.width/2,y:r.y+n.y+r.height/2},t[s?"attr":"animate"](c)):(t.align(n,null,r),c=t.alignAttr),t.attr({visibility:n.crop===!1||o.isInsidePlot(a,f,u)?o.renderer.isSVG?"inherit":W:U})},getSegmentPath:function(e){var t=this,n=[],r=t.options.step;return cn(e,function(i,s){var o=i.plotX,u=i.plotY,a;t.getPointSpline?n.push.apply(n,t.getPointSpline(e,i,s)):(n.push(s?J:$),r&&s&&(a=e[s-1],r==="right"?n.push(a.plotX,u):r==="center"?n.push((a.plotX+o)/2,a.plotY,(a.plotX+o)/2,u):n.push(o,a.plotY)),n.push(i.plotX,i.plotY))}),n},getGraphPath:function(){var e=this,t=[],n,r=[];return cn(e.segments,function(i){n=e.getSegmentPath(i),i.length>1?t=t.concat(n):r.push(i[0])}),e.singlePoints=r,e.graphPath=t,t},drawGraph:function(){var e=this,t=this.options,n=[["graph",t.lineColor||this.color]],r=t.lineWidth,i=t.dashStyle,s=this.getGraphPath(),o=t.negativeColor;o&&n.push(["graphNeg",o]),cn(n,function(n,o){var u=n[0],a=e[u],f;a?(wn(a),a.animate({d:s})):r&&s.length&&(f={stroke:n[1],"stroke-width":r,zIndex:1},i&&(f.dashstyle=i),e[u]=e.chart.renderer.path(s).attr(f).add(e.group).shadow(!o&&t.shadow))})},clipNeg:function(){var e=this.options,t=this.chart,n=t.renderer,r=e.negativeColor,i,s,a,f=this.posClip,l=this.negClip,c=t.chartWidth,h=t.chartHeight,p=u(c,h),d,v;r&&this.graph&&(i=o(this.yAxis.len-this.yAxis.translate(e.threshold||0)),d={x:0,y:0,width:p,height:i},v={x:0,y:i,width:p,height:p-i},t.inverted&&n.isVML&&(d={x:t.plotWidth-i-t.plotLeft,y:0,width:c,height:h},v={x:i+t.plotLeft-c,y:0,width:t.plotLeft+i,height:c}),this.yAxis.reversed?(s=v,a=d):(s=d,a=v),f?(f.animate(s),l.animate(a)):(this.posClip=f=n.clipRect(s),this.graph.clip(f),this.negClip=l=n.clipRect(a),this.graphNeg.clip(l),this.area&&(this.area.clip(f),this.areaNeg.clip(l))))},invertGroups:function(){function n(){var t={width:e.yAxis.len,height:e.xAxis.len};cn(["group","markerGroup"],function(n){e[n]&&e[n].attr(t).invert()})}var e=this,t=e.chart;vn(t,"resize",n),vn(e,"destroy",function(){mn(t,"resize",n)}),n(),e.invertGroups=n},plotGroup:function(e,t,n,r,i){var s=this[e],o=!s,u=this.chart,a=this.xAxis,f=this.yAxis;return o&&(this[e]=s=u.renderer.g(t).attr({visibility:n,zIndex:r||.1}).add(i)),s[o?"attr":"animate"]({translateX:a?a.left:u.plotLeft,translateY:f?f.top:u.plotTop}),s},render:function(){var e=this,t=e.chart,n,r=e.options,i=r.animation,s=i&&!!e.animate&&t.renderer.isSVG,o=e.visible?W:U,u=r.zIndex,a=e.hasRendered,f=t.seriesGroup;n=e.plotGroup("group","series",o,u,f),e.markerGroup=e.plotGroup("markerGroup","markers",o,u,f),s&&e.animate(!0),e.getAttribs(),n.inverted=t.inverted,e.drawGraph&&(e.drawGraph(),e.clipNeg()),e.drawDataLabels(),e.drawPoints(),e.options.enableMouseTracking!==!1&&e.drawTracker(),t.inverted&&e.invertGroups(),r.clip!==!1&&!e.sharedClipKey&&!a&&n.clip(t.clipRect),s?e.animate():a||e.afterAnimate(),e.isDirty=e.isDirtyData=!1,e.hasRendered=!0},redraw:function(){var e=this,t=e.chart,n=e.isDirtyData,r=e.group,i=e.xAxis,s=e.yAxis;r&&(t.inverted&&r.attr({width:t.plotWidth,height:t.plotHeight}),r.animate({translateX:jt(i&&i.left,t.plotLeft),translateY:jt(s&&s.top,t.plotTop)})),e.translate(),e.setTooltipPoints(!0),e.render(),n&&gn(e,"updatedData")},setState:function(e){var t=this,n=t.options,r=t.graph,i=t.graphNeg,s=n.states,o=n.lineWidth,u;e=e||Q;if(t.state!==e){t.state=e;if(s[e]&&s[e].enabled===!1)return;e&&(o=s[e].lineWidth||o+1),r&&!r.dashstyle&&(u={"stroke-width":o},r.attr(u),i&&i.attr(u))}},setVisible:function(t,n){var r=this,i=r.chart,s=r.legendItem,o,u=i.options.chart.ignoreHiddenSeries,a=r.visible;r.visible=t=r.userOptions.visible=t===e?!a:t,o=t?"show":"hide",cn(["group","dataLabelsGroup","markerGroup","tracker"],function(e){r[e]&&r[e][o]()}),i.hoverSeries===r&&r.onMouseOut(),s&&i.legend.colorizeItem(r,t),r.isDirty=!0,r.options.stacking&&cn(i.series,function(e){e.options.stacking&&e.visible&&(e.isDirty=!0)}),cn(r.linkedSeries,function(e){e.setVisible(t,!1)}),u&&(i.isDirtyBox=!0),n!==!1&&i.redraw(),gn(r,o)},show:function(){this.setVisible(!0)},hide:function(){this.setVisible(!1)},select:function(t){var n=this;n.selected=t=t===e?!n.selected:t,n.checkbox&&(n.checkbox.checked=t),gn(n,t?"select":"unselect")},drawTracker:function(){var e=this,t=e.options,n=t.trackByArea,r=[].concat(n?e.areaPath:e.graphPath),i=r.length,s=e.chart,o=s.pointer,u=s.renderer,a=s.options.tooltip.snap,f=e.tracker,l=t.cursor,c=l&&{cursor:l},h=e.singlePoints,p,d,v=function(){s.hoverSeries!==e&&e.onMouseOver()};if(i&&!n){d=i+1;while(d--)r[d]===$&&r.splice(d+1,0,r[d+1]-a,r[d+2],J),(d&&r[d]===$||d===i)&&r.splice(d,0,J,r[d-2]+a,r[d-1])}for(d=0;d<h.length;d++)p=h[d],r.push($,p.plotX-a,p.plotY,J,p.plotX+a,p.plotY);f?f.attr({d:r}):(e.tracker=f=u.path(r).attr({"class":z+"tracker","stroke-linejoin":"round",visibility:e.visible?W:U,stroke:K,fill:n?K:V,"stroke-width":t.lineWidth+(n?0:2*a),zIndex:2}).addClass(z+"tracker").on("mouseover",v).on("mouseout",function(e){o.onTrackerMouseOut(e)}).css(c).add(e.markerGroup),C&&f.on("touchstart",v))}};var Xn=qt(Wn);St.line=Xn,Sn.area=Tt(xn,{threshold:0});var Vn=qt(Wn,{type:"area",getSegments:function(){var e=[],t=this.yAxis.stacks[this.stackKey],n={},r,i,s=this.points,o,u;if(this.options.stacking&&!this.cropped){for(o=0;o<s.length;o++)n[s[o].x]=s[o];for(u in t)n[u]?e.push(n[u]):(r=this.xAxis.translate(u),i=this.yAxis.toPixels(t[u].cum,!0),e.push({y:null,plotX:r,clientX:r,plotY:i,yBottom:i,onMouseOver:H}));e=[e]}else Wn.prototype.getSegments.call(this),e=this.segments;this.segments=e},getSegmentPath:function(e){var t=Wn.prototype.getSegmentPath.call(this,e),n=[].concat(t),r,i=this.options,s=t.length;s===3&&n.push(J,t[1],t[2]);if(i.stacking&&!this.closedStacks)for(r=e.length-1;r>=0;r--)r<e.length-1&&i.step&&n.push(e[r+1].plotX,e[r].yBottom),n.push(e[r].plotX,e[r].yBottom);else this.closeSegment(n,e);return this.areaPath=this.areaPath.concat(n),t},closeSegment:function(e,t){var n=this.yAxis.getThreshold(this.options.threshold);e.push(J,t[t.length-1].plotX,n,J,t[0].plotX,n)},drawGraph:function(){this.areaPath=[],Wn.prototype.drawGraph.apply(this);var e=this,t=this.areaPath,n=this.options,r=n.negativeColor,i=[["area",this.color,n.fillColor]];r&&i.push(["areaNeg",n.negativeColor,n.negativeFillColor]),cn(i,function(r){var i=r[0],s=e[i];s?s.animate({d:t}):e[i]=e.chart.renderer.path(t).attr({fill:jt(r[2],kn(r[1]).setOpacity(n.fillOpacity||.75).get()),zIndex:0}).add(e.group)})},drawLegendSymbol:function(e,t){t.legendSymbol=this.chart.renderer.rect(0,e.baseline-11,e.options.symbolWidth,12,2).attr({zIndex:3}).add(t.legendGroup)}});St.area=Vn,Sn.spline=Tt(xn);var $n=qt(Wn,{type:"spline",getPointSpline:function(e,t,n){var r=1.5,i=r+1,s=t.plotX,o=t.plotY,f=e[n-1],l=e[n+1],c,h,p,d,v;if(f&&l){var m=f.plotX,g=f.plotY,y=l.plotX,b=l.plotY,w;c=(r*s+m)/i,h=(r*o+g)/i,p=(r*s+y)/i,d=(r*o+b)/i,w=(d-h)*(p-s)/(p-c)+o-d,h+=w,d+=w,h>g&&h>o?(h=u(g,o),d=2*o-h):h<g&&h<o&&(h=a(g,o),d=2*o-h),d>b&&d>o?(d=u(b,o),h=2*o-d):d<b&&d<o&&(d=a(b,o),h=2*o-d),t.rightContX=p,t.rightContY=d}return n?(v=["C",f.rightContX||f.plotX,f.rightContY||f.plotY,c||s,h||o,s,o],f.rightContX=f.rightContY=null):v=[$,s,o],v}});St.spline=$n,Sn.areaspline=Tt(Sn.area);var Jn=Vn.prototype,Kn=qt($n,{type:"areaspline",closedStacks:!0,getSegmentPath:Jn.getSegmentPath,closeSegment:Jn.closeSegment,drawGraph:Jn.drawGraph});St.areaspline=Kn,Sn.column=Tt(xn,{borderColor:"#FFFFFF",borderWidth:1,borderRadius:0,groupPadding:.2,marker:null,pointPadding:.1,minPointLength:0,cropThreshold:50,pointRange:null,states:{hover:{brightness:.1,shadow:!1},select:{color:"#C0C0C0",borderColor:"#000000",shadow:!1}},dataLabels:{align:null,verticalAlign:null,y:null},stickyTracking:!1,threshold:0});var Qn=qt(Wn,{type:"column",tooltipOutsidePlot:!0,requireSorting:!1,pointAttrToOptions:{stroke:"borderColor","stroke-width":"borderWidth",fill:"color",r:"borderRadius"},trackerGroups:["group","dataLabelsGroup"],init:function(){Wn.prototype.init.apply(this,arguments);var e=this,t=e.chart;t.hasRendered&&cn(t.series,function(t){t.type===e.type&&(t.isDirty=!0)})},getColumnMetrics:function(){var t=this,n=t.chart,r=t.options,i=this.xAxis,s=i.reversed,o,u={},l,c=0;r.grouping===!1?c=1:cn(n.series,function(n){var r=n.options;n.type===t.type&&n.visible&&t.options.group===r.group&&(r.stacking?(o=n.stackKey,u[o]===e&&(u[o]=c++),l=u[o]):r.grouping!==!1&&(l=c++),n.columnIndex=l)});var h=a(f(i.transA)*(i.ordinalSlope||r.pointRange||i.closestPointRange||1),i.len),p=h*r.groupPadding,d=h-2*p,v=d/c,m=r.pointWidth,g=Pt(m)?(v-m)/2:v*r.pointPadding,y=jt(m,v-2*g),b=(s?c-(t.columnIndex||0):t.columnIndex)||0,w=g+(p+b*v-h/2)*(s?-1:1);return t.columnMetrics={width:y,offset:w}},translate:function(){var e=this,t=e.chart,n=e.options,r=n.stacking,i=n.borderWidth,s=e.yAxis,l=n.threshold,c=e.translatedThreshold=s.getThreshold(l),h=jt(n.minPointLength,5),p=e.getColumnMetrics(),d=p.width,v=o(u(d,1+2*i)),m=p.offset;Wn.prototype.translate.apply(e),cn(e.points,function(n){var l=a(u(-999,n.plotY),s.len+999),p=jt(n.yBottom,c),g=n.plotX+m,y=o(a(l,p)),b=o(u(l,p)-y),w=s.stacks[(n.y<0?"-":"")+e.stackKey],E;r&&e.visible&&w&&w[n.x]&&w[n.x].setOffset(m,v),f(b)<h&&h&&(b=h,y=f(y-c)>h?p-h:c-(s.translate(n.y,0,1,0,1)<=c?h:0)),n.barX=g,n.pointWidth=d,n.shapeType="rect",n.shapeArgs=E=t.renderer.Element.prototype.crisp.call(0,i,g,y,v,b),i%2&&(E.y-=1,E.height+=1)})},getSymbol:H,drawLegendSymbol:Vn.prototype.drawLegendSymbol,drawGraph:H,drawPoints:function(){var t=this,n=t.options,r=t.chart.renderer,i;cn(t.points,function(s){var o=s.plotY,u=s.graphic;o!==e&&!isNaN(o)&&s.y!==null?(i=s.shapeArgs,u?(wn(u),u.animate(Tt(i))):s.graphic=u=r[s.shapeType](i).attr(s.pointAttr[s.selected?Y:Q]).add(t.group).shadow(n.shadow,null,n.stacking&&!n.borderRadius)):u&&(s.graphic=u.destroy())})},drawTracker:function(){var t=this,n=t.chart.pointer,r=t.options.cursor,i=r&&{cursor:r},s=function(n){var r=n.target,i;t.onMouseOver();while(r&&!i)i=r.point,r=r.parentNode;i!==e&&i.onMouseOver(n)};cn(t.points,function(e){e.graphic&&(e.graphic.element.point=e),e.dataLabel&&(e.dataLabel.element.point=e)}),t._hasTracking?t._hasTracking=!0:cn(t.trackerGroups,function(e){t[e]&&(t[e].addClass(z+"tracker").on("mouseover",s).on("mouseout",function(e){n.onTrackerMouseOut(e)}).css(i),C&&t[e].on("touchstart",s))})},alignDataLabel:function(e,t,n,r,i){var s=this.chart,o=s.inverted,u=e.dlBox||e.shapeArgs,a=e.below||e.plotY>jt(this.translatedThreshold,s.plotSizeY),f=jt(n.inside,!!this.options.stacking);u&&(r=Tt(u),o&&(r={x:s.plotWidth-r.y-r.height,y:s.plotHeight-r.x-r.width,width:r.height,height:r.width}),f||(o?(r.x+=a?0:r.width,r.width=0):(r.y+=a?r.height:0,r.height=0))),n.align=jt(n.align,!o||f?"center":a?"right":"left"),n.verticalAlign=jt(n.verticalAlign,o||f?"middle":a?"top":"bottom"),Wn.prototype.alignDataLabel.call(this,e,t,n,r,i)},animate:function(e){var t=this,n=this.yAxis,r=t.options,i=this.chart.inverted,s={},o;S&&(e?(s.scaleY=.001,o=a(n.pos+n.len,u(n.pos,n.toPixels(r.threshold))),i?s.translateX=o-n.len:s.translateY=o,t.group.attr(s)):(s.scaleY=1,s[i?"translateX":"translateY"]=n.pos,t.group.animate(s,t.options.animation),t.animate=null))},remove:function(){var e=this,t=e.chart;t.hasRendered&&cn(t.series,function(t){t.type===e.type&&(t.isDirty=!0)}),Wn.prototype.remove.apply(e,arguments)}});St.column=Qn,Sn.bar=Tt(Sn.column);var Gn=qt(Qn,{type:"bar",inverted:!0});St.bar=Gn,Sn.scatter=Tt(xn,{lineWidth:0,tooltip:{headerFormat:'<span style="font-size: 10px; color:{series.color}">{series.name}</span><br/>',pointFormat:"x: <b>{point.x}</b><br/>y: <b>{point.y}</b><br/>",followPointer:!0},stickyTracking:!1});var Yn=qt(Wn,{type:"scatter",sorted:!1,requireSorting:!1,noSharedTooltip:!0,trackerGroups:["markerGroup"],drawTracker:Qn.prototype.drawTracker,setTooltipPoints:H});St.scatter=Yn,Sn.pie=Tt(xn,{borderColor:"#FFFFFF",borderWidth:1,center:[null,null],colorByPoint:!0,dataLabels:{distance:30,enabled:!0,formatter:function(){return this.point.name}},ignoreHiddenPoint:!0,legendType:"point",marker:null,size:null,showInLegend:!1,slicedOffset:10,states:{hover:{brightness:.1,shadow:!1}},stickyTracking:!1,tooltip:{followPointer:!0}});var Zn=qt(zn,{init:function(){zn.prototype.init.apply(this,arguments);var e=this,t;return e.y<0&&(e.y=null),xt(e,{visible:e.visible!==!1,name:jt(e.name,"Slice")}),t=function(){e.slice()},vn(e,"select",t),vn(e,"unselect",t),e},setVisible:function(t){var n=this,r=n.series,i=r.chart,s;n.visible=n.options.visible=t=t===e?!n.visible:t,r.options.data[ln(n,r.data)]=n.options,s=t?"show":"hide",cn(["graphic","dataLabel","connector","shadowGroup"],function(e){n[e]&&n[e][s]()}),n.legendItem&&i.legend.colorizeItem(n,t),!r.isDirty&&r.options.ignoreHiddenPoint&&(r.isDirty=!0,i.redraw())},slice:function(e,t,n){var r=this,i=r.series,s=i.chart,o;sn(n,s),t=jt(t,!0),r.sliced=r.options.sliced=e=Pt(e)?e:!r.sliced,i.options.data[ln(r,i.data)]=r.options,o=e?r.slicedTranslation:{translateX:0,translateY:0},r.graphic.animate(o),r.shadowGroup&&r.shadowGroup.animate(o)}}),er={type:"pie",isCartesian:!1,pointClass:Zn,requireSorting:!1,noSharedTooltip:!0,trackerGroups:["group","dataLabelsGroup"],pointAttrToOptions:{stroke:"borderColor","stroke-width":"borderWidth",fill:"color"},getColor:H,animate:function(e){var t=this,n=t.points,r=t.startAngleRad;e||(cn(n,function(e){var n=e.graphic,i=e.shapeArgs;n&&(n.attr({r:t.center[3]/2,start:r,end:r}),n.animate({r:i.r,start:i.start,end:i.end},t.options.animation))}),t.animate=null)},setData:function(e,t){Wn.prototype.setData.call(this,e,!1),this.processData(),this.generatePoints(),jt(t,!0)&&this.chart.redraw()},getCenter:function(){var e=this.options,t=this.chart,n=2*(e.dataLabels&&e.dataLabels.enabled?0:e.slicedOffset||0),r=t.plotWidth-2*n,i=t.plotHeight-2*n,s=e.center,o=[jt(s[0],"50%"),jt(s[1],"50%"),e.size||"100%",e.innerSize||0],u=a(r,i),f;return dn(o,function(e,t){return f=/%$/.test(e),(f?[r,i,u,u][t]*Ct(e)/100:e)+(t<3?n:0)})},translate:function(e){this.generatePoints();var t=0,n=this,s=0,o=1e3,u=n.options,a=u.slicedOffset,f=a+u.borderWidth,p,d,v,m=n.startAngleRad=h/180*((u.startAngle||0)%360-90),g=n.points,y=2*h,b,w,E,S=u.dataLabels.distance,x=u.ignoreHiddenPoint,T,N=g.length,C;e||(n.center=e=n.getCenter()),n.getX=function(t,n){return v=r.asin((t-e[1])/(e[2]/2+S)),e[0]+(n?-1:1)*l(v)*(e[2]/2+S)};for(T=0;T<N;T++)C=g[T],t+=x&&!C.visible?0:C.y;for(T=0;T<N;T++){C=g[T],b=t?C.y/t:0,p=i((m+s*y)*o)/o;if(!x||C.visible)s+=b;d=i((m+s*y)*o)/o,C.shapeType="arc",C.shapeArgs={x:e[0],y:e[1],r:e[2]/2,innerR:e[3]/2,start:p,end:d},v=(d+p)/2,v>.75*y&&(v-=2*h),C.slicedTranslation={translateX:i(l(v)*a),translateY:i(c(v)*a)},w=l(v)*e[2]/2,E=c(v)*e[2]/2,C.tooltipPos=[e[0]+w*.7,e[1]+E*.7],C.half=v<y/4?0:1,C.angle=v,C.labelPos=[e[0]+w+l(v)*S,e[1]+E+c(v)*S,e[0]+w+l(v)*f,e[1]+E+c(v)*f,e[0]+w,e[1]+E,S<0?"center":C.half?"right":"left",v],C.percentage=b*100,C.total=t}this.setTooltipPoints()},drawGraph:null,drawPoints:function(){var e=this,t=e.chart,n=t.renderer,r,i,s=e.options.shadow,o,u;s&&!e.shadowGroup&&(e.shadowGroup=n.g("shadow").add(e.group)),cn(e.points,function(t){i=t.graphic,u=t.shapeArgs,o=t.shadowGroup,s&&!o&&(o=t.shadowGroup=n.g("shadow").add(e.shadowGroup)),r=t.sliced?t.slicedTranslation:{translateX:0,translateY:0},o&&o.attr(r),i?i.animate(xt(u,r)):t.graphic=i=n.arc(u).setRadialReference(e.center).attr(t.pointAttr[t.selected?Y:Q]).attr({"stroke-linejoin":"round"}).attr(r).add(e.group).shadow(s,o),t.visible===!1&&t.setVisible(!1)})},drawDataLabels:function(){var e=this,t=e.data,n,r=e.chart,s=e.options.dataLabels,o=jt(s.connectorPadding,10),a=jt(s.connectorWidth,1),l=r.plotWidth,c=r.plotHeight,h,p,d=jt(s.softConnector,!0),v=s.distance,m=e.center,g=m[2]/2,y=m[1],b=v>0,w,E,S,x,T=[[],[]],N,C,k,L,A,O,M=[0,0,0,0],_=function(e,t){return t.y-e.y},D=function(e,t){e.sort(function(
e,n){return e.angle!==undefined&&(n.angle-e.angle)*t})};if(!s.enabled&&!e._hasPointLabels)return;Wn.prototype.drawDataLabels.apply(e),cn(t,function(e){e.dataLabel&&T[e.half].push(e)}),A=0;while(!x&&t[A])x=t[A]&&t[A].dataLabel&&(t[A].dataLabel.getBBox().height||21),A++;A=2;while(A--){var P=[],H,B=[],j=T[A],F,I=j.length,q;D(j,A-.5);if(v>0){for(F=y-g-v;F<=y+g+v;F+=x)P.push(F);H=P.length;if(I>H){L=[].concat(j),L.sort(_),O=I;while(O--)L[O].rank=O;O=I;while(O--)j[O].rank>=H&&j.splice(O,1);I=j.length}for(O=0;O<I;O++){n=j[O],S=n.labelPos;var R=9999,z,X;for(X=0;X<H;X++)z=f(P[X]-S[1]),z<R&&(R=z,q=X);if(q<O&&P[O]!==null)q=O;else if(H<I-O+q&&P[O]!==null){q=H-I+O;while(P[q]===null)q++}else while(P[q]===null)q++;B.push({i:q,y:P[q]}),P[q]=null}B.sort(_)}for(O=0;O<I;O++){var V,K;n=j[O],S=n.labelPos,w=n.dataLabel,k=n.visible===!1?U:W,K=S[1];if(v>0){V=B.pop(),q=V.i,C=V.y;if(K>C&&P[q+1]!==null||K<C&&P[q-1]!==null)C=K}else C=K;N=s.justify?m[0]+(A?-1:1)*(g+v):e.getX(q===0||q===P.length-1?K:C,A),w._attr={visibility:k,align:S[6]},w._pos={x:N+s.x+({left:o,right:-o}[S[6]]||0),y:C+s.y-10},w.connX=N,w.connY=C,this.options.size===null&&(E=w.width,N-E<o?M[3]=u(i(E-N+o),M[3]):N+E>l-o&&(M[1]=u(i(N+E-l+o),M[1])),C-x/2<0?M[0]=u(i(-C+x/2),M[0]):C+x/2>c&&(M[2]=u(i(C+x/2-c),M[2])))}}if(Zt(M)===0||this.verifyDataLabelOverflow(M))this.placeDataLabels(),b&&a&&cn(this.points,function(t){h=t.connector,S=t.labelPos,w=t.dataLabel,w&&w._pos?(N=w.connX,C=w.connY,p=d?[$,N+(S[6]==="left"?5:-5),C,"C",N,C,2*S[2]-S[4],2*S[3]-S[5],S[2],S[3],J,S[4],S[5]]:[$,N+(S[6]==="left"?5:-5),C,J,S[2],S[3],J,S[4],S[5]],h?(h.animate({d:p}),h.attr("visibility",k)):t.connector=h=e.chart.renderer.path(p).attr({"stroke-width":a,stroke:s.connectorColor||t.color||"#606060",visibility:k}).add(e.group)):h&&(t.connector=h.destroy())})},verifyDataLabelOverflow:function(e){var t=this.center,n=this.options,r=n.center,i=n.minSize||80,s=i,o;return r[0]!==null?s=u(t[2]-u(e[1],e[3]),i):(s=u(t[2]-e[1]-e[3],i),t[0]+=(e[3]-e[1])/2),r[1]!==null?s=u(a(s,t[2]-u(e[0],e[2])),i):(s=u(a(s,t[2]-e[0]-e[2]),i),t[1]+=(e[0]-e[2])/2),s<t[2]?(t[2]=s,this.translate(t),cn(this.points,function(e){e.dataLabel&&(e.dataLabel._pos=null)}),this.drawDataLabels()):o=!0,o},placeDataLabels:function(){cn(this.points,function(e){var t=e.dataLabel,n;t&&(n=t._pos,n?(t.attr(t._attr),t[t.moved?"animate":"attr"](n),t.moved=!0):t&&t.attr({y:-999}))})},alignDataLabel:H,drawTracker:Qn.prototype.drawTracker,drawLegendSymbol:Vn.prototype.drawLegendSymbol,getSymbol:H};er=qt(Wn,er),St.pie=er,xt(Highcharts,{Axis:Fn,Chart:Un,Color:kn,Legend:Rn,Pointer:qn,Point:zn,Tick:Hn,Tooltip:In,Renderer:N,Series:Wn,SVGElement:Ln,SVGRenderer:An,arrayMin:Yt,arrayMax:Zt,charts:B,dateFormat:M,format:Vt,pathAnim:D,getOptions:Cn,hasBidiBug:x,isTouchDevice:w,numberFormat:Ut,seriesTypes:St,setOptions:Nn,addEvent:vn,removeEvent:mn,createElement:It,discardElement:tn,css:Ft,each:cn,extend:xt,map:dn,merge:Tt,pick:jt,splat:Bt,extendClass:qt,pInt:Ct,wrap:Wt,svg:S,canvas:T,vml:!S&&!T,product:j,version:F})}(),function(e,t){function n(e,t,n){this.init.call(this,e,t,n)}function r(e,t,n){e.call(this,t,n),this.chart.polar&&(this.closeSegment=function(e){var t=this.xAxis.center;e.push("L",t[0],t[1])},this.closedStacks=!0)}function i(e,t){var n=this.chart,r=this.options.animation,i=this.group,s=this.markerGroup,o=this.xAxis.center,u=n.plotLeft,a=n.plotTop;if(n.polar){if(n.renderer.isSVG)if(r===!0&&(r={}),t){if(n={translateX:o[0]+u,translateY:o[1]+a,scaleX:.001,scaleY:.001},i.attr(n),s)s.attrSetters=i.attrSetters,s.attr(n)}else n={translateX:u,translateY:a,scaleX:1,scaleY:1},i.animate(n,r),s&&s.animate(n,r),this.animate=null}else e.call(this,t)}var s=e.arrayMin,o=e.arrayMax,u=e.each,a=e.extend,f=e.merge,l=e.map,c=e.pick,h=e.pInt,p=e.getOptions().plotOptions,d=e.seriesTypes,v=e.extendClass,m=e.splat,g=e.wrap,y=e.Axis,b=e.Tick,w=e.Series,E=d.column.prototype,S=Math,x=S.round,T=S.floor,N=S.ceil,C=S.min,k=S.max,L=function(){};a(n.prototype,{init:function(e,t,n){var r=this,i=r.defaultOptions;r.chart=t,t.angular&&(i.background={}),r.options=e=f(i,e),(e=e.background)&&u([].concat(m(e)).reverse(),function(e){var t=e.backgroundColor,e=f(r.defaultBackgroundOptions,e);t&&(e.backgroundColor=t),e.color=e.backgroundColor,n.options.plotBands.unshift(e)})},defaultOptions:{center:["50%","50%"],size:"85%",startAngle:0},defaultBackgroundOptions:{shape:"circle",borderWidth:1,borderColor:"silver",backgroundColor:{linearGradient:{x1:0,y1:0,x2:0,y2:1},stops:[[0,"#FFF"],[1,"#DDD"]]},from:Number.MIN_VALUE,innerRadius:0,to:Number.MAX_VALUE,outerRadius:"105%"}});var A=y.prototype,b=b.prototype,O={getOffset:L,redraw:function(){this.isDirty=!1},render:function(){this.isDirty=!1},setScale:L,setCategories:L,setTitle:L},M={isRadial:!0,defaultRadialGaugeOptions:{labels:{align:"center",x:0,y:null},minorGridLineWidth:0,minorTickInterval:"auto",minorTickLength:10,minorTickPosition:"inside",minorTickWidth:1,plotBands:[],tickLength:10,tickPosition:"inside",tickWidth:2,title:{rotation:0},zIndex:2},defaultRadialXOptions:{gridLineWidth:1,labels:{align:null,distance:15,x:0,y:null},maxPadding:0,minPadding:0,plotBands:[],showLastLabel:!1,tickLength:0},defaultRadialYOptions:{gridLineInterpolation:"circle",labels:{align:"right",x:-3,y:-2},plotBands:[],showLastLabel:!1,title:{x:4,text:null,rotation:90}},setOptions:function(e){this.options=f(this.defaultOptions,this.defaultRadialOptions,e)},getOffset:function(){A.getOffset.call(this),this.chart.axisOffset[this.side]=0,this.center=this.pane.center=d.pie.prototype.getCenter.call(this.pane)},getLinePath:function(e,t){var n=this.center,t=c(t,n[2]/2-this.offset);return this.chart.renderer.symbols.arc(this.left+n[0],this.top+n[1],t,t,{start:this.startAngleRad,end:this.endAngleRad,open:!0,innerR:0})},setAxisTranslation:function(){A.setAxisTranslation.call(this),this.center&&(this.transA=this.isCircular?(this.endAngleRad-this.startAngleRad)/(this.max-this.min||1):this.center[2]/2/(this.max-this.min||1),this.isXAxis)&&(this.minPixelPadding=this.transA*this.minPointOffset+(this.reversed?(this.endAngleRad-this.startAngleRad)/4:0))},beforeSetTickPositions:function(){this.autoConnect&&(this.max+=this.categories&&1||this.pointRange||this.closestPointRange)},setAxisSize:function(){A.setAxisSize.call(this),this.center&&(this.len=this.width=this.height=this.isCircular?this.center[2]*(this.endAngleRad-this.startAngleRad)/2:this.center[2]/2)},getPosition:function(e,t){return this.isCircular||(t=this.translate(e),e=this.min),this.postTranslate(this.translate(e),c(t,this.center[2]/2)-this.offset)},postTranslate:function(e,t){var n=this.chart,r=this.center,e=this.startAngleRad+e;return{x:n.plotLeft+r[0]+Math.cos(e)*t,y:n.plotTop+r[1]+Math.sin(e)*t}},getPlotBandPath:function(e,t,n){var r=this.center,i=this.startAngleRad,s=r[2]/2,o=[c(n.outerRadius,"100%"),n.innerRadius,c(n.thickness,10)],u=/%$/,a,f=this.isCircular;return this.options.gridLineInterpolation==="polygon"?r=this.getPlotLinePath(e).concat(this.getPlotLinePath(t,!0)):(f||(o[0]=this.translate(e),o[1]=this.translate(t)),o=l(o,function(e){return u.test(e)&&(e=h(e,10)*s/100),e}),n.shape==="circle"||!f?(e=-Math.PI/2,t=Math.PI*1.5,a=!0):(e=i+this.translate(e),t=i+this.translate(t)),r=this.chart.renderer.symbols.arc(this.left+r[0],this.top+r[1],o[0],o[0],{start:e,end:t,innerR:c(o[1],o[0]-o[2]),open:a})),r},getPlotLinePath:function(e,t){var n=this.center,r=this.chart,i=this.getPosition(e),s,o,a;return this.isCircular?a=["M",n[0]+r.plotLeft,n[1]+r.plotTop,"L",i.x,i.y]:this.options.gridLineInterpolation==="circle"?(e=this.translate(e))&&(a=this.getLinePath(0,e)):(s=r.xAxis[0],a=[],e=this.translate(e),n=s.tickPositions,s.autoConnect&&(n=n.concat([n[0]])),t&&(n=[].concat(n).reverse()),u(n,function(t,n){o=s.getPosition(t,e),a.push(n?"L":"M",o.x,o.y)})),a},getTitlePosition:function(){var e=this.center,t=this.chart,n=this.options.title;return{x:t.plotLeft+e[0]+(n.x||0),y:t.plotTop+e[1]-{high:.5,middle:.25,low:0}[n.align]*e[2]+(n.y||0)}}};g(A,"init",function(e,r,i){var s,o=r.angular,u=r.polar,l=i.isX,h=o&&l,p,d;d=r.options;var v=i.pane||0;if(o){if(a(this,h?O:M),p=!l)this.defaultRadialOptions=this.defaultRadialGaugeOptions}else u&&(a(this,M),this.defaultRadialOptions=(p=l)?this.defaultRadialXOptions:f(this.defaultYAxisOptions,this.defaultRadialYOptions));e.call(this,r,i),!h&&(o||u)&&(e=this.options,r.panes||(r.panes=[]),this.pane=(s=r.panes[v]=r.panes[v]||new n(m(d.pane)[v],r,this),v=s),v=v.options,r.inverted=!1,d.chart.zoomType=null,this.startAngleRad=r=(v.startAngle-90)*Math.PI/180,this.endAngleRad=d=(c(v.endAngle,v.startAngle+360)-90)*Math.PI/180,this.offset=e.offset||0,(this.isCircular=p)&&i.max===t&&d-r===2*Math.PI&&(this.autoConnect=!0))}),g(b,"getPosition",function(e,t,n,r,i){var s=this.axis;return s.getPosition?s.getPosition(n):e.call(this,t,n,r,i)}),g(b,"getLabelPosition",function(e,t,n,r,i,s,o,u,a){var f=this.axis,l=s.y,p=s.align,d=(f.translate(this.pos)+f.startAngleRad+Math.PI/2)/Math.PI*180;return f.isRadial?(e=f.getPosition(this.pos,f.center[2]/2+c(s.distance,-25)),s.rotation==="auto"?r.attr({rotation:d}):l===null&&(l=h(r.styles.lineHeight)*.9-r.getBBox().height/2),p===null&&(p=f.isCircular?d>20&&d<160?"left":d>200&&d<340?"right":"center":"center",r.attr({align:p})),e.x+=s.x,e.y+=l):e=e.call(this,t,n,r,i,s,o,u,a),e}),g(b,"getMarkPath",function(e,t,n,r,i,s,o){var u=this.axis;return u.isRadial?(e=u.getPosition(this.pos,u.center[2]/2+r),t=["M",t,n,"L",e.x,e.y]):t=e.call(this,t,n,r,i,s,o),t}),p.arearange=f(p.area,{lineWidth:1,marker:null,threshold:null,tooltip:{pointFormat:'<span style="color:{series.color}">{series.name}</span>: <b>{point.low}</b> - <b>{point.high}</b><br/>'},trackByArea:!0,dataLabels:{verticalAlign:null,xLow:0,xHigh:0,yLow:0,yHigh:0}}),d.arearange=e.extendClass(d.area,{type:"arearange",pointArrayMap:["low","high"],toYData:function(e){return[e.low,e.high]},pointValKey:"low",translate:function(){var e=this.yAxis;d.area.prototype.translate.apply(this),u(this.points,function(t){t.y!==null&&(t.plotLow=t.plotY,t.plotHigh=e.translate(t.high,0,1,0,1))})},getSegmentPath:function(e){var t=[],n=e.length,r=w.prototype.getSegmentPath,i,s;s=this.options;for(var o=s.step;n--;)i=e[n],t.push({plotX:i.plotX,plotY:i.plotHigh});return e=r.call(this,e),o&&(o===!0&&(o="left"),s.step={left:"right",center:"center",right:"left"}[o]),t=r.call(this,t),s.step=o,s=[].concat(e,t),t[0]="L",this.areaPath=this.areaPath.concat(e,t),s},drawDataLabels:function(){var e=this.data,t=e.length,n,r=[],i=w.prototype,s=this.options.dataLabels,o,u=this.chart.inverted;if(s.enabled||this._hasPointLabels){for(n=t;n--;)o=e[n],o.y=o.high,o.plotY=o.plotHigh,r[n]=o.dataLabel,o.dataLabel=o.dataLabelUpper,o.below=!1,u?(s.align="left",s.x=s.xHigh):s.y=s.yHigh;i.drawDataLabels.apply(this,arguments);for(n=t;n--;)o=e[n],o.dataLabelUpper=o.dataLabel,o.dataLabel=r[n],o.y=o.low,o.plotY=o.plotLow,o.below=!0,u?(s.align="right",s.x=s.xLow):s.y=s.yLow;i.drawDataLabels.apply(this,arguments)}},alignDataLabel:d.column.prototype.alignDataLabel,getSymbol:d.column.prototype.getSymbol,drawPoints:L}),p.areasplinerange=f(p.arearange),d.areasplinerange=v(d.arearange,{type:"areasplinerange",getPointSpline:d.spline.prototype.getPointSpline}),p.columnrange=f(p.column,p.arearange,{lineWidth:1,pointRange:null}),d.columnrange=v(d.arearange,{type:"columnrange",translate:function(){var e=this.yAxis,t;E.translate.apply(this),u(this.points,function(n){var r=n.shapeArgs;n.plotHigh=t=e.translate(n.high,0,1,0,1),n.plotLow=n.plotY,r.y=t,r.height=n.plotY-t})},trackerGroups:["group","dataLabels"],drawGraph:L,pointAttrToOptions:E.pointAttrToOptions,drawPoints:E.drawPoints,drawTracker:E.drawTracker,animate:E.animate,getColumnMetrics:E.getColumnMetrics}),p.gauge=f(p.line,{dataLabels:{enabled:!0,y:15,borderWidth:1,borderColor:"silver",borderRadius:3,style:{fontWeight:"bold"},verticalAlign:"top",zIndex:2},dial:{},pivot:{},tooltip:{headerFormat:""},showInLegend:!1}),b={type:"gauge",pointClass:e.extendClass(e.Point,{setState:function(e){this.state=e}}),angular:!0,drawGraph:L,trackerGroups:["group","dataLabels"],translate:function(){var e=this.yAxis,t=this.options,n=e.center;this.generatePoints(),u(this.points,function(r){var i=f(t.dial,r.dial),s=h(c(i.radius,80))*n[2]/200,o=h(c(i.baseLength,70))*s/100,u=h(c(i.rearLength,10))*s/100,a=i.baseWidth||3,l=i.topWidth||1,p=e.startAngleRad+e.translate(r.y,null,null,null,!0);t.wrap===!1&&(p=Math.max(e.startAngleRad,Math.min(e.endAngleRad,p))),p=p*180/Math.PI,r.shapeType="path",r.shapeArgs={d:i.path||["M",-u,-a/2,"L",o,-a/2,s,-l/2,s,l/2,o,a/2,-u,a/2,"z"],translateX:n[0],translateY:n[1],rotation:p},r.plotX=n[0],r.plotY=n[1]})},drawPoints:function(){var e=this,t=e.yAxis.center,n=e.pivot,r=e.options,i=r.pivot,s=e.chart.renderer;u(e.points,function(t){var n=t.graphic,i=t.shapeArgs,o=i.d,u=f(r.dial,t.dial);n?(n.animate(i),i.d=o):t.graphic=s[t.shapeType](i).attr({stroke:u.borderColor||"none","stroke-width":u.borderWidth||0,fill:u.backgroundColor||"black",rotation:i.rotation}).add(e.group)}),n?n.animate({translateX:t[0],translateY:t[1]}):e.pivot=s.circle(0,0,c(i.radius,5)).attr({"stroke-width":i.borderWidth||0,stroke:i.borderColor||"silver",fill:i.backgroundColor||"black"}).translate(t[0],t[1]).add(e.group)},animate:function(e){var t=this;e||(u(t.points,function(e){var n=e.graphic;n&&(n.attr({rotation:t.yAxis.startAngleRad*180/Math.PI}),n.animate({rotation:e.shapeArgs.rotation},t.options.animation))}),t.animate=null)},render:function(){this.group=this.plotGroup("group","series",this.visible?"visible":"hidden",this.options.zIndex,this.chart.seriesGroup),d.pie.prototype.render.call(this),this.group.clip(this.chart.clipRect)},setData:d.pie.prototype.setData,drawTracker:d.column.prototype.drawTracker},d.gauge=e.extendClass(d.line,b),p.boxplot=f(p.column,{fillColor:"#FFFFFF",lineWidth:1,medianWidth:2,states:{hover:{brightness:-0.3}},threshold:null,tooltip:{pointFormat:'<span style="color:{series.color};font-weight:bold">{series.name}</span><br/>Minimum: {point.low}<br/>Lower quartile: {point.q1}<br/>Median: {point.median}<br/>Higher quartile: {point.q3}<br/>Maximum: {point.high}<br/>'},whiskerLength:"50%",whiskerWidth:2}),d.boxplot=v(d.column,{type:"boxplot",pointArrayMap:["low","q1","median","q3","high"],toYData:function(e){return[e.low,e.q1,e.median,e.q3,e.high]},pointValKey:"high",pointAttrToOptions:{fill:"fillColor",stroke:"color","stroke-width":"lineWidth"},drawDataLabels:L,translate:function(){var e=this.yAxis,t=this.pointArrayMap;d.column.prototype.translate.apply(this),u(this.points,function(n){u(t,function(t){n[t]!==null&&(n[t+"Plot"]=e.translate(n[t],0,1,0,1))})})},drawPoints:function(){var e=this,n=e.points,r=e.options,i=e.chart.renderer,s,o,a,f,l,c,h,p,d,v,m,g,y,b,w,E,S,N,C,k,L,A,O=e.doQuartiles!==!1,M=parseInt(e.options.whiskerLength,10)/100;u(n,function(n){d=n.graphic,L=n.shapeArgs,m={},b={},E={},A=n.color||e.color,n.plotY!==t&&((s=n.pointAttr[n.selected?"selected":""],S=L.width,N=T(L.x),C=N+S,k=x(S/2),o=T(O?n.q1Plot:n.lowPlot),a=T(O?n.q3Plot:n.lowPlot),f=T(n.highPlot),l=T(n.lowPlot),m.stroke=n.stemColor||r.stemColor||A,m["stroke-width"]=n.stemWidth||r.stemWidth||r.lineWidth,m.dashstyle=n.stemDashStyle||r.stemDashStyle,b.stroke=n.whiskerColor||r.whiskerColor||A,b["stroke-width"]=n.whiskerWidth||r.whiskerWidth||r.lineWidth,E.stroke=n.medianColor||r.medianColor||A,E["stroke-width"]=n.medianWidth||r.medianWidth||r.lineWidth,h=m["stroke-width"]%2/2,p=N+k+h,v=["M",p,a,"L",p,f,"M",p,o,"L",p,l,"z"],O&&(h=s["stroke-width"]%2/2,p=T(p)+h,o=T(o)+h,a=T(a)+h,N+=h,C+=h,g=["M",N,a,"L",N,o,"L",C,o,"L",C,a,"L",N,a,"z"]),M&&(h=b["stroke-width"]%2/2,f+=h,l+=h,y=["M",p-k*M,f,"L",p+k*M,f,"M",p-k*M,l,"L",p+k*M,l]),h=E["stroke-width"]%2/2,c=x(n.medianPlot)+h,w=["M",N,c,C,c,"z"],d)?(n.stem.animate({d:v}),M&&n.whiskers.animate({d:y}),O&&n.box.animate({d:g}),n.medianShape.animate({d:w})):(n.graphic=d=i.g().add(e.group),n.stem=i.path(v).attr(m).add(d),M&&(n.whiskers=i.path(y).attr(b).add(d)),O&&(n.box=i.path(g).attr(s).add(d)),n.medianShape=i.path(w).attr(E).add(d)))})}}),p.errorbar=f(p.boxplot,{color:"#000000",grouping:!1,linkedTo:":previous",tooltip:{pointFormat:p.arearange.tooltip.pointFormat},whiskerWidth:null}),d.errorbar=v(d.boxplot,{type:"errorbar",pointArrayMap:["low","high"],toYData:function(e){return[e.low,e.high]},pointValKey:"high",doQuartiles:!1,getColumnMetrics:function(){return this.linkedParent&&this.linkedParent.columnMetrics||d.column.prototype.getColumnMetrics.call(this)}}),g(A,"getSeriesExtremes",function(e,t){e.call(this,t);if(!this.isXAxis){var n=this,r=[],i=!0;u(n.series,function(e){if(e.visible&&e.stackKey&&e.type==="waterfall"&&HighchartsAdapter.inArray(e.stackKey)===-1){i&&(n.dataMin=n.dataMax=null,i=!1);var t=e.processedYData,s=t.length,o=t[0],u=t[0],a=e.options.threshold,f=n.stacks,l=e.stackKey,h="-"+l,p,d,v,m;for(m=0;m<s;m++)v=t[m]<a?h:l,p=f[v][m].total,m>a&&(p+=d,f[v][m].setTotal(p),f[v][m]._cum=null),p<o&&(o=p),p>u&&(u=p),d=p;e.dataMin=o,e.dataMax=u,n.dataMin=C(c(n.dataMin,o),o,a),n.dataMax=k(c(n.dataMax,u),u,a),r.push(e.stackKey),typeof a=="number"&&(n.dataMin>=a?(n.dataMin=a,n.ignoreMinPadding=!0):n.dataMax<a&&(n.dataMax=a,n.ignoreMaxPadding=!0))}})}}),p.waterfall=f(p.column,{lineWidth:1,lineColor:"#333",dashStyle:"dot",borderColor:"#333"}),d.waterfall=v(d.column,{type:"waterfall",upColorProp:"fill",pointArrayMap:["y","low"],pointValKey:"y",init:function(e,t){t.stacking=!0,d.column.prototype.init.call(this,e,t)},translate:function(){var e=this.yAxis,t,n,r,i,s,o,u,a,f,l,c,h,p=this.options.borderWidth%2/2;d.column.prototype.translate.apply(this),r=this.points,f=u=r[0],o=a=r[0].y;for(n=1,t=r.length;n<t;n++)i=r[n],s=i.shapeArgs,l=this.getStack(n),c=this.getStack(n-1),h=this.getStackY(c),f===null&&(f=i,a=0),i.y&&!i.isSum&&!i.isIntermediateSum&&(o+=i.y,a+=i.y),i.isSum||i.isIntermediateSum?(i.isIntermediateSum?(l=this.getSumEdges(f,r[n-1]),i.y=a,f=null):(l=this.getSumEdges(u,r[n-1]),i.y=o),s.y=i.plotY=l[1],s.height=l[0]-l[1]):i.y<0?(c=l._cum===null?c.total:l._cum,l._cum=c+i.y,i=N(e.translate(c,0,1))-p,l=e.translate(l._cum,0,1),s.y=i,s.height=N(l-i)):s.height=T(h-s.y)},processData:function(e){w.prototype.processData.call(this,e);var e=this.yData,t=e.length,n,r;for(r=0;r<t;r++)n=e[r],n!==null&&typeof n!="number"&&(e[r]=n==="sum"?null:n==="intermediateSum"?null:n[0])},toYData:function(e){return e.isSum?"sum":e.isIntermediateSum?"intermediateSum":[e.y]},getAttribs:function(){d.column.prototype.getAttribs.apply(this,arguments);var t=this.options,n=t.states,r=t.upColor||this.color,t=e.Color(r).brighten(.1).get(),i=f(this.pointAttr),s=this.upColorProp;i[""][s]=r,i.hover[s]=n.hover.upColor||t,i.select[s]=n.select.upColor||r,u(this.points,function(e){e.y>0&&!e.color&&(e.pointAttr=i,e.color=r)})},getGraphPath:function(){var e=this.data,t=e.length,n=x(this.options.lineWidth+this.options.borderWidth)%2/2,r=[],i,s,o;for(o=1;o<t;o++)s=e[o].shapeArgs,i=e[o-1].shapeArgs,s=["M",i.x+i.width,i.y+n,"L",s.x,i.y+n],e[o-1].y<0&&(s[2]+=i.height,s[5]+=i.height),r=r.concat(s);return r},getStack:function(e){var t=this.yAxis.stacks,n=this.stackKey;return this.processedYData[e]<this.options.threshold&&(n="-"+n),t[n][e]},getStackY:function(e){return N(this.yAxis.translate(e.total,null,!0))},getSumEdges:function(e,t){var n,r,i;return r=this.options.threshold,n=e.y>=r?e.shapeArgs.y+e.shapeArgs.height:e.shapeArgs.y,r=t.y>=r?t.shapeArgs.y:t.shapeArgs.y+t.shapeArgs.height,r>n&&(i=n,n=r,r=i),[n,r]},drawGraph:w.prototype.drawGraph}),p.bubble=f(p.scatter,{dataLabels:{inside:!0,style:{color:"white",textShadow:"0px 0px 3px black"},verticalAlign:"middle"},marker:{lineColor:null,lineWidth:1},minSize:8,maxSize:"20%",tooltip:{pointFormat:"({point.x}, {point.y}), Size: {point.z}"},zThreshold:0}),d.bubble=v(d.scatter,{type:"bubble",pointArrayMap:["y","z"],trackerGroups:["group","dataLabelsGroup"],pointAttrToOptions:{stroke:"lineColor","stroke-width":"lineWidth",fill:"fillColor"},applyOpacity:function(t){var n=this.options.marker,r=c(n.fillOpacity,.5),t=t||n.fillColor||this.color;return r!==1&&(t=e.Color(t).setOpacity(r).get("rgba")),t},convertAttribs:function(){var e=w.prototype.convertAttribs.apply(this,arguments);return e.fill=this.applyOpacity(e.fill),e},getRadii:function(e,t,n,r){var i,s,o,u=this.zData,a=[];for(s=0,i=u.length;s<i;s++)o=t-e,o=o>0?(u[s]-e)/(t-e):.5,a.push(S.round(n+o*(r-n))/2);this.radii=a},animate:function(e){var t=this.options.animation;e||(u(this.points,function(e){var n=e.graphic,e=e.shapeArgs;n&&e&&(n.attr("r",1),n.animate({r:e.r},t))}),this.animate=null)},translate:function(){var e,t=this.data,n,r,i=this.radii;d.scatter.prototype.translate.call(this);for(e=t.length;e--;)n=t[e],r=i[e],n.negative=n.z<(this.options.zThreshold||0),r>=this.minPxSize/2?(n.shapeType="circle",n.shapeArgs={x:n.plotX,y:n.plotY,r:r},n.dlBox={x:n.plotX-r,y:n.plotY-r,width:2*r,height:2*r}):n.shapeArgs=n.plotY=n.dlBox=null},drawLegendSymbol:function(e,t){var n=h(e.itemStyle.fontSize)/2;t.legendSymbol=this.chart.renderer.circle(n,e.baseline-n,n).attr({zIndex:3}).add(t.legendGroup)},drawPoints:d.column.prototype.drawPoints,alignDataLabel:d.column.prototype.alignDataLabel}),y.prototype.beforePadding=function(){var e=this.len,n=this.chart,r=0,i=e,a=this.isXAxis,f=a?"xData":"yData",l=this.min,p={},d=S.min(n.plotWidth,n.plotHeight),v=Number.MAX_VALUE,m=-Number.MAX_VALUE,g=this.max-l,y=e/g,b=[];this.tickPositions&&(u(this.series,function(e){var t=e.options;e.type==="bubble"&&e.visible&&(b.push(e),a)&&(u(["minSize","maxSize"],function(e){var n=t[e],r=/%$/.test(n),n=h(n);p[e]=r?d*n/100:n}),e.minPxSize=p.minSize,e=e.zData,v=S.min(v,S.max(s(e),t.displayNegative===!1?t.zThreshold:-Number.MAX_VALUE)),m=S.max(m,o(e)))}),u(b,function(e){var t=e[f],n=t.length,s;a&&e.getRadii(v,m,p.minSize,p.maxSize);if(g>0)for(;n--;)s=e.radii[n],r=Math.min((t[n]-l)*y-s,r),i=Math.max((t[n]-l)*y+s,i)}),g>0&&c(this.options.min,this.userMin)===t&&(i-=e,y*=(e+r-i)/e,this.min+=r/y,this.max+=i/y))};var _=w.prototype,p=e.Pointer.prototype;_.toXY=function(e){var t,n=this.chart;t=e.plotX;var r=e.plotY;e.rectPlotX=t,e.rectPlotY=r,e.clientX=t/Math.PI*180,t=this.xAxis.postTranslate(e.plotX,this.yAxis.len-r),e.plotX=e.polarPlotX=t.x-n.plotLeft,e.plotY=e.polarPlotY=t.y-n.plotTop},g(d.area.prototype,"init",r),g(d.areaspline.prototype,"init",r),g(d.spline.prototype,"getPointSpline",function(e,t,n,r){var i,s,o,u,a,f,l;return this.chart.polar?(i=n.plotX,s=n.plotY,e=t[r-1],o=t[r+1],this.connectEnds&&(e||(e=t[t.length-2]),o||(o=t[1])),e&&o&&(u=e.plotX,a=e.plotY,t=o.plotX,f=o.plotY,u=(1.5*i+u)/2.5,a=(1.5*s+a)/2.5,o=(1.5*i+t)/2.5,l=(1.5*s+f)/2.5,t=Math.sqrt(Math.pow(u-i,2)+Math.pow(a-s,2)),f=Math.sqrt(Math.pow(o-i,2)+Math.pow(l-s,2)),u=Math.atan2(a-s,u-i),a=Math.atan2(l-s,o-i),l=Math.PI/2+(u+a)/2,Math.abs(u-l)>Math.PI/2&&(l-=Math.PI),u=i+Math.cos(l)*t,a=s+Math.sin(l)*t,o=i+Math.cos(Math.PI+l)*f,l=s+Math.sin(Math.PI+l)*f,n.rightContX=o,n.rightContY=l),r?(n=["C",e.rightContX||e.plotX,e.rightContY||e.plotY,u||i,a||s,i,s],e.rightContX=e.rightContY=null):n=["M",i,s]):n=e.call(this,t,n,r),n}),g(_,"translate",function(e){e.call(this);if(this.chart.polar&&!this.preventPostTranslate)for(var e=this.points,t=e.length;t--;)this.toXY(e[t])}),g(_,"getSegmentPath",function(e,t){var n=this.points;return this.chart.polar&&this.options.connectEnds!==!1&&t[t.length-1]===n[n.length-1]&&n[0].y!==null&&(this.connectEnds=!0,t=[].concat(t,[n[0]])),e.call(this,t)}),g(_,"animate",i),g(E,"animate",i),g(_,"setTooltipPoints",function(e,t){return this.chart.polar&&a(this.xAxis,{tooltipLen:360}),e.call(this,t)}),g(E,"translate",function(e){var t=this.xAxis,n=this.yAxis.len,r=t.center,i=t.startAngleRad,s=this.chart.renderer,o,u;this.preventPostTranslate=!0,e.call(this);if(t.isRadial){t=this.points;for(u=t.length;u--;)o=t[u],e=o.barX+i,o.shapeType="path",o.shapeArgs={d:s.symbols.arc(r[0],r[1],n-o.plotY,null,{start:e,end:e+o.pointWidth,innerR:n-c(o.yBottom,n)})},this.toXY(o)}}),g(E,"alignDataLabel",function(e,t,n,r,i,s){this.chart.polar?(e=t.rectPlotX/Math.PI*180,r.align===null&&(r.align=e>20&&e<160?"left":e>200&&e<340?"right":"center"),r.verticalAlign===null&&(r.verticalAlign=e<45||e>315?"bottom":e>135&&e<225?"top":"middle"),_.alignDataLabel.call(this,t,n,r,i,s)):e.call(this,t,n,r,i,s)}),g(p,"getIndex",function(e,t){var n,r=this.chart,i;return r.polar?(i=r.xAxis[0].center,n=t.chartX-i[0]-r.plotLeft,r=t.chartY-i[1]-r.plotTop,n=180-Math.round(Math.atan2(n,r)/Math.PI*180)):n=e.call(this,t),n}),g(p,"getCoordinates",function(e,t){var n=this.chart,r={xAxis:[],yAxis:[]};return n.polar?u(n.axes,function(e){var i=e.isXAxis,s=e.center,o=t.chartX-s[0]-n.plotLeft,s=t.chartY-s[1]-n.plotTop;r[i?"xAxis":"yAxis"].push({axis:e,value:e.translate(i?Math.PI-Math.atan2(o,s):Math.sqrt(Math.pow(o,2)+Math.pow(s,2)),!0)})}):r=e.call(this,t),r})}(Highcharts),function(e){var t=e.Chart,n=e.addEvent,r=e.removeEvent,i=e.createElement,s=e.discardElement,o=e.css,u=e.merge,a=e.each,f=e.extend,l=Math,c=l.max,h=document,p=window,d=e.isTouchDevice,v="M",m="L",g="div",y="hidden",b="none",w="highcharts-",E="absolute",S="px",x,T=e.Renderer.prototype.symbols,N=e.getOptions(),C;f(N.lang,{downloadPNG:"Download PNG image",downloadJPEG:"Download JPEG image",downloadPDF:"Download PDF document",downloadSVG:"Download SVG vector image",contextButtonTitle:"Chart context menu"}),N.navigation={menuStyle:{border:"1px solid #A0A0A0",background:"#FFFFFF",padding:"5px 0"},menuItemStyle:{padding:"0 10px",background:b,color:"#303030",fontSize:d?"14px":"11px"},menuItemHoverStyle:{background:"#4572A5",color:"#FFFFFF"},buttonOptions:{symbolFill:"#E0E0E0",symbolSize:14,symbolStroke:"#666",symbolStrokeWidth:3,symbolX:12.5,symbolY:10.5,align:"right",buttonSpacing:3,height:22,theme:{fill:"white",stroke:"none"},verticalAlign:"top",width:24}},N.exporting={type:"image/png",url:"http://export.highcharts.com/",buttons:{contextButton:{symbol:"menu",_titleKey:"contextButtonTitle",menuItems:[{text:"Print chart",onclick:function(){this.print()}},{separator:!0},{textKey:"downloadPNG",onclick:function(){this.exportChart()}},{textKey:"downloadJPEG",onclick:function(){this.exportChart({type:"image/jpeg"})}},{textKey:"downloadPDF",onclick:function(){this.exportChart({type:"application/pdf"})}},{textKey:"downloadSVG",onclick:function(){this.exportChart({type:"image/svg+xml"})}}]}}},e.post=function(e,t){var n,r;r=i("form",{method:"post",action:e,enctype:"multipart/form-data"},{display:b},h.body);for(n in t)i("input",{type:y,name:n,value:t[n]},null,r);r.submit(),s(r)},f(t.prototype,{getSVG:function(t){var n=this,r,o,l,c,p,d,v,m,y=u(n.options,t);return h.createElementNS||(h.createElementNS=function(e,t){return h.createElement(t)}),o=i(g,null,{position:E,top:"-9999em",width:n.chartWidth+S,height:n.chartHeight+S},h.body),v=n.renderTo.style.width,m=n.renderTo.style.height,p=y.exporting.sourceWidth||y.chart.width||/px$/.test(v)&&parseInt(v,10)||600,d=y.exporting.sourceHeight||y.chart.height||/px$/.test(m)&&parseInt(m,10)||400,f(y.chart,{animation:!1,renderTo:o,forExport:!0,width:p,height:d}),y.exporting.enabled=!1,y.chart.plotBackgroundImage=null,y.series=[],a(n.series,function(e){c=u(e.options,{animation:!1,showCheckbox:!1,visible:e.visible}),c.isInternal||y.series.push(c)}),r=new e.Chart(y,n.callback),a(["xAxis","yAxis"],function(e){a(n[e],function(t,n){var i=r[e][n],s=t.getExtremes(),o=s.userMin,u=s.userMax;(o!==x||u!==x)&&i.setExtremes(o,u,!0,!1)})}),l=r.container.innerHTML,y=null,r.destroy(),s(o),l=l.replace(/zIndex="[^"]+"/g,"").replace(/isShadow="[^"]+"/g,"").replace(/symbolName="[^"]+"/g,"").replace(/jQuery[0-9]+="[^"]+"/g,"").replace(/url\([^#]+#/g,"url(#").replace(/<svg /,'<svg xmlns:xlink="http://www.w3.org/1999/xlink" ').replace(/ href=/g," xlink:href=").replace(/\n/," ").replace(/<\/svg>.*?$/,"</svg>").replace(/&nbsp;/g," ").replace(/&shy;/g,"­").replace(/<IMG /g,"<image ").replace(/height=([^" ]+)/g,'height="$1"').replace(/width=([^" ]+)/g,'width="$1"').replace(/hc-svg-href="([^"]+)">/g,'xlink:href="$1"/>').replace(/id=([^" >]+)/g,'id="$1"').replace(/class=([^" ]+)/g,'class="$1"').replace(/ transform /g," ").replace(/:(path|rect)/g,"$1").replace(/style="([^"]+)"/g,function(e){return e.toLowerCase()}),l=l.replace(/(url\(#highcharts-[0-9]+)&quot;/g,"$1").replace(/&quot;/g,"'"),l.match(/ xmlns="/g).length===2&&(l=l.replace(/xmlns="[^"]+"/,"")),l},exportChart:function(t,n){t=t||{};var r=this,i=r.getSVG(u({chart:{borderRadius:0}},r.options.exporting.chartOptions,n,{exporting:{sourceWidth:t.sourceWidth,sourceHeight:t.sourceHeight}}));t=u(r.options.exporting,t),e.post(t.url,{filename:t.filename||"chart",type:t.type,width:t.width||0,scale:t.scale||2,svg:i})},print:function(){var e=this,t=e.container,n=[],r=t.parentNode,i=h.body,s=i.childNodes;if(e.isPrinting)return;e.isPrinting=!0,a(s,function(e,t){e.nodeType===1&&(n[t]=e.style.display,e.style.display=b)}),i.appendChild(t),p.focus(),p.print(),setTimeout(function(){r.appendChild(t),a(s,function(e,t){e.nodeType===1&&(e.style.display=n[t])}),e.isPrinting=!1},1e3)},contextMenu:function(e,t,r,s,u,l,h){var p=this,d=p.options.navigation,v=d.menuItemStyle,m=p.chartWidth,y=p.chartHeight,x="cache-"+e,T=p[x],N=c(u,l),C="3px 3px 10px #888",k,L,A,O;T||(p[x]=T=i(g,{className:w+e},{position:E,zIndex:1e3,padding:N+S},p.container),k=i(g,null,f({MozBoxShadow:C,WebkitBoxShadow:C,boxShadow:C},d.menuStyle),T),L=function(){o(T,{display:b}),h&&h.setState(0)},n(T,"mouseleave",function(){A=setTimeout(L,500)}),n(T,"mouseenter",function(){clearTimeout(A)}),a(t,function(e){if(e){var t=e.separator?i("hr",null,null,k):i(g,{onmouseover:function(){o(this,d.menuItemHoverStyle)},onmouseout:function(){o(this,v)},onclick:function(){L(),e.onclick.apply(p,arguments)},innerHTML:e.text||p.options.lang[e.textKey]},f({cursor:"pointer"},v),k);p.exportDivElements.push(t)}}),p.exportDivElements.push(k,T),p.exportMenuWidth=T.offsetWidth,p.exportMenuHeight=T.offsetHeight),O={display:"block"},r+p.exportMenuWidth>m?O.right=m-r-u-N+S:O.left=r-N+S,s+l+p.exportMenuHeight>y?O.bottom=y-s-N+S:O.top=s+l-N+S,o(T,O)},addButton:function(t){var n=this,r=n.renderer,i=u(n.options.navigation.buttonOptions,t),s=i.onclick,o=i.menuItems,a,l,c={stroke:i.symbolStroke,fill:i.symbolFill},h=i.symbolSize||12,p;n.btnCount||(n.btnCount=0),p=n.btnCount++,n.exportDivElements||(n.exportDivElements=[],n.exportSVGElements=[]);if(i.enabled===!1)return;var d=i.theme,v=d.states,m=v&&v.hover,g=v&&v.select,y;delete d.states,s?y=function(){s.apply(n,arguments)}:o&&(y=function(){n.contextMenu("contextmenu",o,l.translateX,l.translateY,l.width,l.height,l),l.setState(2)}),i.text&&i.symbol?d.paddingLeft=e.pick(d.paddingLeft,25):i.text||f(d,{width:i.width,height:i.height,padding:0}),l=r.button(i.text,0,0,y,d,m,g).attr({title:n.options.lang[i._titleKey],"stroke-linecap":"round"}),i.symbol&&(a=r.symbol(i.symbol,i.symbolX-h/2,i.symbolY-h/2,h,h).attr(f(c,{"stroke-width":i.symbolStrokeWidth||1,zIndex:1})).add(l)),l.add().align(f(i,{width:l.width,x:C}),!0,"spacingBox"),C+=(l.width+i.buttonSpacing)*(i.align==="right"?-1:1),n.exportSVGElements.push(l,a)},destroyExport:function(e){var t=e.target,n,i;for(n=0;n<t.exportSVGElements.length;n++)i=t.exportSVGElements[n],i.onclick=i.ontouchstart=null,t.exportSVGElements[n]=i.destroy();for(n=0;n<t.exportDivElements.length;n++)i=t.exportDivElements[n],r(i,"mouseleave"),t.exportDivElements[n]=i.onmouseout=i.onmouseover=i.ontouchstart=i.onclick=null,s(i)}}),T.menu=function(e,t,n,r){var i=[v,e,t+2.5,m,e+n,t+2.5,v,e,t+r/2+.5,m,e+n,t+r/2+.5,v,e,t+r-1.5,m,e+n,t+r-1.5];return i},t.prototype.callbacks.push(function(e){var t,r=e.options.exporting,i=r.buttons;C=0;if(r.enabled!==!1){for(t in i)e.addButton(i[t]);n(e,"destroy",e.destroyExport)}})}(Highcharts),function(){$(function(){var e,t,n,r;return GoldenData.charts={},$(document).on("click",".btn.download",function(){var e;e=GoldenData.charts[$(this).data("target")];if(e)return e.exportChart()}),e=function(){function e(e,t,n,r){var i,s,o,u,a,f,l,c,h,p,d;r==null&&(r=null),this.options=this.defaultOptions(),this.options.chart.renderTo=n,this.options.chart.type=t,s=$("#"+n).width(),a=$("#"+n).next().height(),this.options.chart.height=Math.min(s,a),f={showInLegend:!1,name:"数量",data:[]},c=0,i=[],d=e.choices;for(h=0,p=d.length;h<p;h++)o=d[h],l=o[0],u=o[1],f.data.push({name:l,y:u}),i.push(l),c+=u;this.options.series.push(f),this.options.plotOptions.series.dataLabels.formatter=function(){var e;return e=Highcharts.numberFormat(this.y*100/c,1),""+e+" %"},this.options.xAxis.categories=i,r&&r(this.options)}return e.prototype.defaultOptions=function(){return{chart:{marginTop:36,backgroundColor:GoldenData.isMobile?"transparent":"#FFFFFF"},colors:["#73a8c7","#da6d4e","#edb055","#b1c95b","#c3aad0"],credits:{enabled:!1},title:{text:null},xAxis:{labels:{style:{fontFamily:"verdana"},useHTML:!0,formatter:function(){return"<span>"+this.value+"</span>"}}},yAxis:{min:0,labels:{enabled:!1,style:{fontFamily
:"verdana"}},title:{text:null},gridLineWidth:0},exporting:{enabled:!1},tooltip:{borderRadius:0,borderColor:"#333",shadow:!1,useHTML:!0},plotOptions:{bar:{shadow:!1},pie:{dataLabels:{distance:15,style:{fontFamily:"verdana"}}},series:{pointWidth:12,pointPadding:0,groupPadding:0,borderWidth:0,colorByPoint:!0,dataLabels:{enabled:!0}}},series:[]}},e.prototype.render=function(){return GoldenData.charts[this.options.chart.renderTo]=new Highcharts.Chart(this.options)},e}(),t=function(){function t(t,n){this.chart=(new e(t,"bar",n,function(e){return e.chart.height=$("#"+n).next().height()})).render()}return t}(),n=function(){function t(t,n){this.chart=(new e(t,"column",n,function(e){e.xAxis.labels={align:"right",rotation:-45,useHTML:!1,style:{fontFamily:"verdana"},formatter:function(){return this.value.length>5?""+this.value.slice(0,5)+"...":this.value}},e.chart.height=$("#"+n).height();if($("#"+n).hasClass("compact"))return e.chart.marginTop=10})).render()}return t}(),r=function(){function t(t,n){this.chart=(new e(t,"pie",n,function(e){return e.plotOptions.series.dataLabels.formatter=function(){var e;return e=this.point.name.length>15?""+this.point.name.slice(0,15)+"...":this.point.name,""+e+" "+Highcharts.numberFormat(this.percentage,1)+" %"}})).render()}return t}(),GoldenData.ReportChart=function(){function e(e){var i,s,o,u,a;u=e.attr("id"),i=_.clone(e.data("report")),i.choices=_.pairs(i.choices),o=e.data("sort"),!o||(s=_.sortBy(i.choices,function(e){return o==="down"?-e[1]:e[1]}),i.choices=s),a=e.data("chart-type");switch(a){case"column":new n(i,u);break;case"pie":new r(i,u);break;default:new t(i,u)}}return e}(),GoldenData.ReportLineChart=function(){function e(e){this.options={chart:{renderTo:e.div_id,type:"line"},colors:["#73a8c7","#da6d4e","#edb055","#b1c95b","#c3aad0"],credits:{enabled:!1},title:{text:e.title},legend:{borderRadius:0},xAxis:{type:"datetime",dateTimeLabelFormats:{day:"%m-%d"}},yAxis:{title:{text:e.y_label},min:0,allowDecimals:!1},tooltip:{xDateFormat:"%Y-%m-%d",borderRadius:0,borderColor:"#333",shadow:!1,style:{lineHeight:"150%",padding:"8px"}},series:[{name:e.y_label,data:e.data,pointStart:e.start_date,pointInterval:864e5}]}}return e.prototype.render=function(){return Highcharts.setOptions({global:{useUTC:!1}}),new Highcharts.Chart(this.options)},e}(),GoldenData.AdminReportLineChart=function(){function e(e){this.options={chart:{renderTo:e.div_id},colors:["#73a8c7","#da6d4e","#edb055","#b1c95b","#c3aad0"],credits:{enabled:!1},title:{text:e.title},legend:{borderRadius:0},xAxis:{type:"datetime",dateTimeLabelFormats:{day:"%m-%d"}},yAxis:[{title:{text:e.y_label},min:0,allowDecimals:!1},{title:{text:e.daily_y_label},min:0,allowDecimals:!1,opposite:!0}],tooltip:{xDateFormat:"%Y-%m-%d",borderRadius:0,borderColor:"#333",shadow:!1,style:{padding:"8px"}},series:[{type:"line",name:e.y_label,data:e.increment_data,pointStart:e.start_date,pointInterval:864e5},{type:"column",name:e.daily_y_label,data:e.daily_data,pointStart:e.start_date,pointInterval:864e5,yAxis:1}],plotOptions:{series:{shadow:!1}}}}return e.prototype.render=function(){return Highcharts.setOptions({global:{useUTC:!1}}),new Highcharts.Chart(this.options)},e}()})}.call(this),function(){$(function(){var e;return $(document).on("click",".chart-type-toggle .btn",function(){var e;return e=$(this).parents(".field_content").find(".field_chart"),e.data("chart-type",$(this).data("chart-type")),new GoldenData.ReportChart(e)}),e=function(e,t){var n;return n=e.clone().sort(function(e,n){var r,i;return r=parseInt($(e).find(".c-value").html()),i=parseInt($(n).find(".c-value").html()),t===void 0&&(r=parseInt($(e).find(".c-value").data("index")),i=parseInt($(n).find(".c-value").data("index"))),t?i-r:r-i}),n},$(document).on("click",".data_count",function(){var t,n,r,i;r=$(this),n=$(r.closest("table").children("tbody")),t=$(r.closest(".field_data").siblings(".field_chart")),i=n.children("tr");if(r.hasClass("sort"))return r.removeClass("sort").addClass("sort-down"),n.html(e(i,!0)),t.data("sort","down"),new GoldenData.ReportChart(t);if(r.hasClass("sort-down"))return r.removeClass("sort-down").addClass("sort-up"),n.html(e(i,!1)),t.data("sort","up"),new GoldenData.ReportChart(t);if(r.hasClass("sort-up"))return r.removeClass("sort-up").addClass("sort"),n.html(e(i)),t.removeData("sort"),new GoldenData.ReportChart(t)}),GoldenData.showReport=function(){if($("#reports_container:visible").length>0&&!$("#reports_container").data("charts-shown"))return $.each($(".field_chart[data-report]"),function(e,t){return new GoldenData.ReportChart($(t))}),$("#reports_container").data("charts-shown",!0)},$(document).on("page:load ready",function(){return setTimeout(GoldenData.showReport,10)}),$(document).on("ajax:complete",".reports_page .filter-content",function(){return setTimeout(GoldenData.showReport,10)}),$(document).on("shown click",".results_nav, [data-role=navbar] a",function(){return setTimeout(GoldenData.showReport,10)})})}.call(this),function(){}.call(this),function(){$(document).on("page:load ready",function(){return $("#change_per_page").clickover({animation:!1,container:"body",tip_id:"per_page_choices_container",content:function(){return $("#available_per_pages").html()},onShown:function(){return $("#per_page_choices_container a").click(function(){return $("#available_per_pages").click()})}})})}.call(this),function(){var e;e=function(){function e(){}return e.prototype.setEntry=function(e){return this.title=encodeURIComponent(e.title||document.title),this.url=encodeURIComponent((e.url||document.location).replace("https://","http://")),this.pic=encodeURIComponent(e.pic||""),this.description=encodeURIComponent(e.description||"")},e.prototype.weibo=function(){var e,t,n;return e=1843447738,t="http://v.t.sina.com.cn/share/share.php?appkey="+e+"&title="+this.title+"&url="+this.url+"&pic="+this.pic,n="width=700,height=480, top="+(screen.height-430)/2+", left="+(screen.width-440)/2+", toolbar=no, menubar=no, scrollbars=no, location=yes, resizable=1, status=no",window.open(t,"分享到微博",n)},e.prototype.qqmb=function(){var e,t;return e="http://v.t.qq.com/share/share.php?title="+this.title+"&url="+this.url+"&pic="+this.pic,t="width=600, height=480, top="+(screen.height-700)/2+", left="+(screen.width-580)/2+", toolbar=no, menubar=no, scrollbars=no, location=yes, resizable=no, status=no",window.open(e,"转播到腾讯微博",t)},e.prototype.qzone=function(){var e,t,n;return e=""+this.title+" - "+this.description,t="http://sns.qzone.qq.com/cgi-bin/qzshare/cgi_qzshare_onekey?title="+e+"&url="+this.url,n="width=700, height=600, top="+(screen.height-700)/2+", left="+(screen.width-600)/2+", toolbar=no, menubar=no, scrollbars=no, location=yes, resizable=no, status=no",window.open(t,"分享到QZONE",n)},e.prototype.renren=function(){var e,t;return e="http://widget.renren.com/dialog/share?title="+this.title+"&resourceUrl="+this.url+"&images="+this.pic+"&description="+this.description,t="width=700, height=600, top="+(screen.height-700)/2+", left="+(screen.width-600)/2+", toolbar=no, menubar=no, scrollbars=no, location=yes, resizable=no, status=no",window.open(e,"分享到人人",t)},e}(),window.shareIt=new e}.call(this),function(){$(function(){var e;e=function(e){return $(".table-content table thead tr th:not(.hide)").length===1&&e.hasClass("selected")},$(document).on("click",".filter-content .filter [data-field]",function(t){var n;return t.stopPropagation(),n=$(this).data("field"),e($(this))?!1:($("#entries_table").find("th[data-field="+n+"], td[data-field="+n+"]").toggleClass("hide"),$(".filter-content").find("a[data-field="+n+"]").toggleClass("selected"),$(this).blur())}),$(".with_tooltip").tooltip({animation:!1}),$(document).on("click","#entries_table tbody tr[data-url], #entries_table thead [data-url]",function(e){var t;t=$(this).data("url");if(t)return window.location.href=t});if(url("#")==="reports")return $(".results_content .title .results_nav a").click()})}.call(this),function(){var e=function(e,t){return function(){return e.apply(t,arguments)}};window.GoldenData||(window.GoldenData={}),$(function(){var t;return t=function(){function t(t){this.$container=t,this.selectMarker=e(this.selectMarker,this),this.initialExistedPositions=e(this.initialExistedPositions,this),this.initialGoogleMap=e(this.initialGoogleMap,this),this.initialMap=e(this.initialMap,this),this.mobile=$("body").hasClass("mobile"),this.initialMap()}return t.prototype.initialMap=function(){return this.initialGoogleMap()},t.prototype.initialGoogleMap=function(){var e;return this.geoMapDom=this.$container.find(".google-map-container")[0],this.coordsList=$(this.geoMapDom).data("coordinates"),this.coordsList.length===0?$(this.geoMapDom).closest(".geo-map-container").html("<div class='no-geo-data'>"+l("%no_geo_data")+"</div>"):(this.fieldApiCode=$(this.geoMapDom).data("api-code"),this.urlPrefix=$(this.geoMapDom).data("url-prefix"),e={zoom:4,mapTypeId:google.maps.MapTypeId.ROADMAP,streetViewControl:!1,zoomControlOptions:{position:google.maps.ControlPosition.LEFT_TOP}},this.googleMap=new google.maps.Map(this.geoMapDom,e),this.initialExistedPositions())},t.prototype.initialExistedPositions=function(){var e,t=this;return this.defaultIcon=new google.maps.MarkerImage("/assets/map_marker_default.png",null,null,null,new google.maps.Size(22,22)),this.selectedIcon=new google.maps.MarkerImage("/assets/map_marker_selected.png",null,null,null,new google.maps.Size(22,22)),e=new google.maps.LatLngBounds,_.each(this.coordsList,function(n,r){var i,s,o,u;i=n.latitude,s=n.longitude,u=new google.maps.LatLng(parseFloat(i),parseFloat(s)),o=new google.maps.Marker({position:u,map:t.googleMap,animation:google.maps.Animation.Drop,title:n.address,icon:t.defaultIcon}),e.extend(u);if(t.fieldApiCode!=null&&t.urlPrefix!=null){google.maps.event.addListener(o,"click",function(){return t.selectMarker(o,n.entry_id)});if(r===0)return t.selectMarker(o,n.entry_id)}}),this.googleMap.fitBounds(e)},t.prototype.selectMarker=function(e,t){var n,r=this;!this.selectedMarker||this.selectedMarker.setIcon(this.defaultIcon),e.setIcon(this.selectedIcon),e.setZIndex(google.maps.Marker.MAX_ZINDEX+1),this.selectedMarker&&this.selectedMarker.setZIndex(google.maps.Marker.MAX_ZINDEX),this.selectedMarker=e,this.$container.find(".selected-geo-address").text(this.selectedMarker.getTitle());if(!this.mobile)return n=""+this.urlPrefix+"/"+t+"/entry_for_geo_report",$.get(n,{id:t,field_api_code:this.fieldApiCode},function(e){return r.$container.find(".geo-entry-container").html(e),r.$container.find(".selected-geo-entry").scrollTop(0)})},t}(),GoldenData.initGeoReportView=function(){var e,n;return e=function(){var e,n,r,i,s;i=$(".geo-reports .geo-map-container"),s=[];for(n=0,r=i.length;n<r;n++)e=i[n],s.push(new t($(e)));return s},n=$(".results_content .title .results_nav"),$("body").hasClass("mobile")&&(n=$('.mobile [data-role="navbar"] .results_nav')),n.length>0&&!n.hasClass("active")?n.find("a").click(function(){return setTimeout(e,10)}):e()}})}.call(this),function(){window.GoldenData||(window.GoldenData={})}.call(this);