<html><head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>$form['name']</title>
<meta name="viewport" content="width=device-width,height=device-height,inital-scale=1.0,maximum-scale=1.0,user-scalable=no;">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black">
<meta name="format-detection" content="telephone=no">
<link href="{HEJIN_PATH}/public/wapimg/hotels.css" rel="stylesheet" type="text/css">
<script src="{HEJIN_PATH}/public/wapimg/jquery.js" type="text/javascript"></script>
<script type="text/javascript" src="{HEJIN_PATH}/public/wapimg/main.js"></script>
<link href="{HEJIN_PATH}css/jquery.datetimepicker.css" charset="utf-8" rel="stylesheet" type="text/css"></link>
<script src="{HEJIN_PATH}js/jquery.js" charset="utf-8" type="text/javascript"></script>
<script src="{HEJIN_PATH}js/jquery.datetimepicker.js" charset="utf-8" type="text/javascript"></script>

</head>

<body id="wrap">
<style>
.deploy_ctype_tip{z-index:1001;width:100%;text-align:center;position:fixed;top:50%;margin-top:-23px;left:0;}.deploy_ctype_tip p{display:inline-block;padding:13px 24px;border:solid #d6d482 1px;background:#f5f4c5;font-size:16px;color:#8f772f;line-height:18px;border-radius:3px;}
</style>
<div class="banner">
<div id="wrapper">
<div id="scroller" style="float:none">
<ul id="thelist">
               

<a>
<img src="{HEJIN_PATH}$form['thumb']" alt="$form['name']" style="width:100%"></a>
</li>
 

</ul>
</div>
</div>

<div class="clr"></div>
</div>


<div class="cardexplain">



<!--intro-->
<ul class="round">
<li>
<h2>$form['name']</h2>
{if $form['description']}<div class="text">
$form['description']</div>{/if}
</li>

</ul>


<form method="post" action="" id="form" enctype="multipart/form-data" onSubmit="return check();"> 
    <input type="hidden" name="referer" value="$_G[referer]">
    <input type="hidden" name="formhash" value="{FORMHASH}" />
    <input type="hidden" name="fid" value="$form['id']" />
     <input type="hidden" name="uid" value="$user['uid']" />
     <input type="hidden" name="account" value="$user['username']" />
  
<ul class="round">
<li class="title mb"><span class="none">{lang hejin_forms:mobilets}</span></li>

<!--{loop $groups $val}-->
<!--{eval $config = fix_json($val[config]);}-->

<li class="nob">
<table class="kuang" border="0" cellpadding="0" cellspacing="0" width="100%">
<tbody><tr>
<th>$val['name']{if $config[GROUP_MUST]}<font style="color: red;">*</font>{/if}</th>
<td>
 {if $val['type'] == 'text'}
<input name='G-{$val[id]}' id='G-{$val[id]}' class="px" type='text' placeholder="{lang hejin_forms:mobileqsr}{$val[name]}" value='' size='{$config[GROUP_SIZE]}' fix_name='{$val[name]}' />

{elseif $val['type'] == "date"}
<input name='G-{$val[id]}' id='G-{$val[id]}' class="px" type='text' placeholder="{lang hejin_forms:mobileqsr}{$val[name]}" value='' size='{$config[GROUP_SIZE]}' fix_name='{$val[name]}' />
 <script>
  $('#G-{$val[id]}').datetimepicker({
	onGenerate:function( ct ){
		$(this).find('.xdsoft_date')
			.toggleClass('xdsoft_disabled');
	},
	minDate:'-1970/01/1',
	maxDate:'+1970/01/1',
	timepicker:false
});

  </script>
{elseif $val['type'] == "time"}
<input name='G-{$val[id]}' id='G-{$val[id]}' class="px" type='text' placeholder="{lang hejin_forms:mobileqsr}{$val[name]}" value='' size='{$config[GROUP_SIZE]}' fix_name='{$val[name]}' />
  <script>
  $('#G-{$val[id]}').datetimepicker({
	datepicker:false,
	format:'H:i',
	step:5
});

  </script>
{elseif $val['type'] == "dati"}
<input name='G-{$val[id]}' id='G-{$val[id]}' class="px" type='text' placeholder="{lang hejin_forms:mobileqsr}{$val[name]}" value='' size='{$config[GROUP_SIZE]}' fix_name='{$val[name]}' />
  <script>
  $('#G-{$val[id]}').datetimepicker();
  </script>


{elseif $val['type'] == "textarea"}

<textarea name='G-{$val[id]}' id="G-{$val[id]}" class="pxtextarea" style=" height:99px;overflow-y:visible" placeholder="{lang hejin_forms:mobileqsr}{$val[name]}" cols='50' rows='4' fix_name='{$val[name]}' {if $config[GROUP_MUST]}fix_null='yes'{/if}></textarea>
{elseif $val['type'] == "file"}	
<input name='G-{$val[id]}' id="G-{$val[id]}" type='file' size='50' fix_name='{$val[name]}' {if $config[GROUP_MUST]}fix_null='yes'{/if} fix_extension='{$config[GROUP_UPLOAD]}' />

                {else}
{if $val['type'] == "select" or $val['type'] == "radio"}
                <select style="line-height:35px;" id='G-{$val[id]}' name='G-{$val[id]}' fix_name='{{$val[name]}}' class="dropdown-select">
                {/if}
  
  			<!--{eval $options = Hejin::_option_lists($val[id]);}-->
            <!--{loop $options $keyi $vala}-->
            
           <!--{if $keyi == 0}-->
            {if $val['type'] == "select" or  $val['type'] == "radio"}
                            <option value='{$vala[id]}' selected> {$vala[name]}</option>
            {/if}
               {if $val['type'] == "checkbox"}
                            <label class="checkbox inline "><input type='checkbox' class='pxcheckbox' name='G-{$val[id]}[]' value='{$vala[id]}' fix_name='{$val[name]}' {if $config[GROUP_MUST]}fix_null='yes'{/if} checked/> {$vala[name]}</label>
            {/if}
		<!--{else}-->
            {if $val['type'] == "select" or  $val['type'] == "radio"}
                            <option value='{$vala[id]}'> {$vala[name]}</option>
            {/if}
               {if $val['type'] == "checkbox"}
                            <label class="checkbox inline "><input type='checkbox' class='pxcheckbox' name='G-{$val[id]}[]' value='{$vala[id]}' fix_name='{$val[name]}' {if $config[GROUP_MUST]}fix_null='yes'{/if}/> {$vala[name]}</label>
            {/if}
            <!--{/if}-->
			<!--{/loop}-->
			{if $val['type'] == "select" or  $val['type'] == "radio"}
                </select>
                {/if}
  
{/if}

    			</td>
</tr>
</tbody></table>
</li>   
<!--{/loop}-->                            		



                                                       
</ul>

<div class="footReturn" style="text-align:center">


{if $form['start']<time() && $form['expire']>time() && $form['state']>0 }
            <input style="margin:0 auto 20px auto;width:90%" class="submit" name="commit" id="submita" value="{lang hejin_forms:tijiao}" type="submit">
        {else}
            <input type='submit' style="margin:0 auto 20px auto;width:90%" class='submit' value='{if $form['expire']<time()}{lang hejin_forms:formend}{else}{lang hejin_forms:formnost}{/if}' disabled='disabled' />
        {/if}


</div>
</form>
<script>
function showTip(tipTxt) {
	var div = document.createElement('div');
	div.innerHTML = '<div class="deploy_ctype_tip"><p>' + tipTxt + '</p></div>';
	var tipNode = div.firstChild;
	$("#wrap").after(tipNode);
	setTimeout(function () {
		$(tipNode).remove();
	}, 1500);
}



function check(){
	
	
	
<!--{loop $grojs $keya $grojsa}-->	

   if($("#G-$grojsa['id']").val() ==  null || $("#G-$grojsa['id']").val() == ''){
	   	error$grojsa['id']="$grojsa['name']{lang hejin_forms:bixutian}";
		showTip(error$grojsa['id']);
        e$grojsa['id'] = false;
   }else{
	    e$grojsa['id'] = true;
	   }
<!--{/loop}-->



	
<!--{loop $groajs $keyb $grojsb}-->	
<!--{if $grojsb['config']['GROUP_VERIFY'] == "number"}-->
if($("#G-$grojsb['id']").val() !=  null && $("#G-$grojsb['id']").val() != ''){

if(!/^[0-9]*$/.test($("#G-$grojsb['id']").val())){
	errora$grojsb['id']="$grojsb['name']{lang hejin_forms:mustnumber}";
	showTip(errora$grojsb['id']);
    ea$grojsb['id'] = false;

	}else{
	    ea$grojsb['id'] = true;
	   }
	}else{
		 ea$grojsb['id'] = true;
		}
<!--{/if}-->


<!--{if $grojsb['config']['GROUP_VERIFY'] == "email"}-->
if($("#G-$grojsb['id']").val() !=  null && $("#G-$grojsb['id']").val() != ''){

if(!/^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/.test($("#G-$grojsb['id']").val())){
	errora$grojsb['id']="{lang hejin_forms:mustemail}";
	showTip(errora$grojsb['id']);
    ea$grojsb['id'] = false;

	}else{
	    ea$grojsb['id'] = true;
	   }
	}else{
		 ea$grojsb['id'] = true;
		}
<!--{/if}-->



<!--{if $grojsb['config']['GROUP_VERIFY'] == "mobile"}-->
if($("#G-$grojsb['id']").val() !=  null && $("#G-$grojsb['id']").val() != ''){
if(!/^\d{11}$/.test($("#G-$grojsb['id']").val())){
	errora$grojsb['id']="{lang hejin_forms:mustmobile}";
	showTip(errora$grojsb['id']);
    ea$grojsb['id'] = false;

	}else{
	    ea$grojsb['id'] = true;
	   }
	}else{
		 ea$grojsb['id'] = true;
		}
<!--{/if}-->

<!--{if $grojsb['config']['GROUP_VERIFY'] == "idcard"}-->
if($("#G-$grojsb['id']").val() !=  null && $("#G-$grojsb['id']").val() != ''){

if(!/^\d{15}|\d{18}$/.test($("#G-$grojsb['id']").val())){
	errora$grojsb['id']="{lang hejin_forms:mustidcard}";
	showTip(errora$grojsb['id']);
    ea$grojsb['id'] = false;

	}else{
	    ea$grojsb['id'] = true;
	   }
	}else{
		 ea$grojsb['id'] = true;
		}
<!--{/if}-->




<!--{/loop}-->





if($jstj){
   return true;
   }else{
   return false;
	   }
}




	</script>
</div>

</body></html>