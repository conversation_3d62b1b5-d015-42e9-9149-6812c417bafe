<html><head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>$form['name']</title>
<meta name="viewport" content="width=device-width,height=device-height,inital-scale=1.0,maximum-scale=1.0,user-scalable=no;">
<meta name="apple-mobile-web-app-capable" content="yes">
<meta name="apple-mobile-web-app-status-bar-style" content="black">
<meta name="format-detection" content="telephone=no">
<link href="{HEJIN_PATH}/public/wapimg/hotels.css" rel="stylesheet" type="text/css">
<script src="{HEJIN_PATH}/public/wapimg/jquery.js" type="text/javascript"></script>
<script type="text/javascript" src="{HEJIN_PATH}/public/wapimg/main.js"></script>
<link href="{HEJIN_PATH}css/jquery.datetimepicker.css" charset="utf-8" rel="stylesheet" type="text/css"></link>
<script src="{HEJIN_PATH}js/jquery.js" charset="utf-8" type="text/javascript"></script>
<script src="{HEJIN_PATH}js/jquery.datetimepicker.js" charset="utf-8" type="text/javascript"></script>
<script src="{HEJIN_PATH}public/mobile/jquery.mobile-1.3.0.js" type="text/javascript"></script>
<link rel="stylesheet" href="{HEJIN_PATH}public/mobile/jquery.mobile-1.3.0.css" />


</head>

<body id="wrap">
<style>
.deploy_ctype_tip{z-index:1001;width:100%;text-align:center;position:fixed;top:50%;margin-top:-23px;left:0;}.deploy_ctype_tip p{display:inline-block;padding:13px 24px;border:solid #d6d482 1px;background:#f5f4c5;font-size:16px;color:#8f772f;line-height:18px;border-radius:3px;}
</style>


<div class="cardexplain">



<!--intro-->
<ul class="round">
<li>
<h2>{if $form['config']}$form['config']{else} {lang hejin_forms:tijiaocg}{/if}
</h2>
<div class="text">
{lang hejin_forms:ymzdtz} <a id="href" href="{if $form['quote']}$form['quote']{else}$_G['siteurl']plugin.php?id=hejin_forms&formid=$form['id']{/if}">{lang hejin_forms:tiaozhuan}</a> {lang hejin_forms:waittime} <b id="wait">3</b>
<script type="text/javascript">
(function(){
var wait = document.getElementById('wait'),href = document.getElementById('href').href;
var interval = setInterval(function(){
	var time = --wait.innerHTML;
	if(time <= 0) {
		location.href = href;
		clearInterval(interval);
	};
}, 1000);
})();
</script></div>
</li>

</ul>




</div>

</body></html>