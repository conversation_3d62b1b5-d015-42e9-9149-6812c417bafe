<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=7" />
<meta name="robots" content="noindex, nofollow" />
<link type="text/css" rel="stylesheet" charset="utf-8" href="{HEJIN_PATH}public/style/dialog.css?ver=5.0" />
<link type="text/css" rel="stylesheet" charset="utf-8" href="{HEJIN_PATH}public/style/ubb.css?ver=5.0" />
<link type="text/css" rel="stylesheet" charset="utf-8" href="{HEJIN_PATH}public/style/ui.css?ver=5.0" />
<link type="text/css" rel="stylesheet" charset="utf-8" href="{HEJIN_PATH}public/style/calendar.css?ver=5.0" />
<title></title>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.ajax.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.drag.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.interface.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.ubb.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.form.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.ui.js?ver=5.0"></script>
<script type="text/javascript">
	Mo.store.site		="";
	Mo.store.web		="";
	Mo.store.domain		="";
	Mo.store.root		="{HEJIN_PATH}public/";
	Mo.store.product		="Bee";
	Mo.store.version		="5.0";
	Mo.store.build		="20111006";
	Mo.store.licence		="free";
	</script>

	<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/serv.dialog.js?ver=5.0"></script>
	<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/serv.upload.js?ver=5.0"></script>
	
	</head>
	
	<body>
	<div id="wrapper">
	
<!--body start-->
	<!--main-->

	
    <div id="nav">
        <strong><a href="$SELF?action=plugins&identifier=hejin_forms&pmod=admin_group&model=list&formid=$form[id]">$form[name]</a> &gt;&gt; $group[name]</strong> 	
    </div>
    
    <ul id="naver">
        <li><a href="$SELF?action=plugins&identifier=hejin_forms&pmod=admin_option&model=add&formid=$_GET['formid']&groupid=$_GET[groupid]">{lang hejin_forms:optaddurl}</a></li>
        <li class="active"><a href="$SELF?action=plugins&identifier=hejin_forms&pmod=admin_option&formid=$_GET['formid']&model=list&groupid=$_GET[groupid]">{lang hejin_forms:optlisturl}</a></li>
    </ul>
	    
	    
    
        
    <form name="formPost" id="formPost" method="post" action="?fid=1&gid=2">
		<input name="action" id="action" type="hidden" value="mass" />
		<input name="state" id="state" type="hidden" value="" />
		
		<input name="jump" type="hidden" value="" />
		<script>Mo('#formPost input[name=jump]').value( location.href );</script>

    <table width="100%" border="0" cellpadding="1" cellspacing="1" id="table" class="table">
	
			
        <tr class="title">
        <td>{lang hejin_forms:grouplista}</td>
        <td>{lang hejin_forms:grouplistb}</td>
        <td>{lang hejin_forms:optbqmc}</td>
        <td>{lang hejin_forms:grouplistg}</td>
        <td>{lang hejin_forms:grouplisth}</td>
        <td width="70">{lang hejin_forms:grouplisti}</td>
        </tr>
<!--{loop $options $val}-->
                        <tr class="line" mark="1" edit='$SELF?action=plugins&identifier=hejin_forms&pmod=admin_option&model=edit&oid=$val[id]'>
					
                    <td>$val[id]</td>
                    <td><input type="text" class="text" name="sort" size="4" value="$val[sort]" /></td>
                    <td><input type="text" class="text text-yes" name="name" size="22" value="$val[name]" /></td>
                    <!--{eval $dateline = date("Y/m/d",$val["dateline"]);}-->
                    <td>$dateline</td>
                    <td>
                      {if $val[state]== 1}<span class="text-yes">{lang hejin_forms:stateyes}</span>{else}<span class="text-no">{lang hejin_forms:stateno}</span>{/if}       
                    </td>
                    <td>
                       <a href="$SELF?action=plugins&identifier=hejin_forms&pmod=admin_option&model=edit&oid=$val[id]&gid=$_GET[groupid]&fid=$_GET[formid]"><img src="{HEJIN_PATH}public/image/icon/pencil.png" alt="{lang hejin_forms:bianji}" align="absmiddle"></a>  <a href="$SELF?action=plugins&identifier=hejin_forms&pmod=admin_option&model=del&oid=$val[id]&gid=$_GET[groupid]&fid=$_GET[formid]" onclick="return confirm(&quot;{lang hejin_forms:shanchuts}&quot;);"><img src="{HEJIN_PATH}public/image/icon/trash.png" alt="{lang hejin_forms:shanchu}" align="absmiddle"> 
                </a> 
                    </td>
                </tr>				
<!--{/loop}-->    				                			
						
			
		</table>
    
    </form>
	
        
<!--body end-->

<!--Processed in 0.031018 second(s) , 1 queries-->
	</div>
	<script type="text/javascript">Serv.Manager.frLoad();Mo.Message("info", "" , 3, { "unique" : "message", "center":true } );</script>
	</body>
	</html>