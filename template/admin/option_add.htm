<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=7" />
<meta name="robots" content="noindex, nofollow" />
<link type="text/css" rel="stylesheet" charset="utf-8" href="{HEJIN_PATH}public/style/dialog.css?ver=5.0" />
<link type="text/css" rel="stylesheet" charset="utf-8" href="{HEJIN_PATH}public/style/ubb.css?ver=5.0" />
<link type="text/css" rel="stylesheet" charset="utf-8" href="{HEJIN_PATH}public/style/ui.css?ver=5.0" />
<link type="text/css" rel="stylesheet" charset="utf-8" href="{HEJIN_PATH}public/style/calendar.css?ver=5.0" />
<title></title>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.ajax.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.drag.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.interface.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.ubb.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.form.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.ui.js?ver=5.0"></script>
<script type="text/javascript">
	Mo.store.site		="";
	Mo.store.web		="";
	Mo.store.domain		="";
	Mo.store.root		="";
	Mo.store.product		="Bee";
	Mo.store.version		="5.0";
	Mo.store.build		="20111006";
	Mo.store.licence		="free";
	</script>

	<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/serv.dialog.js?ver=5.0"></script>
	<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/serv.upload.js?ver=5.0"></script>
	
	</head>
	
	<body>
	<div id="wrapper">
	
<!--body start-->
	<!--main-->

	
    <div id="nav">
        <strong>$form[name] &gt;&gt; $group[name]</strong> 	
    </div>
    
    <ul id="naver">
        <li class="active"><a href="$SELF?action=plugins&identifier=hejin_forms&pmod=admin_option&model=add&formid=$_GET['formid']&groupid=$_GET[groupid]">{lang hejin_forms:optaddurl}</a></li>
        <li><a href="$SELF?action=plugins&identifier=hejin_forms&pmod=admin_option&model=list&formid=$_GET['formid']&groupid=$_GET[groupid]">{lang hejin_forms:optlisturl}</a></li>
    </ul>
	    
	    
    
    		<div id="box">
    
    	<form action="" method="post" verify="true">
    	<input type="hidden" name="referer" value="$_G[referer]">
    	<input type="hidden" name="formhash" value="{FORMHASH}" />
						<input name="fid" type="hidden" id="fid" value="$_GET[formid]" />
						<input name="gid" type="hidden" id="gid" value="$_GET[groupid]" />
        <table cellpadding="0" cellspacing="0" class="form">
        
        	<tr>
            	<td width="60"><strong>{lang hejin_forms:opttype}{lang hejin_forms:maohao}</strong></td>
                <td>
                {if $group[type]=='radio'}{lang hejin_forms:grouptypea}{elseif $group[type]=='checkbox'}{lang hejin_forms:grouptypeb}{elseif $group[type]=='select'}{lang hejin_forms:grouptypee}{/if}               </td>
            </tr>
            
                    
        	<tr>
            	<td><strong>{lang hejin_forms:optdadd}{lang hejin_forms:maohao}</strong></td>
                <td>

                	<div id="tag-box">
                    
                    <table id="tag-" cellpadding="0" cellspacing="1" border="0" class="gird">
                        <tr>
                            <td>
                            	<span class="action"><a onclick="javascript:Planer.remove(this);void(0);">{lang hejin_forms:shanchu}</a></span>
                           		{lang hejin_forms:optname}{lang hejin_forms:maohao}
                                <br />
                                <input name="_name[]" type="text" class="text" value="" size="20" fix_name="{lang hejin_forms:optname}" fix_null="yes" />
                                <br />
                                {lang hejin_forms:optsort}{lang hejin_forms:maohao}<br />
                                <input name="_sort[]" type="text" class="text digi" value="" size="20" fix_name="{lang hejin_forms:optsort}" fix_number="no" onclick="Mo.Soler( this, event, this.value, 'mo-soler', { '{lang hejin_forms:optsort}' : (function(){ var a = []; for(var i=-9;i<=60;i++){a.push(i);} return a; })() }, function( value ){ this.value = value; }, 1 , 23 );" readonly="true" />
                            </td>
                      </tr>
                    </table>
                    
                  </div>
                
                    <script type="text/javascript">
                    var Planer = new Mo.Planer(Mo("#tag-box").item(0));
                    </script>
                    
                    <a onclick="javascript:Planer.copy();void(0);">{lang hejin_forms:tianjia}</a>
                
                </td>
            </tr>
            
                    
        	<tr>
            	<td><strong>{lang hejin_forms:groupsta}{lang hejin_forms:maohao}</strong></td>
                <td>
                    <label>
                    <input name="state" type="radio" class="radio" value="1" checked> 
                    {lang hejin_forms:groupstaa}
                    </label>
                    <label>
                    <input name="state" type="radio" class="radio" value="0">
                    {lang hejin_forms:groupstab}
                    </label>
                </td>
                
            </tr>
       
        	<tr class="nobr">
            	<td><strong></strong></td>
                <td>
					
						<input name="model" type="hidden" id="model" value="add" />
						<input type="submit" name="submitadd" value="{lang hejin_forms:optaddname}" class="submit" />                </td>
            </tr>
        
        </table>
        
        <script type="text/javascript">
        	Mo("input[name=state]").value("1"); </script>
        
	 </form>
	 
	 </div>
	
        
<!--body end-->

<!--Processed in 0.010427 second(s) , 0 queries-->
	</div>
	<script type="text/javascript">Serv.Manager.frLoad();Mo.Message("info", "" , 3, { "unique" : "message", "center":true } );</script>
	</body>
	</html>