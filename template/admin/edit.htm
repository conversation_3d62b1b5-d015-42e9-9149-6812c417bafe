<link href="{HEJIN_PATH}css/dialog.css" charset="utf-8" rel="stylesheet" type="text/css"></link>
<link href="{HEJIN_PATH}css/jquery.datetimepicker.css" charset="utf-8" rel="stylesheet" type="text/css"></link>
<script src="{HEJIN_PATH}js/jquery.js" charset="utf-8" type="text/javascript"></script>
<script src="{HEJIN_PATH}js/jquery.datetimepicker.js" charset="utf-8" type="text/javascript"></script>
<script src="{HEJIN_PATH}ckeditor/ckeditor.js" charset="utf-8" type="text/javascript"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.js?ver=5.0"></script>
<div id="wrapper">
	

	
	<div id="box">
	    <form action="" method="post" name="from1" enctype="multipart/form-data">
    <input type="hidden" name="referer" value="$_G[referer]">
    <input type="hidden" name="formhash" value="{FORMHASH}" />
    <input type="hidden" name="model" value="edit" />
        <table class="form" style="width:70%" cellpadding="0" cellspacing="0" align="left">
    
            <tbody><tr><td colspan="2" class="section"><strong>{lang hejin_forms:editform} </strong></td></tr>

            
    
            <tr>
                <td width="60"><strong>{lang hejin_forms:formname}{lang hejin_forms:maohao}</strong></td>
                <td>
                
                    <input name="name" class="text" id="name" value="$form[name]" size="60" fix_name="{lang hejin_forms:formname}" fix_null="yes" type="text">
                    <select name="skin" id="skin" style="width: 120px; visibility: visible;">
                    <option value="default"{if $form[skin] == 'default'} selected="selected"{/if}>{lang hejin_forms:skina}</option><option value="yellow"{if $form[skin] == 'yellow'} selected="selected"{/if}>{lang hejin_forms:skinb}</option><option value="bule"{if $form[skin] == 'bule'} selected="selected"{/if}>{lang hejin_forms:skinc}</option><option value="kaqt"{if $form[skin]== 'kaqt'} selected="selected"{/if}>{lang hejin_forms:skind}</option><option value="mise"{if $form[skin] == 'mise'} selected="selected"{/if}>{lang hejin_forms:skine}</option><option value="hxcq"{if $form[skin] == 'hxcq'} selected="selected"{/if}>{lang hejin_forms:skinf}</option><option value="ross"{if $form[skin] == 'ross'} selected="selected"{/if}>{lang hejin_forms:skinj}</option><option value="shuixian"{if $form[skin] == 'shuixian'} selected="selected"{/if}>{lang hejin_forms:skinh}</option><option value="qnhc"{if $form[skin] == 'qnhc'} selected="selected"{/if}>{lang hejin_forms:skini}</option><option value="muwen"{if $form[skin] == 'muwen'} selected="selected"{/if}>{lang hejin_forms:sking}</option>                    </select>
                    <script type="text/javascript">Mo("#skin").bind( 'change', function( ele, index, event ){
                        
                        Mo("#thempic").show().html( '<img src="{HEJIN_PATH}public/them/'+ Mo(this).value() +'.jpg"  width="300"/>' );							
                    
                    });						
                    
</script>
                </td>
                
            </tr>
        
            <tr>
                <td><strong>{lang hejin_forms:jsname}{lang hejin_forms:maohao}</strong></td>
                <td>
                    
                    <textarea name="description" cols="50" rows="9" id="description" style="float:left;"><!--{eval echo stripslashes($form['description']);}--></textarea>
                    
                    <script>
                // Replace the <textarea id="editor1"> with a CKEditor
                // instance, using default configuration.
                CKEDITOR.replace( 'description' );
            </script>
                    <div style="clear:both;"></div>
                </td>
                
            </tr>
        
                            <!--{eval $starttime = date("Y/m/d H:i:s",$form["start"]);}-->
                            <!--{eval $expire = date("Y/m/d H:i:s",$form["expire"]);}-->
            <tr text="">
                <td><strong>{lang hejin_forms:setime}{lang hejin_forms:maohao}</strong></td>
                <td>
                <input name="start" class="text date" value="$starttime" id="datetimestart" type="text">
                
                
                -
                <input name="expire" class="text date" value="$expire" id="datetimeexpire" type="text">
                <script>
				
				$('#datetimestart').datetimepicker();
				$('#datetimeexpire').datetimepicker();
				</script>
                
                </td>
            </tr>
        
            <tr>
                <td><strong>{lang hejin_forms:formsta}{lang hejin_forms:maohao}</strong></td>
                <td>
                    <label>
                    <input class="radio" name="state" value="1"{if $form[state]==1} checked=""{/if} type="radio"> 
                    {lang hejin_forms:formstaa}
                    </label>
                    <label>
                    <input class="radio" name="state" value="0"{if $form[state]==0} checked=""{/if} type="radio">
                    {lang hejin_forms:formstab}
                    </label>
                </td>
                
            </tr>

<tr>
                <td><strong>{lang hejin_forms:formstb}{lang hejin_forms:maohao}</strong></td>
                <td>
                    <label>
                    <input class="radio" name="modify" value="1"{if $form[modify]==1} checked=""{/if} type="radio"> 
                    {lang hejin_forms:formstba}
                    </label>
                    <label>
                    <input class="radio" name="modify" value="2"{if $form[modify]==2} checked=""{/if} type="radio">
                    {lang hejin_forms:formstbb}
                    </label>
                </td>
                
            </tr>


<tr>
                <td><strong>{lang hejin_forms:huodongpd}{lang hejin_forms:maohao}</strong></td>
                <td>
                    <label>
                    <input class="radio" name="sort" value="0"{if $form[sort]==0} checked=""{/if} type="radio"> 
                    {lang hejin_forms:noxshdpd}
                    </label>
                    <label>
                    <input class="radio" name="sort" value="1"{if $form[sort]==1} checked=""{/if} type="radio">
                    {lang hejin_forms:xianshihdpd}
                    </label>
                </td>
                
            </tr>


            <tr>
                <td><strong>{lang hejin_forms:formpic}{lang hejin_forms:maohao}</strong></td>
                <td>
<span id="thumb_span"><input class="text" name="thumb" value="" size="35" type="file"></span>
{if $form[thumb]}
<img src="{HEJIN_PATH}/$form[thumb]" width="50" />
{/if}
                </td>
            </tr>
            
            <tr>
                <td><strong>{lang hejin_forms:formpdpic}{lang hejin_forms:maohao}</strong></td>
                <td>
<span id="thumb_span"><input class="text" name="tags" value="" size="35" type="file"></span>
{if $form[tags]}
<img src="{HEJIN_PATH}/$form[tags]" width="50" />
{/if}
                </td>
            </tr>


            <tr>
                <td><strong>{lang hejin_forms:baomcgts}{lang hejin_forms:maohao}</strong></td>
                <td>
<span id="thumb_span"><input name="config" class="text" id="config" value="$form[config]" size="60" fix_name="{lang hejin_forms:baomcgts}" fix_null="yes" type="text"></span>
{lang hejin_forms:baomcgtsa}
                </td>
            </tr>
            <tr>
                <td><strong>{lang hejin_forms:baomcgtz}{lang hejin_forms:maohao}</strong></td>
                <td>
<span id="thumb_span"><input name="quote" class="text" id="quote" value="$form[quote]" size="60" fix_name="{lang hejin_forms:baomcgtz}" fix_null="yes" type="text"></span>
{lang hejin_forms:baomcgtza}
                </td>
            </tr>
                    

                            
          
            </tbody><tbody>
                <tr class="nobr">
                    <td><strong></strong></td>
                    <td>
                        <input name="editsubmit" value="{lang hejin_forms:formedit}" class="submit" type="submit">                    </td>				
                </tr>
            </tbody>
        
        </table>
     
	 </form>
     
     <table class="thempic" align="center">
     <tr>
     <td width="100%" align="center" id="thempic"><img src="{HEJIN_PATH}public/them/$form[skin].jpg"  width="300"/></td>
     </tr>
     
     </table>
	</div>
	
	</div>