<style>
#search {
    padding: 10px;
    margin: 10px 0px;
    line-height: 20px;
    border: 1px solid #B7CDE5;
    background: none repeat scroll 0% 0% #FFF;
	height:24px;
}
#search .action {
    float: right;
}
.button {
    width: 61px;
    height: 25px;
    line-height: 25px;
    font-size: 12px;
    background: url('{HEJIN_PATH}public/image/bg.png') repeat-x scroll -30px -170px transparent;
    margin: 0px 5px;
	cursor: pointer;
	text-indent:5px;
color: #FFF;
display:block;
}
.table {
    background: none repeat scroll 0% 0% #C4CACD;
    margin: 0px auto 10px;
    overflow: hidden;
	border-collapse:separate !important;
}
.table .title td {
    background: none repeat scroll 0% 0% #DEEAF8;
    color: #333;
}
.table td {
    text-indent: 5px;
    padding: 5px;
    line-height: 20px;
}
td, input, select, body {
    font-family: Verdana;
    font-size: 12px;
}
body, td, input, textarea, select, button {
    color: #555;
    font: 12px "Lucida Grande",Verdana,Lucida,Helvetica,Arial,"{lang hejin_forms:styletif}",sans-serif;
}
.table .line {
    background: none repeat scroll 0% 0% #FFF;
    color: #333;
}
.table .tfoot {
    background: none repeat scroll 0% 0% #E9F3FD;
}
.text-no {
    color: #F00;
}
.text-yes {
    color: #390;
}
#saving {
    background: none repeat scroll 0% 0% #E3EDF9;
    padding: 8px;
    line-height: 20px;
    clear: both;
    overflow: hidden;
}
.page {
    line-height: 20px;
    text-decoration: none;
    color: #666;
}
.page strong {
    font-weight: bold;
    padding: 2px 6px;
    color: #CCC;
    margin-right: 2px;
    vertical-align: middle;
}
.page a {
    border: 1px solid #DDD;
    padding: 2px 6px;
    background: none repeat scroll 0% 0% #FFF;
    color: #333;
    margin-right: 2px;
    vertical-align: middle;
    text-decoration: none;
}
.page span {
    margin-left: 5px;
}
</style>
<div id="search">
    
        <span class="action">
            <a href="http://bbs.weixinhj.com/sysm.htm" target="_blank" class="button">{lang hejin_forms:sysmurl}</a>
        </span>
      
    </div>

<table id="table" class="table" border="0" cellpadding="1" cellspacing="1" width="100%">
		      
        <tbody><tr class="title" id="thead">
            <td>{lang hejin_forms:formlista}</td>
            <td align="center">{lang hejin_forms:formlistb}</td>
            <td align="center" width="80">{lang hejin_forms:formlistc}</td>
            <td>{lang hejin_forms:formlistd}</td>
            <td>{lang hejin_forms:formliste}</td>
            <td>{lang hejin_forms:formlistf}</td>
            <td>{lang hejin_forms:huodongpd}</td>
            <td>{lang hejin_forms:formlists}</td>
            <td>{lang hejin_forms:formlistg}</td>
            <td>{lang hejin_forms:formlisth}</td>
        </tr>
      <!--{loop $forms_list $val}-->
              <tr class="line" mark="1" edit="">
            
            <td title="$val[name]">
                <a href="$_G['siteurl']plugin.php?id=hejin_forms&formid=$val[id]" target="_blank">$val[name]</a>
                {if $val[start] > time()}<sup class="text-no">{lang hejin_forms:formlisti}</sup>{elseif $val[expire] < time()}<sup class="text-no">{lang hejin_forms:formlistj}</sup>{/if}
                            </td>
            <td align="center">
                <a href="$SELF?action=plugins&identifier=hejin_forms&pmod=admin_value&model=list&formid=$val[id]">{lang hejin_forms:formlistk}$val[stat]{lang hejin_forms:formlistm}</a>
                            </td>
            <td id="mark_1" text="" align="center">
                <a href="$SELF?action=plugins&identifier=hejin_forms&pmod=admin_group&model=list&formid=$val[id]"><img src="{HEJIN_PATH}public/image/action/manage.png" title="{lang hejin_forms:formlistn}" alt="{lang hejin_forms:formlistn}" class=""></a>
            </td>
            <td>
                <a href="$_G['siteurl']plugin.php?id=hejin_forms&formid=$val[id]" target="_blank">{lang hejin_forms:formlistr}</a>
                /
                <a href="$SELF?action=plugins&identifier=hejin_forms&pmod=admin_list&model=fabu&formid=$val[id]">{lang hejin_forms:formlistq}</a>
            </td>
                            <!--{eval $starttime = date("Y/m/d",$val["start"]);}-->
                            <!--{eval $expire = date("Y/m/d",$val["expire"]);}-->
                            <!--{eval $dateline = date("Y/m/d",$val["dateline"]);}-->
            <td>
                <span class="{if $val[start] > time()}text-no{else}text-yes{/if}">$starttime</span>                -
                <span class="{if $val[expire] < time()}text-no{else}text-yes{/if}">$expire</span>            </td>
            <td title="$dateline">$dateline</td>
            <td>{if $val[sort]== 1}<span class="text-yes">{lang hejin_forms:xianshihdpd}</span>{else}<span class="text-no">{lang hejin_forms:noxshdpd}</span>{/if}</td>
             <td>{if $val[modify]== 1}<span>{lang hejin_forms:modifya}</span>{else}<span>{lang hejin_forms:modifyb}</span>{/if}</td>
            <td>{if $val[state]== 1}<span class="text-yes">{lang hejin_forms:stateyes}</span>{else}<span class="text-no">{lang hejin_forms:stateno}</span>{/if}</td>
            <td>
 <a href="$SELF?action=plugins&identifier=hejin_forms&pmod=admin_edit&model=edit&formid=$val[id]"><img src="{HEJIN_PATH}public/image/icon/pencil.png" alt="{lang hejin_forms:bianji}" align="absmiddle"></a>  <a href="$SELF?action=plugins&identifier=hejin_forms&pmod=admin_edit&model=del&formid=$val[id]" onclick="return confirm(&quot;{lang hejin_forms:shanchuts}&quot;);"><img src="{HEJIN_PATH}public/image/icon/trash.png" alt="{lang hejin_forms:shanchu}" align="absmiddle"> 
            </a></td>
            </tr>
            <!--{/loop}-->    
            
        </tbody></table>
        
