<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=7" />
<meta name="robots" content="noindex, nofollow" />
<link type="text/css" rel="stylesheet" charset="utf-8" href="{HEJIN_PATH}public/style/dialog.css?ver=5.0" />
<link type="text/css" rel="stylesheet" charset="utf-8" href="{HEJIN_PATH}public/style/ubb.css?ver=5.0" />
<link type="text/css" rel="stylesheet" charset="utf-8" href="{HEJIN_PATH}public/style/ui.css?ver=5.0" />
<link type="text/css" rel="stylesheet" charset="utf-8" href="{HEJIN_PATH}public/style/calendar.css?ver=5.0" />
<title></title>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.ajax.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.drag.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.interface.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.ubb.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.form.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.ui.js?ver=5.0"></script>
<script type="text/javascript">
	Mo.store.site		="";
	Mo.store.web		="";
	Mo.store.domain		="";
	Mo.store.root		="{HEJIN_PATH}public/";
	Mo.store.product		="Bee";
	Mo.store.version		="5.0";
	Mo.store.build		="20111006";
	Mo.store.licence		="free";
	</script>

	<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/serv.dialog.js?ver=5.0"></script>
	<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/serv.upload.js?ver=5.0"></script>
	
	</head>
	
	<body>
	<div id="wrapper">
	
<!--body start-->
	<!--main-->

	
    <div id="nav">
        <strong>$form[name]</strong> 	
    </div>
    
    <ul id="naver">
        <li class="active"><a>{lang hejin_forms:groupbjurl}</a></li>
        <li><a href="$SELF?action=plugins&identifier=hejin_forms&pmod=admin_group&model=list&formid=$group[fid]">{lang hejin_forms:groupediturl}</a></li>
    </ul>
	    
    
    
        
	<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.ubb.js?ver=5.0"></script>
	
	<div id="box">

	<form action="" method="post" name="from1" verify="true">
    <input type="hidden" name="referer" value="$_G[referer]">
    <input type="hidden" name="formhash" value="{FORMHASH}" />
		<input name="fid" type="hidden" id="fid" value="$fid" />
		<input name="id" type="hidden" id="id" value="$gid" />
        <table cellpadding="0" cellspacing="0" class="form">
        
        	<tr>
            	<td width="60"><strong>{lang hejin_forms:groupname}{lang hejin_forms:maohao}</strong></td>
                <td><input name="name" type="text" class="text" id="name" value="$group[name]" size="60" fix_name="{lang hejin_forms:groupname}" fix_null="yes" /></td>
                
            </tr>
        
        	<tr>
            	<td><strong>{lang hejin_forms:grouptype}{lang hejin_forms:maohao}</strong></td>
                <td>
                    <label> <input type="radio" class="radio" name="type" value="radio" checked onclick='doChanged(this.value)'>{lang hejin_forms:grouptypea}</label>
                    <label> <input type="radio" class="radio" name="type" value="checkbox" onclick='doChanged(this.value)'>{lang hejin_forms:grouptypeb}</label>
                    <label> <input type="radio" class="radio" name="type" value="text" onclick='doChanged(this.value)'>{lang hejin_forms:grouptypec}</label>
                    <label> <input type="radio" class="radio" name="type" value="textarea" onclick='doChanged(this.value)'>{lang hejin_forms:grouptyped}</label>
                    <label> <input type="radio" class="radio" name="type" value="select" onclick='doChanged(this.value)'>{lang hejin_forms:grouptypee}</label>
                    <label> <input type="radio" class="radio" name="type" value="date" onclick='doChanged(this.value)'>{lang hejin_forms:groupdate}</label>
                    <label> <input type="radio" class="radio" name="type" value="time" onclick='doChanged(this.value)'>{lang hejin_forms:grouptime}</label>
                    <label> <input type="radio" class="radio" name="type" value="dati" onclick='doChanged(this.value)'>{lang hejin_forms:groupdati}</label>
                    <!--Full-->
                    <label> <input type="radio" class="radio" name="type" value="file" onclick='doChanged(this.value)'>{lang hejin_forms:grouptypef}</label>
                    <!--/Full-->
                </td>
                
            </tr>
         
        	<tr>
            	<td><strong>{lang hejin_forms:groupsx}{lang hejin_forms:maohao}</strong></td>
                <td>
                    
                    <table cellpadding="0" cellspacing="1" border="0" class="gird">
                        <tr>
                            <td id="addv" style="display:none">
                            	{lang hejin_forms:groupbreak}<br />
                                <select name="config[GROUP_BREAK]">
                                    <option value=0>{lang hejin_forms:groupbreaka}</option> 
                                    <option value=1>{lang hejin_forms:groupbreakb}1{lang hejin_forms:groupbreakc}</option> 
                                    <option value=2>{lang hejin_forms:groupbreakb}2{lang hejin_forms:groupbreakc}</option> 
                                    <option value=3>{lang hejin_forms:groupbreakb}3{lang hejin_forms:groupbreakc}</option> 
                                    <option value=4>{lang hejin_forms:groupbreakb}4{lang hejin_forms:groupbreakc}</option> 
                                    <option value=5>{lang hejin_forms:groupbreakb}5{lang hejin_forms:groupbreakc}</option> 
                                    <option value=6>{lang hejin_forms:groupbreakb}6{lang hejin_forms:groupbreakc}</option> 
                                    <option value=7>{lang hejin_forms:groupbreakb}7{lang hejin_forms:groupbreakc}</option> 
                                    <option value=8>{lang hejin_forms:groupbreakb}8{lang hejin_forms:groupbreakc}</option> 
                                    <option value=9>{lang hejin_forms:groupbreakb}9{lang hejin_forms:groupbreakc}</option> 
                                </select>
                                
                                <label>
                                <input type="checkbox" class="checkbox" name="config[GROUP_INPUT]" value="Y">
                                {lang hejin_forms:groupinp}
                                </label>
                                <script>Mo.Tools("Tip","");</script>
                            </td>
                            <td>
                            
                                <span id="extra_radio" class="none">               
                                	
                                </span>
                                
                                <span id="extra_checkbox" class="none">
                                    {lang hejin_forms:groupxzfw}<br />                                    
                                    <input type="text" name="config[GROUP_MIN]" value="$config[GROUP_MIN]" size="6" />                            		-
                                    <input type="text" name="config[GROUP_MAX]" value="$config[GROUP_MAX]" size="7" />                            		<script>Mo.Tools("Tip","{lang hejin_forms:groupfxsm}");</script>
                                </span>
                                
                                <span id="extra_text" class="none">
                                    {lang hejin_forms:groupyzx}<br />
                                    <select name="config[GROUP_VERIFY]">
                                        <option value="">{lang hejin_forms:groupyzxa}</option>
                                        <option value="mobile">{lang hejin_forms:groupyzxb}</option>
                                        <option value="email">{lang hejin_forms:groupyzxc}</option>
                                        <option value="number">{lang hejin_forms:groupyzxd}</option>                                        
                                       <option value="idcard">{lang hejin_forms:groupyzxe}</option>                              
                                    </select>
                                    {lang hejin_forms:groupyzxf}
                                    <input type="text" name="config[GROUP_SIZE]" value="$config[GROUP_SIZE]" size="4" />
                                </span>
                                
                                <span id="extra_textarea" class="none">
                                	
                                </span>
                                
                                <span id="extra_select" class="none">
                                	
                                </span>
                                
                                <!--Full-->
                                <span id="extra_file" class="none">
                                    {lang hejin_forms:groupfile}<br />
                                    <input name="config[GROUP_UPLOAD]" type="text" class="text" size="20" value="$config[GROUP_UPLOAD]">
                                    <script>Mo.Tools("Tip","");</script>
                                </span>
                                <!--/Full-->
                            </td>
                        </tr>
                    </table>
	     	
                </td>
                
            </tr>
        
        	<tr>
            	<td><strong>{lang hejin_forms:groupkz}{lang hejin_forms:maohao}</strong></td>
                <td>
                    
                    <table cellpadding="0" cellspacing="1" border="0" class="gird">
                        <tr>
                            <td>
                            {lang hejin_forms:groupkzyz}<br />
                            <label>
                                <input type="checkbox" class="checkbox" name="config[GROUP_MUST]" value="Y" />
                               {lang hejin_forms:groupkzyza}
                            </label>
                            </td>
                            <td>
                            {lang hejin_forms:groupsort}<br />
                            <input name="sort" type="text" class="text" value="$group[sort]" size="20" fix_name="{lang hejin_forms:groupsort}" fix_number="no">
                            <script>Mo.Tools("Tip","{lang hejin_forms:groupsorta}");</script>
                            </td>
                        </tr>
                    </table>
                    
                </td>
                
            </tr>
            
        	<tr>
            	<td><strong>{lang hejin_forms:jsname}{lang hejin_forms:maohao}</strong></td>
                <td>
                <textarea name="description" cols="50" rows="5" id="description">$group[description]</textarea>
                </td>
                
            </tr>
        
        	<tr>
            	<td><strong>{lang hejin_forms:groupsta}{lang hejin_forms:maohao}</strong></td>
                <td>
                    <label>
                    <input name="state" type="radio" class="radio" value="1"> 
                    {lang hejin_forms:groupstaa}
                    </label>
                    <label>
                    <input name="state" type="radio" class="radio" value="0">
                    {lang hejin_forms:groupstab}
                    </label>
                </td>
                
            </tr>
            
        	<tr>
            	<td><strong></strong></td>
                <td>
                
					
                        <input name="model" type="hidden" id="model" value="update" />
                        <input type="submit" name="groupedit" value="{lang hejin_forms:groupedit}" class="submit" />                </td>
                
            </tr>
        
        </table>
          
            <script type="text/javascript">
            
            function doChanged(s){
    
                var a = ['radio','checkbox','text','textarea','select','file'];
    
                for(var i=0; i<a.length; i++){                
                Mo( '#extra_'+a[i] ).hide();                
                }
    
                Mo( '#extra_'+s ).show(); 
    
                var b = ['radio','checkbox'];
                if( Mo.Array( b ).indexOf( s ) > -1 ){                
                	Mo( '#addv' ).enabled();                
                }else{                
               		Mo( '#addv' ).disabled();
                }
            }	
            
            Mo("select[name='config[GROUP_BREAK]']").value("$config[GROUP_BREAK]");            
            Mo("input[name='config[GROUP_INPUT]']").value("$config[GROUP_INPUT]");            
           
            Mo("input[name='config[GROUP_MUST]']").value("$config[GROUP_MUST]");            
            Mo("select[name='config[GROUP_VERIFY]']").value("$config[GROUP_VERIFY]");
			
            Mo("input[name=state]").value("$group[state]");            
            Mo("input[name=type]").value("$group[type]");
            
            doChanged("$group[type]");
            
        </script>
	</form>
	</div>
	
    
<!--body end-->

<!--Processed in 0.033844 second(s) , 0 queries-->
	</div>
	<script type="text/javascript">Serv.Manager.frLoad();Mo.Message("info", "" , 3, { "unique" : "message", "center":true } );</script>
	</body>
	</html>