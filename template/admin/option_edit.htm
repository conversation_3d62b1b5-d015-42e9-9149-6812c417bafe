<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=7" />
<meta name="robots" content="noindex, nofollow" />
<link type="text/css" rel="stylesheet" charset="utf-8" href="{HEJIN_PATH}public/style/dialog.css?ver=5.0" />
<link type="text/css" rel="stylesheet" charset="utf-8" href="{HEJIN_PATH}public/style/ubb.css?ver=5.0" />
<link type="text/css" rel="stylesheet" charset="utf-8" href="{HEJIN_PATH}public/style/ui.css?ver=5.0" />
<link type="text/css" rel="stylesheet" charset="utf-8" href="{HEJIN_PATH}public/style/calendar.css?ver=5.0" />
<title></title>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.ajax.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.drag.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.interface.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.ubb.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.form.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.ui.js?ver=5.0"></script>
<script type="text/javascript">
	Mo.store.site		="";
	Mo.store.web		="";
	Mo.store.domain		="";
	Mo.store.root		="{HEJIN_PATH}public/";
	Mo.store.product		="Bee";
	Mo.store.version		="5.0";
	Mo.store.build		="20111006";
	Mo.store.licence		="free";
	</script>

	<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/serv.dialog.js?ver=5.0"></script>
	<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/serv.upload.js?ver=5.0"></script>
	
	</head>
	
	<body>
	<div id="wrapper">
	
<!--body start-->
	<!--main-->

	
    <div id="nav">
        <strong>$forminfo['name'] &gt;&gt; $groupinfo['name']</strong> 	
    </div>
    
    <ul id="naver">
        <li class="active"><a href="$SELF?action=plugins&identifier=hejin_forms&pmod=admin_option&model=edit&oid=$_GET[oid]&gid=$_GET[gid]&fid=$_GET[fid]">{lang hejin_forms:optediturl}</a></li>
        <li><a href="$SELF?action=plugins&identifier=hejin_forms&pmod=admin_option&model=list&gid=$_GET[gid]&fid=$_GET[fid]">{lang hejin_forms:optlisturl}</a></li>
    </ul>
	    
	    
    
    		<div id="box">
    
    	<form action="" method="post" verify="true">
    	<input type="hidden" name="referer" value="$_G[referer]">
    	<input type="hidden" name="formhash" value="{FORMHASH}" />

        <table cellpadding="0" cellspacing="0" class="form">
        
        	<tr>
            	<td width="60"><strong>{lang hejin_forms:opttype}{lang hejin_forms:maohao}</strong></td>
                <td>
               {if $groupinfo[type]=='radio'}{lang hejin_forms:grouptypea}{elseif $groupinfo[type]=='checkbox'}{lang hejin_forms:grouptypeb}{elseif $groupinfo[type]=='select'}{lang hejin_forms:grouptypee}{/if}               </td>
            </tr>
            
                    
        	<tr>
            	<td><strong>{lang hejin_forms:optname}{lang hejin_forms:maohao}</strong></td>
                <td><input name="name" type="text" class="text" id="name" value="$optioninfo['name']" size="35" fix_name="{lang hejin_forms:optname}" fix_null="yes" />
                </td>
            </tr>
        
        	<tr>
            	<td><strong>{lang hejin_forms:optsort}{lang hejin_forms:maohao}</strong></td>
                <td><input name="sort" type="text" class="text digi" value="$optioninfo['sort']" size="35" fix_name="{lang hejin_forms:optsort}" fix_number="no" onclick="Mo.Soler( this, event, this.value, 'mo-soler', { '{lang hejin_forms:optsort}' : (function(){ var a = []; for(var i=-9;i<=60;i++){a.push(i);} return a; })() }, function( value ){ this.value = value; }, 1 , 23 );" readonly="true">
                </td>
            </tr>
            
                    
        	<tr>
            	<td><strong>{lang hejin_forms:groupsta}{lang hejin_forms:maohao}</strong></td>
                <td>
                    <label>
                    <input name="state" type="radio" class="radio" value="1"> 
                    {lang hejin_forms:groupstaa}
                    </label>
                    <label>
                    <input name="state" type="radio" class="radio" value="0">
                    {lang hejin_forms:groupstab}
                    </label>
                </td>
                
            </tr>
       
        	<tr class="nobr">
            	<td><strong></strong></td>
                <td>
					
						<input name="model" type="hidden" id="model" value="update" />
						<input name="oid" type="hidden" id="oid" value="$optioninfo['id']" />
						<input name="gid" type="hidden" id="oid" value="$_GET['gid']" />
						<input name="fid" type="hidden" id="oid" value="$_GET['fid']" />
						<input type="submit" name="submitup" value="{lang hejin_forms:groupedit}" class="submit" />                </td>
            </tr>
        
        </table>
        
      <script type="text/javascript">
        	Mo("input[name=state]").value("$optioninfo['state']"); </script>  
	 </form>
	 
	 </div>
	
        
	</div>
	</body>
	</html>