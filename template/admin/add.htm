<link href="{HEJIN_PATH}css/dialog.css" charset="utf-8" rel="stylesheet" type="text/css"></link>
<link href="{HEJIN_PATH}css/jquery.datetimepicker.css" charset="utf-8" rel="stylesheet" type="text/css"></link>
<script src="{HEJIN_PATH}js/jquery.js" charset="utf-8" type="text/javascript"></script>
<script src="{HEJIN_PATH}js/jquery.datetimepicker.js" charset="utf-8" type="text/javascript"></script>
<script src="{HEJIN_PATH}ckeditor/ckeditor.js" charset="utf-8" type="text/javascript"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.js?ver=5.0"></script>

<div id="wrapper">
	

	
	<div id="box">
	    <form action="" method="post" name="from1" enctype="multipart/form-data">
    <input type="hidden" name="referer" value="$_G[referer]">
    <input type="hidden" name="formhash" value="{FORMHASH}" />
    <input type="hidden" name="model" value="add" />
        <table class="form" style="width:70%" cellpadding="0" cellspacing="0" align="left">
    
            <tbody><tr><td colspan="2" class="section"><strong>{lang hejin_forms:nameform}</strong></td></tr>

            
    
            <tr valign="top">
                <td width="60"><strong>{lang hejin_forms:formname}{lang hejin_forms:maohao}</strong></td>
                <td>
                
                    <input name="name" class="text" id="name" value="" size="60" fix_name="{lang hejin_forms:formname}" fix_null="yes" type="text">
                    <select name="skin" id="skin" style="width: 120px; visibility: visible;">
                    <option value="default">{lang hejin_forms:skina}</option><option value="yellow">{lang hejin_forms:skinb}</option><option value="bule">{lang hejin_forms:skinc}</option><option value="kaqt">{lang hejin_forms:skind}</option><option value="mise">{lang hejin_forms:skine}</option><option value="hxcq">{lang hejin_forms:skinf}</option><option value="ross">{lang hejin_forms:sking}</option><option value="shuixian">{lang hejin_forms:skinh}</option><option value="qnhc">{lang hejin_forms:skini}</option><option value="muwen">{lang hejin_forms:skinj}</option>                   </select>
                    
                    <script type="text/javascript">Mo("#skin").bind( 'change', function( ele, index, event ){
                        
                        Mo("#thempic").show().html( '<img src="{HEJIN_PATH}public/them/'+ Mo(this).value() +'.jpg"  width="300"/>' );							
                    
                    });						
                    
</script>
                </td>
                
            </tr>
        
            <tr>
                <td><strong>{lang hejin_forms:jsname}{lang hejin_forms:maohao}</strong></td>
                <td>
                    
                    <textarea name="description" cols="50" rows="9" id="description" style="float:left;"></textarea>
                    
                    <script>
                // Replace the <textarea id="editor1"> with a CKEditor
                // instance, using default configuration.
                CKEDITOR.replace( 'description' );
            </script>
                    <div style="clear:both;"></div>
                </td>
                
            </tr>
        
        
            <tr text="">
                <td><strong>{lang hejin_forms:setime}{lang hejin_forms:maohao}</strong></td>
                <td>
                <input name="start" class="text date" value="" id="datetimestart" type="text">
                
                
                -
                <input name="expire" class="text date" value="" id="datetimeexpire" type="text">
                <script>
				
				$('#datetimestart').datetimepicker();
				$('#datetimeexpire').datetimepicker();
				</script>
                
                </td>
            </tr>
        
            <tr>
                <td><strong>{lang hejin_forms:formsta}{lang hejin_forms:maohao}</strong></td>
                <td>
                    <label>
                    <input class="radio" name="state" value="1" checked="" type="radio"> 
                    {lang hejin_forms:formstaa}
                    </label>
                    <label>
                    <input class="radio" name="state" value="0" type="radio">
                    {lang hejin_forms:formstab}
                    </label>
                </td>
                
            </tr>

            <tr>
                <td><strong>{lang hejin_forms:formstb}{lang hejin_forms:maohao}</strong></td>
                <td>
                    <label>
                    <input class="radio" name="modify" value="1" checked="" type="radio"> 
                    {lang hejin_forms:formstba}
                    </label>
                    <label>
                    <input class="radio" name="modify" value="2" type="radio">
                    {lang hejin_forms:formstbb}
                    </label>
                </td>
                
            </tr>


<tr>
                <td><strong>{lang hejin_forms:huodongpd}{lang hejin_forms:maohao}</strong></td>
                <td>
                    <label>
                    <input class="radio" name="sort" value="0" checked="" type="radio"> 
                    {lang hejin_forms:noxshdpd}
                    </label>
                    <label>
                    <input class="radio" name="sort" value="1" type="radio">
                    {lang hejin_forms:xianshihdpd}
                    </label>
                </td>
                
            </tr>
			
            
            <tr>
                <td><strong>{lang hejin_forms:formpic}{lang hejin_forms:maohao}</strong></td>
                <td>
<span id="thumb_span"><input class="text" name="thumb" value="" size="35" type="file"></span>
                </td>
            </tr>
            
<tr>
                <td><strong>{lang hejin_forms:formpdpic}{lang hejin_forms:maohao}</strong></td>
                <td>
<span id="thumb_span"><input class="text" name="tags" value="" size="35" type="file"></span> 
                </td>
            </tr>
 
 
             <tr>
                <td><strong>{lang hejin_forms:baomcgts}{lang hejin_forms:maohao}</strong></td>
                <td>
<span id="thumb_span"><input name="config" class="text" id="config" value="" fix_name="{lang hejin_forms:baomcgts}" fix_null="yes" type="text"></span>
{lang hejin_forms:baomcgtsa}
                </td>
            </tr>
            <tr>
                <td><strong>{lang hejin_forms:baomcgtz}{lang hejin_forms:maohao}</strong></td>
                <td>
<span id="thumb_span"><input name="quote" class="text" id="quote" value="" fix_name="{lang hejin_forms:baomcgtz}" fix_null="yes" type="text"></span>
{lang hejin_forms:baomcgtza}
                </td>
            </tr>

                      
            </tbody><tbody>
                <tr class="nobr">
                    <td><strong></strong></td>
                    <td>
                        <input name="submitadd" value="{lang hejin_forms:formadd}" class="submit" type="submit">                    </td>				
                </tr>
            </tbody>
        
        </table>
     
	 </form>
     
     <table class="thempic" align="center">
     <tr>
     <td width="100%" align="center" id="thempic"><img src="{HEJIN_PATH}public/them/default.jpg"  width="300"/></td>
     </tr>
     
     </table>
     
	</div>
	
	</div>
    
