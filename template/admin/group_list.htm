<style>
#wrapper {
    padding: 10px;
    background: url('{HEJIN_PATH}public/style/pic/dialog.gif') no-repeat scroll right bottom transparent;
}
#nav {
    padding: 1px 5px 0px;
    color: #333;
}
#nav strong {
    font-size: 16px;
}
#naver {
    background: url('{HEJIN_PATH}public/style/pic/bg.png') repeat-x scroll 0px -620px transparent;
    height: 35px;
    clear: both;
    overflow: hidden;
    margin-bottom: 10px;
    padding: 0px 5px;
}
#naver li {
    font-weight: bold;
    line-height: 30px;
    height: 30px;
    text-align: center;
    margin: 5px 0px;
    display: inline-block;
    float: left;
    overflow: hidden;
}
#naver li a {
    padding: 5px 10px;
    color: #FFF;
    line-height: 30px;
}
a, a:link, a:visited {
    color: #1E5494;
}
a {
    text-decoration: none;
    cursor: pointer;
}
#naver li.active {
    background: none repeat scroll 0% 0% #FFF;
}
#naver li.active a {
    color: #4B90B2;
    text-decoration: none;
}
.table {
    background: none repeat scroll 0% 0% #C4CACD;
    margin: 0px auto 10px;
    overflow: hidden;
	border-collapse:separate !important;
}
.table .title td {
    background: none repeat scroll 0% 0% #DEEAF8;
    color: #333;
}
.table td {
    text-indent: 5px;
    padding: 5px;
    line-height: 20px;
}
td, input, select, body {
    font-family: Verdana;
    font-size: 12px;
}
body, td, input, textarea, select, button {
    color: #555;
    font: 12px "Lucida Grande",Verdana,Lucida,Helvetica,Arial,"{lang hejin_forms:styletif}",sans-serif;
}
.table .line {
    background: none repeat scroll 0% 0% #FFF;
    color: #333;
}
.table .tfoot {
    background: none repeat scroll 0% 0% #E9F3FD;
}
.text-no {
    color: #F00;
}
.text-yes {
    color: #390;
}
#saving {
    background: none repeat scroll 0% 0% #E3EDF9;
    padding: 8px;
    line-height: 20px;
    clear: both;
    overflow: hidden;
}
.page {
    line-height: 20px;
    text-decoration: none;
    color: #666;
}
.page strong {
    font-weight: bold;
    padding: 2px 6px;
    color: #CCC;
    margin-right: 2px;
    vertical-align: middle;
}
.page a {
    border: 1px solid #DDD;
    padding: 2px 6px;
    background: none repeat scroll 0% 0% #FFF;
    color: #333;
    margin-right: 2px;
    vertical-align: middle;
    text-decoration: none;
}
.page span {
    margin-left: 5px;
}

</style>
<div id="wrapper">
	
<!--body start-->
	<!--main-->

	
    <div id="nav">
        <strong>$form[name]</strong> 	
    </div>
    
    <ul id="naver">
        <li><a href="$SELF?action=plugins&identifier=hejin_forms&pmod=admin_group&model=add&formid=$_GET['formid']">{lang hejin_forms:groupaddurl}</a></li>
        <li class="active"><a href="$SELF?action=plugins&identifier=hejin_forms&pmod=admin_group&model=list&formid=$_GET['formid']">{lang hejin_forms:groupediturl}</a></li>
    </ul>
	    
    
    
        
		<script type="text/javascript">
            Mo.reader(function(){
				
								
				
            });
            
        </script>    
    
      <table class="table" id="table" border="0" cellpadding="1" cellspacing="1" width="100%">
	  
				
        <tbody><tr class="title">
            <td>{lang hejin_forms:grouplista}</td>
            <td>{lang hejin_forms:grouplistb}</td>
            <td>{lang hejin_forms:grouplistc}</td>
            <td>{lang hejin_forms:grouplistd}</td>
            <td>{lang hejin_forms:groupliste}</td>
            <td>{lang hejin_forms:grouplistf}</td>
            <td>{lang hejin_forms:grouplistg}</td>
            <td>{lang hejin_forms:grouplisth}</td>
            <td width="70">{lang hejin_forms:grouplisti}</td>
        </tr>
        
        
<!--{loop $groups $val}-->
<tr class="line" mark="1015" edit="">
                <td>$val[id]</td>
                <td>$val[sort]</td>
                <td><strong class="text-yes">$val[name]</strong></td>
                <td>                           
                    {if in_array($val[type],array("radio","checkbox","select"))}
                                            
                                
               		<a id="mark-2" href="$SELF?action=plugins&identifier=hejin_forms&pmod=admin_option&model=list&formid=$_GET['formid']&groupid=$val[id]">{lang hejin_forms:grouplistj}</a>
				
					<sup class="text-yes">[{$val['stat']}]</sup>  {/if}              
                                
                    
                    <!--{eval $config = fix_json($val[config]);}--> 
                              </td>
                <td>
                    {if $val[type]=='text'}{lang hejin_forms:grouptypec}{elseif $val[type]=='radio'}{lang hejin_forms:grouptypea}{elseif $val[type]=='checkbox'}{lang hejin_forms:grouptypeb}{elseif $val[type]=='textarea'}{lang hejin_forms:grouptyped}{elseif $val[type]=='select'}{lang hejin_forms:grouptypee}{elseif $val[type]=='file'}{lang hejin_forms:grouptypef}{elseif $val[type]=='date'}{lang hejin_forms:groupdate}{elseif $val[type]=='time'}{lang hejin_forms:grouptime}{elseif $val[type]=='dati'}{lang hejin_forms:groupdati}{/if}  {if $config["GROUP_INPUT"]=='Y'}{lang hejin_forms:grouptypeg}{/if}                                    </td>
                <td>{if $config["GROUP_MUST"]=='Y'}{lang hejin_forms:groupmyes}{else}{lang hejin_forms:groupmno}{/if}</td>
                                            <!--{eval $dateline = date("Y/m/d",$val["dateline"]);}-->

                <td>$dateline</td>
                <td>{if $val[state]== 1}<span class="text-yes">{lang hejin_forms:stateyes}</span>{else}<span class="text-no">{lang hejin_forms:stateno}</span>{/if}</td>
                <td>
                <a href="$SELF?action=plugins&identifier=hejin_forms&pmod=admin_group&model=edit&groupid=$val[id]&fid=$form[id]"><img src="{HEJIN_PATH}public/image/icon/pencil.png" alt="{lang hejin_forms:bianji}" align="absmiddle"></a>  <a href="$SELF?action=plugins&identifier=hejin_forms&pmod=admin_group&model=del&formid=$_GET['formid']&groupid=$val[id]" onclick="return confirm(&quot;{lang hejin_forms:shanchuts}&quot;);"><img src="{HEJIN_PATH}public/image/icon/trash.png" alt="{lang hejin_forms:shanchu}" align="absmiddle"> 
                </a></td>
            </tr>
<!--{/loop}-->                            		
	</tbody></table>
    
	</div>