<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
<meta http-equiv="X-UA-Compatible" content="IE=7" />
<meta name="robots" content="noindex, nofollow" />
<link type="text/css" rel="stylesheet" charset="utf-8" href="{HEJIN_PATH}public/style/dialog.css?ver=5.0" />
<link type="text/css" rel="stylesheet" charset="utf-8" href="{HEJIN_PATH}public/style/ubb.css?ver=5.0" />
<link type="text/css" rel="stylesheet" charset="utf-8" href="{HEJIN_PATH}public/style/ui.css?ver=5.0" />
<link type="text/css" rel="stylesheet" charset="utf-8" href="{HEJIN_PATH}public/style/calendar.css?ver=5.0" />
<title>$form[name]</title>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.ajax.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.drag.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.interface.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.ubb.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.form.js?ver=5.0"></script>
<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.ui.js?ver=5.0"></script>
<script type="text/javascript">
	Mo.store.site		="";
	Mo.store.web		="";
	Mo.store.domain		="";
	Mo.store.root		="{HEJIN_PATH}public/";
	Mo.store.product		="Bee";
	Mo.store.version		="5.0";
	Mo.store.build		="20111006";
	Mo.store.licence		="free";
	</script>

	<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/serv.dialog.js?ver=5.0"></script>
	<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/serv.upload.js?ver=5.0"></script>
	
	</head>
	
	<body>
	<div id="wrapper">
	
<!--body start-->
	    
    <div id="nav">
        <strong>$form[name]</strong> {lang hejin_forms:valuelista} <a href="$SELF?action=plugins&identifier=hejin_forms&pmod=admin_value&model=list&formid=$formid">{lang hejin_forms:shuaxin}</a> <a href="$SELF?action=plugins&identifier=hejin_forms&pmod=admin_value&model=lists&formid=$formid">{lang hejin_forms:daochushuju}</a>
    </div>

	<script type="text/javascript" charset="utf-8" src="{HEJIN_PATH}public/js/mo.calendar.js?ver=5.0"></script>
        
    
    

        
		
    
    <table width="100%" border="0" cellpadding="1" cellspacing="1" id="table" class="table">
		      
        <tr class="title" id="thead">
            <td width="80">{lang hejin_forms:valuelistb}</td>
            <td>{lang hejin_forms:valuelistc}</td>
            <!--{loop $groups $val}--><td title="GID $val[id]">$val[name]</td><!--{/loop}-->           <td width="60">{lang hejin_forms:valuelistd}</td>
            <td width="100">{lang hejin_forms:valueliste}</td>
            <td width="130">{lang hejin_forms:valuelistf}</td>
            <td>{lang hejin_forms:valuelistg}</td>
        </tr>
      <!--{loop $valsu $val}-->
              <tr class="line" mark="3">
            <td>
                $val[id]           </td>
            <td title=''>
            	                <a href="$_G['siteurl']home.php?mod=space&uid=$value[uid]&do=profile" target="_blank">$val[account]</a>
                
                            </td>
<!--{loop $gsorts $gsort}-->
	 <!--{eval $gid = $gsort;}-->
	 <!--{eval $gname = 'G-'.$gid;}-->
	 <!--{eval $configcc = $val[config][$gname];}-->
<!--{if $configcc == "" ||  $configcc == null}-->
<td></td>
<!--{else}-->
<td>$configcc</td>
<!--{/if}-->
<!--{/loop}-->
                      
            <td>
				{if $val[state] == 0}<a href="$SELF?action=plugins&identifier=hejin_forms&pmod=admin_value&model=up&formid=$formid&valueid=$val[id]"><span class="text-no">{lang hejin_forms:valuelisti}</span></a>{else}<span class="text-yes">{lang hejin_forms:valuelisth}</span>{/if}            </td>
            <td>
                <span class="text-no">$val[ip]</span>            </td>
            <td title="$value[dateline]">
                                        <!--{eval $dateline = date("y/m/d H:i",$val["dateline"]);}-->

                <span class="text-yes">$dateline</span>            </td>
                <td>
 <a href="$SELF?action=plugins&identifier=hejin_forms&pmod=admin_value&model=del&opid=$val[id]&fid=$form[id]" onclick="return confirm(&quot;{lang hejin_forms:shanchuts}?&quot;);"><img src="{HEJIN_PATH}public/image/icon/trash.png" alt="{lang hejin_forms:shanchu}" align="absmiddle"> 
            </a></td>
            </tr>
  <!--{/loop}-->          
            
                  
                 
                
            
        </table>
        
<div id="saving">
		<div class="page">$page_string</div>    </div>	
    <!--up-->
    
    
    
<!--body end-->

<!--Processed in 0.03665 second(s) , 2 queries-->
	</div>
	</body>
	</html>