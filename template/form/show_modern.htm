<!--{template header}-->

<!-- 现代化样式和脚本 -->
<link href="{HEJIN_PATH}css/hejin-forms-modern.css" charset="utf-8" rel="stylesheet" type="text/css">
<link href="{HEJIN_PATH}css/hejin-forms-async.css" charset="utf-8" rel="stylesheet" type="text/css">
<link href="{HEJIN_PATH}css/jquery.datetimepicker.css" charset="utf-8" rel="stylesheet" type="text/css">

<script src="{HEJIN_PATH}js/jquery.js" charset="utf-8" type="text/javascript"></script>
<script src="{HEJIN_PATH}js/jquery.datetimepicker.js" charset="utf-8" type="text/javascript"></script>
<script src="{HEJIN_PATH}js/hejin-forms-async-compatible.js" charset="utf-8" type="text/javascript"></script>
<script src="{HEJIN_PATH}js/hejin-forms-modern.js" charset="utf-8" type="text/javascript"></script>

<!-- 表单容器 -->
<div class="hf-form-wrapper">
    <!-- 表单标题区域 -->
    <div class="hf-form-header">
        <h1 class="hf-form-title">{$form['name']}</h1>
        <div class="hf-form-description">{$form['description']}</div>
        <div class="hf-form-meta">
            <span class="hf-meta-item">
                <i class="hf-icon-time"></i>
                开始时间：{date('Y-m-d H:i', $form['start'])}
            </span>
            <span class="hf-meta-item">
                <i class="hf-icon-time"></i>
                结束时间：{date('Y-m-d H:i', $form['expire'])}
            </span>
            <span class="hf-meta-item">
                <i class="hf-icon-users"></i>
                已参与：{$form['stat']} 人
            </span>
        </div>
    </div>

    <!-- 表单主体 -->
    <form action="" class="hf-form-container" id="new_entry" method="post" enctype="multipart/form-data">
        <input type="hidden" name="formhash" value="{FORMHASH}" />
        <input type="hidden" name="fid" value="{$form['id']}" />
        <input type="hidden" name="uid" value="{$_G['uid']}" />
        <input type="hidden" name="account" value="{$_G['username']}" />

        <!-- 表单字段 -->
        <!--{loop $groups $key $val}-->
        <!--{eval $config = fix_json($val['config']);}-->
        
        <div class="field" data-field-type="{$val['type']}" data-field-id="{$val['id']}">
            <label class="control-label" for="G-{$val['id']}">
                {$val['name']}
                <!--{if $config['GROUP_MUST']}-->
                <span class="required">*</span>
                <!--{/if}-->
            </label>
            
            <!--{if $val['description']}-->
            <div class="help-block">
                <p>{$val['description']}</p>
            </div>
            <!--{/if}-->
            
            <div class="field_content">
                <div class="controls">
                    <!--{if $val['type'] == "text"}-->
                        <!--{if $config['GROUP_VERIFY'] == "number"}-->
                        <input name='G-{$val[id]}' id="G-{$val[id]}" class="hf-input-base" type='number' value='' 
                               fix_name='{$val[name]}' {if $config[GROUP_MUST]}required{/if} 
                               placeholder="请输入{$val[name]}" />
                        <!--{elseif $config['GROUP_VERIFY'] == "email"}-->
                        <input name='G-{$val[id]}' id="G-{$val[id]}" class="hf-input-base" type='email' value='' 
                               fix_name='{$val[name]}' {if $config[GROUP_MUST]}required{/if} 
                               placeholder="请输入邮箱地址" />
                        <!--{elseif $config['GROUP_VERIFY'] == "mobile"}-->
                        <input name='G-{$val[id]}' id="G-{$val[id]}" class="hf-input-base" type='tel' value='' 
                               fix_name='{$val[name]}' {if $config[GROUP_MUST]}required{/if} 
                               placeholder="请输入手机号码" />
                        <!--{elseif $config['GROUP_VERIFY'] == "idcard"}-->
                        <input name='G-{$val[id]}' id="G-{$val[id]}" class="hf-input-base" type='text' value='' 
                               fix_name='{$val[name]}' {if $config[GROUP_MUST]}required{/if} 
                               placeholder="请输入身份证号码" maxlength="18" />
                        <!--{else}-->
                        <input name='G-{$val[id]}' id="G-{$val[id]}" class="hf-input-base" type='text' value='' 
                               fix_name='{$val[name]}' {if $config[GROUP_MUST]}required{/if} 
                               placeholder="请输入{$val[name]}" />
                        <!--{/if}-->
                        
                    <!--{elseif $val['type'] == "textarea"}-->
                        <textarea name='G-{$val[id]}' id="G-{$val[id]}" class="hf-input-base" 
                                  rows='6' fix_name='{$val[name]}' {if $config[GROUP_MUST]}required{/if}
                                  placeholder="请输入{$val[name]}"></textarea>
                        
                    <!--{elseif $val['type'] == "file"}-->
                        <input name='G-{$val[id]}' id="G-{$val[id]}" type='file' 
                               fix_name='{$val[name]}' {if $config[GROUP_MUST]}required{/if} 
                               fix_extension='{$config[GROUP_UPLOAD]}' />
                        <!--{if $config['GROUP_UPLOAD']}-->
                        <div class="hf-file-hint">
                            <small>支持的文件格式：{$config[GROUP_UPLOAD]}</small>
                        </div>
                        <!--{/if}-->
                        
                    <!--{elseif $val['type'] == "date"}-->
                        <input name='G-{$val[id]}' id="G-{$val[id]}" class="hf-input-base date" type='text' 
                               value='' fix_name='{$val[name]}' {if $config[GROUP_MUST]}required{/if}
                               placeholder="请选择日期" readonly />
                        <script>
                        $(function(){
                            $("#G-{$val[id]}").datetimepicker({
                                timepicker: false,
                                format: 'Y/m/d',
                                lang: 'ch'
                            });
                        });
                        </script>
                        
                    <!--{elseif $val['type'] == "dati"}-->
                        <input name='G-{$val[id]}' id="G-{$val[id]}" class="hf-input-base datetime" type='text' 
                               value='' fix_name='{$val[name]}' {if $config[GROUP_MUST]}required{/if}
                               placeholder="请选择日期时间" readonly />
                        <script>
                        $(function(){
                            $("#G-{$val[id]}").datetimepicker({
                                format: 'Y/m/d H:i',
                                lang: 'ch'
                            });
                        });
                        </script>
                        
                    <!--{elseif $val['type'] == "select"}-->
                        <select id='G-{$val[id]}' name='G-{$val[id]}' class="hf-input-base" 
                                fix_name='{$val[name]}' {if $config[GROUP_MUST]}required{/if}>
                            <option value="">请选择{$val[name]}</option>
                            <!--{eval $options = Hejin::_option_lists($val[id]);}-->
                            <!--{loop $options $keyi $vala}-->
                            <option value="{$vala['name']}">{$vala['name']}</option>
                            <!--{/loop}-->
                        </select>
                        
                    <!--{elseif $val['type'] == "radio"}-->
                        <div class="radio-group" data-role="controlgroup">
                            <!--{eval $options = Hejin::_option_lists($val[id]);}-->
                            <!--{loop $options $keyi $vala}-->
                            <label class="hf-radio-label">
                                <input type="radio" name='G-{$val[id]}' value="{$vala['name']}" 
                                       fix_name='{$val[name]}' {if $config[GROUP_MUST]}required{/if} />
                                <span>{$vala['name']}</span>
                            </label>
                            <!--{/loop}-->
                        </div>
                        
                    <!--{elseif $val['type'] == "checkbox"}-->
                        <div class="checkbox-group" data-role="controlgroup">
                            <!--{eval $options = Hejin::_option_lists($val[id]);}-->
                            <!--{loop $options $keyi $vala}-->
                            <label class="hf-checkbox-label">
                                <input type="checkbox" name='G-{$val[id]}[]' value="{$vala['name']}" 
                                       fix_name='{$val[name]}' />
                                <span>{$vala['name']}</span>
                            </label>
                            <!--{/loop}-->
                        </div>
                        
                    <!--{/if}-->
                </div>
            </div>
            
            <!-- 错误提示区域 -->
            <div class="hf-field-error" style="display: none;"></div>
        </div>
        
        <!-- 字段分割线 -->
        <!--{if !$val@last}-->
        <div class="section-break">
            <hr>
        </div>
        <!--{/if}-->
        
        <!--{/loop}-->
        
        <!-- 提交按钮区域 -->
        <div class="submit-field">
            <input id="submita" class="submit hf-btn hf-btn-primary" name="commit" value="提交表单" type="submit">
            <div class="hf-submit-hint">
                <small>提交前请仔细检查填写的信息</small>
            </div>
        </div>
    </form>
    
    <!-- 表单底部信息 -->
    <div class="hf-form-footer">
        <div class="hf-footer-info">
            <span>本表单由和金表单系统提供技术支持</span>
            <span>数据安全可靠，请放心填写</span>
        </div>
    </div>
</div>

<!-- 额外的现代化样式 -->
<style>
.hf-form-wrapper {
    max-width: 800px;
    margin: 20px auto;
    padding: 0 20px;
}

.hf-form-header {
    text-align: center;
    margin-bottom: 32px;
    padding: 32px;
    background: linear-gradient(135deg, var(--primary-color), var(--success-color));
    border-radius: var(--border-radius-large);
    color: white;
    box-shadow: var(--box-shadow-light);
}

.hf-form-title {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 12px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hf-form-description {
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 24px;
    opacity: 0.9;
}

.hf-form-meta {
    display: flex;
    justify-content: center;
    gap: 24px;
    flex-wrap: wrap;
}

.hf-meta-item {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    opacity: 0.9;
}

.hf-icon-time::before {
    content: "🕒";
}

.hf-icon-users::before {
    content: "👥";
}

.hf-submit-hint {
    margin-top: 12px;
    color: var(--text-secondary);
}

.hf-form-footer {
    text-align: center;
    margin-top: 32px;
    padding: 24px;
    background: var(--background-light);
    border-radius: var(--border-radius-base);
    border: 1px solid var(--border-lighter);
}

.hf-footer-info {
    display: flex;
    justify-content: center;
    gap: 24px;
    flex-wrap: wrap;
    font-size: 12px;
    color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hf-form-wrapper {
        padding: 0 10px;
    }
    
    .hf-form-header {
        padding: 24px 16px;
    }
    
    .hf-form-title {
        font-size: 24px;
    }
    
    .hf-form-meta {
        flex-direction: column;
        gap: 12px;
    }
    
    .hf-footer-info {
        flex-direction: column;
        gap: 8px;
    }
}

/* 加载动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hf-form-wrapper {
    animation: fadeInUp 0.6s ease-out;
}

/* 字段聚焦效果 */
.field:focus-within {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

/* 进度指示器样式 */
.hf-progress-indicator {
    position: sticky;
    top: 0;
    background: var(--background-white);
    padding: 16px;
    border-radius: var(--border-radius-base);
    margin-bottom: 24px;
    box-shadow: var(--box-shadow-base);
    z-index: 100;
}

.hf-progress-bar-container {
    width: 100%;
    height: 6px;
    background: var(--border-lighter);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 8px;
}

.hf-progress-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--success-color));
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 3px;
}

.hf-progress-text {
    text-align: center;
    font-size: 12px;
    color: var(--text-secondary);
    font-weight: 500;
}
</style>

<!--{template footer}-->
