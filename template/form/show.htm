<!DOCTYPE html>
<html class=" js flexbox flexboxlegacy canvas canvastext webgl touch geolocation postmessage no-websqldatabase indexeddb hashchange history draganddrop websockets rgba hsla multiplebgs backgroundsize borderimage borderradius boxshadow textshadow opacity cssanimations csscolumns cssgradients no-cssreflections csstransforms csstransforms3d csstransitions fontface generatedcontent video audio localstorage sessionstorage webworkers applicationcache svg inlinesvg smil svgclippaths" style=""><head>
  <title>$form['name']</title>
  <meta name="description" content="<!--{eval echo strip_tags($form['description']);}-->">
  <link href="{HEJIN_PATH}images/published.css" media="screen" rel="stylesheet" type="text/css">
$temp[$form['skin']]
  <!--[if lte IE 8]>
    <link href="{HEJIN_PATH}images/lte-ie8-d37ac0e7c9c275161eece5cfe471ed3b.css" media="screen" rel="stylesheet" type="text/css" />
    <script src="{HEJIN_PATH}images/html5-953594fdd1a0de919fc28151733c47e3.js" type="text/javascript"></script>
  <![endif]-->
  <!--[if lte IE 7]>
    <link href="{HEJIN_PATH}images/lte-ie7-7087708de51776ea27e541ec07a0acd8.css" media="screen" rel="stylesheet" type="text/css" />
  <![endif]-->
  <!--[if IE 6]> <link href="{HEJIN_PATH}images/ie6-873619c2088cc2ede97879b06ff7f350.css" media="screen" rel="stylesheet" type="text/css" /> <![endif]-->
  <script src="{HEJIN_PATH}images/application.js" type="text/javascript"></script>
  <!--[if IE 6]>
    <script src="{HEJIN_PATH}images/fix-ie6-b0ecfdc615773cdd73548f95836b86f3.js" type="text/javascript"></script>
  <![endif]-->
<link href="{HEJIN_PATH}css/jquery.datetimepicker.css" charset="utf-8" rel="stylesheet" type="text/css"></link>
<link href="{HEJIN_PATH}css/hejin-forms-async.css" charset="utf-8" rel="stylesheet" type="text/css"></link>
<script src="{HEJIN_PATH}js/jquery.js" charset="utf-8" type="text/javascript"></script>
<script src="{HEJIN_PATH}js/jquery.datetimepicker.js" charset="utf-8" type="text/javascript"></script>
<script src="{HEJIN_PATH}js/hejin-forms-async.js" charset="utf-8" type="text/javascript"></script>

  <meta content="authenticity_token" name="csrf-param">
<meta content="mOI/ZgBdwLP+vfsoA3nPAxu1F2hZGs+GoGmgFg+78fM=" name="csrf-token">
</head>
<body class="entry-container bg-image">
{if $user['username']}
    <header class="center clearfix user-info-container">
      <div class="user-info">
        
        <div class="user-name">
          <div class="name-with-vip-style">
  <span class="vip-user std1_user ">$user['username']</span>
</div>
          
        </div>
      </div>
    </header>
    {/if}
  <form action="" class="center" id="new_entry" method="post" enctype="multipart/form-data">
  <div style="margin:0;padding:0;display:inline">
    <input type="hidden" name="referer" value="$_G[referer]">
    <input type="hidden" name="formhash" value="{FORMHASH}" />
    <input type="hidden" name="fid" value="$form['id']" />
     <input type="hidden" name="uid" value="$user['uid']" />
     <input type="hidden" name="account" value="$user['username']" />
 </div>
      <div class="banner">
          <div class="banner-img banner-content">
          {if $form['thumb']}
            <img alt="$form['name']" src="{HEJIN_PATH}$form['thumb']">           
           {/if}
          </div>
    </div>

  <h1 class="form-name">$form['name']</h1>
  
    {if $form['description']}<div class="form-description"><p>
<!--{eval echo stripslashes($form['description']);}--></p></div>{/if}
  
  <fieldset>
    <div class="form-content">
   <div class="error_explanation" id="errormsg" style="display:none">
      <ul id="errorm">

      </ul>
    </div>   

<div class="form-message hide"></div>

<!--{loop $groups $val}-->
<!--{eval $config = fix_json($val[config]);}-->
          <div class="field    " data-api-code="field_$val['id']">
            <div class="control-group ">
  <label class="control-label field_title" data-role="collapse_toggle" for="entry_field_$val['id']">
    $val['name']
     {if $config[GROUP_MUST]}<span style="color: red;">*</span>{/if} <span style="color: red;" id="ermsg$val['id']"></span>
</label>
  <div class="field_content">
  {if $val['description']}
      <div class="help-block"><p>$val['description']</p></div>
 {/if}
                      
<div class="controls">
 {if $val['type'] == 'text'}
    
      
      <div id="divG{$val[id]}"><input name='G-{$val[id]}' id='G-{$val[id]}' style="height:30px; line-height:30px;" type='text' value='' size='{$config[GROUP_SIZE]}' fix_name='{$val[name]}' /></div>
 
{elseif $val['type'] == "date"}
 
      <div id="divG{$val[id]}"><input name='G-{$val[id]}' id='G-{$val[id]}' style="height:30px; line-height:30px;" type='text' value='' size='{$config[GROUP_SIZE]}' fix_name='{$val[name]}' /></div>
  <script>
  $('#G-{$val[id]}').datetimepicker({
	onGenerate:function( ct ){
		$(this).find('.xdsoft_date')
			.toggleClass('xdsoft_disabled');
	},
	minDate:'-1970/01/1',
	maxDate:'+1970/01/1',
	timepicker:false
});

  </script>
  
{elseif $val['type'] == "time"}
 
      <div id="divG{$val[id]}"><input name='G-{$val[id]}' id='G-{$val[id]}' style="height:30px; line-height:30px;" type='text' value='' size='{$config[GROUP_SIZE]}' fix_name='{$val[name]}' /></div>
  <script>
  $('#G-{$val[id]}').datetimepicker({
	datepicker:false,
	format:'H:i',
	step:5
});

  </script>
{elseif $val['type'] == "dati"}
 
      <div id="divG{$val[id]}"><input name='G-{$val[id]}' id='G-{$val[id]}' style="height:30px; line-height:30px;" type='text' value='' size='{$config[GROUP_SIZE]}' fix_name='{$val[name]}' /></div>
  <script>
  $('#G-{$val[id]}').datetimepicker();
  </script>
   
{elseif $val['type'] == "textarea"}
               <div id="divG{$val[id]}"> <textarea name='G-{$val[id]}' id="G-{$val[id]}" class="input-xxlarge" cols='50' rows='4' fix_name='{$val[name]}' {if $config[GROUP_MUST]}fix_null='yes'{/if}></textarea> </div>
{elseif $val['type'] == "file"}			
                <div id="divG{$val[id]}"><input name='G-{$val[id]}' id="G-{$val[id]}" type='file' size='50' fix_name='{$val[name]}' {if $config[GROUP_MUST]}fix_null='yes'{/if} fix_extension='{$config[GROUP_UPLOAD]}' /></div>
                {if $config[GROUP_UPLOAD]}
                {lang hejin_forms:showfile}$config[GROUP_UPLOAD]
                {/if}
{else}
<div class="clearfix radio-group" data-role="controlgroup">
                {if $val['type'] == "select"}
                <select id='G-{$val[id]}' name='G-{$val[id]}' fix_name='{{$val[name]}}' >
                {/if}
			<!--{eval $options = Hejin::_option_lists($val[id]);}-->
            <!--{loop $options $keyi $vala}-->
            
           <!--{if $keyi == 0}-->
            
            {if $val['type'] == "radio"}
            <label class="radio inline "><input type='radio' class='radio' name='G-{$val[id]}' value='{$vala[id]}' fix_name='{$val[id]}' {if $config[GROUP_MUST]}fix_null='yes'{/if} checked/> $vala[name]</label>
            {/if}
            
            {if $val['type'] == "checkbox"}
                            <label class="checkbox inline "><input type='checkbox' class='checkbox' name='G-{$val[id]}[]' value='{$vala[id]}' fix_name='{$val[name]}' {if $config[GROUP_MUST]}fix_null='yes'{/if} checked/> {$vala[name]}</label>
            {/if}
            {if $val['type'] == "select"}
                            <option value='{$vala[id]}' selected> {$vala[name]}</option>
            {/if}
            
			<!--{else}-->
 
 
             {if $val['type'] == "radio"}
            <label class="radio inline "><input type='radio' class='radio' name='G-{$val[id]}' value='{$vala[id]}' fix_name='{$val[id]}' {if $config[GROUP_MUST]}fix_null='yes'{/if} /> $vala[name]</label>
            {/if}
            
            {if $val['type'] == "checkbox"}
                            <label class="checkbox inline "><input type='checkbox' class='checkbox' name='G-{$val[id]}[]' value='{$vala[id]}' fix_name='{$val[name]}' {if $config[GROUP_MUST]}fix_null='yes'{/if} /> {$vala[name]}</label>
            {/if}
            {if $val['type'] == "select"}
                            <option value='{$vala[id]}'> {$vala[name]}</option>
            {/if}

 
            
            <!--{/if}-->
			<!--{/loop}-->
			{if $val['type'] == "select"}
                </select>
                {/if}
</div>
{/if}
 </div>
  </div>
</div>

          </div>
          
          
          <div class="section-break  ">
              <hr>
              </div>
<!--{/loop}-->                            		
          
          
          
          

          

          <div id="shopping_cart"></div>





        <div class="field submit-field ">
          
          <div class="value">

{if $form['start']<time() && $form['expire']>time() && $form['state']>0 }
            <input id="submita" class="submit" name="commit" value="{lang hejin_forms:tijiao}" type="submit">
        {else}
            <input type='submit' class='submit' value='{if $form['expire']<time()}{lang hejin_forms:formend}{else}{lang hejin_forms:formnost}{/if}' disabled='disabled' />
        {/if}
          </div>
        </div>
    </div>
  </fieldset>
</form>

<!-- 原有的同步验证函数，现已被异步验证替代 -->
<script>
function check(){
    // 此函数已被 hejin-forms-async.js 中的异步验证替代
    // 保留作为备用方案
	
	
	
<!--{loop $grojs $keya $grojsa}-->	

   if($("#G-$grojsa['id']").val() ==  null || $("#G-$grojsa['id']").val() == ''){
	   	error$grojsa['id']="<li>$grojsa['name']{lang hejin_forms:bixutian}</li>";
	$("#divG$grojsa['id']").addClass("field_with_errors");
	$("#ermsg$grojsa['id']").html(error$grojsa['id']);
        e$grojsa['id'] = false;
   }else{
	$("#divG$grojsa['id']").removeClass("field_with_errors");
	   	error$grojsa['id']="";
	$("#ermsg$grojsa['id']").html(error$grojsa['id']);
	    e$grojsa['id'] = true;
	   }
<!--{/loop}-->



	
<!--{loop $groajs $keyb $grojsb}-->	
<!--{if $grojsb['config']['GROUP_VERIFY'] == "number"}-->
if($("#G-$grojsb['id']").val() !=  null && $("#G-$grojsb['id']").val() != ''){

if(!/^[0-9]*$/.test($("#G-$grojsb['id']").val())){
	errora$grojsb['id']="<li>$grojsb['name']{lang hejin_forms:mustnumber}</li>";
	$("#divG$grojsb['id']").addClass("field_with_errors");
	$("#ermsg$grojsb['id']").html(errora$grojsb['id']);
    ea$grojsb['id'] = false;

	}else{
	$("#divG$grojsb['id']").removeClass("field_with_errors");
	   	errora$grojsb['id']="";
	$("#ermsg$grojsb['id']").html(errora$grojsb['id']);
	    ea$grojsb['id'] = true;
	   }
	}else{
		 ea$grojsb['id'] = true;
		}
<!--{/if}-->


<!--{if $grojsb['config']['GROUP_VERIFY'] == "email"}-->
if($("#G-$grojsb['id']").val() !=  null && $("#G-$grojsb['id']").val() != ''){

if(!/^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/.test($("#G-$grojsb['id']").val())){
	errora$grojsb['id']="<li>{lang hejin_forms:mustemail}</li>";
	$("#divG$grojsb['id']").addClass("field_with_errors");
	$("#ermsg$grojsb['id']").html(errora$grojsb['id']);
    ea$grojsb['id'] = false;

	}else{
	$("#divG$grojsb['id']").removeClass("field_with_errors");
	   	errora$grojsb['id']="";
	$("#ermsg$grojsb['id']").html(errora$grojsb['id']);
	    ea$grojsb['id'] = true;
	   }
	}else{
		 ea$grojsb['id'] = true;
		}
<!--{/if}-->



<!--{if $grojsb['config']['GROUP_VERIFY'] == "mobile"}-->
if($("#G-$grojsb['id']").val() !=  null && $("#G-$grojsb['id']").val() != ''){
if(!/^\d{11}$/.test($("#G-$grojsb['id']").val())){
	errora$grojsb['id']="<li>{lang hejin_forms:mustmobile}</li>";
	$("#divG$grojsb['id']").addClass("field_with_errors");
	$("#ermsg$grojsb['id']").html(errora$grojsb['id']);
    ea$grojsb['id'] = false;

	}else{
	$("#divG$grojsb['id']").removeClass("field_with_errors");
	   	errora$grojsb['id']="";
	$("#ermsg$grojsb['id']").html(errora$grojsb['id']);
	    ea$grojsb['id'] = true;
	   }
	}else{
		 ea$grojsb['id'] = true;
		}
<!--{/if}-->

<!--{if $grojsb['config']['GROUP_VERIFY'] == "idcard"}-->
if($("#G-$grojsb['id']").val() !=  null && $("#G-$grojsb['id']").val() != ''){

if(!/^\d{15}$|\d{17}([0-9]|X)$/.test($("#G-$grojsb['id']").val())){
	errora$grojsb['id']="<li>{lang hejin_forms:mustidcard}</li>";
	$("#divG$grojsb['id']").addClass("field_with_errors");
	$("#ermsg$grojsb['id']").html(errora$grojsb['id']);
    ea$grojsb['id'] = false;

	}else{
	$("#divG$grojsb['id']").removeClass("field_with_errors");
	   	errora$grojsb['id']="";
	$("#ermsg$grojsb['id']").html(errora$grojsb['id']);
	    ea$grojsb['id'] = true;
	   }
	}else{
		 ea$grojsb['id'] = true;
		}
<!--{/if}-->




<!--{/loop}-->





if($jstj){
   return true;
   }else{
   return false;
	   }
}
</script>





</body></html>