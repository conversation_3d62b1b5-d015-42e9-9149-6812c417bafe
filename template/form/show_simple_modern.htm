<!--{template header}-->

<!-- 简化版现代化样式 -->
<link href="{HEJIN_PATH}css/hejin-forms-modern.css" rel="stylesheet" type="text/css">
<link href="{HEJIN_PATH}css/jquery.datetimepicker.css" rel="stylesheet" type="text/css">

<script src="{HEJIN_PATH}js/jquery.js" type="text/javascript"></script>
<script src="{HEJIN_PATH}js/jquery.datetimepicker.js" type="text/javascript"></script>
<script src="{HEJIN_PATH}js/hejin-forms-async-compatible.js" type="text/javascript"></script>

<!-- 表单容器 -->
<div class="hf-form-wrapper">
    <!-- 表单标题 -->
    <div class="hf-form-header">
        <h1 class="hf-form-title">{$form['name']}</h1>
        <div class="hf-form-description">{$form['description']}</div>
    </div>

    <!-- 表单主体 -->
    <form action="" class="hf-form-container" id="new_entry" method="post" enctype="multipart/form-data">
        <input type="hidden" name="formhash" value="{FORMHASH}" />
        <input type="hidden" name="fid" value="{$form['id']}" />
        <input type="hidden" name="uid" value="{$_G['uid']}" />
        <input type="hidden" name="account" value="{$_G['username']}" />

        <!-- 表单字段循环 -->
        <!--{loop $groups $key $val}-->
        <!--{eval $config = fix_json($val['config']);}-->
        
        <div class="field">
            <label class="control-label" for="G-{$val['id']}">
                {$val['name']}
                <!--{if $config['GROUP_MUST']}-->
                <span style="color: red;">*</span>
                <!--{/if}-->
            </label>
            
            <!--{if $val['description']}-->
            <div class="help-block">
                <p>{$val['description']}</p>
            </div>
            <!--{/if}-->
            
            <div class="controls">
                <!--{if $val['type'] == "text"}-->
                    <input name='G-{$val[id]}' id="G-{$val[id]}" class="hf-input-base" type='text' value='' 
                           fix_name='{$val[name]}' <!--{if $config[GROUP_MUST]}-->required<!--{/if}--> 
                           placeholder="请输入{$val[name]}" />
                           
                <!--{elseif $val['type'] == "textarea"}-->
                    <textarea name='G-{$val[id]}' id="G-{$val[id]}" class="hf-input-base" 
                              rows='6' fix_name='{$val[name]}' <!--{if $config[GROUP_MUST]}-->required<!--{/if}-->
                              placeholder="请输入{$val[name]}"></textarea>
                              
                <!--{elseif $val['type'] == "file"}-->
                    <input name='G-{$val[id]}' id="G-{$val[id]}" type='file' 
                           fix_name='{$val[name]}' <!--{if $config[GROUP_MUST]}-->required<!--{/if}--> />
                           
                <!--{elseif $val['type'] == "date"}-->
                    <input name='G-{$val[id]}' id="G-{$val[id]}" class="hf-input-base date" type='text' 
                           value='' fix_name='{$val[name]}' <!--{if $config[GROUP_MUST]}-->required<!--{/if}-->
                           placeholder="请选择日期" readonly />
                    <script>
                    $(function(){
                        $("#G-{$val[id]}").datetimepicker({
                            timepicker: false,
                            format: 'Y/m/d',
                            lang: 'ch'
                        });
                    });
                    </script>
                    
                <!--{elseif $val['type'] == "dati"}-->
                    <input name='G-{$val[id]}' id="G-{$val[id]}" class="hf-input-base datetime" type='text' 
                           value='' fix_name='{$val[name]}' <!--{if $config[GROUP_MUST]}-->required<!--{/if}-->
                           placeholder="请选择日期时间" readonly />
                    <script>
                    $(function(){
                        $("#G-{$val[id]}").datetimepicker({
                            format: 'Y/m/d H:i',
                            lang: 'ch'
                        });
                    });
                    </script>
                    
                <!--{elseif $val['type'] == "select"}-->
                    <select id='G-{$val[id]}' name='G-{$val[id]}' class="hf-input-base" 
                            fix_name='{$val[name]}' <!--{if $config[GROUP_MUST]}-->required<!--{/if}-->>
                        <option value="">请选择{$val[name]}</option>
                        <!--{eval $options = Hejin::_option_lists($val[id]);}-->
                        <!--{loop $options $keyi $vala}-->
                        <option value="{$vala['name']}">{$vala['name']}</option>
                        <!--{/loop}-->
                    </select>
                    
                <!--{elseif $val['type'] == "radio"}-->
                    <div class="radio-group">
                        <!--{eval $options = Hejin::_option_lists($val[id]);}-->
                        <!--{loop $options $keyi $vala}-->
                        <label>
                            <input type="radio" name='G-{$val[id]}' value="{$vala['name']}" 
                                   fix_name='{$val[name]}' <!--{if $config[GROUP_MUST]}-->required<!--{/if}--> />
                            {$vala['name']}
                        </label>
                        <!--{/loop}-->
                    </div>
                    
                <!--{elseif $val['type'] == "checkbox"}-->
                    <div class="checkbox-group">
                        <!--{eval $options = Hejin::_option_lists($val[id]);}-->
                        <!--{loop $options $keyi $vala}-->
                        <label>
                            <input type="checkbox" name='G-{$val[id]}[]' value="{$vala['name']}" 
                                   fix_name='{$val[name]}' />
                            {$vala['name']}
                        </label>
                        <!--{/loop}-->
                    </div>
                    
                <!--{/if}-->
            </div>
        </div>
        
        <!--{/loop}-->
        
        <!-- 提交按钮 -->
        <div class="submit-field">
            <input id="submita" class="submit hf-btn hf-btn-primary" name="commit" value="提交表单" type="submit">
        </div>
    </form>
</div>

<!-- 内联样式补充 -->
<style>
/* 基础现代化样式 */
.hf-form-wrapper {
    max-width: 800px;
    margin: 20px auto;
    padding: 0 20px;
}

.hf-form-header {
    text-align: center;
    margin-bottom: 32px;
    padding: 32px;
    background: linear-gradient(135deg, #409EFF, #67C23A);
    border-radius: 8px;
    color: white;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
}

.hf-form-title {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 12px;
}

.hf-form-description {
    font-size: 16px;
    line-height: 1.6;
    opacity: 0.9;
}

.hf-form-container {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.1);
    padding: 32px;
    position: relative;
}

.field {
    margin-bottom: 24px;
    transition: all 0.3s ease;
}

.field:hover {
    transform: translateY(-1px);
}

.control-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: #606266;
    margin-bottom: 8px;
}

.hf-input-base {
    width: 100%;
    padding: 12px 16px;
    font-size: 14px;
    color: #606266;
    background-color: white;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    transition: all 0.3s ease;
    outline: none;
    box-sizing: border-box;
}

.hf-input-base:hover {
    border-color: #909399;
}

.hf-input-base:focus {
    border-color: #409EFF;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

textarea.hf-input-base {
    min-height: 80px;
    resize: vertical;
    line-height: 1.5;
    font-family: inherit;
}

select.hf-input-base {
    height: 40px;
    cursor: pointer;
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
}

.radio-group,
.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 8px;
}

.radio-group label,
.checkbox-group label {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 16px;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    background: white;
    transition: all 0.3s ease;
}

.radio-group label:hover,
.checkbox-group label:hover {
    border-color: #409EFF;
    background: rgba(64, 158, 255, 0.05);
}

.radio-group input,
.checkbox-group input {
    margin-right: 8px;
}

.hf-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 4px;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    outline: none;
}

.hf-btn-primary {
    background: #409EFF;
    color: white;
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
}

.hf-btn-primary:hover {
    background: #337ecc;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
}

.submit-field {
    text-align: center;
    margin-top: 40px;
    padding-top: 24px;
    border-top: 1px solid #EBEEF5;
}

.submit {
    min-width: 120px;
    height: 44px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 6px;
}

.help-block p {
    font-size: 13px;
    color: #909399;
    margin: 0;
    line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hf-form-wrapper {
        padding: 0 10px;
    }
    
    .hf-form-header {
        padding: 24px 16px;
    }
    
    .hf-form-title {
        font-size: 24px;
    }
    
    .hf-form-container {
        padding: 20px;
    }
    
    .radio-group,
    .checkbox-group {
        flex-direction: column;
        gap: 8px;
    }
}

/* 加载动画 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.hf-form-wrapper {
    animation: fadeInUp 0.6s ease-out;
}
</style>

<!--{template footer}-->
