<!DOCTYPE html>
<html>
<head><script type="text/javascript">var NREUMQ=NREUMQ||[];NREUMQ.push(["mark","firstbyte",new Date().getTime()]);</script>
  <title>$form['name']</title>
  <meta name="description" content="<!--{eval echo strip_tags($form['description']);}-->">
  <link href="{HEJIN_PATH}images/published.css" media="screen" rel="stylesheet" type="text/css" />
$temp[$form['skin']]
  <!--[if lte IE 8]>
    <link href="/assets/ie/lte-ie8-76bd85c84b07b7a29c5180344519036c.css" media="screen" rel="stylesheet" type="text/css" />
    <script src="/assets/html5-953594fdd1a0de919fc28151733c47e3.js" type="text/javascript"></script>
  <![endif]-->
  <!--[if lte IE 7]>
    <link href="/assets/ie/lte-ie7-7087708de51776ea27e541ec07a0acd8.css" media="screen" rel="stylesheet" type="text/css" />
  <![endif]-->
  <!--[if IE 6]> <link href="/assets/ie/ie6-873619c2088cc2ede97879b06ff7f350.css" media="screen" rel="stylesheet" type="text/css" /> <![endif]-->
  <script src="{HEJIN_PATH}images/application.js" type="text/javascript"></script>
  <!--[if IE 6]>
    <script src="/assets/published_forms/fix-ie6-b0ecfdc615773cdd73548f95836b86f3.js" type="text/javascript"></script>
  <![endif]-->


  <meta content="authenticity_token" name="csrf-param" />
<meta content="C0hP/cl+58MyRrmunxYKCPC2pnsL5D12wFfpCCAUL50=" name="csrf-token" />
</head>
<body class="entry-container bg-image">
{if $user['username']}
    <header class="center clearfix user-info-container">
      <div class="user-info">
        
        <div class="user-name">
          <div class="name-with-vip-style">
  <span class="vip-user std1_user ">$user['username']</span>
</div>
          
        </div>
      </div>
    </header>
    {/if}
  <div class="submit-modal">
  <div class="align-middle">
    <p class="message">
     {if $form['config']}$form['config']{else} {lang hejin_forms:tijiaocg}{/if}
    </p>


    <p>
      
{lang hejin_forms:ymzdtz} <a id="href" href="{if $form['quote']}$form['quote']{else}$_G['siteurl']plugin.php?id=hejin_forms&formid=$form['id']{/if}">{lang hejin_forms:tiaozhuan}</a> {lang hejin_forms:waittime} <b id="wait">3</b>
<script type="text/javascript">
(function(){
var wait = document.getElementById('wait'),href = document.getElementById('href').href;
var interval = setInterval(function(){
	var time = --wait.innerHTML;
	if(time <= 0) {
		location.href = href;
		clearInterval(interval);
	};
}, 1000);
})();
</script>

      
    </p>
  </div>
</div>

  

  
</body>
</html>
