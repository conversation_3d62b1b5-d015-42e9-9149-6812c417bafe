/**
 * 和金表单现代化交互增强
 * Element UI风格的动画和交互效果
 */

// 现代化表单增强类
function HejinFormsModern() {
    this.init();
}

HejinFormsModern.prototype = {
    
    /**
     * 初始化
     */
    init: function() {
        this.enhanceFormStructure();
        this.initializeAnimations();
        this.bindEvents();
        this.initializeTooltips();
        this.initializeProgressIndicator();
    },
    
    /**
     * 增强表单结构
     */
    enhanceFormStructure: function() {
        var form = document.querySelector('#new_entry');
        if (!form) return;
        
        // 添加表单容器类
        if (!form.classList.contains('hf-form-container')) {
            form.classList.add('hf-form-container');
        }
        
        // 增强输入框
        this.enhanceInputs();
        
        // 增强文件上传
        this.enhanceFileUploads();
        
        // 增强单选框和复选框
        this.enhanceRadioCheckbox();
        
        // 增强按钮
        this.enhanceButtons();
        
        // 添加必填标识
        this.addRequiredIndicators();
    },
    
    /**
     * 增强输入框
     */
    enhanceInputs: function() {
        var inputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="tel"], input[type="password"], input[type="number"], input[type="url"], textarea, select');
        
        for (var i = 0; i < inputs.length; i++) {
            var input = inputs[i];
            input.classList.add('hf-input-base');
            
            // 添加浮动标签效果
            this.addFloatingLabel(input);
            
            // 添加输入动画
            this.addInputAnimation(input);
        }
    },
    
    /**
     * 添加浮动标签效果
     */
    addFloatingLabel: function(input) {
        var field = input.closest('.field');
        if (!field) return;
        
        var label = field.querySelector('.control-label');
        if (!label) return;
        
        // 检查输入框是否有值
        var checkValue = function() {
            if (input.value.trim() !== '') {
                label.classList.add('hf-label-float');
            } else {
                label.classList.remove('hf-label-float');
            }
        };
        
        input.addEventListener('focus', function() {
            label.classList.add('hf-label-focus');
        });
        
        input.addEventListener('blur', function() {
            label.classList.remove('hf-label-focus');
            checkValue();
        });
        
        input.addEventListener('input', checkValue);
        
        // 初始检查
        checkValue();
    },
    
    /**
     * 添加输入动画
     */
    addInputAnimation: function(input) {
        input.addEventListener('focus', function() {
            this.style.transform = 'translateY(-1px)';
        });
        
        input.addEventListener('blur', function() {
            this.style.transform = 'translateY(0)';
        });
    },
    
    /**
     * 增强文件上传
     */
    enhanceFileUploads: function() {
        var fileInputs = document.querySelectorAll('input[type="file"]');
        
        for (var i = 0; i < fileInputs.length; i++) {
            this.createModernFileUpload(fileInputs[i]);
        }
    },
    
    /**
     * 创建现代化文件上传
     */
    createModernFileUpload: function(fileInput) {
        var wrapper = document.createElement('div');
        wrapper.className = 'hf-file-upload';
        
        var trigger = document.createElement('div');
        trigger.className = 'hf-file-trigger';
        trigger.innerHTML = 
            '<div class="hf-file-icon">📁</div>' +
            '<div class="hf-file-text">点击选择文件或拖拽到此处</div>' +
            '<div class="hf-file-hint">支持 jpg, png, gif, doc, pdf 等格式</div>';
        
        fileInput.parentNode.insertBefore(wrapper, fileInput);
        wrapper.appendChild(fileInput);
        wrapper.appendChild(trigger);
        
        fileInput.classList.add('hf-file-input');
        
        // 绑定事件
        this.bindFileUploadEvents(wrapper, fileInput, trigger);
    },
    
    /**
     * 绑定文件上传事件
     */
    bindFileUploadEvents: function(wrapper, fileInput, trigger) {
        var self = this;
        
        // 点击事件
        trigger.addEventListener('click', function() {
            fileInput.click();
        });
        
        // 拖拽事件
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(function(eventName) {
            trigger.addEventListener(eventName, function(e) {
                e.preventDefault();
                e.stopPropagation();
            });
        });
        
        ['dragenter', 'dragover'].forEach(function(eventName) {
            trigger.addEventListener(eventName, function() {
                trigger.classList.add('hf-drag-over');
            });
        });
        
        ['dragleave', 'drop'].forEach(function(eventName) {
            trigger.addEventListener(eventName, function() {
                trigger.classList.remove('hf-drag-over');
            });
        });
        
        // 文件选择事件
        fileInput.addEventListener('change', function() {
            self.updateFileUploadDisplay(this, trigger);
        });
        
        // 拖拽放置事件
        trigger.addEventListener('drop', function(e) {
            var files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                self.updateFileUploadDisplay(fileInput, trigger);
            }
        });
    },
    
    /**
     * 更新文件上传显示
     */
    updateFileUploadDisplay: function(fileInput, trigger) {
        if (fileInput.files.length > 0) {
            var file = fileInput.files[0];
            trigger.innerHTML = 
                '<div class="hf-file-icon">✓</div>' +
                '<div class="hf-file-text">已选择: ' + file.name + '</div>' +
                '<div class="hf-file-hint">大小: ' + this.formatFileSize(file.size) + '</div>';
            trigger.classList.add('hf-file-selected');
        }
    },
    
    /**
     * 格式化文件大小
     */
    formatFileSize: function(bytes) {
        if (bytes === 0) return '0 Bytes';
        var k = 1024;
        var sizes = ['Bytes', 'KB', 'MB', 'GB'];
        var i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    
    /**
     * 增强单选框和复选框
     */
    enhanceRadioCheckbox: function() {
        var radioGroups = document.querySelectorAll('.radio-group');
        var checkboxGroups = document.querySelectorAll('.checkbox-group');
        
        // 处理单选框组
        for (var i = 0; i < radioGroups.length; i++) {
            this.enhanceRadioGroup(radioGroups[i]);
        }
        
        // 处理复选框组
        for (var i = 0; i < checkboxGroups.length; i++) {
            this.enhanceCheckboxGroup(checkboxGroups[i]);
        }
    },
    
    /**
     * 增强单选框组
     */
    enhanceRadioGroup: function(group) {
        var radios = group.querySelectorAll('input[type="radio"]');
        
        for (var i = 0; i < radios.length; i++) {
            this.wrapRadioCheckbox(radios[i], 'hf-radio');
        }
    },
    
    /**
     * 增强复选框组
     */
    enhanceCheckboxGroup: function(group) {
        var checkboxes = group.querySelectorAll('input[type="checkbox"]');
        
        for (var i = 0; i < checkboxes.length; i++) {
            this.wrapRadioCheckbox(checkboxes[i], 'hf-checkbox');
        }
    },
    
    /**
     * 包装单选框/复选框
     */
    wrapRadioCheckbox: function(input, className) {
        var label = input.closest('label') || input.nextElementSibling;
        if (!label) return;
        
        var wrapper = document.createElement('div');
        wrapper.className = className;
        
        input.parentNode.insertBefore(wrapper, input);
        wrapper.appendChild(input);
        wrapper.appendChild(label);
        
        // 绑定事件
        var self = this;
        input.addEventListener('change', function() {
            self.updateRadioCheckboxState(this, wrapper);
        });
        
        // 初始状态
        this.updateRadioCheckboxState(input, wrapper);
    },
    
    /**
     * 更新单选框/复选框状态
     */
    updateRadioCheckboxState: function(input, wrapper) {
        if (input.checked) {
            wrapper.classList.add('checked');
        } else {
            wrapper.classList.remove('checked');
        }
    },
    
    /**
     * 增强按钮
     */
    enhanceButtons: function() {
        var buttons = document.querySelectorAll('input[type="submit"], button, .btn');
        
        for (var i = 0; i < buttons.length; i++) {
            var button = buttons[i];
            
            if (!button.classList.contains('hf-btn')) {
                button.classList.add('hf-btn');
            }
            
            // 添加默认样式
            if (!button.classList.contains('hf-btn-primary') && 
                !button.classList.contains('hf-btn-success') && 
                !button.classList.contains('hf-btn-danger')) {
                button.classList.add('hf-btn-primary');
            }
            
            // 添加波纹效果
            this.addRippleEffect(button);
        }
    },
    
    /**
     * 添加波纹效果
     */
    addRippleEffect: function(button) {
        button.addEventListener('click', function(e) {
            var ripple = document.createElement('span');
            ripple.className = 'hf-ripple';
            
            var rect = this.getBoundingClientRect();
            var size = Math.max(rect.width, rect.height);
            var x = e.clientX - rect.left - size / 2;
            var y = e.clientY - rect.top - size / 2;
            
            ripple.style.cssText = 
                'position: absolute; border-radius: 50%; background: rgba(255,255,255,0.6); ' +
                'width: ' + size + 'px; height: ' + size + 'px; ' +
                'left: ' + x + 'px; top: ' + y + 'px; ' +
                'animation: ripple 0.6s ease-out; pointer-events: none;';
            
            this.appendChild(ripple);
            
            setTimeout(function() {
                ripple.remove();
            }, 600);
        });
    },
    
    /**
     * 添加必填标识
     */
    addRequiredIndicators: function() {
        var requiredInputs = document.querySelectorAll('input[required], textarea[required], select[required]');
        
        for (var i = 0; i < requiredInputs.length; i++) {
            var input = requiredInputs[i];
            var field = input.closest('.field');
            if (!field) continue;
            
            var label = field.querySelector('.control-label');
            if (!label) continue;
            
            if (!label.querySelector('.required')) {
                var required = document.createElement('span');
                required.className = 'required';
                required.textContent = '*';
                label.appendChild(required);
            }
        }
    },
    
    /**
     * 初始化动画
     */
    initializeAnimations: function() {
        // 添加CSS动画样式
        if (!document.querySelector('#hf-animations')) {
            var style = document.createElement('style');
            style.id = 'hf-animations';
            style.textContent = 
                '@keyframes ripple { 0% { transform: scale(0); opacity: 1; } 100% { transform: scale(4); opacity: 0; } }' +
                '.hf-label-focus { color: var(--primary-color) !important; transform: translateY(-2px); }' +
                '.hf-label-float { font-size: 12px; transform: translateY(-20px); }' +
                '.hf-ripple { z-index: 1; }';
            document.head.appendChild(style);
        }
        
        // 页面加载动画
        this.animateFormEntrance();
    },
    
    /**
     * 表单入场动画
     */
    animateFormEntrance: function() {
        var form = document.querySelector('.hf-form-container');
        if (!form) return;
        
        form.style.opacity = '0';
        form.style.transform = 'translateY(30px)';
        
        setTimeout(function() {
            form.style.transition = 'all 0.6s cubic-bezier(0.645, 0.045, 0.355, 1)';
            form.style.opacity = '1';
            form.style.transform = 'translateY(0)';
        }, 100);
        
        // 字段逐个显示
        var fields = form.querySelectorAll('.field');
        for (var i = 0; i < fields.length; i++) {
            (function(field, index) {
                field.style.opacity = '0';
                field.style.transform = 'translateX(-20px)';
                
                setTimeout(function() {
                    field.style.transition = 'all 0.4s cubic-bezier(0.645, 0.045, 0.355, 1)';
                    field.style.opacity = '1';
                    field.style.transform = 'translateX(0)';
                }, 200 + index * 100);
            })(fields[i], i);
        }
    },
    
    /**
     * 绑定事件
     */
    bindEvents: function() {
        var self = this;
        
        // 表单提交动画
        var form = document.querySelector('#new_entry');
        if (form) {
            form.addEventListener('submit', function() {
                self.animateFormSubmit();
            });
        }
        
        // 滚动时的视差效果
        window.addEventListener('scroll', function() {
            self.handleScrollEffects();
        });
    },
    
    /**
     * 表单提交动画
     */
    animateFormSubmit: function() {
        var submitBtn = document.querySelector('.submit');
        if (submitBtn) {
            submitBtn.style.transform = 'scale(0.95)';
            setTimeout(function() {
                submitBtn.style.transform = 'scale(1)';
            }, 150);
        }
    },
    
    /**
     * 处理滚动效果
     */
    handleScrollEffects: function() {
        var fields = document.querySelectorAll('.field');
        var scrollTop = window.pageYOffset;
        var windowHeight = window.innerHeight;
        
        for (var i = 0; i < fields.length; i++) {
            var field = fields[i];
            var rect = field.getBoundingClientRect();
            
            if (rect.top < windowHeight && rect.bottom > 0) {
                field.classList.add('hf-in-view');
            }
        }
    },
    
    /**
     * 初始化工具提示
     */
    initializeTooltips: function() {
        // 为帮助文本添加工具提示效果
        var helpBlocks = document.querySelectorAll('.help-block');
        
        for (var i = 0; i < helpBlocks.length; i++) {
            var helpBlock = helpBlocks[i];
            helpBlock.style.cursor = 'help';
            
            helpBlock.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-1px)';
                this.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
            });
            
            helpBlock.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            });
        }
    },
    
    /**
     * 初始化进度指示器
     */
    initializeProgressIndicator: function() {
        var form = document.querySelector('#new_entry');
        if (!form) return;
        
        var fields = form.querySelectorAll('.field input[required], .field textarea[required], .field select[required]');
        if (fields.length === 0) return;
        
        // 创建进度指示器
        var progressContainer = document.createElement('div');
        progressContainer.className = 'hf-progress-indicator';
        progressContainer.innerHTML = 
            '<div class="hf-progress-bar-container">' +
                '<div class="hf-progress-bar-fill"></div>' +
            '</div>' +
            '<div class="hf-progress-text">完成度: 0%</div>';
        
        form.insertBefore(progressContainer, form.firstChild);
        
        var progressBar = progressContainer.querySelector('.hf-progress-bar-fill');
        var progressText = progressContainer.querySelector('.hf-progress-text');
        
        // 更新进度
        var updateProgress = function() {
            var completed = 0;
            for (var i = 0; i < fields.length; i++) {
                if (fields[i].value.trim() !== '') {
                    completed++;
                }
            }
            
            var percentage = Math.round((completed / fields.length) * 100);
            progressBar.style.width = percentage + '%';
            progressText.textContent = '完成度: ' + percentage + '%';
        };
        
        // 绑定输入事件
        for (var i = 0; i < fields.length; i++) {
            fields[i].addEventListener('input', updateProgress);
            fields[i].addEventListener('change', updateProgress);
        }
        
        // 初始更新
        updateProgress();
    }
};

// 自动初始化
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('#new_entry')) {
        window.hejinFormsModern = new HejinFormsModern();
    }
});
