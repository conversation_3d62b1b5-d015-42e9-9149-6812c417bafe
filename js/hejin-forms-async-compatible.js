/**
 * 和金表单异步处理类 - 兼容版本
 * 使用ES5语法确保更好的浏览器兼容性
 */
function HejinFormsAsync(options) {
    // 默认配置
    this.options = {
        formSelector: '#new_entry',
        apiUrl: 'plugin.php?id=hejin_forms',
        loadingClass: 'hf-loading',
        errorClass: 'field_with_errors',
        successClass: 'hf-success'
    };
    
    // 合并用户配置
    if (options) {
        for (var key in options) {
            if (options.hasOwnProperty(key)) {
                this.options[key] = options[key];
            }
        }
    }
    
    this.form = document.querySelector(this.options.formSelector);
    this.isSubmitting = false;
    this.validationCache = {};
    this.draftTimer = null;
    this.lastDraftSave = 0;
    
    this.init();
}

/**
 * 初始化
 */
HejinFormsAsync.prototype.init = function() {
    if (!this.form) {
        console.error('表单元素未找到');
        return;
    }
    
    this.bindEvents();
    this.initializeFields();
    this.createLoadingOverlay();
    this.initializeDraftSaving();
    this.initializeKeyboardShortcuts();
    this.loadDraft();
};

/**
 * 绑定事件
 */
HejinFormsAsync.prototype.bindEvents = function() {
    var self = this;
    
    // 表单提交事件
    this.form.addEventListener('submit', function(e) {
        e.preventDefault();
        self.handleFormSubmit();
    });
    
    // 实时验证事件（防抖处理）
    this.form.addEventListener('input', this.debounce(function(e) {
        if (e.target.matches && e.target.matches('input, textarea, select')) {
            self.validateField(e.target);
        } else if (e.target.tagName && 
                  (e.target.tagName.toLowerCase() === 'input' || 
                   e.target.tagName.toLowerCase() === 'textarea' || 
                   e.target.tagName.toLowerCase() === 'select')) {
            self.validateField(e.target);
        }
    }, 300));
    
    // 文件上传事件
    this.form.addEventListener('change', function(e) {
        if (e.target.type === 'file') {
            self.handleFileUpload(e.target);
        }
    });
    
    // 失焦验证
    this.form.addEventListener('blur', function(e) {
        if (e.target.matches && e.target.matches('input, textarea, select')) {
            self.validateField(e.target);
        } else if (e.target.tagName && 
                  (e.target.tagName.toLowerCase() === 'input' || 
                   e.target.tagName.toLowerCase() === 'textarea' || 
                   e.target.tagName.toLowerCase() === 'select')) {
            self.validateField(e.target);
        }
    }, true);
};

/**
 * 初始化字段
 */
HejinFormsAsync.prototype.initializeFields = function() {
    var fields = this.form.querySelectorAll('input, textarea, select');
    for (var i = 0; i < fields.length; i++) {
        var field = fields[i];
        
        // 添加必填标识
        if (field.hasAttribute('fix_null') && field.getAttribute('fix_null') === 'yes') {
            field.setAttribute('required', 'required');
        }
        
        // 添加验证属性
        var fieldName = field.getAttribute('fix_name');
        if (fieldName) {
            field.setAttribute('data-field-name', fieldName);
        }
        
        // 初始化文件上传字段
        if (field.type === 'file') {
            this.initializeFileField(field);
        }
    }
};

/**
 * 创建加载遮罩
 */
HejinFormsAsync.prototype.createLoadingOverlay = function() {
    var overlay = document.createElement('div');
    overlay.className = 'hf-loading-overlay';
    overlay.innerHTML = 
        '<div class="hf-loading-spinner">' +
            '<div class="hf-spinner"></div>' +
            '<div class="hf-loading-text">正在处理...</div>' +
        '</div>';
    overlay.style.display = 'none';
    document.body.appendChild(overlay);
    this.loadingOverlay = overlay;
};

/**
 * 显示/隐藏加载状态
 */
HejinFormsAsync.prototype.toggleLoading = function(show) {
    if (show === undefined) show = true;
    
    if (this.loadingOverlay) {
        this.loadingOverlay.style.display = show ? 'flex' : 'none';
    }
    
    var submitBtn = this.form.querySelector('input[type="submit"]');
    if (submitBtn) {
        submitBtn.disabled = show;
        submitBtn.value = show ? '提交中...' : '提交';
    }
};

/**
 * 防抖函数
 */
HejinFormsAsync.prototype.debounce = function(func, wait) {
    var timeout;
    return function() {
        var context = this;
        var args = arguments;
        var later = function() {
            timeout = null;
            func.apply(context, args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
};

/**
 * 验证单个字段
 */
HejinFormsAsync.prototype.validateField = function(field) {
    var self = this;
    var fieldId = this.getFieldId(field);
    var value = this.getFieldValue(field);
    var formId = this.getFormId();
    
    if (!fieldId || !formId) return;
    
    // 检查缓存
    var cacheKey = fieldId + '_' + value;
    if (this.validationCache[cacheKey]) {
        var result = this.validationCache[cacheKey];
        this.showFieldValidation(field, result.success, result.message);
        return;
    }
    
    // 显示加载状态
    this.showFieldLoading(field, true);
    
    // 发送验证请求
    this.makeRequest('POST', {
        action: 'validate_field',
        field_id: fieldId,
        value: value,
        form_id: formId
    }).then(function(response) {
        // 缓存结果
        self.validationCache[cacheKey] = response;
        self.showFieldValidation(field, response.success, response.message);
    }).catch(function(error) {
        console.error('字段验证失败:', error);
        self.showFieldValidation(field, false, '验证失败');
    }).finally(function() {
        // 隐藏加载状态
        self.showFieldLoading(field, false);
    });
};

/**
 * 显示字段验证结果
 */
HejinFormsAsync.prototype.showFieldValidation = function(field, success, message) {
    var fieldContainer = this.getFieldContainer(field);
    var errorElement = fieldContainer.querySelector('.hf-field-error') || 
                      this.createErrorElement(fieldContainer);
    
    // 移除之前的状态
    this.removeClass(fieldContainer, this.options.errorClass);
    this.removeClass(fieldContainer, this.options.successClass);
    this.removeClass(fieldContainer, 'hf-loading');
    
    if (success) {
        this.addClass(fieldContainer, this.options.successClass);
        errorElement.textContent = '';
        errorElement.style.display = 'none';
        this.addSuccessAnimation(fieldContainer);
    } else {
        this.addClass(fieldContainer, this.options.errorClass);
        errorElement.textContent = message;
        errorElement.style.display = 'block';
        this.addErrorAnimation(fieldContainer);
    }
};

/**
 * 获取字段容器
 */
HejinFormsAsync.prototype.getFieldContainer = function(field) {
    var container = field;
    while (container && container.parentElement) {
        if (this.hasClass(container, 'field')) {
            return container;
        }
        container = container.parentElement;
    }
    return field.parentElement || field;
};

/**
 * 创建错误提示元素
 */
HejinFormsAsync.prototype.createErrorElement = function(container) {
    var errorElement = document.createElement('div');
    errorElement.className = 'hf-field-error';
    errorElement.style.cssText = 'color: #e74c3c; font-size: 12px; margin-top: 5px;';
    container.appendChild(errorElement);
    return errorElement;
};

/**
 * 处理表单提交
 */
HejinFormsAsync.prototype.handleFormSubmit = function() {
    var self = this;
    
    if (this.isSubmitting) return;
    
    this.isSubmitting = true;
    this.toggleLoading(true);
    
    // 验证所有必填字段
    var isValid = this.validateAllFields();
    if (!isValid) {
        this.showMessage('请检查表单中的错误', 'error');
        this.isSubmitting = false;
        this.toggleLoading(false);
        return;
    }
    
    // 提交表单
    var formData = new FormData(this.form);
    formData.append('action', 'submit_form');
    
    this.makeRequest('POST', formData).then(function(response) {
        if (response.success) {
            self.showSuccessPage();
        } else {
            self.showMessage(response.message || '提交失败', 'error');
            if (response.data && response.data.errors) {
                self.showFieldErrors(response.data.errors);
            }
        }
    }).catch(function(error) {
        console.error('表单提交失败:', error);
        self.showMessage('网络错误，请稍后重试', 'error');
    }).finally(function() {
        self.isSubmitting = false;
        self.toggleLoading(false);
    });
};

/**
 * 发送请求
 */
HejinFormsAsync.prototype.makeRequest = function(method, data, onProgress) {
    var self = this;
    var url = this.options.apiUrl;
    
    return new Promise(function(resolve, reject) {
        var xhr = new XMLHttpRequest();
        
        xhr.open(method, url);
        xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
        
        if (!(data instanceof FormData)) {
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            var params = [];
            for (var key in data) {
                if (data.hasOwnProperty(key)) {
                    params.push(encodeURIComponent(key) + '=' + encodeURIComponent(data[key]));
                }
            }
            data = params.join('&');
        }
        
        // 上传进度
        if (onProgress && xhr.upload) {
            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    var progress = (e.loaded / e.total) * 100;
                    onProgress(progress);
                }
            });
        }
        
        xhr.onload = function() {
            try {
                var response = JSON.parse(xhr.responseText);
                resolve(response);
            } catch (error) {
                reject(new Error('响应解析失败'));
            }
        };
        
        xhr.onerror = function() {
            reject(new Error('网络错误'));
        };
        
        xhr.ontimeout = function() {
            reject(new Error('请求超时'));
        };
        
        xhr.timeout = 30000; // 30秒超时
        xhr.send(data);
    });
};

/**
 * 工具函数
 */
HejinFormsAsync.prototype.addClass = function(element, className) {
    if (element.classList) {
        element.classList.add(className);
    } else {
        var classes = element.className.split(' ');
        if (classes.indexOf(className) === -1) {
            classes.push(className);
            element.className = classes.join(' ');
        }
    }
};

HejinFormsAsync.prototype.removeClass = function(element, className) {
    if (element.classList) {
        element.classList.remove(className);
    } else {
        var classes = element.className.split(' ');
        var index = classes.indexOf(className);
        if (index !== -1) {
            classes.splice(index, 1);
            element.className = classes.join(' ');
        }
    }
};

HejinFormsAsync.prototype.hasClass = function(element, className) {
    if (element.classList) {
        return element.classList.contains(className);
    } else {
        return element.className.split(' ').indexOf(className) !== -1;
    }
};

HejinFormsAsync.prototype.getFieldId = function(field) {
    var name = field.name;
    if (name && name.indexOf('G-') === 0) {
        return name.substring(2);
    }
    return null;
};

HejinFormsAsync.prototype.getFieldValue = function(field) {
    if (field.type === 'checkbox') {
        var checkboxes = this.form.querySelectorAll('input[name="' + field.name + '"]:checked');
        var values = [];
        for (var i = 0; i < checkboxes.length; i++) {
            values.push(checkboxes[i].value);
        }
        return values;
    }
    return field.value;
};

HejinFormsAsync.prototype.getFormId = function() {
    var fidInput = this.form.querySelector('input[name="fid"]');
    return fidInput ? fidInput.value : null;
};

// 简化版本的其他方法
HejinFormsAsync.prototype.showFieldLoading = function(field, loading) {
    var fieldContainer = this.getFieldContainer(field);
    if (loading) {
        this.addClass(fieldContainer, 'hf-loading');
    } else {
        this.removeClass(fieldContainer, 'hf-loading');
    }
};

HejinFormsAsync.prototype.addSuccessAnimation = function(element) {
    element.style.animation = 'hf-success-pulse 0.6s ease-out';
    setTimeout(function() {
        element.style.animation = '';
    }, 600);
};

HejinFormsAsync.prototype.addErrorAnimation = function(element) {
    element.style.animation = 'hf-error-shake 0.5s ease-out';
    setTimeout(function() {
        element.style.animation = '';
    }, 500);
};

HejinFormsAsync.prototype.validateAllFields = function() {
    var fields = this.form.querySelectorAll('input[required], textarea[required], select[required]');
    var isValid = true;
    
    for (var i = 0; i < fields.length; i++) {
        var field = fields[i];
        if (!field.value.trim()) {
            this.showFieldValidation(field, false, '此字段为必填项');
            isValid = false;
        }
    }
    
    return isValid;
};

HejinFormsAsync.prototype.showMessage = function(message, type) {
    var messageEl = document.createElement('div');
    messageEl.className = 'hf-message hf-message-' + (type || 'info');
    messageEl.textContent = message;
    
    this.form.insertBefore(messageEl, this.form.firstChild);
    
    setTimeout(function() {
        if (messageEl.parentNode) {
            messageEl.parentNode.removeChild(messageEl);
        }
    }, 5000);
};

HejinFormsAsync.prototype.showSuccessPage = function() {
    this.clearDraft();
    
    this.form.innerHTML = 
        '<div class="hf-success-page">' +
            '<div class="hf-success-icon">✓</div>' +
            '<h2>提交成功</h2>' +
            '<p>您的表单已成功提交，感谢您的参与！</p>' +
            '<button onclick="location.reload()" class="hf-btn">返回</button>' +
        '</div>';
};

// 简化的草稿功能
HejinFormsAsync.prototype.initializeDraftSaving = function() {
    // 简化版本，仅实现基本功能
};

HejinFormsAsync.prototype.saveDraft = function() {
    // 简化版本
    console.log('草稿保存功能（简化版本）');
};

HejinFormsAsync.prototype.loadDraft = function() {
    // 简化版本
};

HejinFormsAsync.prototype.clearDraft = function() {
    // 简化版本
};

HejinFormsAsync.prototype.initializeKeyboardShortcuts = function() {
    var self = this;
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey && e.key === 'Enter') {
            e.preventDefault();
            self.handleFormSubmit();
        }
    });
};

HejinFormsAsync.prototype.initializeFileField = function(fileInput) {
    // 简化版本，保持原有功能
};

HejinFormsAsync.prototype.handleFileUpload = function(fileInput) {
    // 简化版本，保持基本上传功能
    console.log('文件上传功能（简化版本）');
};

HejinFormsAsync.prototype.showFieldErrors = function(errors) {
    // 简化版本
    console.log('显示字段错误:', errors);
};

// 自动初始化
document.addEventListener('DOMContentLoaded', function() {
    if (document.querySelector('#new_entry')) {
        window.hejinForms = new HejinFormsAsync();
    }
});
