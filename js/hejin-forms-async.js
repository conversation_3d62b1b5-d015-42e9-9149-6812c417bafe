/**
 * 和金表单异步处理类
 * 使用现代JavaScript实现异步表单提交和实时验证
 */
class HejinFormsAsync {
    constructor(options = {}) {
        this.options = {
            formSelector: '#new_entry',
            apiUrl: 'plugin.php?id=hejin_forms',
            loadingClass: 'hf-loading',
            errorClass: 'field_with_errors',
            successClass: 'hf-success',
            ...options
        };
        
        this.form = document.querySelector(this.options.formSelector);
        this.isSubmitting = false;
        this.validationCache = new Map();
        this.draftTimer = null;
        this.lastDraftSave = 0;

        this.init();
    }
    
    /**
     * 初始化
     */
    init() {
        if (!this.form) {
            console.error('表单元素未找到');
            return;
        }
        
        this.bindEvents();
        this.initializeFields();
        this.createLoadingOverlay();
        this.initializeDraftSaving();
        this.initializeKeyboardShortcuts();
        this.initializeTooltips();
        this.loadDraft();
    }
    
    /**
     * 绑定事件
     */
    bindEvents() {
        // 表单提交事件
        this.form.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleFormSubmit();
        });
        
        // 实时验证事件
        this.form.addEventListener('input', this.debounce((e) => {
            if (e.target.matches('input, textarea, select')) {
                this.validateField(e.target);
            }
        }, 300));
        
        // 文件上传事件
        this.form.addEventListener('change', (e) => {
            if (e.target.type === 'file') {
                this.handleFileUpload(e.target);
            }
        });
        
        // 失焦验证
        this.form.addEventListener('blur', (e) => {
            if (e.target.matches('input, textarea, select')) {
                this.validateField(e.target);
            }
        }, true);
    }
    
    /**
     * 初始化字段
     */
    initializeFields() {
        const fields = this.form.querySelectorAll('input, textarea, select');
        fields.forEach(field => {
            // 添加必填标识
            if (field.hasAttribute('fix_null') && field.getAttribute('fix_null') === 'yes') {
                field.setAttribute('required', 'required');
            }

            // 添加验证属性
            const fieldName = field.getAttribute('fix_name');
            if (fieldName) {
                field.setAttribute('data-field-name', fieldName);
            }

            // 初始化文件上传字段
            if (field.type === 'file') {
                this.initializeFileField(field);
            }
        });
    }

    /**
     * 初始化文件上传字段
     */
    initializeFileField(fileInput) {
        // 创建拖拽上传区域
        const dropZone = this.createDropZone(fileInput);
        fileInput.parentElement.insertBefore(dropZone, fileInput);
        fileInput.style.display = 'none';

        // 绑定拖拽事件
        this.bindDragEvents(dropZone, fileInput);

        // 添加多文件支持（如果需要）
        if (fileInput.hasAttribute('data-multiple')) {
            fileInput.setAttribute('multiple', 'multiple');
        }
    }

    /**
     * 创建拖拽上传区域
     */
    createDropZone(fileInput) {
        const dropZone = document.createElement('div');
        dropZone.className = 'hf-drop-zone';
        dropZone.innerHTML = `
            <div class="hf-drop-content">
                <div class="hf-drop-icon">📁</div>
                <div class="hf-drop-text">
                    <p>拖拽文件到此处或 <span class="hf-browse-link">点击选择文件</span></p>
                    <p class="hf-drop-hint">支持的文件类型：jpg, jpeg, gif, png, doc, docx, xls, xlsx, wps</p>
                </div>
            </div>
            <div class="hf-file-list"></div>
        `;

        // 点击选择文件
        const browseLink = dropZone.querySelector('.hf-browse-link');
        browseLink.addEventListener('click', () => {
            fileInput.click();
        });

        return dropZone;
    }

    /**
     * 绑定拖拽事件
     */
    bindDragEvents(dropZone, fileInput) {
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, (e) => {
                e.preventDefault();
                e.stopPropagation();
            });
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, () => {
                dropZone.classList.add('hf-drag-over');
            });
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, () => {
                dropZone.classList.remove('hf-drag-over');
            });
        });

        dropZone.addEventListener('drop', (e) => {
            const files = e.dataTransfer.files;
            this.handleDroppedFiles(files, fileInput, dropZone);
        });
    }
    
    /**
     * 创建加载遮罩
     */
    createLoadingOverlay() {
        const overlay = document.createElement('div');
        overlay.className = 'hf-loading-overlay';
        overlay.innerHTML = `
            <div class="hf-loading-spinner">
                <div class="hf-spinner"></div>
                <div class="hf-loading-text">正在处理...</div>
            </div>
        `;
        overlay.style.display = 'none';
        document.body.appendChild(overlay);
        this.loadingOverlay = overlay;
    }
    
    /**
     * 显示/隐藏加载状态
     */
    toggleLoading(show = true) {
        if (this.loadingOverlay) {
            this.loadingOverlay.style.display = show ? 'flex' : 'none';
        }
        
        const submitBtn = this.form.querySelector('input[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = show;
            submitBtn.value = show ? '提交中...' : '提交';
        }
    }
    
    /**
     * 防抖函数
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
    
    /**
     * 验证单个字段
     */
    async validateField(field) {
        const fieldId = this.getFieldId(field);
        const value = this.getFieldValue(field);
        const formId = this.getFormId();

        if (!fieldId || !formId) return;

        // 检查缓存
        const cacheKey = `${fieldId}_${value}`;
        if (this.validationCache.has(cacheKey)) {
            const result = this.validationCache.get(cacheKey);
            this.showFieldValidation(field, result.success, result.message);
            return result;
        }

        // 显示加载状态
        this.showFieldLoading(field, true);

        try {
            const response = await this.makeRequest('POST', {
                action: 'validate_field',
                field_id: fieldId,
                value: value,
                form_id: formId
            });

            // 缓存结果
            this.validationCache.set(cacheKey, response);

            this.showFieldValidation(field, response.success, response.message);
            return response;
        } catch (error) {
            console.error('字段验证失败:', error);
            this.showFieldValidation(field, false, '验证失败');
            return { success: false, message: '验证失败' };
        } finally {
            // 隐藏加载状态
            this.showFieldLoading(field, false);
        }
    }
    
    /**
     * 显示字段验证结果
     */
    showFieldValidation(field, success, message) {
        const fieldContainer = field.closest('.field') || field.parentElement;
        const errorElement = fieldContainer.querySelector('.hf-field-error') ||
                           this.createErrorElement(fieldContainer);

        // 移除之前的状态
        fieldContainer.classList.remove(this.options.errorClass, this.options.successClass, 'hf-loading');

        if (success) {
            fieldContainer.classList.add(this.options.successClass);
            errorElement.textContent = '';
            errorElement.style.display = 'none';

            // 添加成功动画
            this.addSuccessAnimation(fieldContainer);
        } else {
            fieldContainer.classList.add(this.options.errorClass);
            errorElement.textContent = message;
            errorElement.style.display = 'block';

            // 添加错误动画
            this.addErrorAnimation(fieldContainer);
        }
    }

    /**
     * 添加成功动画
     */
    addSuccessAnimation(element) {
        element.style.animation = 'hf-success-pulse 0.6s ease-out';
        setTimeout(() => {
            element.style.animation = '';
        }, 600);
    }

    /**
     * 添加错误动画
     */
    addErrorAnimation(element) {
        element.style.animation = 'hf-error-shake 0.5s ease-out';
        setTimeout(() => {
            element.style.animation = '';
        }, 500);
    }

    /**
     * 显示字段加载状态
     */
    showFieldLoading(field, loading = true) {
        const fieldContainer = field.closest('.field') || field.parentElement;

        if (loading) {
            fieldContainer.classList.add('hf-loading');
        } else {
            fieldContainer.classList.remove('hf-loading');
        }
    }
    
    /**
     * 创建错误提示元素
     */
    createErrorElement(container) {
        const errorElement = document.createElement('div');
        errorElement.className = 'hf-field-error';
        errorElement.style.cssText = 'color: #e74c3c; font-size: 12px; margin-top: 5px;';
        container.appendChild(errorElement);
        return errorElement;
    }
    
    /**
     * 处理表单提交
     */
    async handleFormSubmit() {
        if (this.isSubmitting) return;
        
        this.isSubmitting = true;
        this.toggleLoading(true);
        
        try {
            // 先验证所有字段
            const isValid = await this.validateAllFields();
            if (!isValid) {
                this.showMessage('请检查表单中的错误', 'error');
                return;
            }
            
            // 提交表单
            const formData = new FormData(this.form);
            formData.append('action', 'submit_form');
            
            const response = await this.makeRequest('POST', formData);
            
            if (response.success) {
                this.showSuccessPage();
            } else {
                this.showMessage(response.message || '提交失败', 'error');
                
                // 显示字段级错误
                if (response.data && response.data.errors) {
                    this.showFieldErrors(response.data.errors);
                }
            }
        } catch (error) {
            console.error('表单提交失败:', error);
            this.showMessage('网络错误，请稍后重试', 'error');
        } finally {
            this.isSubmitting = false;
            this.toggleLoading(false);
        }
    }
    
    /**
     * 验证所有字段
     */
    async validateAllFields() {
        const fields = this.form.querySelectorAll('input[required], textarea[required], select[required]');
        const validationPromises = Array.from(fields).map(field => this.validateField(field));
        
        const results = await Promise.all(validationPromises);
        return results.every(result => result.success);
    }
    
    /**
     * 显示字段错误
     */
    showFieldErrors(errors) {
        errors.forEach(error => {
            // 解析错误信息，找到对应字段
            const parts = error.split(':');
            if (parts.length >= 2) {
                const fieldName = parts[0].trim();
                const message = parts.slice(1).join(':').trim();
                
                const field = this.form.querySelector(`[data-field-name="${fieldName}"]`);
                if (field) {
                    this.showFieldValidation(field, false, message);
                }
            }
        });
    }
    
    /**
     * 处理拖拽文件
     */
    handleDroppedFiles(files, fileInput, dropZone) {
        const fileArray = Array.from(files);

        if (fileInput.hasAttribute('multiple')) {
            // 多文件上传
            this.handleMultipleFiles(fileArray, fileInput, dropZone);
        } else {
            // 单文件上传
            if (fileArray.length > 0) {
                this.handleSingleFile(fileArray[0], fileInput, dropZone);
            }
        }
    }

    /**
     * 处理单个文件
     */
    async handleSingleFile(file, fileInput, dropZone) {
        if (!this.validateFile(file)) {
            return;
        }

        // 更新文件列表显示
        this.updateFileList(dropZone, [file]);

        // 上传文件
        await this.uploadFile(file, fileInput, dropZone);
    }

    /**
     * 处理多个文件
     */
    async handleMultipleFiles(files, fileInput, dropZone) {
        const validFiles = files.filter(file => this.validateFile(file));

        if (validFiles.length === 0) {
            return;
        }

        // 更新文件列表显示
        this.updateFileList(dropZone, validFiles);

        // 逐个上传文件
        for (const file of validFiles) {
            await this.uploadFile(file, fileInput, dropZone);
        }
    }

    /**
     * 验证文件
     */
    validateFile(file) {
        const allowedTypes = ['jpg', 'jpeg', 'gif', 'png', 'doc', 'dot', 'docx', 'xls', 'xlt', 'xlsx', 'wps'];
        const maxSize = 10 * 1024 * 1024; // 10MB

        const extension = file.name.split('.').pop().toLowerCase();

        if (!allowedTypes.includes(extension)) {
            this.showMessage(`文件 ${file.name} 类型不支持`, 'error');
            return false;
        }

        if (file.size > maxSize) {
            this.showMessage(`文件 ${file.name} 大小超过10MB限制`, 'error');
            return false;
        }

        return true;
    }

    /**
     * 更新文件列表显示
     */
    updateFileList(dropZone, files) {
        const fileList = dropZone.querySelector('.hf-file-list');
        fileList.innerHTML = '';

        files.forEach((file, index) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'hf-file-item';
            fileItem.innerHTML = `
                <div class="hf-file-info">
                    <span class="hf-file-name">${file.name}</span>
                    <span class="hf-file-size">${this.formatFileSize(file.size)}</span>
                </div>
                <div class="hf-file-progress">
                    <div class="hf-progress-bar">
                        <div class="hf-progress-fill" style="width: 0%"></div>
                    </div>
                    <span class="hf-progress-text">0%</span>
                </div>
                <div class="hf-file-status">等待上传</div>
            `;
            fileList.appendChild(fileItem);
        });
    }

    /**
     * 上传文件
     */
    async uploadFile(file, fileInput, dropZone) {
        const fileItems = dropZone.querySelectorAll('.hf-file-item');
        const fileItem = Array.from(fileItems).find(item =>
            item.querySelector('.hf-file-name').textContent === file.name
        );

        if (!fileItem) return;

        const progressFill = fileItem.querySelector('.hf-progress-fill');
        const progressText = fileItem.querySelector('.hf-progress-text');
        const statusElement = fileItem.querySelector('.hf-file-status');

        statusElement.textContent = '上传中...';
        statusElement.className = 'hf-file-status hf-uploading';

        try {
            const formData = new FormData();
            formData.append('action', 'upload_file');
            formData.append('field_name', fileInput.name);
            formData.append(fileInput.name, file);

            const response = await this.makeRequest('POST', formData, (progress) => {
                progressFill.style.width = progress + '%';
                progressText.textContent = Math.round(progress) + '%';
            });

            if (response.success) {
                statusElement.textContent = '上传成功';
                statusElement.className = 'hf-file-status hf-success';
                progressFill.style.width = '100%';
                progressText.textContent = '100%';
            } else {
                statusElement.textContent = '上传失败: ' + response.message;
                statusElement.className = 'hf-file-status hf-error';
            }
        } catch (error) {
            console.error('文件上传失败:', error);
            statusElement.textContent = '上传失败';
            statusElement.className = 'hf-file-status hf-error';
        }
    }

    /**
     * 格式化文件大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 处理文件上传（兼容原有方法）
     */
    async handleFileUpload(fileInput) {
        const files = fileInput.files;
        if (!files.length) return;

        const dropZone = fileInput.parentElement.querySelector('.hf-drop-zone');
        if (dropZone) {
            // 使用新的拖拽上传处理
            this.handleDroppedFiles(files, fileInput, dropZone);
        } else {
            // 使用原有的处理方式
            const file = files[0];
            const maxSize = 10 * 1024 * 1024; // 10MB

            if (file.size > maxSize) {
                this.showFieldValidation(fileInput, false, '文件大小不能超过10MB');
                fileInput.value = '';
                return;
            }

            // 显示上传进度
            this.showUploadProgress(fileInput, 0);

            try {
                const formData = new FormData();
                formData.append('action', 'upload_file');
                formData.append('field_name', fileInput.name);
                formData.append(fileInput.name, file);

                const response = await this.makeRequest('POST', formData, (progress) => {
                    this.showUploadProgress(fileInput, progress);
                });

                if (response.success) {
                    this.showFieldValidation(fileInput, true, '上传成功');
                    this.showUploadProgress(fileInput, 100);
                } else {
                    this.showFieldValidation(fileInput, false, response.message);
                    fileInput.value = '';
                }
            } catch (error) {
                console.error('文件上传失败:', error);
                this.showFieldValidation(fileInput, false, '上传失败');
                fileInput.value = '';
            }
        }
    }
    
    /**
     * 显示上传进度
     */
    showUploadProgress(fileInput, progress) {
        let progressBar = fileInput.parentElement.querySelector('.hf-upload-progress');
        
        if (!progressBar) {
            progressBar = document.createElement('div');
            progressBar.className = 'hf-upload-progress';
            progressBar.innerHTML = `
                <div class="hf-progress-bar">
                    <div class="hf-progress-fill"></div>
                </div>
                <div class="hf-progress-text">0%</div>
            `;
            fileInput.parentElement.appendChild(progressBar);
        }
        
        const fill = progressBar.querySelector('.hf-progress-fill');
        const text = progressBar.querySelector('.hf-progress-text');
        
        fill.style.width = progress + '%';
        text.textContent = Math.round(progress) + '%';
        
        if (progress >= 100) {
            setTimeout(() => {
                progressBar.style.display = 'none';
            }, 1000);
        } else {
            progressBar.style.display = 'block';
        }
    }
    
    /**
     * 发送请求
     */
    async makeRequest(method, data, onProgress = null) {
        const url = this.options.apiUrl;
        
        const options = {
            method: method,
            headers: {}
        };
        
        if (data instanceof FormData) {
            options.body = data;
        } else {
            options.headers['Content-Type'] = 'application/x-www-form-urlencoded';
            options.body = new URLSearchParams(data).toString();
        }
        
        // 添加Ajax标识
        options.headers['X-Requested-With'] = 'XMLHttpRequest';
        
        return new Promise((resolve, reject) => {
            const xhr = new XMLHttpRequest();
            
            xhr.open(method, url);
            
            // 设置请求头
            Object.keys(options.headers).forEach(key => {
                xhr.setRequestHeader(key, options.headers[key]);
            });
            
            // 上传进度
            if (onProgress && xhr.upload) {
                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const progress = (e.loaded / e.total) * 100;
                        onProgress(progress);
                    }
                });
            }
            
            xhr.onload = () => {
                try {
                    const response = JSON.parse(xhr.responseText);
                    resolve(response);
                } catch (error) {
                    reject(new Error('响应解析失败'));
                }
            };
            
            xhr.onerror = () => reject(new Error('网络错误'));
            xhr.ontimeout = () => reject(new Error('请求超时'));
            
            xhr.timeout = 30000; // 30秒超时
            xhr.send(options.body);
        });
    }
    
    /**
     * 获取字段ID
     */
    getFieldId(field) {
        const name = field.name;
        if (name && name.startsWith('G-')) {
            return name.substring(2);
        }
        return null;
    }
    
    /**
     * 获取字段值
     */
    getFieldValue(field) {
        if (field.type === 'checkbox') {
            const checkboxes = this.form.querySelectorAll(`input[name="${field.name}"]:checked`);
            return Array.from(checkboxes).map(cb => cb.value);
        }
        return field.value;
    }
    
    /**
     * 获取表单ID
     */
    getFormId() {
        const fidInput = this.form.querySelector('input[name="fid"]');
        return fidInput ? fidInput.value : null;
    }
    
    /**
     * 显示消息
     */
    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageEl = document.createElement('div');
        messageEl.className = `hf-message hf-message-${type}`;
        messageEl.textContent = message;
        
        // 插入到表单顶部
        this.form.insertBefore(messageEl, this.form.firstChild);
        
        // 自动隐藏
        setTimeout(() => {
            messageEl.remove();
        }, 5000);
    }
    
    /**
     * 显示成功页面
     */
    showSuccessPage() {
        // 清除草稿
        this.clearDraft();

        this.form.innerHTML = `
            <div class="hf-success-page">
                <div class="hf-success-icon">✓</div>
                <h2>提交成功</h2>
                <p>您的表单已成功提交，感谢您的参与！</p>
                <button onclick="location.reload()" class="hf-btn">返回</button>
            </div>
        `;
    }

    /**
     * 初始化草稿保存
     */
    initializeDraftSaving() {
        // 监听表单变化
        this.form.addEventListener('input', this.debounce(() => {
            this.saveDraft();
        }, 2000));

        // 页面卸载时保存草稿
        window.addEventListener('beforeunload', () => {
            this.saveDraft();
        });
    }

    /**
     * 保存草稿
     */
    saveDraft() {
        const now = Date.now();
        if (now - this.lastDraftSave < 1000) return; // 防止频繁保存

        const formData = new FormData(this.form);
        const draftData = {};

        for (const [key, value] of formData.entries()) {
            if (key !== 'formhash' && key !== 'commit') {
                draftData[key] = value;
            }
        }

        const formId = this.getFormId();
        if (formId) {
            localStorage.setItem(`hf_draft_${formId}`, JSON.stringify({
                data: draftData,
                timestamp: now
            }));

            this.lastDraftSave = now;
            this.showDraftSavedIndicator();
        }
    }

    /**
     * 加载草稿
     */
    loadDraft() {
        const formId = this.getFormId();
        if (!formId) return;

        const draftKey = `hf_draft_${formId}`;
        const draftData = localStorage.getItem(draftKey);

        if (draftData) {
            try {
                const draft = JSON.parse(draftData);
                const age = Date.now() - draft.timestamp;

                // 草稿有效期7天
                if (age < 7 * 24 * 60 * 60 * 1000) {
                    this.showDraftRestorePrompt(draft.data);
                } else {
                    localStorage.removeItem(draftKey);
                }
            } catch (error) {
                console.error('加载草稿失败:', error);
                localStorage.removeItem(draftKey);
            }
        }
    }

    /**
     * 显示草稿恢复提示
     */
    showDraftRestorePrompt(draftData) {
        const prompt = document.createElement('div');
        prompt.className = 'hf-draft-prompt';
        prompt.innerHTML = `
            <div class="hf-draft-content">
                <h4>发现未完成的表单</h4>
                <p>检测到您之前填写的表单数据，是否要恢复？</p>
                <div class="hf-draft-actions">
                    <button class="hf-btn hf-btn-primary" onclick="this.parentElement.parentElement.parentElement.remove(); window.hejinForms.restoreDraft()">恢复</button>
                    <button class="hf-btn hf-btn-secondary" onclick="this.parentElement.parentElement.parentElement.remove(); window.hejinForms.clearDraft()">忽略</button>
                </div>
            </div>
        `;

        this.form.insertBefore(prompt, this.form.firstChild);
        this.draftToRestore = draftData;
    }

    /**
     * 恢复草稿
     */
    restoreDraft() {
        if (!this.draftToRestore) return;

        Object.keys(this.draftToRestore).forEach(key => {
            const field = this.form.querySelector(`[name="${key}"]`);
            if (field) {
                if (field.type === 'checkbox' || field.type === 'radio') {
                    if (field.value === this.draftToRestore[key]) {
                        field.checked = true;
                    }
                } else {
                    field.value = this.draftToRestore[key];
                }
            }
        });

        this.showMessage('草稿已恢复', 'success');
        this.draftToRestore = null;
    }

    /**
     * 清除草稿
     */
    clearDraft() {
        const formId = this.getFormId();
        if (formId) {
            localStorage.removeItem(`hf_draft_${formId}`);
        }
    }

    /**
     * 显示草稿保存指示器
     */
    showDraftSavedIndicator() {
        let indicator = document.querySelector('.hf-draft-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.className = 'hf-draft-indicator';
            indicator.textContent = '草稿已保存';
            document.body.appendChild(indicator);
        }

        indicator.style.display = 'block';
        indicator.style.opacity = '1';

        setTimeout(() => {
            indicator.style.opacity = '0';
            setTimeout(() => {
                indicator.style.display = 'none';
            }, 300);
        }, 2000);
    }

    /**
     * 初始化键盘快捷键
     */
    initializeKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+S 保存草稿
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                this.saveDraft();
                this.showMessage('草稿已保存', 'success');
            }

            // Ctrl+Enter 提交表单
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                this.handleFormSubmit();
            }

            // Esc 清除当前字段
            if (e.key === 'Escape' && e.target.matches('input, textarea')) {
                e.target.value = '';
                e.target.focus();
            }
        });
    }

    /**
     * 初始化工具提示
     */
    initializeTooltips() {
        // 为必填字段添加提示
        const requiredFields = this.form.querySelectorAll('[required]');
        requiredFields.forEach(field => {
            const label = field.closest('.field').querySelector('.control-label');
            if (label && !label.querySelector('.hf-tooltip')) {
                const tooltip = document.createElement('span');
                tooltip.className = 'hf-tooltip';
                tooltip.textContent = '此字段为必填项';
                tooltip.setAttribute('data-tooltip', '此字段为必填项');
                label.appendChild(tooltip);
            }
        });

        // 为文件上传字段添加提示
        const fileFields = this.form.querySelectorAll('input[type="file"]');
        fileFields.forEach(field => {
            const container = field.closest('.field');
            if (container && !container.querySelector('.hf-file-hint')) {
                const hint = document.createElement('div');
                hint.className = 'hf-file-hint';
                hint.innerHTML = `
                    <small>
                        <strong>提示：</strong>
                        支持拖拽上传，最大文件大小10MB<br>
                        支持格式：jpg, jpeg, gif, png, doc, docx, xls, xlsx, wps
                    </small>
                `;
                container.appendChild(hint);
            }
        });
    }
}

// 自动初始化
document.addEventListener('DOMContentLoaded', () => {
    if (document.querySelector('#new_entry')) {
        window.hejinForms = new HejinFormsAsync();
    }
});
