<?php
/**
 * 和金表单调试工具
 * 用于排查前端空白问题
 */

// 检查是否在Discuz环境中
if (!defined('IN_DISCUZ')) {
    echo "<h1>调试信息</h1>";
    echo "<p style='color: red;'>警告：未在Discuz环境中运行</p>";
    
    // 尝试模拟基本环境
    define('IN_DISCUZ', true);
    
    // 模拟基本变量
    $_G = array(
        'uid' => 1,
        'username' => 'testuser',
        'formhash' => 'test123',
        'siteurl' => 'http://localhost/',
        'setting' => array(
            'ucenterurl' => 'http://localhost/uc_server'
        )
    );
    
    // 模拟常量
    if (!defined('DISCUZ_ROOT')) {
        define('DISCUZ_ROOT', dirname(__FILE__) . '/');
    }
}

// 包含必要文件
require_once 'config.inc.php';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>和金表单调试</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .info{background:#e3f2fd;padding:10px;margin:10px 0;border-radius:5px;} .error{background:#ffebee;padding:10px;margin:10px 0;border-radius:5px;color:#c62828;} .success{background:#e8f5e8;padding:10px;margin:10px 0;border-radius:5px;color:#2e7d32;}</style>";
echo "</head><body>";

echo "<h1>和金表单调试工具</h1>";

// 检查基本信息
echo "<div class='info'>";
echo "<h2>基本信息</h2>";
echo "<p><strong>PHP版本:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>当前目录:</strong> " . __DIR__ . "</p>";
echo "<p><strong>插件路径:</strong> " . (defined('HEJIN_PATH') ? HEJIN_PATH : '未定义') . "</p>";
echo "<p><strong>插件根目录:</strong> " . (defined('HEJIN_ROOT') ? HEJIN_ROOT : '未定义') . "</p>";
echo "</div>";

// 检查文件存在性
echo "<div class='info'>";
echo "<h2>文件检查</h2>";

$files_to_check = [
    'hejin_forms.inc.php' => '主文件',
    'api.inc.php' => 'API接口文件',
    'js/hejin-forms-async.js' => '异步JavaScript文件',
    'css/hejin-forms-async.css' => '异步CSS文件',
    'template/form/show.htm' => '表单模板文件',
    'db.class.php' => '数据库类文件',
    'function.func.php' => '函数文件'
];

foreach ($files_to_check as $file => $desc) {
    $path = __DIR__ . '/' . $file;
    if (file_exists($path)) {
        $size = filesize($path);
        echo "<p style='color:green;'>✓ {$desc}: {$file} ({$size} bytes)</p>";
    } else {
        echo "<p style='color:red;'>✗ {$desc}: {$file} (文件不存在)</p>";
    }
}
echo "</div>";

// 检查权限
echo "<div class='info'>";
echo "<h2>权限检查</h2>";

$dirs_to_check = [
    __DIR__ => '插件根目录',
    __DIR__ . '/js' => 'JS目录',
    __DIR__ . '/css' => 'CSS目录',
    __DIR__ . '/template' => '模板目录',
    __DIR__ . '/data/uploads/submit' => '上传目录'
];

foreach ($dirs_to_check as $dir => $desc) {
    if (is_dir($dir)) {
        $writable = is_writable($dir) ? '可写' : '只读';
        $color = is_writable($dir) ? 'green' : 'orange';
        echo "<p style='color:{$color};'>📁 {$desc}: {$writable}</p>";
    } else {
        echo "<p style='color:red;'>📁 {$desc}: 目录不存在</p>";
    }
}
echo "</div>";

// 测试表单数据
echo "<div class='info'>";
echo "<h2>测试表单数据</h2>";

// 模拟表单数据
$test_form = array(
    'id' => 1,
    'name' => '测试表单',
    'description' => '这是一个测试表单',
    'skin' => 'default',
    'start' => time() - 3600,
    'expire' => time() + 3600,
    'state' => 1,
    'stat' => 0
);

$test_groups = array(
    array(
        'id' => 1,
        'name' => '姓名',
        'type' => 'text',
        'description' => '请输入您的姓名',
        'config' => '{"GROUP_MUST":"1","GROUP_SIZE":"30"}'
    ),
    array(
        'id' => 2,
        'name' => '邮箱',
        'type' => 'text',
        'description' => '请输入您的邮箱',
        'config' => '{"GROUP_MUST":"1","GROUP_VERIFY":"email"}'
    )
);

echo "<p><strong>测试表单:</strong></p>";
echo "<pre>" . print_r($test_form, true) . "</pre>";

echo "<p><strong>测试字段:</strong></p>";
echo "<pre>" . print_r($test_groups, true) . "</pre>";
echo "</div>";

// 生成测试表单HTML
echo "<div class='info'>";
echo "<h2>测试表单渲染</h2>";

try {
    // 设置变量
    $form = $test_form;
    $groups = $test_groups;
    $user = array(
        'uid' => $_G['uid'],
        'username' => $_G['username'],
        'logo' => $_G['setting']['ucenterurl'] . '/avatar.php?uid=' . $_G['uid'] . '&size=small'
    );
    
    // 包含样式
    $temp = array();
    $temp['default'] = '<style type="text/css">.entry-container{background:#f5f5f5;padding:20px;}.field{margin:15px 0;}.control-label{font-weight:bold;margin-bottom:5px;display:block;}</style>';
    
    echo "<p style='color:green;'>✓ 变量设置成功</p>";
    
    // 简单的表单HTML
    echo "<div style='border:1px solid #ddd;padding:20px;margin:10px 0;'>";
    echo $temp['default'];
    echo "<form id='new_entry' method='post'>";
    echo "<h3>{$form['name']}</h3>";
    echo "<p>{$form['description']}</p>";
    
    foreach ($groups as $group) {
        echo "<div class='field'>";
        echo "<label class='control-label'>{$group['name']}</label>";
        echo "<input type='text' name='G-{$group['id']}' style='width:300px;padding:5px;' />";
        echo "<div style='font-size:12px;color:#666;'>{$group['description']}</div>";
        echo "</div>";
    }
    
    echo "<input type='submit' value='提交' style='background:#007cba;color:white;padding:10px 20px;border:none;border-radius:3px;' />";
    echo "</form>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color:red;'>✗ 表单渲染失败: " . $e->getMessage() . "</p>";
}
echo "</div>";

// JavaScript测试
echo "<div class='info'>";
echo "<h2>JavaScript测试</h2>";
echo "<button onclick='testJS()' style='background:#28a745;color:white;padding:8px 16px;border:none;border-radius:3px;'>测试JavaScript</button>";
echo "<div id='js-result' style='margin-top:10px;'></div>";
echo "</div>";

// API测试
echo "<div class='info'>";
echo "<h2>API测试</h2>";
echo "<button onclick='testAPI()' style='background:#17a2b8;color:white;padding:8px 16px;border:none;border-radius:3px;'>测试API</button>";
echo "<div id='api-result' style='margin-top:10px;'></div>";
echo "</div>";

echo "<script>";
echo "function testJS() {";
echo "  var result = document.getElementById('js-result');";
echo "  try {";
echo "    if (typeof HejinFormsAsync !== 'undefined') {";
echo "      result.innerHTML = '<p style=\"color:green;\">✓ HejinFormsAsync 类已加载</p>';";
echo "    } else {";
echo "      result.innerHTML = '<p style=\"color:orange;\">⚠ HejinFormsAsync 类未加载，检查JS文件</p>';";
echo "    }";
echo "  } catch(e) {";
echo "    result.innerHTML = '<p style=\"color:red;\">✗ JavaScript错误: ' + e.message + '</p>';";
echo "  }";
echo "}";

echo "function testAPI() {";
echo "  var result = document.getElementById('api-result');";
echo "  result.innerHTML = '<p>正在测试API...</p>';";
echo "  ";
echo "  fetch('?action=get_form&formid=1', {";
echo "    method: 'GET',";
echo "    headers: {'X-Requested-With': 'XMLHttpRequest'}";
echo "  })";
echo "  .then(response => response.json())";
echo "  .then(data => {";
echo "    result.innerHTML = '<p style=\"color:green;\">✓ API响应: ' + JSON.stringify(data) + '</p>';";
echo "  })";
echo "  .catch(error => {";
echo "    result.innerHTML = '<p style=\"color:red;\">✗ API错误: ' + error.message + '</p>';";
echo "  });";
echo "}";

echo "// 自动加载测试";
echo "document.addEventListener('DOMContentLoaded', function() {";
echo "  console.log('调试页面已加载');";
echo "  testJS();";
echo "});";
echo "</script>";

echo "<div class='info'>";
echo "<h2>使用说明</h2>";
echo "<ol>";
echo "<li>检查上述所有项目是否正常</li>";
echo "<li>如果文件缺失，请重新上传</li>";
echo "<li>如果权限有问题，请设置正确的目录权限</li>";
echo "<li>点击测试按钮验证功能</li>";
echo "<li>如果仍有问题，请查看浏览器控制台错误信息</li>";
echo "</ol>";
echo "</div>";

echo "</body></html>";
?>
