﻿<?php
/*
 * 出处：魔趣吧
 * 官网: Www.moqu8.com
 * 备用网址: www.moqu8.com (请收藏备用!)
 * 技术支持/更新维护：QQ 1218894030
 * 
 */
if(!defined('IN_DISCUZ') || !defined('IN_ADMINCP')) {
	exit('Access Denied');
}



$sql = <<<EOF
CREATE TABLE IF NOT EXISTS `cdb_hejin_form` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `appid` varchar(15) NOT NULL,
  `thumb` varchar(100) DEFAULT NULL,
  `name` varchar(100) NOT NULL,
  `config` text,
  `description` varchar(1000) DEFAULT NULL,
  `color` varchar(10) DEFAULT NULL,
  `tags` varchar(100) DEFAULT NULL,
  `state` tinyint(4) NOT NULL,
  `skin` varchar(20) DEFAULT NULL,
  `stat` mediumint(9) NOT NULL DEFAULT '0',
  `sort` int(11) NOT NULL DEFAULT '0',
  `quote` varchar(100) DEFAULT NULL,
  `start` int(11) NOT NULL,
  `expire` int(11) NOT NULL,
  `dateline` int(11) NOT NULL,
  `modify` int(11) DEFAULT NULL,
  `mender` varchar(30) DEFAULT NULL,
  `ip` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `appid` (`appid`),
  KEY `name` (`name`),
  KEY `tags` (`tags`)
) ENGINE=MyISAM AUTO_INCREMENT=1 ;
EOF;
runquery($sql);

$sql = <<<EOF
CREATE TABLE IF NOT EXISTS `cdb_hejin_group` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fid` int(11) NOT NULL,
  `name` varchar(100) DEFAULT NULL,
  `type` varchar(20) DEFAULT NULL,
  `dateline` int(11) NOT NULL,
  `modify` int(11) NOT NULL,
  `state` tinyint(4) NOT NULL,
  `sort` mediumint(9) NOT NULL,
  `config` varchar(1000) DEFAULT NULL,
  `selected` varchar(255) DEFAULT NULL,
  `description` text,
  `stat` mediumint(9) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fid` (`fid`),
  KEY `name` (`name`),
  KEY `sort` (`sort`)
) ENGINE=MyISAM AUTO_INCREMENT=1 ;
EOF;
runquery($sql);

$sql = <<<EOF
CREATE TABLE IF NOT EXISTS `cdb_hejin_option` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fid` int(11) NOT NULL,
  `gid` int(11) DEFAULT NULL,
  `name` varchar(200) NOT NULL,
  `dateline` int(11) DEFAULT NULL,
  `sort` mediumint(9) DEFAULT NULL,
  `config` varchar(255) DEFAULT NULL,
  `description` varchar(1000) DEFAULT NULL,
  `quote` varchar(100) DEFAULT NULL,
  `image` varchar(100) DEFAULT NULL,
  `state` tinyint(4) DEFAULT '1',
  `stat` mediumint(9) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `fid` (`fid`),
  KEY `gid` (`gid`),
  KEY `sort` (`sort`),
  KEY `name` (`name`)
) ENGINE=MyISAM AUTO_INCREMENT=1 ;
EOF;
runquery($sql);

$sql = <<<EOF
CREATE TABLE IF NOT EXISTS `cdb_hejin_value` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `fid` int(11) NOT NULL,
  `appid` varchar(15) NOT NULL,
  `uid` int(11) NOT NULL,
  `account` varchar(30) DEFAULT NULL,
  `config` text,
  `state` tinyint(4) NOT NULL,
  `dateline` int(11) NOT NULL,
  `ip` varchar(15) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `account` (`account`),
  KEY `fid` (`fid`),
  KEY `appid` (`appid`),
  KEY `uid` (`uid`)
) ENGINE=MyISAM AUTO_INCREMENT=1 ;
EOF;
runquery($sql);






$finish = TRUE;
?>