<?php
/**
 * 多行输入行数修改工具
 * 自动修改所有模板文件中的textarea默认行数
 */

// 检查是否在正确的目录中
if (!file_exists('template/form/show.htm')) {
    die('错误：请在插件根目录中运行此脚本');
}

// 配置
$new_rows = isset($_GET['rows']) ? intval($_GET['rows']) : 6; // 默认6行
$new_height_desktop = $new_rows * 20; // 桌面端高度（每行约20px）
$new_height_mobile = $new_rows * 18;  // 移动端高度（每行约18px）

// 需要修改的文件列表
$files_to_modify = [
    'template/form/show.htm' => [
        'type' => 'desktop',
        'pattern' => '/(<textarea[^>]*rows=[\'"]\d+[\'"][^>]*>)/',
        'replacement_pattern' => '/rows=[\'"](\d+)[\'"]/',
        'replacement' => 'rows="' . $new_rows . '"'
    ],
    'template/form/mobile_show.htm' => [
        'type' => 'mobile',
        'pattern' => '/(<textarea[^>]*rows=[\'"]\d+[\'"][^>]*>)/',
        'replacement_pattern' => '/rows=[\'"](\d+)[\'"]/',
        'replacement' => 'rows="' . $new_rows . '"',
        'height_pattern' => '/height:\s*\d+px/',
        'height_replacement' => 'height:' . $new_height_mobile . 'px'
    ],
    'template/touch/form/show.htm' => [
        'type' => 'mobile',
        'pattern' => '/(<textarea[^>]*rows=[\'"]\d+[\'"][^>]*>)/',
        'replacement_pattern' => '/rows=[\'"](\d+)[\'"]/',
        'replacement' => 'rows="' . $new_rows . '"',
        'height_pattern' => '/height:\s*\d+px/',
        'height_replacement' => 'height:' . $new_height_mobile . 'px'
    ]
];

/**
 * 备份文件
 */
function backupFile($file) {
    $backup_dir = 'backup_textarea_' . date('Y-m-d_H-i-s');
    if (!is_dir($backup_dir)) {
        mkdir($backup_dir, 0755, true);
    }
    
    $backup_file = $backup_dir . '/' . basename($file);
    $backup_subdir = dirname($backup_file);
    if (!is_dir($backup_subdir)) {
        mkdir($backup_subdir, 0755, true);
    }
    
    return copy($file, $backup_file);
}

/**
 * 修改文件中的textarea行数
 */
function modifyTextareaRows($file, $config) {
    if (!file_exists($file)) {
        return ['success' => false, 'message' => "文件不存在: $file"];
    }
    
    // 备份文件
    if (!backupFile($file)) {
        return ['success' => false, 'message' => "备份失败: $file"];
    }
    
    $content = file_get_contents($file);
    $original_content = $content;
    $changes = 0;
    
    // 修改rows属性
    if (isset($config['replacement_pattern'])) {
        $content = preg_replace($config['replacement_pattern'], $config['replacement'], $content, -1, $count);
        $changes += $count;
    }
    
    // 修改height样式（仅移动端）
    if (isset($config['height_pattern']) && isset($config['height_replacement'])) {
        $content = preg_replace($config['height_pattern'], $config['height_replacement'], $content, -1, $count);
        $changes += $count;
    }
    
    if ($changes > 0) {
        if (file_put_contents($file, $content)) {
            return ['success' => true, 'message' => "成功修改 $file，共 $changes 处变更"];
        } else {
            return ['success' => false, 'message' => "写入失败: $file"];
        }
    } else {
        return ['success' => true, 'message' => "文件 $file 无需修改"];
    }
}

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $new_rows = intval($_POST['rows']);
    $new_height_desktop = $new_rows * 20;
    $new_height_mobile = $new_rows * 18;
    
    // 更新配置
    foreach ($files_to_modify as $file => &$config) {
        $config['replacement'] = 'rows="' . $new_rows . '"';
        if (isset($config['height_replacement'])) {
            $config['height_replacement'] = 'height:' . $new_height_mobile . 'px';
        }
    }
    
    echo "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>修改结果</title></head><body>";
    echo "<h1>多行输入行数修改结果</h1>";
    echo "<p><strong>新行数：</strong> $new_rows</p>";
    echo "<p><strong>桌面端高度：</strong> {$new_height_desktop}px</p>";
    echo "<p><strong>移动端高度：</strong> {$new_height_mobile}px</p>";
    echo "<hr>";
    
    $success_count = 0;
    $total_count = count($files_to_modify);
    
    foreach ($files_to_modify as $file => $config) {
        $result = modifyTextareaRows($file, $config);
        $color = $result['success'] ? 'green' : 'red';
        echo "<p style='color: $color;'>" . ($result['success'] ? '✓' : '✗') . " {$result['message']}</p>";
        if ($result['success']) $success_count++;
    }
    
    echo "<hr>";
    echo "<p><strong>修改完成：</strong> $success_count/$total_count 个文件</p>";
    
    if ($success_count > 0) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>✅ 修改成功！</h3>";
        echo "<p>请清除浏览器缓存后测试效果。</p>";
        echo "<p>如果效果不理想，可以从备份目录恢复文件。</p>";
        echo "</div>";
    }
    
    echo "<p><a href='?' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>返回设置</a></p>";
    echo "</body></html>";
    exit;
}

// 显示设置页面
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多行输入行数修改工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="number"] {
            width: 100px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .btn {
            background: #007cba;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background: #005a87;
        }
        .preview {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        .file-list {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        textarea.preview-textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: inherit;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>多行输入行数修改工具</h1>
        
        <div class="warning">
            <h3>⚠️ 重要提示</h3>
            <ul>
                <li>此工具会自动备份原文件</li>
                <li>建议先在测试环境中验证</li>
                <li>修改后请清除浏览器缓存</li>
                <li>如有问题可从备份目录恢复</li>
            </ul>
        </div>

        <form method="post">
            <div class="form-group">
                <label for="rows">新的默认行数：</label>
                <input type="number" id="rows" name="rows" value="<?php echo $new_rows; ?>" min="2" max="20" onchange="updatePreview()">
                <small>建议范围：2-20行</small>
            </div>

            <div class="preview">
                <h3>预览效果</h3>
                <p><strong>桌面端高度：</strong> <span id="desktop-height"><?php echo $new_height_desktop; ?></span>px</p>
                <p><strong>移动端高度：</strong> <span id="mobile-height"><?php echo $new_height_mobile; ?></span>px</p>
                
                <p><strong>实际效果：</strong></p>
                <textarea class="preview-textarea" id="preview-textarea" rows="<?php echo $new_rows; ?>" placeholder="这是一个多行输入框的预览效果..."></textarea>
            </div>

            <div class="file-list">
                <h3>将要修改的文件</h3>
                <ul>
                    <?php foreach ($files_to_modify as $file => $config): ?>
                        <li>
                            <strong><?php echo $file; ?></strong> 
                            (<?php echo $config['type'] === 'desktop' ? '桌面端' : '移动端'; ?>)
                            <?php if (file_exists($file)): ?>
                                <span style="color: green;">✓ 存在</span>
                            <?php else: ?>
                                <span style="color: red;">✗ 不存在</span>
                            <?php endif; ?>
                        </li>
                    <?php endforeach; ?>
                </ul>
            </div>

            <button type="submit" class="btn">开始修改</button>
        </form>

        <div style="margin-top: 30px;">
            <h3>推荐设置</h3>
            <ul>
                <li><strong>桌面端：</strong> 6-8行（适合大屏幕）</li>
                <li><strong>移动端：</strong> 4-6行（适合小屏幕）</li>
                <li><strong>通用设置：</strong> 6行（平衡各种设备）</li>
            </ul>
        </div>
    </div>

    <script>
        function updatePreview() {
            const rows = parseInt(document.getElementById('rows').value) || 6;
            const desktopHeight = rows * 20;
            const mobileHeight = rows * 18;
            
            document.getElementById('desktop-height').textContent = desktopHeight;
            document.getElementById('mobile-height').textContent = mobileHeight;
            document.getElementById('preview-textarea').rows = rows;
        }

        // 页面加载时更新预览
        document.addEventListener('DOMContentLoaded', updatePreview);
    </script>
</body>
</html>
