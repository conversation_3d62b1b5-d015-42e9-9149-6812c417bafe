<?php
/*
 * 出处：魔趣吧
 * 官网: Www.moqu8.com
 * 备用网址: www.moqu8.com (请收藏备用!)
 * 技术支持/更新维护：QQ 1218894030
 * 
 */
if(!defined('IN_DISCUZ') || !defined('IN_ADMINCP')) {
	exit('Access Denied');
}
require_once DISCUZ_ROOT.'./source/plugin/hejin_forms/config.inc.php';
include_once ('function.func.php');
$model = addslashes($_GET['model']);

if($model=="del"){
		$opid=intval($_GET['opid']);
		$fid = intval($_GET['fid']);
		
		if(!empty($opid)){
			
	   $del= C::t('#hejin_forms#hejin_value')->delete_by_id($opid);
	   
	   if($del){
		$form = C::t('#hejin_forms#hejin_form')->fetch_by_id($fid);
		

			$data=array();
			
	         $data['stat']  =--$form['stat'];           
			$statj =  C::t('#hejin_forms#hejin_form')->update_by_id($fid,$data);	
		 
			 
		if($statj){
	$url = 'action=plugins&identifier=hejin_forms&pmod=admin_value&model=list&formid='.$fid;
			cpmsg(lang('plugin/hejin_forms', 'delok'), $url, 'succeed');				}				
		
		   }
		
			}

	}elseif($model=="up"){
		
		$fid = intval($_GET['formid']);
		$vid = intval($_GET['valueid']);
		$data=array();
		$data['state'] =1;
		$valueup = C::t('#hejin_forms#hejin_value')->update_by_id($data,$vid);
if($valueup){
			 $url = 'action=plugins&identifier=hejin_forms&pmod=admin_value&model=list&formid='.$fid;
	    		cpmsg(lang('plugin/hejin_forms', 'editok'), $url, 'succeed');	
	
	}
		
		}elseif($model=="lists"){





$formid = intval($_GET['formid']);
$form =  C::t('#hejin_forms#hejin_form')->fetch_by_id($formid);


$groups =  C::t('#hejin_forms#hejin_group')->fetch_by_fida($formid);



$query = C::t('#hejin_forms#hejin_value')->fetch_by_fid($formid);

$values =$query;


foreach ($groups as $key=>$value)
{
	$gsorts[]= $value['id'];
}




foreach ($values as $key=>$value)
{
		$valsu[$key]['id'] = $value['id'];
		$valsu[$key]['uid'] = $value['uid'];
		$valsu[$key]['account'] = $value['account'];
		
		$configs = fix_json($value['config'],$_G['charset']);
			foreach ($configs as $ckey=>$cvalue)
			{
				$gid = str_replace('G-','',$ckey);
				$gorinfo = C::t('#hejin_forms#hejin_group')->fetch_by_id($gid);
				if($gorinfo['type'] == "radio" or $gorinfo['type'] == "select"){
					if($cvalue){
						
						
					$option = C::t('#hejin_forms#hejin_option')->fetch_by_id($cvalue);
					$config[$ckey] = $option['name'];
					}else{
						$config[$ckey] = "";
						}
					}elseif($gorinfo['type'] == "checkbox"){
						
					if($cvalue !=""){
						if(strstr($cvalue,',')){
							
							$cvaluess = explode(",", $cvalue);
							foreach ($cvaluess as $cvkey=>$cvalues)
							{
					$option = C::t('#hejin_forms#hejin_option')->fetch_by_id($cvalues);
					$cvaluecoi[]= $option['name'];
							}
					$config[$ckey] = implode(",",$cvaluecoi);
							
							}else{
								
					$option = C::t('#hejin_forms#hejin_option')->fetch_by_id($cvalue);
					$config[$ckey] = $option['name'];
								}
						
						
					}else{
						$config[$ckey] = "";
						}

						
						}elseif($gorinfo['type'] == "text" or $gorinfo['type'] == "textarea" or $gorinfo['type'] == "time"){
						$config[$ckey] = $cvalue;
						
						}elseif($gorinfo['type'] == "date"){

						$config[$ckey] = date('Y-m-d', $cvalue);
							
						}elseif($gorinfo['type'] == "dati"){

						$config[$ckey] = date('Y-m-d H:i:s', $cvalue);
							
						}elseif($gorinfo['type'] == "file"){
							if($cvalue){
								
								
						$url = '<a href="'.HEJIN_PATH.$cvalue.'" target="_blank">'.lang('plugin/hejin_forms', 'seeurl').'</a>';	
						$config[$ckey] = $url;
							}else{
							$config[$ckey]="";	
								}
							}
			}
		$valsu[$key]['config'] = $config;
		$valsu[$key]['state'] = $value['state'];
		$valsu[$key]['dateline'] = $value['dateline'];
		$valsu[$key]['ip'] = $value['ip'];
}


include template('hejin_forms:admin/member_values');

}


elseif($model=="list"){





$formid = intval($_GET['formid']);



$yshdps =  C::t('#hejin_forms#hejin_value')->fetch_by_fid($formid);

$amount = count($yshdps);
if( isset($_GET['page']) ){
   $page = intval($_GET['page']);
}else{
   $page = 1;
}
// 每页数量
$PageSize = 30;
// 记算总共有多少页
if( $amount ){
   if( $amount < $PageSize ){ $page_count = 1; }               //如果总数据量小于$PageSize，那么只有一页
   if( $amount % $PageSize ){                                  //取总数据量除以每页数的余数
       $page_count = (int)($amount/$PageSize) + 1;           //如果有余数，则页数等于总数据量除以每页数的结果取整再加一
   }else{
       $page_count = $amount/$PageSize;                      //如果没有余数，则页数等于总数据量除以每页数的结果
   }
}
else{
   $page_count = 0;
}
// 翻页链接
$page_string = '';
if( $page == 1 ){
   $page_string .= '<li><span>'.lang('plugin/hejin_forms', 'shouyephp').'</span><span>'.lang('plugin/hejin_forms', 'shangyiye').'</span>';
}
else{
   $page_string .= '<span><a href="'.$SELF.'?action=plugins&identifier=hejin_forms&pmod=admin_value&model=list&formid='.$formid.'&page=1">'.lang('plugin/hejin_forms', 'shouyephp').'</a></span><span><a href="'.$SELF.'?action=plugins&identifier=hejin_forms&pmod=admin_value&model=list&formid='.$formid.'&page='.($page-1).'">'.lang('plugin/hejin_forms', 'shangyiye').'</a></span>';
}
	$page_string .='<strong>'.$page.'</strong>';
if( ($page == $page_count) || ($page_count == 0) ){
   $page_string .= '<span>'.lang('plugin/hejin_forms', 'xiayiye').'</span><span>'.lang('plugin/hejin_forms', 'moye').'</span>';
}
else{
   $page_string .= '<span><a href="'.$SELF.'?action=plugins&identifier=hejin_forms&pmod=admin_value&model=list&formid='.$formid.'&page='.($page+1).'">'.lang('plugin/hejin_forms', 'xiayiye').'</a></span><span><a href="'.$SELF.'?action=plugins&identifier=hejin_forms&pmod=admin_value&model=list&formid='.$formid.'&page='.$page_count.'">'.lang('plugin/hejin_forms', 'moye').'</a></span>';
}
$page_string .=  '<span>'.lang('plugin/hejin_forms', 'ye').$page.'/'.$page_count.' '.$PageSize.lang('plugin/hejin_forms', 'tiaoye').' '.lang('plugin/hejin_forms', 'gong').$amount.lang('plugin/hejin_forms', 'tiao').'</span></li>';
$statnum = ($page-1)*$PageSize;

$query =  C::t('#hejin_forms#hejin_value')->fetch_limit_fid($formid,intval($statnum),intval($PageSize));



$form =  C::t('#hejin_forms#hejin_form')->fetch_by_id($formid);


$groups =  C::t('#hejin_forms#hejin_group')->fetch_by_fida($formid);




$values =$query;


foreach ($groups as $key=>$value)
{
	$gsorts[]= $value['id'];
}




foreach ($values as $key=>$value)
{
		$valsu[$key]['id'] = $value['id'];
		$valsu[$key]['uid'] = $value['uid'];
		$valsu[$key]['account'] = $value['account'];
		
		$configs = fix_json($value['config'],$_G['charset']);
			foreach ($configs as $ckey=>$cvalue)
			{
				$gid = str_replace('G-','',$ckey);
				$gorinfo = C::t('#hejin_forms#hejin_group')->fetch_by_id($gid);
				if($gorinfo['type'] == "radio" or $gorinfo['type'] == "select"){
					if($cvalue){
						
						
					$option = C::t('#hejin_forms#hejin_option')->fetch_by_id($cvalue);
					$config[$ckey] = $option['name'];
					}else{
						$config[$ckey] = "";
						}
					}elseif($gorinfo['type'] == "checkbox"){
						
					if($cvalue !=""){
						if(strstr($cvalue,',')){
							
							$cvaluess = explode(",", $cvalue);
							foreach ($cvaluess as $cvkey=>$cvalues)
							{
					$option = C::t('#hejin_forms#hejin_option')->fetch_by_id($cvalues);
					$cvaluecoi[]= $option['name'];
							}
					$config[$ckey] = implode(",",$cvaluecoi);
							
							}else{
								
					$option = C::t('#hejin_forms#hejin_option')->fetch_by_id($cvalue);
					$config[$ckey] = $option['name'];
								}
						
						
					}else{
						$config[$ckey] = "";
						}

						
						}elseif($gorinfo['type'] == "text" or $gorinfo['type'] == "textarea" or $gorinfo['type'] == "time"){
						$config[$ckey] = $cvalue;
						
						}elseif($gorinfo['type'] == "date"){

						$config[$ckey] = date('Y-m-d', $cvalue);
							
						}elseif($gorinfo['type'] == "dati"){

						$config[$ckey] = date('Y-m-d H:i:s', $cvalue);
							
						}elseif($gorinfo['type'] == "file"){
							if($cvalue){
								
								
						$url = '<a href="'.HEJIN_PATH.$cvalue.'" target="_blank">'.lang('plugin/hejin_forms', 'seeurl').'</a>';	
						$config[$ckey] = $url;
							}else{
							$config[$ckey]="";	
								}
							}
			}
		$valsu[$key]['config'] = $config;
		$valsu[$key]['state'] = $value['state'];
		$valsu[$key]['dateline'] = $value['dateline'];
		$valsu[$key]['ip'] = $value['ip'];
}


include template('hejin_forms:admin/member_value');

}
//WWW.moqu8.com
?>