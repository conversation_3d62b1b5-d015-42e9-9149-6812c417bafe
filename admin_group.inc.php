<?php
/*
 * 出处：魔趣吧
 * 官网: Www.moqu8.com
 * 备用网址: www.moqu8.com (请收藏备用!)
 * 技术支持/更新维护：QQ 1218894030
 * 
 */
if(!defined('IN_DISCUZ') || !defined('IN_ADMINCP')) {
	exit('Access Denied');
}
require_once DISCUZ_ROOT.'./source/plugin/hejin_forms/config.inc.php';
include_once ('function.func.php');
$pmodel = addslashes($_POST['model']);


if(submitcheck('groupaddsub')){
			if($pmodel=='add'){
			
			
			
				$config = fix_json($_POST['config']); //$_POST['config']为数组，转化为字符串

				 $post_add = array(
	       			 'name'  =>addslashes($_POST['name']),               
	       			 'state'  =>intval($_POST['state']),      
	        		'fid'  =>intval($_POST['fid']),        
	        		'sort'  =>intval($_POST['sort']),          
	        		'type'  =>addslashes($_POST['type']),        
	        		'dateline'  =>time(),              
	        		'description'  =>addslashes($_POST['description']),          
	        		'config'  =>$config,              
	        		'stat'  =>0,               
					);	
			
			$insertst =  C::t('#hejin_forms#hejin_group')->insert($post_add);
			
			if($insertst){
			 $url = 'action=plugins&identifier=hejin_forms&pmod=admin_group&model=list&formid='.intval($_POST['fid']);
			cpmsg(lang('plugin/hejin_forms', 'addok'), $url, 'succeed');	
				}


						}
	
	}


if(submitcheck('groupedit')){
	if($pmodel=='update'){
			$config = fix_json($_POST['config']);//$_POST['config']为数组，转化为字符串


		 $data= array();
	        $data['name']  =addslashes($_POST['name']);               
	         $data['state']   =intval($_POST['state']);                  
			 $data['description'] =addslashes($_POST['description']);         
			 $data['sort']  =intval($_POST['sort']);             
			 $data['type']  =addslashes($_POST['type']);               
			 $data['dateline']  =time();               
			 $data['config'] =$config;
				$id = intval($_POST['id']);
			$groupup =  C::t('#hejin_forms#hejin_group')->update_by_id($data,$id);
if($groupup){
			 $url = 'action=plugins&identifier=hejin_forms&pmod=admin_group&model=list&formid='.intval($_POST['fid']);
	    		cpmsg(lang('plugin/hejin_forms', 'editok'), $url, 'succeed');	
	
	}


	}
}





$model = addslashes($_GET['model']);

$gmodel = !empty($model) ? $model : 'list';


if($gmodel == 'list'){
	$fid = intval($_GET['formid']);
$groups = C::t('#hejin_forms#hejin_group')->fetch_by_fid($fid);
$form =  C::t('#hejin_forms#hejin_form')->fetch_by_id($fid);

include template('hejin_forms:admin/group_list');
	
	}

	elseif($gmodel == 'add'){
	$fid = intval($_GET['formid']);
$form = C::t('#hejin_forms#hejin_form')->fetch_by_id($fid);
		
include template('hejin_forms:admin/group_add');
		}
		
		
	elseif($gmodel == 'edit'){
	$gid = intval($_GET['groupid']);
	$fid = intval($_GET['fid']);
$group = C::t('#hejin_forms#hejin_group')->fetch_by_id($gid);
$config = fix_json($group['config']);
include template('hejin_forms:admin/group_edit');
		}

	elseif($gmodel == 'del'){
		$formid= intval($_GET['formid']);
		$groupid= intval($_GET['groupid']);
		if(!empty($groupid)){
	   $del= C::t('#hejin_forms#hejin_group')->delete_by_id($groupid);
	   if($del){
		$url = 'action=plugins&identifier=hejin_forms&pmod=admin_group&model=list&formid='.$formid;
			cpmsg(lang('plugin/hejin_forms', 'delok'), $url, 'succeed');	
		   }
		
			}
		}





?>