<?php

function fix_json( $data,$charset = 'utf-8'){
	
	if(  is_string( $data ) ){
		
		$data = preg_replace("/([\r\n\t\f])/"," ",$data);
		
		$temp = array();
		
		if( $charset == "gbk" ){
			$array = json_decode(iconv('GBK', 'UTF-8', $data),true);
			
			if( is_array($array) ){
				foreach ($array as $key => $val){
					$temp[ iconv('UTF-8',$charset, $key) ] = iconv('UTF-8', $charset, $val);
				}
			}
		}else{
			
			$temp = json_decode($data,true);	
		}
		
		
	}else{
		
		$temp = array();
				
		if( $charset == "gbk" && PHP_VERSION >= '5.2' ){				
			foreach ($data as $key => $val){
				$temp[ iconv($charset,'UTF-8', $key) ] = iconv( $charset,'UTF-8', $val);
			}
		}else{
			$temp = $data;			
		}
		
		$temp = json_encode($temp);
		
	}
	
	return $temp;
	
}


function GetIP(){
	if(!empty($_SERVER["HTTP_CLIENT_IP"])) $cip = $_SERVER["HTTP_CLIENT_IP"];
	else if(!empty($_SERVER["HTTP_X_FORWARDED_FOR"])) $cip = $_SERVER["HTTP_X_FORWARDED_FOR"];
	else if(!empty($_SERVER["REMOTE_ADDR"])) $cip = $_SERVER["REMOTE_ADDR"];
	else $cip = "";
	return $cip;
}

function format_json($code){
	global $Project;
	
	return preg_replace("#\\\u([0-9a-f]{4}+)#ie", "iconv('".VI_UCS."', '".$Project["charset"]."//IGNORE', pack('H4', '\\1'))", $code);	
}

function gbk2utf8($data){
if(is_array($data)){
return array_map('gbk2utf8', $data);
}
return iconv('gbk','utf-8',$data);
}
//WWW.moqu8.com
?>