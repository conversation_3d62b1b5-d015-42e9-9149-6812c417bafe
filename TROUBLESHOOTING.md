# 和金表单异步版本故障排除指南

## 🚨 前端显示空白问题

如果您遇到前端显示空白的问题，请按以下步骤排查：

### 1. 检查文件是否正确上传

确保以下文件已正确上传到插件目录：

```
hejin_forms/
├── api.inc.php                           ✓ 必须存在
├── js/hejin-forms-async-compatible.js   ✓ 必须存在
├── css/hejin-forms-async.css            ✓ 必须存在
├── template/form/show.htm                ✓ 已更新
└── hejin_forms.inc.php                   ✓ 已更新
```

### 2. 检查浏览器控制台错误

1. 按 `F12` 打开浏览器开发者工具
2. 切换到 `Console` 标签
3. 刷新页面，查看是否有错误信息
4. 常见错误及解决方案：

#### JavaScript 文件加载失败
```
GET http://yoursite.com/source/plugin/hejin_forms/js/hejin-forms-async-compatible.js 404
```
**解决方案：** 确认 JS 文件已上传到正确位置

#### CSS 文件加载失败
```
GET http://yoursite.com/source/plugin/hejin_forms/css/hejin-forms-async.css 404
```
**解决方案：** 确认 CSS 文件已上传到正确位置

#### 语法错误
```
SyntaxError: Unexpected token
```
**解决方案：** 使用兼容版本的 JavaScript 文件

### 3. 使用调试工具

访问以下调试页面检查问题：

1. **基本调试：** `debug.php`
2. **兼容性测试：** `test_compatibility.html`
3. **表单测试：** `test_form.html`

### 4. 检查 PHP 错误

如果页面完全空白，可能是 PHP 错误：

1. 检查 PHP 错误日志
2. 临时启用错误显示：
   ```php
   error_reporting(E_ALL);
   ini_set('display_errors', 1);
   ```

### 5. 常见问题及解决方案

#### 问题：表单不显示
**可能原因：**
- 数据库连接失败
- 表单ID不存在
- 权限不足

**解决方案：**
1. 检查数据库连接
2. 确认表单ID正确
3. 检查用户权限

#### 问题：异步功能不工作
**可能原因：**
- JavaScript 文件未加载
- 浏览器不支持某些功能
- API 接口无法访问

**解决方案：**
1. 使用兼容版本 JS 文件
2. 检查浏览器兼容性
3. 测试 API 接口

#### 问题：文件上传失败
**可能原因：**
- 上传目录权限不足
- 文件大小超限
- 文件类型不支持

**解决方案：**
1. 设置目录权限为 755 或 777
2. 检查 PHP 上传限制
3. 确认文件类型在允许列表中

### 6. 回退到原版本

如果问题无法解决，可以回退到原版本：

1. 从备份目录恢复原文件：
   ```bash
   cp backup_*/hejin_forms.inc.php ./
   cp backup_*/template/form/show.htm ./template/form/
   ```

2. 删除新增的文件：
   ```bash
   rm api.inc.php
   rm js/hejin-forms-async*.js
   rm css/hejin-forms-async.css
   ```

### 7. 逐步启用功能

为了更好地排查问题，可以逐步启用功能：

#### 步骤 1：仅启用基本异步功能
1. 只上传 `api.inc.php`
2. 使用简化版本的模板

#### 步骤 2：添加样式
1. 上传 `css/hejin-forms-async.css`
2. 在模板中引入 CSS

#### 步骤 3：添加 JavaScript
1. 上传 `js/hejin-forms-async-compatible.js`
2. 在模板中引入 JS

#### 步骤 4：测试各项功能
1. 测试表单提交
2. 测试字段验证
3. 测试文件上传

### 8. 获取技术支持

如果问题仍然存在，请提供以下信息：

1. **环境信息：**
   - Discuz 版本
   - PHP 版本
   - 浏览器版本
   - 服务器类型

2. **错误信息：**
   - 浏览器控制台错误
   - PHP 错误日志
   - 具体错误截图

3. **调试结果：**
   - `debug.php` 的输出结果
   - `test_compatibility.html` 的测试结果

### 9. 预防措施

为避免类似问题，建议：

1. **备份数据：** 升级前务必备份
2. **测试环境：** 先在测试环境验证
3. **分步部署：** 逐步启用新功能
4. **监控日志：** 定期检查错误日志

### 10. 快速修复脚本

创建一个快速修复脚本 `quick_fix.php`：

```php
<?php
// 快速检查和修复常见问题
echo "正在检查文件...";

$files = [
    'api.inc.php',
    'js/hejin-forms-async-compatible.js',
    'css/hejin-forms-async.css'
];

foreach ($files as $file) {
    if (!file_exists($file)) {
        echo "缺失文件: $file\n";
    } else {
        echo "文件正常: $file\n";
    }
}

// 检查权限
$dirs = ['js', 'css', 'template', 'data/uploads/submit'];
foreach ($dirs as $dir) {
    if (is_writable($dir)) {
        echo "权限正常: $dir\n";
    } else {
        echo "权限异常: $dir\n";
    }
}
?>
```

---

## 📞 联系支持

如果以上方法都无法解决问题，请：

1. 运行所有调试工具
2. 收集错误信息
3. 准备环境详情
4. 联系技术支持

记住：大多数问题都是由文件路径、权限或浏览器兼容性引起的，按照本指南逐步排查通常能解决问题。
