<?php
/**
 * 和金表单异步版本安装脚本
 * 用于自动安装和配置异步功能
 */

// 检查是否在Discuz环境中运行
if (!defined('IN_DISCUZ')) {
    define('IN_DISCUZ', true);
    // 尝试包含Discuz配置文件
    $discuz_paths = [
        '../../../config/config_global.php',
        '../../config/config_global.php',
        '../config/config_global.php',
        'config/config_global.php'
    ];
    
    $discuz_found = false;
    foreach ($discuz_paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            $discuz_found = true;
            break;
        }
    }
    
    if (!$discuz_found) {
        die('错误：无法找到Discuz配置文件，请确保在正确的目录中运行此脚本。');
    }
}

class HejinFormsAsyncInstaller {
    
    private $pluginPath;
    private $backupPath;
    private $errors = [];
    private $warnings = [];
    private $success = [];
    
    public function __construct() {
        $this->pluginPath = dirname(__FILE__);
        $this->backupPath = $this->pluginPath . '/backup_' . date('Y-m-d_H-i-s');
    }
    
    /**
     * 运行安装程序
     */
    public function install() {
        echo "<h1>和金表单异步版本安装程序</h1>\n";
        
        // 检查环境
        $this->checkEnvironment();
        
        // 备份原文件
        $this->backupFiles();
        
        // 检查文件权限
        $this->checkPermissions();
        
        // 验证文件完整性
        $this->validateFiles();
        
        // 显示结果
        $this->showResults();
        
        // 如果没有错误，显示下一步说明
        if (empty($this->errors)) {
            $this->showNextSteps();
        }
    }
    
    /**
     * 检查环境
     */
    private function checkEnvironment() {
        echo "<h2>环境检查</h2>\n";
        
        // 检查PHP版本
        if (version_compare(PHP_VERSION, '5.6.0', '>=')) {
            $this->success[] = "PHP版本: " . PHP_VERSION . " (符合要求)";
        } else {
            $this->errors[] = "PHP版本过低: " . PHP_VERSION . " (需要5.6.0或更高)";
        }
        
        // 检查必要的PHP扩展
        $required_extensions = ['json', 'mbstring'];
        foreach ($required_extensions as $ext) {
            if (extension_loaded($ext)) {
                $this->success[] = "PHP扩展 {$ext}: 已安装";
            } else {
                $this->errors[] = "PHP扩展 {$ext}: 未安装";
            }
        }
        
        // 检查Discuz版本
        if (defined('DISCUZ_VERSION')) {
            if (version_compare(DISCUZ_VERSION, 'X3.2', '>=')) {
                $this->success[] = "Discuz版本: " . DISCUZ_VERSION . " (兼容)";
            } else {
                $this->warnings[] = "Discuz版本: " . DISCUZ_VERSION . " (可能不完全兼容)";
            }
        } else {
            $this->warnings[] = "无法检测Discuz版本";
        }
    }
    
    /**
     * 备份原文件
     */
    private function backupFiles() {
        echo "<h2>备份原文件</h2>\n";
        
        $files_to_backup = [
            'hejin_forms.inc.php',
            'template/form/show.htm'
        ];
        
        if (!is_dir($this->backupPath)) {
            if (mkdir($this->backupPath, 0755, true)) {
                $this->success[] = "创建备份目录: {$this->backupPath}";
            } else {
                $this->errors[] = "无法创建备份目录: {$this->backupPath}";
                return;
            }
        }
        
        foreach ($files_to_backup as $file) {
            $source = $this->pluginPath . '/' . $file;
            $backup = $this->backupPath . '/' . $file;
            
            if (file_exists($source)) {
                $backup_dir = dirname($backup);
                if (!is_dir($backup_dir)) {
                    mkdir($backup_dir, 0755, true);
                }
                
                if (copy($source, $backup)) {
                    $this->success[] = "备份文件: {$file}";
                } else {
                    $this->errors[] = "备份失败: {$file}";
                }
            } else {
                $this->warnings[] = "文件不存在，跳过备份: {$file}";
            }
        }
    }
    
    /**
     * 检查文件权限
     */
    private function checkPermissions() {
        echo "<h2>权限检查</h2>\n";
        
        $directories_to_check = [
            $this->pluginPath,
            $this->pluginPath . '/js',
            $this->pluginPath . '/css',
            $this->pluginPath . '/template/form',
            $this->pluginPath . '/data/uploads/submit'
        ];
        
        foreach ($directories_to_check as $dir) {
            if (is_dir($dir)) {
                if (is_writable($dir)) {
                    $this->success[] = "目录可写: {$dir}";
                } else {
                    $this->warnings[] = "目录不可写: {$dir} (可能影响功能)";
                }
            } else {
                if (mkdir($dir, 0755, true)) {
                    $this->success[] = "创建目录: {$dir}";
                } else {
                    $this->errors[] = "无法创建目录: {$dir}";
                }
            }
        }
    }
    
    /**
     * 验证文件完整性
     */
    private function validateFiles() {
        echo "<h2>文件完整性检查</h2>\n";
        
        $required_files = [
            'api.inc.php',
            'js/hejin-forms-async.js',
            'css/hejin-forms-async.css',
            'README_ASYNC.md',
            'test_compatibility.html'
        ];
        
        foreach ($required_files as $file) {
            $filepath = $this->pluginPath . '/' . $file;
            if (file_exists($filepath)) {
                $size = filesize($filepath);
                if ($size > 0) {
                    $this->success[] = "文件完整: {$file} ({$size} bytes)";
                } else {
                    $this->errors[] = "文件为空: {$file}";
                }
            } else {
                $this->errors[] = "文件缺失: {$file}";
            }
        }
        
        // 检查模板文件是否包含异步功能
        $template_file = $this->pluginPath . '/template/form/show.htm';
        if (file_exists($template_file)) {
            $content = file_get_contents($template_file);
            if (strpos($content, 'hejin-forms-async.js') !== false) {
                $this->success[] = "模板文件已包含异步功能";
            } else {
                $this->warnings[] = "模板文件可能未正确更新";
            }
        }
    }
    
    /**
     * 显示结果
     */
    private function showResults() {
        echo "<h2>安装结果</h2>\n";
        
        if (!empty($this->success)) {
            echo "<h3 style='color: green;'>成功项目:</h3>\n<ul>\n";
            foreach ($this->success as $item) {
                echo "<li style='color: green;'>✓ {$item}</li>\n";
            }
            echo "</ul>\n";
        }
        
        if (!empty($this->warnings)) {
            echo "<h3 style='color: orange;'>警告项目:</h3>\n<ul>\n";
            foreach ($this->warnings as $item) {
                echo "<li style='color: orange;'>⚠ {$item}</li>\n";
            }
            echo "</ul>\n";
        }
        
        if (!empty($this->errors)) {
            echo "<h3 style='color: red;'>错误项目:</h3>\n<ul>\n";
            foreach ($this->errors as $item) {
                echo "<li style='color: red;'>✗ {$item}</li>\n";
            }
            echo "</ul>\n";
        }
    }
    
    /**
     * 显示下一步说明
     */
    private function showNextSteps() {
        echo "<h2>安装完成！</h2>\n";
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>\n";
        echo "<h3>下一步操作:</h3>\n";
        echo "<ol>\n";
        echo "<li>访问 <a href='test_compatibility.html' target='_blank'>兼容性测试页面</a> 检查浏览器兼容性</li>\n";
        echo "<li>测试表单功能是否正常工作</li>\n";
        echo "<li>如有问题，可以从备份目录恢复原文件: <code>{$this->backupPath}</code></li>\n";
        echo "<li>阅读 <a href='README_ASYNC.md' target='_blank'>详细说明文档</a></li>\n";
        echo "</ol>\n";
        echo "</div>\n";
        
        echo "<h3>新功能说明:</h3>\n";
        echo "<ul>\n";
        echo "<li><strong>异步表单提交:</strong> 无需刷新页面即可提交表单</li>\n";
        echo "<li><strong>实时验证:</strong> 输入时即时验证字段内容</li>\n";
        echo "<li><strong>拖拽上传:</strong> 支持拖拽文件到上传区域</li>\n";
        echo "<li><strong>草稿保存:</strong> 自动保存表单内容，防止数据丢失</li>\n";
        echo "<li><strong>键盘快捷键:</strong> Ctrl+S保存草稿，Ctrl+Enter提交表单</li>\n";
        echo "</ul>\n";
    }
}

// 运行安装程序
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    ?>
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>和金表单异步版本安装</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
            h1, h2, h3 { color: #333; }
            .warning { background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0; }
            .button { background: #007cba; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0; }
            .button:hover { background: #005a87; }
            code { background: #f1f1f1; padding: 2px 6px; border-radius: 3px; }
        </style>
    </head>
    <body>
        <h1>和金表单异步版本安装程序</h1>
        
        <div class="warning">
            <h3>⚠️ 安装前请注意:</h3>
            <ul>
                <li>此操作将修改现有的表单文件</li>
                <li>建议先在测试环境中验证</li>
                <li>安装程序会自动备份原文件</li>
                <li>确保有足够的磁盘空间</li>
            </ul>
        </div>
        
        <h2>安装内容:</h2>
        <ul>
            <li>异步API接口 (api.inc.php)</li>
            <li>现代JavaScript库 (js/hejin-forms-async.js)</li>
            <li>响应式CSS样式 (css/hejin-forms-async.css)</li>
            <li>更新的表单模板</li>
            <li>兼容性测试工具</li>
        </ul>
        
        <a href="?install=1" class="button">开始安装</a>
        <a href="README_ASYNC.md" class="button" style="background: #28a745;">查看说明文档</a>
    </body>
    </html>
    <?php
} else if (isset($_GET['install'])) {
    echo "<!DOCTYPE html><html><head><meta charset='UTF-8'><title>安装进度</title></head><body>";
    echo "<style>body{font-family:Arial,sans-serif;max-width:800px;margin:0 auto;padding:20px;}h1,h2,h3{color:#333;}ul{margin:10px 0;}li{margin:5px 0;}</style>";
    
    $installer = new HejinFormsAsyncInstaller();
    $installer->install();
    
    echo "</body></html>";
}
?>
