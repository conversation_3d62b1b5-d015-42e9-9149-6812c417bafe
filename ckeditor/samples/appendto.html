<!DOCTYPE html>
<!--
Copyright (c) 2003-2014, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md 
-->
<html>
<head>
	<meta charset="utf-8">
	<title>CKEDITOR.appendTo &mdash; CKEditor Sample</title>
	<script src="../ckeditor.js"></script>
	<link rel="stylesheet" href="sample.css">
</head>
<body>
	<h1 class="samples">
		<a href="index.html">CKEditor Samples</a> &raquo; Append To Page Element Using JavaScript Code
	</h1>
	<div id="section1">
		<div class="description">
			<p>
				<code>CKEDITOR.appendTo</code> is basically to place editors
				inside existing DOM elements. Unlike <code>CKEDITOR.replace</code>,
				a target container to be replaced is no longer necessary. A new editor
				instance is inserted directly wherever it is desired.
			</p>
<pre class="samples">CKEDITOR.appendTo( '<em>container_id</em>',
	{ /* Configuration options to be used. */ }
	'Editor content to be used.'
);</pre>
		</div>
		<script>

			// This call can be placed at any point after the
			// <textarea>, or inside a <head><script> in a
			// window.onload event handler.

			// Replace the <textarea id="editor"> with an CKEditor
			// instance, using default configurations.
			CKEDITOR.appendTo( 'section1',
				null,
				'<p>This is some <strong>sample text</strong>. You are using <a href="">CKEditor</a>.</p>'
			);

		</script>
	</div>
	<br>
	<div id="footer">
		<hr>
		<p>
			CKEditor - The text editor for the Internet - <a class="samples" href=""></a>
		</p>
		<p id="copy">
			Copyright &copy; 2003-2014, <a class="samples" href="">CKSource</a> - Frederico
			Knabben. All rights reserved.
		</p>
	</div>
</body>
</html>
