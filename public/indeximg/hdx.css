article, aside, details, figcaption, figure, footer, header, hgroup, main, nav, section, summary {
	display:block
}
audio, canvas, video {
	display:inline-block
}
audio:not([controls]) {
	display:none;
	height:0
}
[hidden], template {
display:none
}
html {
	font-family:sans-serif;
	-ms-text-size-adjust:100%;
	-webkit-text-size-adjust:100%
}
body {
	margin:0
}
a {
	background:0 0
}
a:active, a:hover {
	outline:0
}
h1 {
	margin:.67em 0
}
abbr[title] {
	border-bottom:1px dotted
}
b, strong {
	font-weight:700
}
dfn {
	font-style:italic
}
hr {
	height:0;
	-moz-box-sizing:content-box;
	box-sizing:content-box
}
mark {
	color:#000;
	background:#ff0
}
code, kbd, pre, samp {
	font-family:monospace, serif;
	font-size:1em
}
pre {
	white-space:pre-wrap
}
q {
	quotes:"\201C" "\201D" "\2018" "\2019"
}
img {
	border:0
}
svg:not(:root) {
	overflow:hidden
}
figure {
	margin:0
}
button, input, select, textarea {
	margin:0
}
button, select {
	text-transform:none
}
button, html input[type=button], input[type=reset], input[type=submit] {
	-webkit-appearance:button;
	cursor:pointer
}
button[disabled], html input[disabled] {
	cursor:default
}
input[type=checkbox], input[type=radio] {
	box-sizing:border-box;
	padding:0
}
input[type=search] {
	-webkit-appearance:textfield
}
input[type=search]::-webkit-search-cancel-button, input[type=search]::-webkit-search-decoration {
-webkit-appearance:none
}
button::-moz-focus-inner, input::-moz-focus-inner {
padding:0;
border:0
}
textarea {
	overflow:auto;
	vertical-align:top
}
table {
	border-spacing:0;
	border-collapse:collapse
}
html {
	font-size:62.5%;
	-webkit-tap-highlight-color:rgba(0,0,0,0)
}
body {
	font-family:"Helvetica Neue", Helvetica, Arial, sans-serif;
	font-size:12px;
	line-height:1.42857143;
	color:#333;
	background-color:#fff
}
button, input, select, textarea {
	font-family:inherit;
	font-size:inherit;
	line-height:inherit
}
a {
	color:#2578bf;
	text-decoration:none
}
a:focus, a:hover {
	color:#00a0e9;
	text-decoration:underline
}
a:focus {
	outline:5px auto -webkit-focus-ring-color;
	outline-offset:-2px
}
img {
	vertical-align:middle
}
.img-responsive {
	display:block;
	max-width:100%;
	height:auto
}
.img-circle {
	border-radius:50%
}
hr {
	margin-top:17px;
	margin-bottom:17px;
	border:0;
	border-top:1px solid #a0a0a0
}
.sr-only {
	position:absolute;
	width:1px;
	height:1px;
	padding:0;
	margin:-1px;
	overflow:hidden;
	clip:rect(0 0 0 0);
	border:0
}
.container {
	width:960px;
	margin-right:auto;
	margin-left:auto
}
.header .container, .main_nav .container {
	position:relative
}
.header .logo, .main_nav .logo {
	float:left
}
.header .sign, .main_nav .sign {
	position:absolute;
	right:0
}
.header .sign b, .main_nav .sign b {
	display:inline-block;
	width:1px;
	height:13px;
	margin:0 7px;
	vertical-align:middle;
*display:inline;
*zoom:1
}
.header .user, .main_nav .user {
	position:absolute;
	right:0;
	min-width:116px;
	max-width:128px
}
.header .user>a, .main_nav .user>a {
	display:block;
	padding:0 7px;
	overflow:hidden;
	text-overflow:ellipsis;
	white-space:nowrap
}
.header .user .face, .main_nav .user .face {
	border-radius:2px;
	-webkit-box-shadow:1px 1px 1px rgba(101,117,60,.75);
	box-shadow:1px 1px 1px rgba(101,117,60,.75)
}
.header .user a:focus, .header .user a:hover, .main_nav .user a:focus, .main_nav .user a:hover {
	text-decoration:none
}
.header .user li a, .main_nav .user li a {
	padding-left:45px;
*padding-left:0;
*text-align:center
}
.header .user li a:before, .main_nav .user li a:before {
	float:left;
	margin-top:12px;
	margin-left:-32px;
	content:''
}
.header .search input, .main_nav .search input {
	height:16px;
	color:#333;
	vertical-align:middle;
	border:0 none
}
.header .search input:focus, .main_nav .search input:focus {
	outline:0
}
.header {
	height:80px;
*position:relative;
*z-index:1010
}
.header .logo {
	margin-top:11px
}
.header .sign {
	top:32px
}
.header .sign b {
	background:#333
}
.header .sign a {
	color:#333
}
.header .search {
	position:absolute;
	top:21px;
	left:175px;
	padding-right:68px;
	border:2px solid #62b651
}
.header .search a {
	position:absolute;
	top:-2px;
	right:-2px;
	width:70px;
	height:40px;
	font-family:Microsoft Yahei;
	font-size:18px;
	line-height:40px;
	color:#fff;
	text-align:center;
	text-indent:.27777778em;
	letter-spacing:.27777778em;
	cursor:pointer;
	background:#62b651;
*height:41px
}
.header .search a:active, .header .search a:focus, .header .search a:hover {
	text-decoration:none;
	background:#3fa039
}
.header .search input {
	width:456px;
	padding:10px
}
.header .search input:-moz-placeholder {
color:#b5b5b5
}
.header .search input::-moz-placeholder {
color:#b5b5b5
}
.header .search input:-ms-input-placeholder {
color:#b5b5b5
}
.header .search input::-webkit-input-placeholder {
color:#b5b5b5
}
.header .user {
	top:22px
}
.header .user>a {
	line-height:39px;
	color:#62b651
}
.main_nav, .main_nav_index {
	background-color:#62b651
}
.main_nav .items>li, .main_nav_index .items>li {
	float:left
}
.main_nav .items>li>a, .main_nav_index .items>li>a {
	display:block;
	color:#e4ffdf;
	text-decoration:none;
	text-shadow:0 1px 1px #529a44
}
.main_nav .items>li>a.current, .main_nav .items>li>a.current:active, .main_nav .items>li>a.current:focus, .main_nav .items>li>a.current:hover, .main_nav_index .items>li>a.current, .main_nav_index .items>li>a.current:active, .main_nav_index .items>li>a.current:focus, .main_nav_index .items>li>a.current:hover {
	color:#fff;
	background-color:#539b45
}
.main_nav .items>li>a:active, .main_nav .items>li>a:focus, .main_nav .items>li>a:hover, .main_nav_index .items>li>a:active, .main_nav_index .items>li>a:focus, .main_nav_index .items>li>a:hover {
	color:#fff;
	background-color:#59a64a
}
.main_nav .items>li:first-child>a, .main_nav_index .items>li:first-child>a {
	text-indent:.3em;
	letter-spacing:.3em
}
.main_nav .items .dropdown2-white .dropdown2-menu, .main_nav_index .items .dropdown2-white .dropdown2-menu {
	width:383px;
	padding-top:10px;
	padding-right:20px;
	padding-left:10px;
	font-family:"Helvetica Neue", Helvetica, Arial, sans-serif;
	font-size:14px;
	filter:alpha(opacity=95);
	border:1px solid #62b651;
	border-radius:0;
	-webkit-box-shadow:0 1px 5px rgba(0,0,0,.5);
	box-shadow:0 1px 5px rgba(0,0,0,.5);
	opacity:.95;
	-ms-filter:"alpha(Opacity=95)"
}
.main_nav .items .dropdown2-white .dropdown2-menu strong, .main_nav_index .items .dropdown2-white .dropdown2-menu strong {
	float:left;
	margin-left:-42px;
	color:#62b651
}
.main_nav .items .dropdown2-white .dropdown2-menu strong:before, .main_nav_index .items .dropdown2-white .dropdown2-menu strong:before {
	position:absolute;
	margin-top:6px;
	margin-left:-20px;
	content:''
}
.main_nav .items .dropdown2-white .dropdown2-menu sup, .main_nav_index .items .dropdown2-white .dropdown2-menu sup {
	position:absolute;
	margin-top:-5px;
	font:0/0 a;
	color:red;
	color:transparent;
	text-shadow:none;
	background-color:transparent;
	border:0
}
.main_nav .items .dropdown2-white .dropdown2-menu>div, .main_nav_index .items .dropdown2-white .dropdown2-menu>div {
	padding-top:20px;
	padding-left:78px;
	border-top:1px dashed #e5e5e5;
*padding-bottom:20px
}
.main_nav .items .dropdown2-white .dropdown2-menu>div:first-child, .main_nav_index .items .dropdown2-white .dropdown2-menu>div:first-child {
	border-top:none
}
.main_nav .items .dropdown2-white .dropdown2-menu>div div, .main_nav_index .items .dropdown2-white .dropdown2-menu>div div {
	float:left;
	padding-right:10px;
	padding-left:10px;
	margin-bottom:20px;
	margin-left:-1px;
	border-right:1px solid #c9c9c9;
	border-left:1px solid #c9c9c9;
*white-space:nowrap
}
.main_nav .items .dropdown2-white .dropdown2-menu>div div a, .main_nav_index .items .dropdown2-white .dropdown2-menu>div div a {
	color:#3e443c
}
.main_nav .items .dropdown2-white .dropdown2-menu>div div a:focus, .main_nav .items .dropdown2-white .dropdown2-menu>div div a:hover, .main_nav_index .items .dropdown2-white .dropdown2-menu>div div a:focus, .main_nav_index .items .dropdown2-white .dropdown2-menu>div div a:hover {
	color:#2578bf
}
.main_nav .dropdown2>a, .main_nav_index .dropdown2>a {
	border:none;
	border-radius:0
}
.main_nav .dropdown2.open>a, .main_nav_index .dropdown2.open>a {
	color:#fff;
	background:#59a64a
}
.main_nav {
	position:fixed;
	top:0;
	right:0;
	left:0;
	z-index:1030;
	height:45px
}
.main_nav .logo {
	margin-top:9px
}
.main_nav .items {
	float:left;
	margin-left:15px;
	font-size:16px
}
.main_nav .items>li>a {
	padding:0 10px;
	line-height:45px
}
.main_nav .items .dropdown2 .dropdown2-menu {
	border-radius:0
}
.main_nav .items .dropdown2 a {
	padding-left:10px;
	border-radius:0
}
.main_nav .search {
	position:relative;
	float:left;
	margin-top:8px;
	margin-left:20px
}
.main_nav .search .icon-search {
	position:absolute;
	top:7px;
	right:7px;
	background-color:transparent;
	border:none
}
.main_nav .search input {
	width:168px;
	padding:7px 32px 7px 10px;
	background:#e4efe2;
	border-radius:2px;
	-webkit-box-shadow:inset 0 1px 0 #539b45;
	box-shadow:inset 0 1px 0 #539b45
}
.main_nav .search input:focus {
	background:#fff
}
.main_nav .sign {
	top:12px
}
.main_nav .sign a, .main_nav .sign b {
	color:#fff
}
.main_nav .sign b {
	background:#fff
}
.main_nav .user>a {
	line-height:44px;
	color:#fff
}
.main_nav_index {
	height:48px;
	border-bottom:1px solid #539b45;
*position:relative;
*z-index:1000
}
.main_nav_index .items {
	font-family:Microsoft Yahei;
	font-size:18px
}
.main_nav_index .items>li>a {
	padding:0 20px;
	line-height:48px
}
.main_nav_index+#container {
	padding-top:40px
}
#container, #intro-1, #intro-2, #intro-3, #intro-4, #special-topic, .intro-container, .map-details, .not-found {
	padding-top:85px
}
#container .article {
	float:left;
	width:685px
}
#container .aside {
	float:right;
	width:240px
}
#container .article>h2, #container .aside>h2, #container>h2 {
	padding-bottom:12px;
	margin:0;
	font-weight:400;
	border-bottom:1px solid #f4f7fa
}
#container .article>h2.no-line, #container .aside>h2.no-line, #container>h2.no-line {
	border-bottom:none
}
#container .article>h2 .subhead, #container .aside>h2 .subhead, #container>h2 .subhead {
	margin-left:10px;
	font-size:12px
}
.page-footer {
	padding-top:10px;
	margin-top:20px;
	background:#f4f7fa
}
.page-footer h4 {
	font-size:12px;
	font-weight:700;
	color:#707070
}
.page-footer a {
	color:#889eaf
}
.page-footer a:hover {
	color:#2578bf;
	text-decoration:none
}
.page-footer .links, .page-footer .links>ul>li, .page-footer .sns, .page-footer .sns>ul>li {
	float:left
}
.page-footer .links {
	position:relative;
	margin-right:100px;
	margin-bottom:15px;
*border-right:1px solid #dee2e5
}
.page-footer .links:after {
	position:absolute;
	right:0;
	width:1px;
	height:100px;
	margin-top:12px;
	content:'';
	background:#dee2e5
}
.page-footer .links li ul {
	margin-right:90px
}
.page-footer .links li li {
	margin-bottom:5px
}
.page-footer .sns {
	float:left;
	width:240px;
*width:330px
}
.page-footer .sns li {
	margin-right:30px;
	margin-bottom:5px
}
.page-footer .sns div {
	position:absolute;
	margin-top:-66px;
	margin-left:110px;
	color:#889eaf
}
.page-footer .sns div img {
	display:block;
	margin-left:-10px
}
.page-footer .ft {
	clear:both;
	line-height:28px;
	color:#c9c9c9;
	text-align:center;
	border-top:1px solid #dee2e5
}
p {
	margin:0 0 8.5px
}
.lead {
	margin-bottom:17px;
	font-size:13px;
	font-weight:200;
	line-height:1.4
}
@media (min-width:768px) {
.lead {
	font-size:18px
}
}
.small, small {
	font-size:85%
}
cite {
	font-style:normal
}
.text-muted {
	color:#7d7d7d
}
.text-primary {
	color:#62b651
}
a.text-primary:hover {
	color:#4d953f
}
.text-success {
	color:#468847
}
a.text-success:hover {
	color:#356635
}
.text-warning {
	color:#f88a48
}
a.text-warning:hover {
	color:#f66b17
}
.text-danger {
	color:#b94a48
}
a.text-danger:hover {
	color:#953b39
}
.text-info {
	color:#2578bf
}
.bg-primary {
	color:#fff;
	background-color:#62b651
}
a.bg-primary:hover {
	background-color:#4d953f
}
.bg-success {
	background-color:#dff0d8
}
a.bg-success:hover {
	background-color:#c1e2b3
}
.bg-info {
	background-color:#d9edf7
}
a.bg-info:hover {
	background-color:#afd9ee
}
.bg-warning {
	background-color:#fff9f8
}
a.bg-warning:hover {
	background-color:#ffcdc5
}
.bg-danger {
	background-color:#f2dede
}
a.bg-danger:hover {
	background-color:#e4b9b9
}
.text-left {
	text-align:left
}
.text-right {
	text-align:right
}
.text-center {
	text-align:center
}
.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6 {
	font-family:"Helvetica Neue", Helvetica, Arial, sans-serif;
	font-weight:500;
	line-height:1.1;
	color:inherit
}
.h1 .small, .h1 small, .h2 .small, .h2 small, .h3 .small, .h3 small, .h4 .small, .h4 small, .h5 .small, .h5 small, .h6 .small, .h6 small, h1 .small, h1 small, h2 .small, h2 small, h3 .small, h3 small, h4 .small, h4 small, h5 .small, h5 small, h6 .small, h6 small {
	font-weight:400;
	line-height:1;
	color:#7d7d7d
}
h1, h2, h3 {
	margin-top:17px;
	margin-bottom:8.5px
}
h1 .small, h1 small, h2 .small, h2 small, h3 .small, h3 small {
	font-size:65%
}
h4, h5, h6 {
	margin-top:8.5px;
	margin-bottom:8.5px
}
h4 .small, h4 small, h5 .small, h5 small, h6 .small, h6 small {
	font-size:75%
}
.h1, h1 {
	font-size:31px
}
.h2, h2 {
	font-size:25px
}
.h3, h3 {
	font-size:21px
}
.h4, h4 {
	font-size:15px
}
.h5, h5 {
	font-size:12px
}
.h6, h6 {
	font-size:11px
}
.page-header {
	padding-bottom:7.5px;
	margin:34px 0 17px;
	border-bottom:1px solid #a0a0a0
}
ol, ul {
	margin-top:0
}
ol ol, ol ul, ul ol, ul ul {
	margin-bottom:0
}
.list-inline, .list-unstyled {
	padding-left:0;
	list-style:none
}
.list-inline>li {
	display:inline-block;
	padding-right:5px;
	padding-left:5px
}
.list-inline>li:first-child {
	padding-left:0
}
table {
	max-width:100%;
	background-color:transparent
}
th {
	text-align:left
}
.table {
	width:100%;
	margin-bottom:17px
}
.table>tbody>tr>td, .table>tbody>tr>th, .table>tfoot>tr>td, .table>tfoot>tr>th, .table>thead>tr>td, .table>thead>tr>th {
	padding:8px;
	line-height:1.42857143;
	vertical-align:top;
	border-top:1px solid #ddd
}
.table>thead>tr>th {
	vertical-align:bottom;
	border-bottom:2px solid #ddd
}
.table>caption+thead>tr:first-child>td, .table>caption+thead>tr:first-child>th, .table>colgroup+thead>tr:first-child>td, .table>colgroup+thead>tr:first-child>th, .table>thead:first-child>tr:first-child>td, .table>thead:first-child>tr:first-child>th {
	border-top:0
}
.table>tbody+tbody {
	border-top:2px solid #ddd
}
.table .table {
	background-color:#fff
}
.table-condensed>tbody>tr>td, .table-condensed>tbody>tr>th, .table-condensed>tfoot>tr>td, .table-condensed>tfoot>tr>th, .table-condensed>thead>tr>td, .table-condensed>thead>tr>th {
	padding:5px
}
.table-bordered, .table-bordered>tbody>tr>td, .table-bordered>tbody>tr>th, .table-bordered>tfoot>tr>td, .table-bordered>tfoot>tr>th, .table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
	border:1px solid #ddd
}
.table-bordered>thead>tr>td, .table-bordered>thead>tr>th {
	border-bottom-width:2px
}
.table-striped>tbody>tr:nth-child(odd)>td, .table-striped>tbody>tr:nth-child(odd)>th {
	background-color:#f9f9f9
}
.table-hover>tbody>tr:hover>td, .table-hover>tbody>tr:hover>th {
	background-color:#f5f5f5
}
table col[class*=col-] {
	display:table-column;
	float:none
}
table td[class*=col-], table th[class*=col-] {
	display:table-cell;
	float:none
}
.table>tbody>tr.active>td, .table>tbody>tr.active>th, .table>tbody>tr>td.active, .table>tbody>tr>th.active, .table>tfoot>tr.active>td, .table>tfoot>tr.active>th, .table>tfoot>tr>td.active, .table>tfoot>tr>th.active, .table>thead>tr.active>td, .table>thead>tr.active>th, .table>thead>tr>td.active, .table>thead>tr>th.active {
	background-color:#f5f5f5
}
.table>tbody>tr.success>td, .table>tbody>tr.success>th, .table>tbody>tr>td.success, .table>tbody>tr>th.success, .table>tfoot>tr.success>td, .table>tfoot>tr.success>th, .table>tfoot>tr>td.success, .table>tfoot>tr>th.success, .table>thead>tr.success>td, .table>thead>tr.success>th, .table>thead>tr>td.success, .table>thead>tr>th.success {
	background-color:#dff0d8
}
.table-hover>tbody>tr.success:hover>td, .table-hover>tbody>tr.success:hover>th, .table-hover>tbody>tr>td.success:hover, .table-hover>tbody>tr>th.success:hover {
	background-color:#d0e9c6
}
.table>tbody>tr.danger>td, .table>tbody>tr.danger>th, .table>tbody>tr>td.danger, .table>tbody>tr>th.danger, .table>tfoot>tr.danger>td, .table>tfoot>tr.danger>th, .table>tfoot>tr>td.danger, .table>tfoot>tr>th.danger, .table>thead>tr.danger>td, .table>thead>tr.danger>th, .table>thead>tr>td.danger, .table>thead>tr>th.danger {
	background-color:#f2dede
}
.table-hover>tbody>tr.danger:hover>td, .table-hover>tbody>tr.danger:hover>th, .table-hover>tbody>tr>td.danger:hover, .table-hover>tbody>tr>th.danger:hover {
	background-color:#ebcccc
}
.table>tbody>tr.warning>td, .table>tbody>tr.warning>th, .table>tbody>tr>td.warning, .table>tbody>tr>th.warning, .table>tfoot>tr.warning>td, .table>tfoot>tr.warning>th, .table>tfoot>tr>td.warning, .table>tfoot>tr>th.warning, .table>thead>tr.warning>td, .table>thead>tr.warning>th, .table>thead>tr>td.warning, .table>thead>tr>th.warning {
	background-color:#fff9f8
}
.table-hover>tbody>tr.warning:hover>td, .table-hover>tbody>tr.warning:hover>th, .table-hover>tbody>tr>td.warning:hover, .table-hover>tbody>tr>th.warning:hover {
	background-color:#ffe3df
}
@media (max-width:767px) {
.table-responsive {
	width:100%;
	margin-bottom:12.75px;
	overflow-x:scroll;
	overflow-y:hidden;
	-webkit-overflow-scrolling:touch;
	-ms-overflow-style:-ms-autohiding-scrollbar;
	border:1px solid #ddd
}
.table-responsive>.table {
	margin-bottom:0
}
.table-responsive>.table>tbody>tr>td, .table-responsive>.table>tbody>tr>th, .table-responsive>.table>tfoot>tr>td, .table-responsive>.table>tfoot>tr>th, .table-responsive>.table>thead>tr>td, .table-responsive>.table>thead>tr>th {
	white-space:nowrap
}
.table-responsive>.table-bordered {
	border:0
}
.table-responsive>.table-bordered>tbody>tr>td:first-child, .table-responsive>.table-bordered>tbody>tr>th:first-child, .table-responsive>.table-bordered>tfoot>tr>td:first-child, .table-responsive>.table-bordered>tfoot>tr>th:first-child, .table-responsive>.table-bordered>thead>tr>td:first-child, .table-responsive>.table-bordered>thead>tr>th:first-child {
	border-left:0
}
.table-responsive>.table-bordered>tbody>tr>td:last-child, .table-responsive>.table-bordered>tbody>tr>th:last-child, .table-responsive>.table-bordered>tfoot>tr>td:last-child, .table-responsive>.table-bordered>tfoot>tr>th:last-child, .table-responsive>.table-bordered>thead>tr>td:last-child, .table-responsive>.table-bordered>thead>tr>th:last-child {
	border-right:0
}
.table-responsive>.table-bordered>tbody>tr:last-child>td, .table-responsive>.table-bordered>tbody>tr:last-child>th, .table-responsive>.table-bordered>tfoot>tr:last-child>td, .table-responsive>.table-bordered>tfoot>tr:last-child>th {
	border-bottom:0
}
}
fieldset {
	padding:0;
	margin:0
}
legend {
	display:block;
	width:100%;
	padding:0;
	margin-bottom:17px;
	font-size:18px;
	line-height:inherit;
	color:#333;
	border:0;
	border-bottom:1px solid #e5e5e5
}
label {
	display:inline-block;
	margin-bottom:5px
}
input[type=search] {
	-webkit-box-sizing:border-box;
	-moz-box-sizing:border-box;
	box-sizing:border-box
}
input[type=checkbox]:focus, input[type=file]:focus, input[type=radio]:focus {
	outline:thin dotted #333;
	outline:5px auto -webkit-focus-ring-color;
	outline-offset:-2px
}
input[type=checkbox], input[type=radio] {
	margin:3px 0 0;
	margin-top:2px \9;
	line-height:normal;
*margin-top:-1px
}
@media screen and (-webkit-min-device-pixel-ratio:0) {
input[type=checkbox], input[type=radio] {
	-webkit-appearance:none;
	-webkit-user-select:none;
	background-color:#fff;
	border:1px solid #c9c9c9
}
input[type=checkbox]:focus, input[type=radio]:focus {
	outline:0
}
input[type=checkbox]:checked, input[type=radio]:checked {
	border-color:#62b651
}
}
@media screen and (-webkit-min-device-pixel-ratio:0) {
input[type=checkbox] {
	width:10px;
	height:10px
}
input[type=checkbox]:checked::before {
	display:block;
	width:100%;
	height:100%;
	content:'';
	-webkit-user-select:none;
	background-color:#62b651;
	background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAJCAYAAAAGuM1UAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAA2ZpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdpbj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMy1jMDExIDY2LjE0NTY2MSwgMjAxMi8wMi8wNi0xNDo1NjoyNyAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlwdGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wTU09Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9tbS8iIHhtbG5zOnN0UmVmPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvc1R5cGUvUmVzb3VyY2VSZWYjIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDo4NURFMzBFMzYwMUZFMzExOUIyOEZFQ0YxMTJGNENENCIgeG1wTU06RG9jdW1lbnRJRD0ieG1wLmRpZDpDODkxRTY3ODI5NzcxMUUzQkRBNEU3OTI5RjMxRTg1OSIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDpDODkxRTY3NzI5NzcxMUUzQkRBNEU3OTI5RjMxRTg1OSIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgQ1M2IChXaW5kb3dzKSI+IDx4bXBNTTpEZXJpdmVkRnJvbSBzdFJlZjppbnN0YW5jZUlEPSJ4bXAuaWlkOkExOEVCNkM4NDIyN0UzMTFBRDk4Q0FCN0U0QzRDRkY0IiBzdFJlZjpkb2N1bWVudElEPSJ4bXAuZGlkOjg1REUzMEUzNjAxRkUzMTE5QjI4RkVDRjExMkY0Q0Q0Ii8+IDwvcmRmOkRlc2NyaXB0aW9uPiA8L3JkZjpSREY+IDwveDp4bXBtZXRhPiA8P3hwYWNrZXQgZW5kPSJyIj8+L6F4XgAAAIhJREFUeNpi+P//PwOR2ByINxCrWAaInwJxNDGK+YH4EhBXgPgggWlAbIBDMRsQ74WqYYBpiAHiF0Csj6aYEYiXAPEOIGZG1gDCcUD8HIj1kDRUQ53Cj2wQsokJUE3aIM8B8SOoZxlwaQDhRCB+CcSvgFgXm79YGFDBfCC2BuKPQHyZAQsACDAA+hkyGyV8FW8AAAAASUVORK5CYII=);
	background-repeat:no-repeat;
	background-position:50% 50%
}
}
@media screen and (-webkit-min-device-pixel-ratio:0) {
input[type=radio] {
	width:12px;
	height:12px;
	border-radius:50%
}
input[type=radio]:checked {
	background-color:#62b651
}
}
input[type=file] {
	display:block
}
input:not([type=image]), select, textarea {
	-webkit-box-sizing:content-box;
	-moz-box-sizing:content-box;
	box-sizing:content-box
}
select[multiple], select[size] {
	height:auto
}
select optgroup {
	font-family:inherit;
	font-size:inherit;
	font-style:inherit
}
input[type=number]::-webkit-inner-spin-button, input[type=number]::-webkit-outer-spin-button {
height:auto
}
.form-control:-moz-placeholder {
color:#a0a0a0
}
.form-control::-moz-placeholder {
color:#a0a0a0
}
.form-control:-ms-input-placeholder {
color:#a0a0a0
}
.form-control::-webkit-input-placeholder {
color:#a0a0a0
}
.form-control {
	height:20px;
	padding:5px 10px;
	font-size:14px;
	line-height:1.42857143;
	color:#707070;
	vertical-align:middle;
	background-color:#fff;
	border:1px solid #ccc;
	border-radius:2px;
	-webkit-transition:border-color ease-in-out .15s, box-shadow ease-in-out .15s;
	transition:border-color ease-in-out .15s, box-shadow ease-in-out .15s
}
.form-control:focus {
	border-color:#66afe9;
	outline:0;
	-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102,175,233,.6);
	box-shadow:inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102,175,233,.6)
}
.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
	cursor:not-allowed;
	background-color:#a0a0a0
}
textarea.form-control {
	height:auto
}
.form-group {
	margin-bottom:15px
}
.checkbox, .radio {
	display:block;
	min-height:17px;
	padding-left:20px;
	margin-top:10px;
	margin-bottom:10px;
	vertical-align:middle
}
.checkbox label, .radio label {
	display:inline;
	margin-bottom:0;
	font-weight:400;
	cursor:pointer;
	-webkit-user-select:none;
	-moz-user-select:none;
	-ms-user-select:none;
	-o-user-select:none;
	user-select:none
}
.checkbox input[type=checkbox], .checkbox-inline input[type=checkbox], .radio input[type=radio], .radio-inline input[type=radio] {
	float:left;
	margin-left:-20px
}
.radio-inline input[type=radio] {
	margin-top:1px;
	margin-top:2px \9;
*margin-top:-1px
}
@media screen and (-webkit-min-device-pixel-ratio:0) {
.radio-inline input[type=radio] {
	margin-top:2px
}
}
.checkbox+.checkbox, .radio+.radio {
	margin-top:-5px
}
.checkbox-inline, .radio-inline {
	display:inline-block;
	padding-left:20px;
	margin-bottom:0;
	font-weight:400;
	vertical-align:middle;
	cursor:pointer;
*display:inline;
*zoom:1
}
.checkbox-inline label, .radio-inline label {
	-webkit-user-select:none;
	-moz-user-select:none;
	-ms-user-select:none;
	-o-user-select:none;
	user-select:none
}
.checkbox-inline+.checkbox-inline, .radio-inline+.radio-inline {
	margin-top:0;
	margin-left:10px
}
.checkbox-inline[disabled], .checkbox[disabled], .radio-inline[disabled], .radio[disabled], fieldset[disabled] .checkbox, fieldset[disabled] .checkbox-inline, fieldset[disabled] .radio, fieldset[disabled] .radio-inline, fieldset[disabled] input[type=checkbox], fieldset[disabled] input[type=radio], input[type=checkbox][disabled], input[type=radio][disabled] {
	cursor:not-allowed
}
.input-sm {
	height:28px;
	padding:5px 10px;
	font-size:11px;
	line-height:1.5;
	border-radius:2px
}
select.input-sm {
	height:28px;
	line-height:28px
}
textarea.input-sm {
	height:auto
}
.input-lg {
	height:23px;
	padding:10px 16px;
	font-size:16px;
	line-height:1.33;
	border-radius:6px
}
select.input-lg {
	height:23px;
	line-height:23px
}
textarea.input-lg {
	height:auto
}
.has-warning .checkbox, .has-warning .checkbox-inline, .has-warning .control-label, .has-warning .help-block, .has-warning .radio, .has-warning .radio-inline {
	color:#f88a48
}
.has-warning .form-control {
	border-color:#f88a48;
	-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.075);
	box-shadow:inset 0 1px 1px rgba(0,0,0,.075)
}
.has-warning .form-control:focus {
	border-color:#f66b17;
	-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.075), 0 0 6px #fcc9aa;
	box-shadow:inset 0 1px 1px rgba(0,0,0,.075), 0 0 6px #fcc9aa
}
.has-warning .input-group-addon {
	color:#f88a48;
	background-color:#fff9f8;
	border-color:#f88a48
}
.has-error .checkbox, .has-error .checkbox-inline, .has-error .control-label, .has-error .help-block, .has-error .radio, .has-error .radio-inline {
	color:#b94a48
}
.has-error .form-control {
	border-color:#b94a48;
	-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.075);
	box-shadow:inset 0 1px 1px rgba(0,0,0,.075)
}
.has-error .form-control:focus {
	border-color:#953b39;
	-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.075), 0 0 6px #d59392;
	box-shadow:inset 0 1px 1px rgba(0,0,0,.075), 0 0 6px #d59392
}
.has-error .input-group-addon {
	color:#b94a48;
	background-color:#f2dede;
	border-color:#b94a48
}
.has-success .checkbox, .has-success .checkbox-inline, .has-success .control-label, .has-success .help-block, .has-success .radio, .has-success .radio-inline {
	color:#468847
}
.has-success .form-control {
	border-color:#468847;
	-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.075);
	box-shadow:inset 0 1px 1px rgba(0,0,0,.075)
}
.has-success .form-control:focus {
	border-color:#356635;
	-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.075), 0 0 6px #7aba7b;
	box-shadow:inset 0 1px 1px rgba(0,0,0,.075), 0 0 6px #7aba7b
}
.has-success .input-group-addon {
	color:#468847;
	background-color:#dff0d8;
	border-color:#468847
}
.form-control-static {
	padding-top:6px;
	margin-bottom:0
}
.help-block {
	display:block;
	margin-top:5px;
	margin-bottom:10px;
	color:#737373
}
@media (min-width:768px) {
.form-inline .form-group {
	display:inline-block;
	margin-bottom:0;
	vertical-align:middle
}
.form-inline .form-control {
	display:inline-block
}
.form-inline .checkbox, .form-inline .radio {
	display:inline-block;
	padding-left:0;
	margin-top:0;
	margin-bottom:0
}
.form-inline .checkbox input[type=checkbox], .form-inline .radio input[type=radio] {
	float:none;
	margin-left:0
}
}
.form-horizontal .checkbox, .form-horizontal .checkbox-inline, .form-horizontal .control-label, .form-horizontal .radio, .form-horizontal .radio-inline {
	padding-top:6px;
	margin-top:0;
	margin-bottom:0
}
.btn {
	display:inline-block;
	padding:5px 10px;
	margin-bottom:0;
	font-size:12px;
	font-weight:400;
	line-height:1.42857143;
	text-align:center;
	white-space:nowrap;
	vertical-align:middle;
	cursor:pointer;
	-webkit-user-select:none;
	-moz-user-select:none;
	-ms-user-select:none;
	-o-user-select:none;
	user-select:none;
	border:1px solid transparent;
	border-radius:2px;
*display:inline;
*zoom:1
}
.btn:focus {
	outline:0
}
.btn:focus, .btn:hover {
	text-decoration:none
}
.btn.cancle, .btn.disabled, .btn[disabled], fieldset[disabled] .btn {
	color:#fff;
	pointer-events:none;
	cursor:not-allowed;
	background-color:#b5b5b5;
	filter:alpha(opacity=65);
	border-color:#b5b5b5;
	-webkit-box-shadow:none;
	box-shadow:none;
	opacity:.65;
	-ms-filter:"alpha(Opacity=65)"
}
.btn.cancle {
	pointer-events:auto;
	cursor:pointer
}
button.btn {
*overflow:visible
}
.btn-default {
	color:#62b651;
	background-color:#f7f9f2;
	border-color:#62b651
}
.btn-default:focus, .btn-default:hover {
	color:#608e38;
	background-color:#c3e6a5
}
.btn-default.active, .btn-default:active, .btn-primary {
	color:#fff;
	background-color:#62b651;
	border-color:#62b651
}
.btn-primary:focus, .btn-primary:hover {
	color:#fff;
	background-color:#70cb5c;
	border-color:#70cb5c
}
.btn-primary.active, .btn-primary:active {
	background-color:#539b45;
	-webkit-box-shadow:inset 1px 1px 0 #4b9336, inset -1px 0 0 #4b9336;
	box-shadow:inset 1px 1px 0 #4b9336, inset -1px 0 0 #4b9336
}
.btn-like {
	position:relative;
	color:#2578bf;
	background-color:#f4f7fa;
	border-color:#e5e5e5
}
.btn-like:focus, .btn-like:hover {
	color:#2578bf;
	background-color:#eee;
	border-color:#e5e5e5
}
.btn-like:active {
	color:#2578bf;
	background-color:#f4f7fa;
	border-color:#e5e5e5
}
.btn-like.active {
	color:#a0a0a0
}
.btn-like .num {
	margin-left:5px;
	font-style:normal
}
.btn-like strong {
	position:absolute;
	left:19px;
	color:#ff9b9b;
	opacity:0
}
.btn-warning {
	color:#fff;
	background-color:#f3900d;
	border-color:#f3900d
}
.btn-warning:focus, .btn-warning:hover {
	color:#fff;
	background-color:#ffa125;
	border-color:#ffa125
}
.btn-warning.active, .btn-warning:active {
	background-color:#f3900d;
	-webkit-box-shadow:inset 0 1px 2px #d07600;
	box-shadow:inset 0 1px 2px #d07600
}
.btn-danger {
	color:#fff;
	background-color:#d9534f;
	border-color:#d43f3a
}
.btn-danger.active, .btn-danger:active, .btn-danger:focus, .btn-danger:hover, .open .dropdown-toggle.btn-danger {
	color:#fff;
	background-color:#d2322d;
	border-color:#ac2925
}
.btn-danger.active, .btn-danger:active, .open .dropdown-toggle.btn-danger {
	background-image:none
}
.btn-danger.disabled, .btn-danger.disabled.active, .btn-danger.disabled:active, .btn-danger.disabled:focus, .btn-danger.disabled:hover, .btn-danger[disabled], .btn-danger[disabled].active, .btn-danger[disabled]:active, .btn-danger[disabled]:focus, .btn-danger[disabled]:hover, fieldset[disabled] .btn-danger, fieldset[disabled] .btn-danger.active, fieldset[disabled] .btn-danger:active, fieldset[disabled] .btn-danger:focus, fieldset[disabled] .btn-danger:hover {
	background-color:#d9534f;
	border-color:#d43f3a
}
.btn-success {
	color:#fff;
	background-color:#5cb85c;
	border-color:#4cae4c
}
.btn-success.active, .btn-success:active, .btn-success:focus, .btn-success:hover, .open .dropdown-toggle.btn-success {
	color:#fff;
	background-color:#47a447;
	border-color:#398439
}
.btn-success.active, .btn-success:active, .open .dropdown-toggle.btn-success {
	background-image:none
}
.btn-success.disabled, .btn-success.disabled.active, .btn-success.disabled:active, .btn-success.disabled:focus, .btn-success.disabled:hover, .btn-success[disabled], .btn-success[disabled].active, .btn-success[disabled]:active, .btn-success[disabled]:focus, .btn-success[disabled]:hover, fieldset[disabled] .btn-success, fieldset[disabled] .btn-success.active, fieldset[disabled] .btn-success:active, fieldset[disabled] .btn-success:focus, fieldset[disabled] .btn-success:hover {
	background-color:#5cb85c;
	border-color:#4cae4c
}
.btn-info {
	color:#fff;
	background-color:#7bdcfd;
	border-color:#7bdcfd
}
.btn-info:focus, .btn-info:hover {
	color:#fff;
	background-color:#00b7ee;
	border-color:#00b7ee
}
.btn-info:active {
	color:#007294;
	background-color:#00b7ee;
	-webkit-box-shadow:inset 0 1px #3ca5e6;
	box-shadow:inset 0 1px #3ca5e6
}
.btn-link {
	font-weight:400;
	color:#2578bf;
	cursor:pointer;
	border-radius:0
}
.btn-link, .btn-link:active, .btn-link[disabled], fieldset[disabled] .btn-link {
	background-color:transparent;
	-webkit-box-shadow:none;
	box-shadow:none
}
.btn-link, .btn-link:active, .btn-link:focus, .btn-link:hover {
	border-color:transparent;
*border:0 none
}
.btn-link:focus, .btn-link:hover {
	color:#00a0e9;
	text-decoration:underline;
	background-color:transparent
}
.btn-link[disabled]:focus, .btn-link[disabled]:hover, fieldset[disabled] .btn-link:focus, fieldset[disabled] .btn-link:hover {
	color:#fff;
	text-decoration:none
}
.event-create .btn {
	padding-top:0;
	padding-bottom:0;
	line-height:22px
}
.event-create .btn-primary {
	padding-right:4px;
	padding-left:2px;
	margin-right:2px;
	font-size:14px;
	color:#fff;
	background-color:#ffa926;
	border-color:#ffa926
}
.event-create .btn-primary:focus, .event-create .btn-primary:hover {
	color:#fff;
	background-color:#ff9a00;
	border-color:#ff9a00
}
.event-create .btn-primary:active {
	color:#fff;
	background-color:#ff9a00;
	-webkit-box-shadow:inset 0 1px 2px #d07600;
	box-shadow:inset 0 1px 2px #d07600
}
.event-create .btn-primary .icon-add {
	float:left;
	margin-top:2px;
	margin-right:5px
}
.event-create .btn-warning {
	color:#ffa926;
	background:#ffeed5;
	border-color:#ffa926
}
.event-create .btn-warning:focus, .event-create .btn-warning:hover {
	color:#ff9a00;
	background:#ffd79b;
	border-color:#ffa926
}
.event-create .btn-warning:active {
	color:#fff;
	background:#ff9a00;
	border-color:#f69400
}
.btn-send {
	color:#148c08;
	background-color:#aee195;
	border-color:#8ecd70
}
.btn-send:active, .btn-send:focus, .btn-send:hover {
	color:#148c08;
	background-color:#8ecd70;
	border-color:#6ca751
}
.btn-send-repeat {
	color:#786855;
	background-color:#f3ce00;
	border-color:#ffb63b;
	-webkit-box-shadow:inset 1px 1px rgba(255,255,255,.3);
	box-shadow:inset 1px 1px rgba(255,255,255,.3)
}
.btn-send-repeat:active, .btn-send-repeat:focus, .btn-send-repeat:hover {
	color:#786855;
	background-color:#ffc600;
	border-color:#ffb63b
}
.btn-lg {
	padding:10px 16px;
	font-size:16px;
	line-height:1.33;
	border-radius:2px
}
.btn-sm, .btn-xs {
	padding:5px 10px;
	font-size:11px;
	line-height:1.5;
	border-radius:2px
}
.btn-xs {
	padding:1px 5px
}
.btn-block {
	display:block;
	width:100%;
	padding-right:0;
	padding-left:0
}
.btn-block+.btn-block {
	margin-top:5px
}
.btn-file {
	position:relative;
	overflow:hidden
}
.btn-file input {
	position:absolute;
	top:0;
	right:0;
	margin:0;
	font-size:23px;
	cursor:pointer;
	filter:alpha(opacity=0);
	opacity:0;
	transform:translate(-300px, 0) scale(4);
	-ms-filter:"alpha(Opacity=0)";
	direction:ltr
}
.btn-lottery {
	position:relative;
	width:164px;
	padding:0;
	line-height:38px;
	color:#fff;
	background:#56a846
}
.btn-lottery:before {
	position:absolute;
	top:-1px;
	left:-1px;
	width:166px;
	height:20px;
	content:'';
	background:#fff;
	filter:alpha(opacity=15);
	border-top-left-radius:4px;
	border-top-right-radius:4px;
	opacity:.15;
	-ms-filter:"alpha(Opacity=15)"
}
.btn-lottery:active, .btn-lottery:focus, .btn-lottery:hover {
	color:#fff
}
.btn-lottery:active:before, .btn-lottery:focus:before, .btn-lottery:hover:before {
	content:none
}
.btn-lottery:active {
	-webkit-box-shadow:inset 1px 1px 1px #418a32;
	box-shadow:inset 1px 1px 1px #418a32
}
.fileupload-exists .fileupload-new, .fileupload-new .fileupload-exists {
	display:none
}
.fade {
	opacity:0;
	-webkit-transition:opacity .15s linear;
	transition:opacity .15s linear
}
.fade.in {
	opacity:1
}
.collapse {
	display:none
}
.collapse.in {
	display:block
}
.collapsing {
	position:relative;
	height:0;
	overflow:hidden;
	-webkit-transition:height .35s ease;
	transition:height .35s ease
}
.caret {
	display:inline-block;
	width:0;
	height:0;
	margin-left:6px;
	vertical-align:middle;
	border-top:4px solid;
	border-right:4px solid transparent;
	border-left:4px solid transparent
}
.dropdown2 {
	position:relative
}
.dropdown2-toggle:focus {
	outline:0
}
.dropdown2-menu {
	position:absolute;
	top:100%;
	left:0;
	z-index:1000;
	display:none;
	float:left;
	width:100%;
	margin-top:0;
	margin-left:0;
	list-style:none;
	background-color:#539b45;
	background-clip:padding-box;
	border-bottom-right-radius:4px;
	border-bottom-left-radius:4px
}
.dropdown2-menu.pull-right {
	right:0;
	left:auto
}
.dropdown2-menu .divider {
	height:1px;
	margin:7.5px 0;
	overflow:hidden;
	background-color:#e5e5e5
}
.dropdown2-menu li a {
	display:block;
	clear:both;
	font-weight:400;
	line-height:40px;
	color:#fff;
	white-space:nowrap;
	border-top:1px solid #498d3c
}
.dropdown2-menu li:last-child a {
	border-bottom-right-radius:4px;
	border-bottom-left-radius:4px
}
.dropdown2-menu li a {
	outline:0
}
.dropdown2-menu li a:active, .dropdown2-menu li a:focus, .dropdown2-menu li a:hover {
	text-decoration:none;
	background-color:#62b651
}
.open .dropdown2-menu {
	display:block
}
.open>a {
	background-color:#539b45;
	border-color:#498d3c;
	outline:0
}
.dropdown2-white .dropdown2-menu {
	background:#fff;
	border-bottom:1px solid #e5e5e5
}
.dropdown2-white li a {
	color:#62b651;
	border:solid #e5e5e5;
	border-width:1px 1px 0
}
.dropdown2-white li a:active, .dropdown2-white li a:focus, .dropdown2-white li a:hover {
	background-color:#eee
}
.dropdown2-white>a {
	border:solid transparent;
	border-width:1px 1px 0;
	border-top-left-radius:4px;
	border-top-right-radius:4px
}
.dropdown2-white.open>a {
	background:#fff;
	border-color:#e5e5e5
}
.pagination {
	margin:40px 0;
	text-align:center
}
.pagination ul {
	display:inline-block;
	padding-left:0;
*display:inline;
*zoom:1
}
.pagination ul>li {
	display:inline
}
.pagination ul>li>a, .pagination ul>li>span {
	position:relative;
	float:left;
	width:20px;
	margin-right:10px;
	line-height:20px;
	color:#fff;
	text-align:center;
	text-decoration:none;
	background-color:#c9c9c9;
	border-radius:4px
}
.pagination ul>li.first-child>a, .pagination ul>li.first-child>span, .pagination ul>li.last-child>a, .pagination ul>li.last-child>span {
	width:30px;
	margin-top:-5px;
	font-size:18px;
	line-height:30px
}
.pagination ul>li>a:focus, .pagination ul>li>a:hover, .pagination ul>li>span:focus, .pagination ul>li>span:hover {
	background-color:#62b651
}
.pagination ul>li>a:active, .pagination ul>li>span:active {
	color:#518600;
	text-shadow:0 1px rgba(255,255,255,.5);
	background-color:#92c11c;
	-webkit-box-shadow:inset 0 1px 2px #5f9708;
	box-shadow:inset 0 1px 2px #5f9708
}
.pagination ul>.active>a, .pagination ul>.active>a:focus, .pagination ul>.active>a:hover, .pagination ul>.active>span, .pagination ul>.active>span:focus, .pagination ul>.active>span:hover {
	z-index:2;
	cursor:default;
	background-color:#62b651
}
.pagination ul [disabled]>a, .pagination ul [disabled]>a:focus, .pagination ul [disabled]>a:hover, .pagination ul [disabled]>span, .pagination ul>.disabled>a, .pagination ul>.disabled>a:focus, .pagination ul>.disabled>a:hover, .pagination ul>.disabled>span {
	color:#7d7d7d;
	cursor:not-allowed;
	background-color:#c9c9c9;
	border-color:#ddd
}
.media, .media-body {
	overflow:hidden;
	zoom:1
}
.media:first-child {
	margin-top:0
}
.media-object {
	display:block
}
.media-heading {
	margin:0 0 5px
}
.media>.pull-left {
	margin-right:10px
}
.media>.pull-right {
	margin-left:10px
}
.media-list {
	padding-left:0;
	list-style:none
}
.close {
	float:right;
	font-size:18px;
	font-weight:700;
	line-height:1;
	color:#000;
	text-shadow:0 1px 0 #fff;
	background:#fff;
	filter:alpha(opacity=20);
	opacity:.2;
	-ms-filter:"alpha(Opacity=20)"
}
.close:focus, .close:hover {
	color:#000;
	text-decoration:none;
	cursor:pointer;
	filter:alpha(opacity=50);
	opacity:.5;
	-ms-filter:"alpha(Opacity=50)"
}
button.close {
	-webkit-appearance:none;
	padding:0;
	cursor:pointer;
	background:0 0;
	border:0
}
.modal-open {
	overflow:hidden
}
.modal-open body {
	margin-right:15px
}
.modal {
	position:fixed;
	top:0;
	right:0;
	bottom:0;
	left:0;
	z-index:1050;
	display:none;
	overflow:auto;
	overflow-y:scroll;
	-webkit-overflow-scrolling:touch;
	outline:0
}
.modal.fade .modal-dialog {
	-webkit-transition:-webkit-transform .3s ease-out;
	-moz-transition:-moz-transform .3s ease-out;
	-o-transition:-o-transform .3s ease-out;
	transition:transform .3s ease-out;
	-webkit-transform:translate(0, -25%);
	-ms-transform:translate(0, -25%);
	transform:translate(0, -25%)
}
.modal.in .modal-dialog {
	-webkit-transform:translate(0, 0);
	-ms-transform:translate(0, 0);
	transform:translate(0, 0)
}
.modal-dialog {
	padding:10px;
	margin-right:auto;
	margin-left:auto
}
.modal-header .close {
	margin-top:10px;
	margin-right:20px;
	font-size:28px;
	font-weight:400;
*margin-top:5px
}
.modal-content {
	position:relative;
	background-color:#fff;
	background-clip:padding-box;
	border:4px solid #ccc;
	border:4px solid rgba(160,160,160,0);
	border-radius:4px;
	outline:0
}
.modal-backdrop {
	position:fixed;
	top:0;
	right:0;
	bottom:0;
	left:0;
	z-index:1040;
	background-color:#000
}
.modal-backdrop.fade {
	filter:alpha(opacity=0);
	opacity:0;
	-ms-filter:"alpha(Opacity=0)"
}
.modal-backdrop.in {
	filter:alpha(opacity=50);
	opacity:.5;
	-ms-filter:"alpha(Opacity=50)"
}
.modal-title {
	padding-left:20px;
	margin:0;
	font-family:Microsoft Yahei;
	line-height:48px;
	color:#222;
	background-color:#f2f1f1
}
.modal-body {
	position:relative
}
.modal-footer {
	padding:19px 20px 20px;
	margin-top:15px;
	text-align:right;
	border-top:1px solid #e5e5e5
}
.modal-footer .btn+.btn {
	margin-bottom:0;
	margin-left:5px
}
.modal-footer .btn-group .btn+.btn {
	margin-left:-1px
}
.modal-footer .btn-block+.btn-block {
	margin-left:0
}
.modal-dialog {
	padding-top:30px;
	padding-bottom:30px
}
.modal-content {
	-webkit-box-shadow:0 5px 15px rgba(0,0,0,.5);
	box-shadow:0 5px 15px rgba(0,0,0,.5)
}
.tooltip {
	position:absolute;
	z-index:1070;
	display:block;
	font-size:11px;
	line-height:1.4;
	visibility:visible;
	filter:alpha(opacity=0);
	opacity:0;
	-ms-filter:"alpha(Opacity=0)"
}
.tooltip.in {
	filter:alpha(opacity=90);
	opacity:.9;
	-ms-filter:"alpha(Opacity=90)"
}
.tooltip.top {
	padding:5px 0;
	margin-top:-3px
}
.tooltip.right {
	padding:0 5px;
	margin-left:3px
}
.tooltip.bottom {
	padding:5px 0;
	margin-top:3px
}
.tooltip.left {
	padding:0 5px;
	margin-left:-3px
}
.tooltip-inner {
	max-width:200px;
	padding:3px 8px;
	color:#fff;
	text-align:center;
	text-decoration:none;
	background-color:#000;
	border-radius:4px
}
.tooltip-arrow {
	position:absolute;
	width:0;
	height:0;
	border-color:transparent;
	border-style:solid
}
.tooltip.top .tooltip-arrow {
	bottom:0;
	left:50%;
	margin-left:-5px;
	border-width:5px 5px 0;
	border-top-color:#000
}
.tooltip.top-left .tooltip-arrow {
	bottom:0;
	left:5px;
	border-width:5px 5px 0;
	border-top-color:#000
}
.tooltip.top-right .tooltip-arrow {
	right:5px;
	bottom:0;
	border-width:5px 5px 0;
	border-top-color:#000
}
.tooltip.right .tooltip-arrow {
	top:50%;
	left:0;
	margin-top:-5px;
	border-width:5px 5px 5px 0;
	border-right-color:#000
}
.tooltip.left .tooltip-arrow {
	top:50%;
	right:0;
	margin-top:-5px;
	border-width:5px 0 5px 5px;
	border-left-color:#000
}
.tooltip.bottom .tooltip-arrow {
	top:0;
	left:50%;
	margin-left:-5px;
	border-width:0 5px 5px;
	border-bottom-color:#000
}
.tooltip.bottom-left .tooltip-arrow {
	top:0;
	left:5px;
	border-width:0 5px 5px;
	border-bottom-color:#000
}
.tooltip.bottom-right .tooltip-arrow {
	top:0;
	right:5px;
	border-width:0 5px 5px;
	border-bottom-color:#000
}
.popover {
	position:absolute;
	top:0;
	left:0;
	z-index:1060;
	display:none;
	padding:1px;
	white-space:normal;
	background-color:#fff;
	background-clip:padding-box;
	border:1px solid #e7e7e7;
	border-radius:4px;
	-webkit-box-shadow:0 1px 1px rgba(0,0,0,.2);
	box-shadow:0 1px 1px rgba(0,0,0,.2)
}
.popover.top {
	margin-top:-10px
}
.popover.right {
	margin-left:10px
}
.popover.bottom {
	margin-top:10px
}
.popover.left {
	margin-left:-10px
}
.popover-title {
	padding:8px 14px;
	margin:0;
	font-size:12px;
	font-weight:400;
	line-height:18px;
	background-color:#f7f7f7;
	border-bottom:1px solid #ebebeb;
	border-radius:5px 5px 0 0
}
.popover-content {
	padding:9px 14px
}
.popover .arrow, .popover .arrow:after {
	position:absolute;
	display:block;
	width:0;
	height:0;
	border-color:transparent;
	border-style:solid
}
.popover .arrow {
	border-width:11px
}
.popover .arrow:after {
	content:"";
	border-width:10px
}
.popover.top .arrow {
	bottom:-11px;
	left:50%;
	margin-left:-11px;
	border-top-color:#e7e7e7;
	border-bottom-width:0
}
.popover.top .arrow:after {
	bottom:1px;
	margin-left:-10px;
	content:" ";
	border-top-color:#fff;
	border-bottom-width:0
}
.popover.right .arrow {
	top:50%;
	left:-11px;
	margin-top:-11px;
	border-right-color:#e7e7e7;
	border-left-width:0
}
.popover.right .arrow:after {
	bottom:-10px;
	left:1px;
	content:" ";
	border-right-color:#fff;
	border-left-width:0
}
.popover.bottom .arrow {
	top:-11px;
	left:50%;
	margin-left:-11px;
	border-top-width:0;
	border-bottom-color:#e7e7e7
}
.popover.bottom .arrow:after {
	top:1px;
	margin-left:-10px;
	content:" ";
	border-top-width:0;
	border-bottom-color:#fff
}
.popover.left .arrow {
	top:50%;
	right:-11px;
	margin-top:-11px;
	border-right-width:0;
	border-left-color:#e7e7e7
}
.popover.left .arrow:after {
	right:1px;
	bottom:-10px;
	content:" ";
	border-right-width:0;
	border-left-color:#fff
}
.popover em {
	font-style:normal;
	font-weight:700
}
.popover-warning {
	color:#f88a48;
	background:#fff9f8;
	border-color:#ffdfd8
}
.popover-warning.right .arrow {
	border-right-color:#ffdfd8
}
.popover-warning.right .arrow:after {
	border-right-color:#fff9f8
}
.popover-module {
	background:#f4f7fa;
	border-color:#f4f7fa
}
.popover-module.bottom .arrow, .popover-module.bottom .arrow:after {
	border-bottom-color:#f4f7fa
}
#slides-multiple-list .event-vertical-list:after, #slides-multiple-list .event-vertical-list:before, #special-topic .popover .img-txt:after, #special-topic .popover .img-txt:before, .about-content .media-body .sns:after, .about-content .media-body .sns:before, .calendar-list-header:after, .calendar-list-header:before, .clearfix:after, .clearfix:before, .coagent:after, .coagent:before, .container:after, .container:before, .event-news-list li:after, .event-news-list li:before, .event-news-text .func:after, .event-news-text .func:before, .event-news-title:after, .event-news-title:before, .event-vertical-list:after, .event-vertical-list:before, .form-horizontal .form-group:after, .form-horizontal .form-group:before, .forum .feed-list .func:after, .forum .feed-list .func:before, .forum .feed-list .media .media-heading:after, .forum .feed-list .media .media-heading:before, .forum .sendbox:after, .forum .sendbox:before, .login-layer .modal-body:after, .login-layer .modal-body:before, .logo-content .icons:after, .logo-content .icons:before, .logo-content .logo-lg:after, .logo-content .logo-lg:before, .main_nav .items .dropdown2-white .dropdown2-menu>div:after, .main_nav .items .dropdown2-white .dropdown2-menu>div:before, .main_nav_index .items .dropdown2-white .dropdown2-menu>div:after, .main_nav_index .items .dropdown2-white .dropdown2-menu>div:before, .modal-footer:after, .modal-footer:before, .nav-tabs:after, .nav-tabs:before, .organizer-label-list:after, .organizer-label-list:before, .organizer-well-list:after, .organizer-well-list:before, .pay-breadcrumb:after, .pay-breadcrumb:before, .pay-order .result:after, .pay-order .result:before, .tabs:after, .tabs:before, .topic-imgs:after, .topic-imgs:before, .user-main:after, .user-main:before, .view-category-list>li:after, .view-category-list>li:before, .view-category:after, .view-category:before {
	display:table;
	content:" "
}
#slides-multiple-list .event-vertical-list:after, #special-topic .popover .img-txt:after, .about-content .media-body .sns:after, .calendar-list-header:after, .clearfix:after, .coagent:after, .container:after, .event-news-list li:after, .event-news-text .func:after, .event-news-title:after, .event-vertical-list:after, .form-horizontal .form-group:after, .forum .feed-list .func:after, .forum .feed-list .media .media-heading:after, .forum .sendbox:after, .login-layer .modal-body:after, .logo-content .icons:after, .logo-content .logo-lg:after, .main_nav .items .dropdown2-white .dropdown2-menu>div:after, .main_nav_index .items .dropdown2-white .dropdown2-menu>div:after, .modal-footer:after, .nav-tabs:after, .organizer-label-list:after, .organizer-well-list:after, .pay-breadcrumb:after, .pay-order .result:after, .tabs:after, .topic-imgs:after, .user-main:after, .view-category-list>li:after, .view-category:after {
	clear:both
}
.center-block {
	display:block;
	margin-right:auto;
	margin-left:auto
}
.pull-right {
	float:right!important
}
.pull-left {
	float:left!important
}
.hide {
	display:none!important
}
.show {
	display:block!important
}
.invisible {
	visibility:hidden
}
.text-hide {
	font:0/0 a;
	color:transparent;
	text-shadow:none;
	background-color:transparent;
	border:0
}
.hidden {
	display:none!important;
	visibility:hidden!important
}
.affix {
	position:fixed!important
}
#slides-multiple-list .event-vertical-list, #special-topic .popover .img-txt, .about-content .media-body .sns, .calendar-list-header, .clearfix, .coagent, .container, .event-news-list li, .event-news-text .func, .event-news-title, .event-vertical-list, .form-horizontal .form-group, .forum .feed-list .func, .forum .feed-list .media .media-heading, .forum .sendbox, .login-layer .modal-body, .logo-content .icons, .logo-content .logo-lg, .main_nav .items .dropdown2-white .dropdown2-menu>div, .main_nav_index .items .dropdown2-white .dropdown2-menu>div, .modal-footer, .nav-tabs, .organizer-label-list, .organizer-well-list, .pay-breadcrumb, .pay-order .result, .tabs, .topic-imgs, .user-main, .view-category, .view-category-list>li {
*zoom:1
}
.clear {
	clear:both
}
.text-overflow {
	overflow:hidden;
	text-overflow:ellipsis;
	white-space:nowrap
}
ol, ul {
	padding-left:0;
	margin-bottom:0;
	list-style:none;
*margin-left:0
}
fieldset {
	border:none
}
h2, h3 {
	font-size:16px
}
.media, .media .media {
	margin-top:0
}
.about-content h2:before, .btn-buy-ticket, .btn-event-cancle, .btn-event-disabled, .btn-event-over, .btn-sign-up, .douban, .event-like, .feedback-face, .icon-circle, .icon-circle-xs, .icon-collapse, .icon-danger, .icon-expand, .icon-female, .icon-goto, .icon-help, .icon-index, .icon-like, .icon-like-before, .icon-like-lg, .icon-list, .icon-lottery, .icon-male, .icon-master, .icon-message, .icon-mobile, .icon-note, .icon-place, .icon-rank, .icon-refresh, .icon-refresh-code, .icon-reply span, .icon-saying, .icon-statistics, .icon-success, .icon-tick, .icon-time, .icon-top span, .icon-topic-down, .icon-user, .icon-warning, .icon-weixin, .icon-weixin2, .qq, .quick-next, .renren, .share .default, .topic-next-round, .weibo, .weixin, .wx_share_tips {
	display:inline-block;
*display:inline;
*zoom:1
}
.header .sign div, .main_nav .sign div {
	float:left;
	width:113px;
	height:27px;
	padding-top:5px;
	margin-top:-8px;
	margin-right:20px;
	overflow:hidden;
	border:1px solid transparent;
	border-radius:4px;
*margin-top:-6px
}
.header .sign div a, .main_nav .sign div a {
	float:left;
	margin-bottom:5px;
	margin-left:5px
}
.header .sign div.expand, .main_nav .sign div.expand {
	height:54px;
	background:#fff;
	border-color:#c9c9c9
}
.icon-like, .icon-like-before, .icon-note {
	vertical-align:-1px
}
.icon-like, .icon-like-before, .icon-like-lg, .icon-note {
	margin-right:5px
}
.icon-like-lg {
	vertical-align:-4px;
*vertical-align:middle
}
.icon-refresh, .icon-time {
	margin-right:7px;
	vertical-align:-2px;
*vertical-align:1px
}
.icon-weixin {
	margin-right:7px;
	vertical-align:-3px
}
.icon-weixin2 {
	margin-right:6px
}
.icon-collapse, .icon-expand {
	margin-right:6px;
	vertical-align:-3px;
	cursor:pointer
}
.icon_password, .icon_user {
	margin-right:10px
}
.icon-saying {
	margin-right:10px;
	vertical-align:middle
}
.icon-index {
	margin-right:5px
}
.icon-goto {
	margin-left:6px
}
.more.active .icon-refresh, .refreshing, .sign-bd .icon-refresh-code.active {
	-webkit-animation:refresh .7s 0s infinite both;
	-o-animation:refresh .7s 0s infinite both;
	animation:refresh .7s 0s infinite both
}
@-webkit-keyframes refresh {
to {
-webkit-transform:rotate(360deg);
-ms-transform:rotate(360deg);
transform:rotate(360deg)
}
}
@-moz-keyframes refresh {
to {
-webkit-transform:rotate(360deg);
-ms-transform:rotate(360deg);
transform:rotate(360deg)
}
}
@-o-keyframes refresh {
to {
-webkit-transform:rotate(360deg);
-ms-transform:rotate(360deg);
transform:rotate(360deg)
}
}
@keyframes refresh {
to {
-webkit-transform:rotate(360deg);
-ms-transform:rotate(360deg);
transform:rotate(360deg)
}
}
.icon-help {
	width:16px;
	height:16px;
	margin-left:5px;
	font-style:normal;
	font-weight:700;
	line-height:16px;
	color:#fff;
	text-align:center;
	background-color:#f63;
	border-radius:16px
}
.icon-female, .icon-male {
	vertical-align:middle
}
.icon-reply, .icon-top {
	margin-right:5px;
	color:#2578bf;
	cursor:pointer
}
.icon-top {
	position:relative
}
.icon-top span {
	margin-right:10px;
	vertical-align:-1px;
*vertical-align:2px
}
.icon-top.active {
	color:#a0a0a0;
	cursor:default
}
.icon-top strong {
	position:absolute;
	left:0;
	color:#ff9b9b;
	opacity:0
}
.icon-reply span {
	margin-right:10px;
	vertical-align:middle
}
.share {
	-webkit-user-select:none;
	-moz-user-select:none;
	-ms-user-select:none;
	-o-user-select:none;
	user-select:none;
	-webkit-transition:.3s;
	transition:.3s
}
.share .share-btns {
	display:inline-block;
	vertical-align:middle;
*display:inline;
*zoom:1
}
.share a, .share span {
	margin-left:8px;
	vertical-align:middle;
	cursor:pointer
}
.share sup {
	position:absolute;
	margin-top:-13px;
	font:0/0 a;
	color:transparent;
	text-shadow:none;
	background-color:transparent;
	border:0
}
.icon-danger, .icon-success, .icon-warning {
	margin-right:8px;
	vertical-align:middle
}
.quick-next {
	margin-right:5px;
	vertical-align:middle
}
.topic-next-round {
	position:relative;
	display:inline-block;
	font-family:Microsoft Yahei;
	font-size:16px;
	line-height:60px;
	color:#fff
}
.page-footer a span {
	margin-right:8px;
	vertical-align:middle
}
.weibo-follow, .weixin-follow {
	display:inline-block;
	margin-top:-1px;
	margin-left:5px;
	vertical-align:middle;
*display:inline;
*zoom:1
}
.icon-lottery {
	margin-left:7px;
	vertical-align:middle
}
.rank {
	margin-left:10px;
	font-size:14px;
	font-weight:700;
	color:#ff9446
}
.rank .icon-rank {
	margin-right:5px;
	vertical-align:-2px
}
.icon-close-green, .icon-close-lg {
	position:absolute;
	-webkit-appearance:none;
	padding:0;
	cursor:pointer;
	background:0 0;
	border:0
}
.icon-close-lg {
	top:20px;
	right:20px
}
.icon-close-green {
	top:6px;
	right:12px
}
.user-info-header .category strong {
	display:inline-block;
	margin-right:5px;
	vertical-align:middle;
*display:inline;
*zoom:1
}
.face {
	width:27px;
	height:27px;
	margin-right:8px
}
.tabs {
	border-bottom-style:solid;
	border-bottom-width:1px
}
.tabs li {
	position:relative;
	float:left
}
.tabs li a {
	display:block
}
.tabs li a:active, .tabs li a:focus, .tabs li a:hover {
	text-decoration:none
}
.tabs li a.tab-error {
	color:red!important
}
.tabs li.active a {
	cursor:default
}
.tab-content .tab-pane {
	display:none
}
.tab-content .tab-pane.active {
	display:block
}
.tab-content .tab-pane .tips {
	color:#a0a0a0
}
.ad {
	position:relative;
	height:105px;
	margin-bottom:40px
}
.ad img {
	position:absolute;
	top:0;
	left:0;
	display:none\9
}
.ad img:first-child {
	z-index:1;
	-webkit-animation:backstretch 10s infinite;
	-moz-animation:backstretch 10s infinite;
	-ms-animation:backstretch 10s infinite;
	animation:backstretch 10s infinite
}
@media screen and (-ms-high-contrast:active), (-ms-high-contrast:none) {
.ad img {
	display:block
}
}
@-webkit-keyframes backstretch {
30%, 90% {
opacity:1
}
40%, 80% {
opacity:0
}
}
@-moz-keyframes backstretch {
30%, 90% {
opacity:1
}
40%, 80% {
opacity:0
}
}
@-ms-keyframes backstretch {
30%, 90% {
opacity:1
}
40%, 80% {
opacity:0
}
}
.module-box {
	padding:10px;
	margin-bottom:40px;
	color:#7d7d7d;
	background-color:#f4f7fa
}
.module-border-box {
	padding:20px 20px 0;
	margin-bottom:40px;
	border:1px solid #eee
}
.breadcrumb {
	margin-bottom:18px;
	font-size:14px;
	color:#2578bf
}
.breadcrumb a:active, .breadcrumb a:focus, .breadcrumb a:hover {
	text-decoration:none
}
.alert-saying {
	position:relative;
	padding:0 15px;
	margin-bottom:20px;
	line-height:30px;
	background:url(alert_saying_bg.png) repeat-x
}
.alert-saying, .alert-saying a {
	color:#a3bd9e
}
.alert-saying a:hover {
	text-decoration:none
}
.toggle {
	display:inline-block;
	padding:0 5px;
	line-height:23px;
	cursor:pointer;
	background-color:#fff;
*display:inline;
*zoom:1
}
.toggle.active, .toggle:active, .toggle:focus, .toggle:hover {
	color:#fff;
	cursor:default;
	background-color:#62b651
}
.more {
	float:right;
	font-size:12px;
	font-weight:400
}
.bottom_tools {
	position:fixed;
	right:40px;
	z-index:1000
}
.bottom_tools>* {
	display:block;
	margin-top:5px;
	font:0/0 a;
	color:transparent;
	text-shadow:none;
	background-color:transparent;
	border:0
}
.qr_img {
	position:absolute;
	top:-189px;
	left:-183px
}
.login-coagent, .login-form {
	float:left
}
.login-coagent h4, .login-form h4 {
	margin-top:0;
	font-size:14px;
	font-weight:700;
	color:#707070
}
.login-form form {
	border-right:1px solid #dedfdf
}
.login-form .form-group label {
	position:absolute;
	margin-left:12px;
	cursor:pointer
}
.login-form .form-control {
	padding-left:32px
}
.login-form .form-control:-moz-placeholder {
color:#a0a0a0
}
.login-form .form-control::-moz-placeholder {
color:#a0a0a0
}
.login-form .form-control:-ms-input-placeholder {
color:#a0a0a0
}
.login-form .form-control::-webkit-input-placeholder {
color:#a0a0a0
}
.login-form .btn-primary {
	font-family:Microsoft Yahei;
	font-size:20px;
	text-indent:.3em;
	letter-spacing:.3em
}
@media screen and (-webkit-min-device-pixel-ratio:0) {
.login-form .checkbox-inline {
	height:18px
}
}
.login-coagent a {
	float:left;
	font:0/0 a;
	color:transparent;
	text-shadow:none;
	background-color:transparent;
	border:0
}
.login-coagent a:active, .login-coagent a:focus, .login-coagent a:hover {
	filter:alpha(opacity=80);
	opacity:.8;
	-ms-filter:"alpha(Opacity=80)"
}
.login-layer .modal-dialog {
	width:570px;
*width:590px
}
.login-layer .modal-body {
	padding-top:34px;
	padding-bottom:25px
}
.login-layer .close {
	position:relative;
	z-index:1;
	margin-top:8px;
	margin-right:15px
}
.login-layer .login-form {
	padding-left:36px;
	margin-right:40px;
*width:234px
}
.login-layer h4 {
	margin-bottom:12px
}
.login-layer h4 a {
	margin-right:50px;
	font-size:12px;
	font-weight:400
}
.login-layer form {
	width:190px;
	padding-right:42px
}
.login-layer .form-group {
	margin-bottom:8px
}
.login-layer .form-group .icon-user {
	margin-top:12px
}
.login-layer .form-group .icon-password {
	margin-top:10px
}
.login-layer .form-control {
	width:146px;
	height:24px;
	height:21px \9;
	padding-top:8px \9;
	margin-right:10px
}
@media screen and (-ms-high-contrast:active), (-ms-high-contrast:none) {
.login-layer .form-control {
	height:24px;
	padding-top:5px
}
}
.login-layer .checkbox-inline, .login-layer .text-primary {
	margin-top:20px
}
.login-layer .btn-primary {
	margin-top:15px
}
.login-layer .login-coagent {
	width:235px
}
.login-layer .login-coagent h4 {
	margin-bottom:30px;
	margin-left:25px
}
.login-layer .login-coagent a {
	margin-right:15px;
	margin-bottom:17px
}
.not-found {
	width:618px;
	height:471px;
	margin:0 auto;
	background:url(not_found_bg.png) 0 99px no-repeat
}
.not-found .media {
	float:right;
	margin-top:170px;
	margin-right:75px;
	font-family:Microsoft Yahei
}
.not-found .media-body {
	font-size:16px;
	line-height:30px;
	color:#b3d465
}
.not-found .func {
	margin-top:5px;
	font-size:14px;
	color:#fff
}
.not-found a {
	margin-left:15px;
	color:#00b7ee;
	text-decoration:none
}
.weixin-follow {
	position:relative
}
.weixin-follow .layer {
	position:absolute;
	z-index:1070;
	display:none;
	width:216px;
	padding:10px 0 10px 10px;
	margin-top:32px;
	margin-left:-80px;
	color:#333;
	background:#fff;
	border:2px solid #62b651;
	-webkit-box-shadow:1px 1px 5px rgba(0,0,0,.5);
	box-shadow:1px 1px 5px rgba(0,0,0,.5);
*margin-left:-100px
}
.weixin-follow .layer .arrow {
	position:absolute;
	left:50%;
	margin-top:-22px;
	margin-left:-11px
}
.weixin-follow .layer img {
	margin-right:10px
}
.weixin-follow .layer .layer-body {
	overflow:hidden;
	line-height:24px;
	text-align:left
}
.weixin-follow .layer .layer-body div {
	margin-bottom:20px
}
.weixin-follow:focus .layer, .weixin-follow:hover .layer {
	display:block
}
.qr-label {
	width:328px;
	padding:10px;
	margin:20px auto 0;
	background:#f4f7fa;
	border:1px solid #eee
}
.qr-label img {
	width:74px;
	height:74px;
	border:3px solid #fff
}
.qr-label .media-heading {
	margin-top:10px;
	font-size:14px;
	color:#333
}
.qr-label .media-body {
	color:#a0a0a0
}
.backdrop {
	position:absolute;
	top:0;
	right:0;
	bottom:0;
	left:0;
	background-color:#000;
	filter:alpha(opacity=50);
	opacity:.5;
	-ms-filter:"alpha(Opacity=50)"
}
.wx_qr {
	float:left;
	padding-left:8px;
	background:#fff
}
.wx_qr .tips {
	margin-top:-10px;
	font:0/0 a;
	color:transparent;
	text-shadow:none;
	background-color:transparent;
	border:0
}
.wx_qr .tips+div {
	margin-top:-43px
}
.wx_qr .txt {
	float:left;
	margin-right:15px;
	font-family:Microsoft Yahei;
	font-size:16px;
	line-height:20px
}
.wx_qr .txt .text-primary {
	font-size:20px
}
.wx_qr.affix {
	top:85px;
	z-index:1030
}
.content-empty {
	margin:0 0 0 10px;
	font-size:14px;
	line-height:110px;
	background-color:#f4f7fa
}
.feedback-layer .modal-dialog {
	width:484px
}
.feedback-layer .modal-title {
	font-size:16px
}
.feedback-layer .modal-content {
	padding:4px;
	border:1px solid #999;
	border:1px solid rgba(0,0,0,.2);
	border-radius:5px
}
.feedback-layer .modal-body {
	position:relative;
	width:362px;
	height:293px;
	padding:20px 30px 0;
	margin:30px auto 26px;
	background-image:url(paper_bg.png);
	background-repeat:no-repeat
}
.feedback-layer .modal-body>a {
	position:absolute;
	top:-20px;
	right:0
}
.feedback-layer .modal-body .form-control {
	width:330px;
	height:28px;
	height:22px \9;
	padding-top:11px \9;
	padding-right:14px;
	padding-left:14px;
	color:#959595;
	background-color:transparent;
	border-color:#dac49f
}
.feedback-layer .modal-body .form-control:-moz-placeholder {
color:#959595
}
.feedback-layer .modal-body .form-control::-moz-placeholder {
color:#959595
}
.feedback-layer .modal-body .form-control:-ms-input-placeholder {
color:#959595
}
.feedback-layer .modal-body .form-control::-webkit-input-placeholder {
color:#959595
}
@media screen and (-ms-high-contrast:active), (-ms-high-contrast:none) {
.feedback-layer .modal-body .form-control {
	height:28px;
	padding-top:5px
}
}
.feedback-layer .modal-body .form-control:focus {
	-webkit-box-shadow:none;
	box-shadow:none
}
.feedback-layer .modal-body textarea {
	width:344px;
	height:156px;
	padding:0 0 0 14px;
	margin-top:40px;
	margin-bottom:10px;
	font-size:14px;
	line-height:40px;
	color:#959595;
	resize:none;
	background-color:transparent;
	background-image:url(paper_line.png);
	background-repeat:no-repeat;
	background-position:0 35px;
	border:none;
	outline:0
}
.feedback-layer .modal-body textarea:-moz-placeholder {
color:#959595
}
.feedback-layer .modal-body textarea::-moz-placeholder {
color:#959595
}
.feedback-layer .modal-body textarea:-ms-input-placeholder {
color:#959595
}
.feedback-layer .modal-body textarea::-webkit-input-placeholder {
color:#959595
}
.feedback-layer .feedback-face {
	position:absolute;
	top:-26px;
	left:-13px
}
.feedback-layer .text-right .btn {
	margin-left:15px;
	text-indent:.4em;
	letter-spacing:.4em
}
.feedback-layer .text-right .btn-link {
	font-size:14px;
	color:#959595
}
.feedback-layer .text-right .btn-link:active, .feedback-layer .text-right .btn-link:focus, .feedback-layer .text-right .btn-link:hover {
	text-decoration:none
}
.feedback-layer .text-right .btn-primary {
	width:110px;
	padding-top:8px;
	padding-right:0;
	padding-bottom:8px;
	padding-left:0;
	font-weight:700;
	text-shadow:1px 1px #92bd25
}
#uvTab {
	background-color:#eee!important;
	background-image:url(service_fix.png)!important;
	border:none!important;
	-webkit-box-shadow:none!important;
	box-shadow:none!important
}
#uvTab:hover {
	background-color:#e5e5e5!important
}
#uvTab, #uvTab:hover {
*background-color:#a0a0a0!important
}
#uvTab #uvTabLabel {
	width:20px;
	height:60px;
	padding-top:30px!important;
	font-weight:400;
	line-height:16px;
	color:#7d7d7d;
	text-align:center
}
#uvTab #uvTabLabel img {
	display:none;
*display:block
}
#uvTab #uvTabLabel:before {
	content:'在线客服'
}
.coagent {
	margin-bottom:10px;
	margin-left:-30px
}
.coagent a {
	float:left;
	margin-bottom:15px;
	margin-left:30px
}
.signin {
	padding:20px;
	margin-bottom:40px;
	color:#7d7d7d;
	background-color:#f4f7fa
}
.signin h2 {
	padding-left:8px;
	margin-top:0;
	margin-bottom:10px;
	font-size:12px;
	line-height:1;
	color:#333;
	border-left:3px solid #62b651
}
.signin .form-control {
	width:190px;
	height:21px;
	height:18px \9;
	padding:0 0 0 7px;
	padding-top:3px \9;
	font-size:12px;
	border:1px solid #e5e5e5
}
@media screen and (-ms-high-contrast:active), (-ms-high-contrast:none) {
.signin .form-control {
	height:21px;
	padding-top:0
}
}
.signin .form-control:focus {
	outline:0;
	-webkit-box-shadow:none;
	box-shadow:none
}
.signin .form-control:first-child {
	border-bottom:none;
	border-top-left-radius:2px;
	border-top-right-radius:2px;
*border-bottom:1px solid #e5e5e5
}
.signin .form-control:nth-child(2) {
	border-bottom-right-radius:2px;
	border-bottom-left-radius:2px
}
.signin .checkbox {
	margin:5px 0
}
.signin .btn-block {
	font-family:Microsoft Yahei;
	font-size:16px;
	text-indent:.5em;
	letter-spacing:.5em
}
.signin .tips {
	margin-bottom:10px
}
.login-list {
	display:inline-block;
	vertical-align:middle;
*display:inline;
*zoom:1
}
.login-list li {
	float:left;
	margin-right:10px
}
.login-list li a {
	display:block
}
.world_cup {
	font:0/0 a;
	color:transparent;
	text-shadow:none;
	background-color:transparent;
	border:0;
*text-indent:-999em
}
.main_nav_index .items .world_cup {
	padding-top:7px;
	padding-bottom:4px
}
.main_nav .items .world_cup {
	padding-top:5px;
	padding-bottom:3px
}
html:not(.huodongxing) .article * {
	pointer-events:none
}
#find-events {
	padding-top:85px
}
.tags {
	padding-left:3px
}
.tags a {
	display:inline-block;
	padding:4px 12px;
	margin-right:9px;
	margin-bottom:18px;
	color:#7e7e7e;
	border:1px dashed #a7a8a9;
*display:inline;
*zoom:1
}
.tags a:active, .tags a:focus, .tags a:hover {
	text-decoration:none;
	background:#eee
}
.tags a.active {
	color:#fff;
	background:#62b651;
	border-color:#62b651;
	border-style:solid
}
.tags-block {
	width:240px;
	margin-bottom:30px;
	background:#fff
}
.tags-block a {
	display:inline-block;
	padding:8px 12px;
	margin-right:10px;
	margin-bottom:10px;
	color:#a0a0a0;
	background:#fff;
	border:1px solid #efeed9;
	border-radius:2px;
*display:inline;
*zoom:1
}
.tags-block a:active, .tags-block a:focus, .tags-block a:hover {
	text-decoration:none;
	background:#eee
}
.tags-block a.active {
	color:#fff;
	background:#62b651;
	border-color:#62b651
}
.view-category {
	padding-bottom:5px;
	margin:0 0 20px;
	color:#7d7d7d;
	border-bottom:1px solid #ddd
}
.view-category .view_block, .view-category .view_list {
	margin-right:8px;
	vertical-align:-5px;
	cursor:pointer
}
.view-category .view_block.active, .view-category .view_list.active {
	cursor:default
}
.view-category .checkbox-list, .view-category span {
	display:inline-block;
*display:inline;
*zoom:1
}
.view-category .checkbox-list {
	margin-left:40px
}
.view-category .divider {
	margin:0 18px;
	color:#ddd;
	vertical-align:middle
}
.view-category-list {
	margin-bottom:30px;
	color:#7d7d7d
}
.view-category-list>li {
	padding-left:50px;
	margin-bottom:10px
}
.view-category-list>li>div {
	float:left;
	margin-left:-50px
}
.view-category-list>li li {
	float:left;
	padding:2px 5px;
	margin-right:5px
}
.view-category-list>li li a {
	padding:2px 5px
}
.view-category-list>li li a:active, .view-category-list>li li a:focus, .view-category-list>li li a:hover {
	color:#fff;
	text-decoration:none;
	background:#2578bf
}
.view-category-list>li li.active {
	background:#62b651;
	border-radius:2px
}
.view-category-list>li li.active a {
	color:#fff;
	cursor:default
}
.view-category-list>li li.active a:active, .view-category-list>li li.active a:focus, .view-category-list>li li.active a:hover {
	background:0 0
}
#slides-thumbnail {
	position:relative;
	margin-bottom:40px
}
#slides-thumbnail>div {
	display:none
}
#slides-thumbnail>div:first-child {
	display:block
}
#slides-thumbnail .slidesjs-slide img {
	display:block;
	width:683px;
	height:260px;
	border:1px solid #a0a0a0
}
#slides-thumbnail .slidesjs-pagination {
	position:absolute;
	right:16px;
	bottom:-3px;
	z-index:20;
	margin-bottom:0;
	vertical-align:bottom;
*bottom:0
}
#slides-thumbnail .slidesjs-pagination .slidesjs-pagination-item {
	display:inline-block;
	margin-right:4px;
*display:inline;
*zoom:1
}
#slides-thumbnail .slidesjs-pagination .slidesjs-pagination-item a {
	display:block;
	width:42px;
	height:30px;
	overflow:hidden;
	filter:alpha(opacity=60);
	border:3px solid #fff;
	opacity:.6;
	-webkit-transition:.3s;
	transition:.3s;
	-ms-filter:"alpha(Opacity=60)"
}
#slides-thumbnail .slidesjs-pagination .slidesjs-pagination-item a img {
	height:100%;
	margin-left:-35px
}
#slides-thumbnail .slidesjs-pagination .slidesjs-pagination-item a.active {
	filter:alpha(opacity=100);
	opacity:1;
	-webkit-transform:scale(1.1) translate(0, -2px);
	-ms-transform:scale(1.1) translate(0, -2px);
	transform:scale(1.1) translate(0, -2px);
	-ms-filter:"alpha(Opacity=100)"
}
#slides-multiple-list {
	position:relative;
	padding:10px;
	margin-top:20px;
	margin-bottom:40px;
	overflow:visible!important;
	background-color:#f4f7fa
}
#slides-multiple-list .slidesjs-navigation {
	top:-52px
}
#slides-multiple-list .slidesjs-previous {
	right:28px
}
#slides-multiple-list .slidesjs-next {
	right:0
}
#slides-multiple-list .event-vertical-list {
	margin-left:-10px
}
#slides-multiple-list .event-vertical-list li {
	margin-left:11px;
	_display:inline
}
.slidesjs-navigation {
	position:absolute;
	font:0/0 a;
	color:transparent;
	text-shadow:none;
	background-color:transparent;
	border:0;
	outline:0
}
#slides-multiple-list .slidesjs-navigation {
	cursor:default
}
#slides-multiple-list .slidesjs-navigation.enable {
	cursor:pointer
}
.slides-list {
	position:relative;
	padding-top:15px;
	padding-right:30px;
	padding-left:30px;
	margin-bottom:40px;
	background:#f4f7fa
}
.slides-list ul {
	margin-left:-24px;
*zoom:1
}
.slides-list li {
	float:left;
	width:105px;
	height:105px;
	margin-left:24px
}
.slides-list li a {
	display:block;
	text-align:center
}
.slides-list li a img {
	display:block;
	width:105px;
	height:105px;
	margin-bottom:8px;
	border:1px solid #d2d2d2
}
.slides-list li a span {
	display:inline-block;
	max-width:100%;
	overflow:hidden;
	text-overflow:ellipsis;
	white-space:nowrap;
*display:inline;
*zoom:1
}
.slides-list li a:hover {
	text-decoration:none
}
.slides-list li a:hover img {
	border-color:#62b651
}
.slides-list li a:hover span {
	color:#fff;
	background:#2578bf
}
.slides-list .slidesjs-previous {
	top:68px;
	left:10px
}
.slides-list .slidesjs-next {
	top:68px;
	right:10px
}
.event-vertical-list {
	color:#7d7d7d
}
.event-vertical-list li {
	float:left;
	width:202px;
	padding:7px 6px 0;
	overflow:hidden;
	background:#fff
}
.event-vertical-list li img {
	display:inline-block;
	max-width:100%;
	height:auto
}
.event-vertical-list li h3 {
	height:38px;
	margin-top:12px;
	margin-bottom:8px;
	overflow:hidden
}
.event-vertical-list li h3 a {
	font-weight:400;
	line-height:19px
}
.event-vertical-list li h3 a:active, .event-vertical-list li h3 a:focus, .event-vertical-list li h3 a:hover {
	color:#fff;
	text-decoration:none;
	background-color:#2578bf
}
.event-vertical-list li p {
	height:34px;
	overflow:hidden;
	color:#a0a0a0
}
.event-vertical-list li .apply {
	padding:10px;
	margin:12px -6px 0;
	border-top:1px dotted #eee
}
.event-vertical-list li .apply .btn {
	float:right;
	margin-top:-1px
}
.event-vertical-list li .apply .name {
	display:inline-block;
	width:88px;
	overflow:hidden;
	text-overflow:ellipsis;
	white-space:nowrap;
	vertical-align:middle;
*display:inline;
*zoom:1;
*width:80px
}
.event-details .event-vertical-list {
	margin-bottom:32px;
	margin-left:-13px
}
.event-details .event-vertical-list li {
	width:150px;
	padding:0;
	margin-left:13px
}
.event-details .event-vertical-list li h3 {
	font-size:14px
}
.event-horizontal-list {
	padding:10px;
	color:#7d7d7d;
	background-color:#f4f7fa
}
.event-horizontal-list li {
	padding:20px 250px 20px 20px;
	margin-top:10px;
	background:#fff;
	border-radius:4px;
*zoom:1
}
.event-horizontal-list li:first-child {
	margin-top:0
}
.event-horizontal-list li>a img {
	float:right;
	margin-right:-232px;
*position:relative;
*right:-20px
}
.event-horizontal-list li h3 {
	margin-top:0;
	margin-bottom:8px
}
.event-horizontal-list li h3 a {
	display:inline-block;
	max-width:100%;
	overflow:hidden;
	font-weight:400;
	line-height:19px;
	text-overflow:ellipsis;
	white-space:nowrap;
*display:inline;
*zoom:1
}
.event-horizontal-list li h3 a:active, .event-horizontal-list li h3 a:focus, .event-horizontal-list li h3 a:hover {
	color:#fff;
	text-decoration:none;
	background-color:#2578bf
}
.event-horizontal-list li p {
	display:-webkit-box;
	height:50px;
	margin-bottom:15px;
	overflow:hidden;
	color:#a0a0a0;
	text-indent:24px;
	-webkit-line-clamp:3;
	-webkit-box-orient:vertical
}
.event-horizontal-list li .apply .btn {
	float:right;
	margin-top:-3px
}
.event-horizontal-list li .apply .name {
	display:inline-block;
	width:84px;
	overflow:hidden;
	color:#7d7d7d;
	text-overflow:ellipsis;
	white-space:nowrap;
	vertical-align:middle;
*display:inline;
*zoom:1
}
.event-horizontal-list li .time {
	margin:0 14px 0 0
}
.event-list-more, .user-attention-list, .user-list {
	padding-top:20px
}
.event-list-more {
	margin-bottom:30px;
	margin-left:-60px;
	color:#7d7d7d
}
.event-list-more .media {
	float:left;
	width:312px;
	margin-top:0;
	margin-bottom:40px;
	margin-left:60px
}
.event-list-more .media .pull-left {
	margin-right:20px
}
.event-list-more .media .media-object {
	border:1px solid #d3d3d3
}
.event-list-more .media .media-body {
	padding-right:5px
}
.event-list-more .media .media-body .title {
	height:35px;
	margin-bottom:8px;
	overflow:hidden
}
.event-list-more .media .media-body a:active, .event-list-more .media .media-body a:focus, .event-list-more .media .media-body a:hover {
	color:#fff;
	text-decoration:none;
	background-color:#2578bf
}
.event-list-more .media .media-body em {
	margin-right:30px;
	font-style:normal
}
.aside .event-list-more .media {
	width:auto;
	padding-bottom:10px;
	margin-bottom:10px;
	border-bottom:1px dashed #eff7f1
}
.aside .event-list-more .media-body {
	padding-right:0
}
.aside .event-list-more .media-object {
	width:98px;
	height:59px
}
.user-attention-list {
	margin-bottom:40px;
	color:#7d7d7d
}
.user-attention-list .media {
	padding-bottom:18px;
	margin-bottom:15px;
	border-bottom:1px dashed #eff7f1
}
.user-attention-list .media-object {
	width:35px;
	height:35px
}
.user-attention-list .media-body .title {
	width:195px;
	margin-bottom:5px;
	overflow:hidden;
	font-size:14px;
	line-height:1;
	text-overflow:ellipsis;
	white-space:nowrap
}
.user-attention-list .media-body .title a {
	margin-right:7px;
	vertical-align:middle
}
.user-attention-list .media-body .num {
	margin-right:20px
}
.user-list {
	margin-bottom:40px;
	color:#7d7d7d
}
.user-list .media {
	padding-left:20px;
	margin-bottom:20px
}
.user-list .media-object {
	width:46px;
	height:46px
}
.user-list .media-body .name {
	margin-bottom:10px
}
.user-list .media-body strong {
	margin-left:25px;
	font-weight:400;
	color:#333
}
.event-list-block {
*zoom:1
}
.event-list-block>li {
	padding:10px 0;
	margin-bottom:30px;
	background-color:#f4f7fa
}
.event-list-block>li .event-vertical-list>li {
	margin:0 13px
}
.event-list-admin {
	width:642px;
	padding:10px 10px 0;
	margin-left:10px;
	color:#7d7d7d;
	background-color:#f4f7fa;
	border-bottom:1px solid transparent
}
.event-list-admin>li {
	position:relative;
	padding:20px;
	margin-bottom:10px;
	overflow:hidden;
	background-color:#fff;
	border-radius:4px
}
.event-list-admin>li h3 {
	margin-top:0;
	margin-bottom:15px
}
.event-list-admin>li h3 a:hover {
	color:#fff;
	text-decoration:none;
	background-color:#2578bf
}
.event-list-admin>li .divider {
	margin:0 5px;
	color:#7d7d7d
}
.event-list-admin>li .info {
	margin-bottom:15px
}
.event-list-admin>li .info .state {
	margin-left:25px
}
.event-list-admin>li .info .state strong {
	display:inline-block;
	width:50px;
	height:14px;
	font-weight:400;
	line-height:14px;
	color:#fff;
	text-align:center;
*display:inline;
*zoom:1
}
.event-list-admin>li .info .state .begin {
	background-color:#fa9f00
}
.event-list-admin>li .info .state .over {
	background-color:#b5b5b5
}
.event-list-admin>li .info .state .draft {
	background-color:#88abda
}
.event-list-admin>li .share {
	float:right;
	margin-top:-4px;
	margin-right:-124px
}
.event-list-admin>li .share .default {
	margin-right:4px
}
.event-list-admin>li .share.expand {
	margin-right:-3px
}
.event-list-admin>li.event-admin-lite .icon-mobile {
	float:left;
	margin-top:1px;
	margin-top:0\9\0;
	margin-left:-14px
}
.event-list-admin.event-list-admin-favorite li .close {
	position:absolute;
	top:5px;
	right:5px;
	display:none;
	width:11px;
	height:11px;
	font:0/0 a;
	color:#fff;
	color:transparent;
	text-shadow:none;
	background-color:transparent;
	filter:alpha(opacity=100);
	border:0;
	outline:0;
	opacity:1;
	-ms-filter:"alpha(Opacity=100)"
}
.event-list-admin.event-list-admin-favorite li:hover .close {
	display:block
}
.event-list-img {
	padding-top:10px;
	margin-bottom:40px;
	font-size:14px
}
.event-list-img li {
	padding-left:20px;
	margin-bottom:10px;
	overflow:hidden;
	line-height:24px;
	text-overflow:ellipsis;
	white-space:nowrap;
*padding-left:0
}
.event-list-img li:before {
	position:absolute;
	width:4px;
	height:4px;
	margin-top:8px;
	margin-left:-15px;
	content:'';
	background-color:#b5b5b5;
	border-radius:4px
}
.event-list-img li:hover:before {
	background-color:#62b651
}
.event-list-img .img-detail {
	padding-bottom:8px;
	margin-top:2px;
	margin-bottom:10px;
	font-size:12px;
	color:#7d7d7d;
	border-bottom:1px solid #f4f7fa
}
.event-list-img .img-detail img {
	display:block;
	width:240px;
	max-width:100%;
	height:auto;
	height:142px;
	margin-bottom:10px
}
.event-list-img .img-detail .btn {
	margin-top:-3px
}
.event-list-img.event-rank-list {
	padding:0
}
.event-list-img.event-rank-list .img-detail {
	position:relative;
	margin-top:0
}
.event-list-img.event-rank-list .img-detail div {
	position:absolute;
	top:112px;
	right:0;
	left:0;
	height:30px;
	font-size:14px;
	line-height:30px;
	color:#fff
}
.event-list-img.event-rank-list .img-detail div em {
	width:30px;
	margin-right:10px;
	font-size:24px;
	font-style:normal;
	font-weight:700;
	text-align:center;
	background:#cc3743
}
.event-list-img.event-rank-list .img-detail div em, .event-list-img.event-rank-list .img-detail div em+span {
	position:relative;
	float:left
}
.event-list-img.event-rank-list .img-detail div em+span {
	width:200px;
	height:30px;
	overflow:hidden;
	}
.event-list-img.event-rank-list ul:before {
	position:absolute;
	margin-top:6px;
	content:''
}
.event-list-img.event-rank-list li:before {
	content:none
}
.organizer-hot-list {
	padding-top:15px;
	margin-bottom:10px;
	color:#7d7d7d;
*padding-bottom:30px
}
.organizer-hot-list li {
	float:left;
	width:50%;
	margin-bottom:30px;
*width:49%
}
.organizer-hot-list li h3 {
	margin:0 0 8px;
	overflow:hidden;
	font-size:14px;
	text-overflow:ellipsis;
	white-space:nowrap
}
.organizer-hot-list li h3 a:hover {
	color:#fff;
	text-decoration:none;
	background:#2578bf
}
.organizer-hot-list li img {
	float:left;
	width:68px;
	height:68px;
	margin-right:10px;
	border:1px solid #d2d2d2
}
.organizer-hot-list li .media-body {
	margin-right:10px
}
.organizer-hot-list li .media-body div {
	line-height:24px
}
.organizer-well-list {
	padding-top:15px;
	margin-bottom:40px;
	margin-left:-30px;
	color:#7d7d7d;
	text-align:center
}
.organizer-well-list li {
	float:left;
	width:70px;
	margin-left:44px
}
.organizer-well-list li a div {
	display:inline-block;
	max-width:100%;
*display:inline;
*zoom:1
}
.organizer-well-list li a:hover div {
	color:#fff;
	background:#2578bf
}
.organizer-well-list li a div {
	overflow:hidden;
	text-overflow:ellipsis;
	white-space:nowrap
}
.organizer-well-list li img {
	display:block;
	width:68px;
	height:68px;
	margin-bottom:8px;
	border:1px solid #d2d2d2
}
.organizer-label-list {
	padding:25px 0 10px 30px;
	margin-bottom:40px;
	background:#f4f7fa
}
.organizer-label-list li {
	float:left;
	width:58px;
	margin-right:10px;
	margin-bottom:15px;
	overflow:hidden;
	text-overflow:ellipsis;
	white-space:nowrap
}
.organizer-label-list li a {
	display:inline-block;
	padding:4px 5px;
*display:inline;
*zoom:1
}
.organizer-label-list li a.current, .organizer-label-list li a:hover {
	color:#fff;
	text-decoration:none;
	background:#62b651
}
.organizer-lv2-list>li {
	padding-top:20px;
	padding-bottom:20px;
	border-bottom:1px solid #f4f7fa
}
.organizer-lv2-list>li>a img {
	float:left;
	width:108px;
	height:108px;
	margin-right:20px;
	border:1px solid #d2d2d2
}
.organizer-lv2-list>li>a h3 {
	display:inline-block;
	max-width:554px;
	margin-top:0;
	margin-bottom:5px;
	overflow:hidden;
	font-size:14px;
	font-weight:700;
	text-overflow:ellipsis;
	white-space:nowrap;
*display:inline;
*zoom:1
}
.organizer-lv2-list>li>a:hover h3 {
	color:#fff;
	background:#2578bf
}
.organizer-lv2-list>li>a:hover img {
	border-color:#62b651
}
.organizer-lv2-list>li p {
	height:34px;
	margin-bottom:10px;
	overflow:hidden;
	color:#7d7d7d
}
.organizer-lv2-list>li p+div {
	margin-bottom:8px
}
.organizer-lv2-list>li .pull-right {
	width:180px
}
.organizer-lv2-list>li .pull-right span {
	float:left
}
.organizer-lv2-list>li ul {
	float:left;
	margin-top:-7px;
	margin-left:-4px
}
.organizer-lv2-list>li li {
	float:left;
	margin-left:4px
}
.organizer-lv2-list>li li .face {
	margin:0
}
.calendar-list {
	color:#333
}
.calendar-list-header {
	margin-bottom:5px;
	font-family:Microsoft Yahei;
	font-size:24px
}
.calendar-list-header span {
	float:left
}
.calendar-list-header .btn-cal-next, .calendar-list-header .btn-cal-prev {
	margin-top:4px
}
.calendar-list-header .btn-cal-prev {
	margin-left:15px
}
.calendar-list-header .btn-cal-next {
	margin-right:6px
}
.calendar-list-header .text-primary {
	margin:0 6px
}
.calendar-list-header .btn-today {
	padding:1px 16px;
	margin-top:5px;
	margin-left:50px;
	font-size:14px;
	color:#333;
	background:#ecf5ed;
	border-color:#62b651;
	border-radius:16px
}
.calendar-list-header .btn-today:hover {
	color:#fff;
	background:#70cb5c;
	border-color:#62b651
}
.calendar-list-header .btn-today:active {
	color:#fff;
	background:#62b651;
	border-color:#62b651;
	-webkit-box-shadow:inset 0 1px 0 rgba(63,141,47,.75);
	box-shadow:inset 0 1px 0 rgba(63,141,47,.75)
}
.calendar-list-header .pull-right {
	margin-top:5px;
	font-size:12px;
	color:#7d7d7d
}
.calendar-list-header .pull-right span:first-child {
	margin-top:4px;
	margin-right:5px
}
.calendar-list-body {
	background:#f4f7fa
}
.calendar-list-body .month-title {
	padding-top:16px;
	padding-bottom:7px;
	padding-left:20px;
	font-family:Microsoft Yahei;
	font-size:18px;
	border-bottom:1px solid #fff
}
.calendar-list-body .calendar-list-content {
	position:relative;
	padding-top:40px;
	padding-bottom:45px;
	padding-left:12px;
	margin-right:20px;
	margin-left:20px;
	border-left:3px solid #aad6db
}
.calendar-list-body .day-title {
	margin-bottom:8px;
	font-family:Microsoft Yahei;
	font-size:16px
}
.calendar-list-body .day-title:before {
	position:absolute;
	margin-top:3px;
	margin-left:-22px;
	content:''
}
.calendar-list-body .text-warning {
	margin-top:18px;
	font-family:Microsoft Yahei;
	font-size:20px
}
.calendar-list-body .icon-circle-xs {
	position:absolute;
	left:-5px
}
.calendar-list-body .icon-circle-xs.icon-circle-1st {
	top:10px
}
.calendar-list-body .icon-circle-xs.icon-circle-2nd {
	top:25px
}
.calendar-list-body .icon-circle-xs.icon-circle-3rd {
	bottom:25px
}
.calendar-list-body .icon-circle-xs.icon-circle-4th {
	bottom:10px
}
.calendar-list-body .btn-cal-down, .calendar-list-body .btn-cal-up {
	position:absolute;
	right:-20px
}
.calendar-list-body .btn-cal-up {
	top:10px
}
.calendar-list-body .btn-cal-down {
	top:31px
}
.calendar-list-body li {
	padding-bottom:10px;
	margin-bottom:10px;
	border-bottom:1px solid #e5e5e5
}
.calendar-list-body li h2 {
	margin-top:0;
	margin-bottom:6px;
	font-family:Microsoft Yahei;
	font-size:20px
}
.calendar-list-body li h2 a:hover {
	text-decoration:none
}
.calendar-list-body li p {
	color:#7d7d7d
}
.calendar-list-body li .btn {
	font-size:14px
}
.calendar-list-body li .btn.btn-primary {
	margin-right:10px
}
div.calendar-list-body li .btn {
	cursor:default
}
.calendar-list-body li .share {
	margin-top:7px
}
.calendar-list-body .more-list {
	line-height:30px;
	color:#7d7d7d;
	text-align:center;
	cursor:pointer;
	background:#e5e5e5
}
.calendar-list-body .more-list:hover {
	background:#dcdcdc
}
.calendar-list-body .more-list:after {
	position:absolute;
	margin-top:6px;
	margin-left:5px;
	content:''
}
#container.event-details .article {
	width:640px
}
#container.event-details .aside {
	width:280px
}
#container.event-details .forum h2 {
	padding-left:0
}
#container.event-details .sendbox {
	margin-left:0
}
.jumbotron {
	position:relative;
	height:262px;
	margin-bottom:40px;
	font-size:14px
}
.jumbotron .media-object {
	width:440px;
	height:260px;
	border:1px solid #b5b5b5
}
.jumbotron .pull-left {
	margin-right:20px
}
.jumbotron .media-heading {
	margin-bottom:15px;
	font-family:Microsoft Yahei;
	font-size:22px
}
.jumbotron .media-body>div {
	margin-bottom:15px
}
.jumbotron .media-body .address {
	max-height:40px;
	padding-left:24px;
	overflow:hidden;
	text-overflow:ellipsis;
	white-space:nowrap
}
.jumbotron .media-body .address em {
	float:left;
	margin-top:2px;
	margin-left:-24px
}
.jumbotron .media-body .func {
	position:absolute;
	bottom:0;
	left:462px;
	margin-bottom:0
}
.jumbotron .media-body .func .btn {
	padding-right:18px;
	padding-left:18px;
	margin-right:20px;
	font-size:14px
}
.jumbotron .media-body .func .btn-sign-up1, .jumbotron .media-body .func .btn-sign-up2, .jumbotron .media-body .func .btn-sign-up3, .jumbotron .media-body .func .btn-sign-up4 {
	padding-top:0;
	padding-bottom:0;
	font:0/0 a;
	color:transparent;
	text-shadow:none;
	background-color:transparent;
	border:0
}
.jumbotron .media-body .func .btn-like {
*padding:8px 0
}
.jumbotron .media-body .func .share {
	display:inline-block;
	font-size:12px;
*display:inline;
*zoom:1
}
.jumbotron .media-body .func strong {
	font-size:12px
}
.jumbotron em {
	margin-right:10px;
	font-style:normal;
	color:#a0a0a0;
	vertical-align:middle
}
.jumbotron .ticket-tips {
	position:absolute;
	margin-top:-70px;
	margin-left:29px;
	font-size:12px;
	line-height:24px;
	line-height:25px \9;
	color:#7d7d7d;
	text-align:center;
*margin-top:-25px;
*margin-left:-106px
}
.event-tags {
	padding-right:60px;
	border-bottom-color:#f4f7fa
}
.event-tags li {
	padding-bottom:12px;
	font-size:16px
}
.event-tags li a {
	max-width:84px;
	padding-right:10px;
	padding-left:10px;
	overflow:hidden;
	color:#959595;
	text-overflow:ellipsis;
	white-space:nowrap
}
.event-tags li a:hover {
	color:#333
}
.event-tags li.active a {
	max-width:none;
	color:#333
}
.event-tags li.active:after {
	position:absolute;
	bottom:0;
	left:0;
	width:100%;
	height:3px;
	content:'';
	background-color:#62b651
}
.event-intro {
	margin-bottom:40px
}
.event-intro p {
	padding-top:15px;
	font-size:14px
}
.event-intro img {
	display:block;
	max-width:100%;
	height:auto
}
.event-intro .text-center {
	margin-top:8px
}
.event-intro .text-center a:hover {
	text-decoration:none
}
.event-intro .func {
	margin-top:10px;
	text-align:center
}
.event-intro .func .btn {
	padding:7px 18px;
	margin-right:10px;
	font-size:14px;
*padding:5px 0
}
.organizer-list {
	padding-top:20px;
	margin-bottom:40px
}
.organizer-list .media-object {
	width:80px;
	height:80px;
	border:1px solid #b5b5b5
}
.organizer-list .media-heading {
	font-weight:700
}
.organizer-list p {
	display:-webkit-box;
	height:52px;
	overflow:hidden;
	color:#a0a0a0;
	-webkit-line-clamp:3;
	-webkit-box-orient:vertical
}
.organizer-list .event-list-img {
	margin-bottom:20px
}
.organizer-list .event-list-img li {
	padding-left:10px
}
.organizer-list .event-list-img li:before {
	margin-left:-10px
}
.map-details h1 {
	margin-top:0;
	margin-bottom:30px;
	font-family:Microsoft Yahei;
	font-size:24px
}
.map-details h2 {
	margin-top:0;
	margin-bottom:15px;
	font-size:14px;
	line-height:20px
}
.map-details h2.title {
	margin-bottom:60px
}
.map-details h2.title a:hover {
	color:#fff;
	text-decoration:none;
	background-color:#2578bf
}
.map-details .aside {
	float:right;
	width:320px
}
.map-details .aside p {
	margin-bottom:15px;
	color:#959595
}
.map-lg {
	float:left;
	width:590px;
	height:400px;
	overflow:hidden
}
.BaiDuMap {
	margin-bottom:40px!important
}
.layer-success .close, .layer-warning .close {
	position:relative;
	z-index:1;
	margin-top:5px;
	margin-right:10px
}
.layer-success .modal-dialog {
	width:460px
}
.layer-success h3 {
	margin-top:0;
	margin-bottom:20px;
	font-size:18px;
	font-weight:700;
	color:#2578bf
}
.layer-success .modal-body {
	padding-top:30px;
	padding-right:20px;
	padding-bottom:10px;
	padding-left:65px
}
.layer-success .modal-body .tips {
	margin-bottom:15px;
	color:#a0a0a0
}
.layer-success .modal-body p {
	margin-bottom:0
}
.layer-success .icon-success {
	position:absolute;
	margin-top:-3px;
	margin-left:-30px
}
.layer-warning .modal-dialog {
	width:400px
}
.layer-warning.layer-warning-short .modal-dialog {
	width:260px
}
.layer-warning .modal-body {
	padding:40px 0 25px;
	text-align:center
}
.layer-warning p {
	font-size:14px
}
.layer-danger .modal-dialog {
	width:170px
}
.layer-danger .modal-title {
	padding-top:10px;
	padding-bottom:10px;
	padding-left:28px;
	font-size:16px;
	background-color:transparent
}
.layer-danger .close {
	position:relative;
	z-index:1;
	margin-top:0;
	margin-right:5px
}
.login-notify {
	position:fixed;
	right:0;
	bottom:0;
	left:0;
	z-index:1050;
	display:none;
	height:118px;
	color:#fff
}
.login-notify .icon-close-lg {
	z-index:1
}
.login-notify .container {
	position:relative
}
.login-notify .backdrop {
	filter:alpha(opacity=70);
	opacity:.7;
	-ms-filter:"alpha(Opacity=70)"
}
.login-notify .login-list {
	margin-right:30px
}
.login-notify h3 {
	margin:20px 0;
	font-family:Microsoft Yahei;
	font-size:20px
}
.login-notify .btn {
	margin-right:3px;
	border-radius:0
}
.login-notify .media {
	float:left;
	margin-top:20px;
	margin-left:75px
}
.login-notify .media img {
	width:74px;
	height:74px;
	border:3px solid #fff
}
.login-notify h4 {
	margin-bottom:8px;
	font-family:Microsoft Yahei;
	font-size:16px
}
.login-notify strong {
	margin-bottom:10px;
	font-family:Microsoft Yahei;
	font-size:20px;
	font-weight:400
}
.user-page {
	padding-top:298px;
	background-repeat:no-repeat;
	background-position:50% 45px
}
.user-page #container {
	padding-top:0;
	padding-bottom:20px;
	margin-bottom:40px;
	background-color:#fff;
	border:1px solid #eee;
	border-radius:4px
}
.user-info-header {
	position:relative;
	padding:16px 20px 20px 32px;
	overflow:visible
}
.user-info-header .avatar {
	position:relative;
	padding:6px;
	margin-top:-102px;
	margin-right:20px;
	text-align:center;
	background-color:#fff;
	border:1px solid #eee;
	border:1px solid rgba(0,0,0,.1)
}
.user-info-header .avatar img {
	width:170px;
	height:170px;
	border:1px solid #b5b5b5;
	border-radius:2px
}
.user-info-header .avatar .btn {
	display:none
}
.user-info-header .avatar .btn span {
*vertical-align:10px
}
.user-info-header .avatar:active, .user-info-header .avatar:focus, .user-info-header .avatar:hover {
	text-decoration:none
}
.user-info-header .avatar:active .btn, .user-info-header .avatar:focus .btn, .user-info-header .avatar:hover .btn {
	position:absolute;
	top:142px;
	right:10px;
	display:block;
	-webkit-box-shadow:0 0 15px rgba(114,114,114,.5);
	box-shadow:0 0 15px rgba(114,114,114,.5)
}
.user-info-header .avatar:active .btn:active, .user-info-header .avatar:focus .btn:active, .user-info-header .avatar:hover .btn:active {
	-webkit-box-shadow:none;
	box-shadow:none
}
.user-info-header h4 {
	margin-bottom:13px;
	font-family:Microsoft Yahei;
	font-size:20px
}
.user-info-header .info, .user-info-header p {
	color:#7d7d7d
}
.user-info-header .info {
	margin-bottom:18px
}
.user-info-header .info .divider {
	margin:0 8px
}
.user-info-header .edit {
	position:absolute
}
.user-info-header .edit .brief {
	display:none;
	height:17px;
	padding:1px;
	font-size:12px;
	line-height:17px;
	-webkit-box-shadow:none;
	box-shadow:none
}
.user-info-header .edit .text-tips {
	margin-left:10px;
	color:#a0a0a0
}
.user-info-header .edit .text-tips strong {
	font-weight:400;
	color:red
}
.user-info-header [name=brief_edit_area] {
	display:inline-block;
	padding:1px;
	word-wrap:break-word;
	border:1px solid transparent;
	border-radius:2px;
*display:inline;
*zoom:1
}
.user-info-header [name=brief_edit_area].hover {
	min-width:200px;
	background-color:#f6fdff;
	border-color:#7ecef4
}
.user-info-header .intro {
	display:-webkit-box;
	height:52px;
	margin-top:15px;
	margin-bottom:0;
	overflow:hidden;
	-webkit-line-clamp:3;
	-webkit-box-orient:vertical
}
.user-info-header .category {
	color:#a0a0a0
}
.user-info-header .category a {
	margin-right:20px;
	color:#a0a0a0
}
.user-info-header .category a:focus, .user-info-header .category a:hover {
	text-decoration:none
}
.user-info-header .event-create {
	position:absolute;
	top:45px;
	right:20px
}
.user-info-header .attention {
	position:absolute;
	bottom:20px;
	margin-left:-190px
}
.user-info-header .attention .disabled {
	cursor:pointer
}
.user-info-header .attention>.btn {
	width:68px;
	padding:4px 0
}
.user-info-header .attention>.btn span {
	margin-right:6px;
	vertical-align:middle
}
.user-info-header .attention>.btn:first-child {
	margin-right:15px
}
.user-info-header .attention .layer {
	position:absolute;
	top:-74px;
	left:-56px;
	padding:0 15px 8px;
	text-align:center;
	background-color:#fff;
	border-color:#b5b5b5;
	border-style:solid;
	border-width:2px 3px 3px 2px;
	border-radius:2px;
	-webkit-box-shadow:1px 1px rgba(229,229,229,.5);
	box-shadow:1px 1px rgba(229,229,229,.5)
}
.user-info-header .attention .layer h4 {
	font-size:12px
}
.user-info-header .attention .layer .btn {
	padding:3px 12px;
	margin-right:20px;
*padding-left:0;
*padding-right:0
}
.nav-tabs {
	padding-left:32px;
	border-bottom:1px solid #62b651
}
.nav-tabs>li {
	float:left;
	margin-bottom:-1px
}
.nav-tabs>li>a {
	position:relative;
	display:block;
	padding:12px 16px;
	margin-right:10px;
	font-family:Microsoft Yahei;
	font-size:16px;
	line-height:1.42857143;
	color:#333;
	border:1px solid transparent;
*zoom:1
}
.nav-tabs>li>a:hover {
	text-decoration:none;
	border-color:#62b651 #62b651 #fff
}
.nav-tabs>li.active>a, .nav-tabs>li.active>a:focus, .nav-tabs>li.active>a:hover {
	cursor:default;
	border-color:#62b651 #62b651 #fff
}
.nav-tabs>li.active>a:before {
	position:absolute;
	top:0;
	left:0;
	width:100%;
	height:3px;
	content:'';
	background-color:#62b651
}
.user-info {
	padding-top:20px;
	padding-right:300px
}
.user-info>h2 {
	padding-left:20px;
	margin-top:0;
	margin-left:1px;
	line-height:40px;
	background-color:#fbfbfc
}
.user-info .nav {
	float:right;
	width:200px;
	height:348px;
	padding:0 15px;
	margin-right:-275px;
	background-color:#fbfbfc;
*position:relative;
*right:-20px
}
.user-info .nav a {
	display:block;
	padding:6px 18px;
	color:#7d7d7d
}
.user-info .nav a.active, .user-info .nav a:active, .user-info .nav a:focus, .user-info .nav a:hover {
	color:#fff;
	text-decoration:none;
	background-color:#62b651
}
.user-info .nav .divider {
	height:1px;
	margin:10px -15px 0;
	background-color:#e7e7e8
}
.user-info-base {
	padding-left:150px
}
.user-info-base .form-group .btn {
	font-size:14px
}
.user-info-base input+.btn {
	width:80px;
	padding-right:0;
	padding-left:0;
	margin-left:2px
}
.user-info-base .form-group {
	height:40px
}
.user-info-base .avatar-sizes {
	height:164px
}
.user-info-base .avatar-sizes>div {
	float:left;
	margin-right:54px;
	margin-bottom:3px
}
.user-info-base .avatar-sizes .btn-block {
	width:auto
}
.user-info-base .avatar-sizes img {
	display:block;
	max-width:100%;
	height:auto
}
.user-info-base .avatar-sizes .lg .fileupload-preview, .user-info-base .avatar-sizes .lg img {
	width:130px;
	height:130px
}
.user-info-base .avatar-sizes .lg .btn-block {
*width:130px
}
.user-info-base .avatar-sizes .md .fileupload-preview, .user-info-base .avatar-sizes .md img {
	width:95px;
	height:95px
}
.user-info-base .avatar-sizes .md .btn-block {
*width:95px
}
.user-info-base .avatar-sizes .sm .fileupload-preview, .user-info-base .avatar-sizes .sm img {
	width:27px;
	height:27px
}
.user-info-base .avatar-sizes .sm .btn-block {
*width:27px
}
.user-info-base .control-label {
	float:left;
	width:120px;
	margin-left:-135px;
	font-size:16px
}
.user-info-base .control-label em {
	float:right;
	margin-top:2px;
	margin-right:-12px;
	color:#f55
}
.user-info-base .checkbox-inline {
	padding-top:10px
}
.user-info-base [type=email], .user-info-base [type=tel] {
	width:250px
}
.user-info-base select {
	width:148px;
	margin-right:10px;
*height:30px
}
.user-info-base .last.form-control {
	width:335px
}
.user-info-base .submit {
	margin-top:40px;
	text-align:center
}
.user-info-base .submit .btn {
	width:180px
}
.user-info-base .btn-file span {
*vertical-align:5px
}
.user-main {
	padding-top:20px;
	padding-right:20px
}
.user-main .toggles {
	margin-bottom:20px;
	margin-left:30px
}
.user-main .content {
	float:left
}
.user-main .event-horizontal-list {
	width:656px;
	padding:0;
	padding-left:20px;
	background-color:#fff
}
.user-main .event-horizontal-list li {
	padding-top:25px;
	padding-right:235px;
	padding-bottom:25px;
	padding-left:0;
	margin-top:0;
	border-bottom:1px solid #f4f7fa
}
.goto-mobile {
	position:relative;
	display:table;
	width:582px;
	padding:18px 0 0 78px;
	padding-bottom:3px;
	margin-bottom:20px;
	margin-left:10px;
	overflow:visible;
	background:#fffdf8;
	border:1px solid #ffeec2
}
.goto-mobile:after {
	position:absolute;
	bottom:-4px;
	left:0;
	width:100%;
	height:4px;
	content:'';
	background:#ffdb80
}
@media screen and (-webkit-min-device-pixel-ratio:0) {
.goto-mobile:after {
	bottom:-5px;
	left:-1px;
	border-right:1px solid #ffdb80;
	border-left:1px solid #ffdb80
}
}
.goto-mobile .pull-left {
	margin-right:54px;
	text-align:center
}
.goto-mobile .pull-left img {
	display:block;
	margin-bottom:2px;
	border:1px solid #ffdb80
}
.goto-mobile .media-body {
	padding-top:5px;
	font-size:14px
}
.goto-mobile .media-body .title {
	padding-left:55px;
	margin-top:20px
}
.goto-mobile .media-body .title div {
	font-family:Microsoft Yahei;
	font-size:30px;
	color:#7ebfff
}
.goto-mobile .media-body .title:before {
	float:left;
	margin-top:6px;
	margin-left:-55px;
	content:''
}
.goto-mobile .media-body .title span {
	display:block;
	margin-top:-2px;
	font-size:12px;
	color:#a0a0a0
}
.forum {
	width:652px;
	margin-bottom:60px;
	font-size:14px;
	color:#7d7d7d
}
.forum .btn {
	padding:4px 20px;
	font-size:14px;
*padding:4px 10px 0
}
.forum h2 {
	padding-bottom:15px;
	margin-bottom:0;
	border-bottom:1px solid #f4f7fa
}
.forum .sendbox {
	margin-bottom:40px;
	margin-left:10px
}
.forum .sendbox .func .btn {
	float:right
}
.forum .textarea {
	height:80px;
	margin-bottom:5px
}
.forum .textarea textarea {
	width:100%;
	height:80px;
	padding:0;
	margin:0;
	color:#333;
	resize:none;
	border:0
}
.forum .textarea textarea:focus {
	outline:0
}
.forum .textarea.focus {
	border-color:#66afe9;
	outline:0;
	-webkit-box-shadow:inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102,175,233,.6);
	box-shadow:inset 0 1px 1px rgba(0,0,0,.075), 0 0 8px rgba(102,175,233,.6)
}
.forum .feed-list .media {
	padding:20px 0;
	margin-left:20px;
	border-bottom:1px solid #f4f7fa
}
.forum .feed-list .media .pull-left {
	margin-right:15px
}
.forum .feed-list .media .pull-left img {
	display:block;
	width:46px;
	height:46px
}
.forum .feed-list .media .pull-left a {
	display:inline-block;
	width:360px;
	overflow:hidden;
	text-overflow:ellipsis;
	white-space:nowrap;
	vertical-align:middle;
*display:inline;
*zoom:1
}
.forum .feed-list .media .media-heading {
	margin-bottom:15px;
	font-size:14px
}
.forum .feed-list .media .media-heading .name {
	float:left
}
.forum .feed-list .media .media-heading .time {
	float:right;
	color:#c9c9c9
}
.forum .feed-list .icon-reply {
	position:relative;
	font-size:12px
}
.forum .feed-list .icon-reply .arrow {
	position:absolute;
	right:6px;
	bottom:-12px;
	width:0;
	height:0;
	border-color:transparent;
	border-style:solid;
	border-width:6px;
	border-top-width:0;
	border-bottom-color:#f4f7fa
}
.forum .feed-list .func .buttons {
	font-size:12px
}
.forum .feed-list .func .buttons .icon-reply {
	margin-left:14px
}
.forum .feed-list .reply-box {
	background-color:#f4f7fa
}
.forum .feed-list .reply-box .feed-list-reply, .forum .feed-list .reply-box .reply-comment-box {
	padding:6px
}
.forum .feed-list .reply-box .reply-comment-box {
	margin-top:8px
}
.forum .feed-list .reply-box .textarea {
	height:52px;
	border:none;
	border-radius:2px
}
.forum .feed-list .reply-box .textarea textarea {
	height:52px
}
.forum .feed-list .reply-box .media {
	padding-bottom:5px;
	padding-left:0;
	border-color:#e5e5e5
}
.forum .feed-list .reply-box .media .media-heading {
	margin-bottom:5px
}
.forum .feed-list .reply-box .media .pull-left {
	margin-right:10px
}
.forum .feed-list .reply-box .media .pull-left img {
	display:block;
	width:35px;
	height:35px
}
.forum .feed-list .reply-box .media .func .tips {
	font-size:12px;
	color:#b5b5b5
}
.forum .feed-list .reply-box .media p {
	margin-bottom:4px
}
.forum #eventCommentList {
	margin-bottom:20px
}
.intro-container {
	position:relative;
	width:804px;
	padding-bottom:60px;
	padding-left:156px
}
#intro-1, #intro-2, #intro-3, #intro-4 {
	padding-right:10px;
	padding-left:10px
}
:root #intro-1, :root #intro-2, :root #intro-3, :root #intro-4 {
	padding-top:0
}
:root #intro-1:target, :root #intro-2:target, :root #intro-3:target, :root #intro-4:target {
	padding-top:85px
}
.intro-nav {
	position:fixed;
	top:85px;
	float:left;
	width:142px;
	margin-left:-156px
}
.intro-nav>ul {
	padding:2px;
	margin-bottom:20px;
	border:1px solid #c9c9c9
}
.intro-nav li {
	font-size:14px;
	line-height:35px
}
.intro-nav li.active>a {
	font-weight:700;
	background-color:#e8edf3
}
.intro-nav li a {
	display:block;
	padding-left:6px;
	color:#a0a0a0
}
.intro-nav li a:hover {
	text-decoration:none;
	background-color:#e8edf3
}
.intro-nav .last ul {
	display:none
}
.intro-nav .last ul li a {
	padding-left:40px;
	color:#959595
}
.intro-nav .last>a {
	position:relative
}
.intro-nav .last>a:after {
	position:absolute;
	top:50%;
	right:12px;
	width:0;
	height:0;
	margin-top:-7px;
	content:'';
	border-color:transparent;
	border-style:solid;
	border-width:7px;
	border-right-color:#a0a0a0;
	border-left-width:0
}
.intro-nav .last.active>a:hover {
	background-color:transparent
}
.intro-nav .last.active>a:after {
	margin-top:-4px;
	border-color:transparent;
	border-top-color:#a0a0a0;
	border-bottom-width:0;
	border-left-width:7px
}
.intro-nav .last.active ul {
	display:block
}
.intro-content {
	overflow:hidden;
	border-left:1px solid #f4f7fa;
*zoom:1
}
.intro-content .img-step img {
	max-width:160px;
	height:auto
}
.introduce {
	height:450px;
	margin:50px 0 20px 10px
}
.introduce .i {
	position:relative;
	height:100%;
	margin:0 auto;
	overflow:hidden;
	background-repeat:no-repeat
}
.introduce .i1, .introduce .i3, .introduce .i5, .introduce .i7 {
	background-position:right
}
.introduce .i2 {
	background-position:0 100px
}
.introduce .i3 {
	background-position:100% 100px
}
.introduce .i4 {
	background-position:0 110px
}
.introduce .i5 {
	background-position:100% 150px
}
.introduce .i6 {
	background-position:0 120px
}
.introduce .i7 {
	background-position:100% 110px
}
.introduce .i8 {
	background-position:0 90px
}
.i .text {
	position:absolute
}
.introduce .i1 .text, .introduce .i3 .text, .introduce .i5 .text, .introduce .i7 .text {
	left:20px
}
.introduce .i2 .text, .introduce .i4 .text, .introduce .i6 .text, .introduce .i8 .text {
	top:120px;
	left:450px;
	width:320px
}
.introduce .i1 .text {
	top:100px;
	width:243px
}
.introduce .i3 .text {
	top:130px;
	width:200px
}
.introduce .i5 .text {
	top:150px;
	width:230px
}
.introduce .i6 .text {
	top:130px;
	left:580px;
	width:200px
}
.introduce .i7 .text {
	top:110px;
	width:280px
}
.introduce .i8 .text {
	left:510px;
	width:260px
}
.introduce .titleCN {
	font-family:Microsoft Yahei;
	font-size:24px
}
.introduce .titleEN {
	font-family:Microsoft Yahei;
	font-size:20px
}
.introduce .titleL {
	padding-left:20px
}
.introduce .titleR {
	padding-right:20px
}
.introduce .text p {
	font-size:14px;
	line-height:2
}
.sb, .styled_table table {
	margin:10px 0 20px;
	color:#595959;
	text-shadow:none;
	border-collapse:separate;
	border-radius:4px;
	border:1px dashed #d1d1d1
}
.sb_shadow, .styled_table.shadow table {
	-webkit-box-shadow:0 0 3px rgba(0,0,0,.2);
	-moz-box-shadow:0 0 3px rgba(0,0,0,.2);
	box-shadow:0 0 3px #000
}
.sb .box_title, .styled_table table tr th {
	font-size:16px;
	font-weight:400;
	color:#121212;
	background:#d2d2d2;
	background:-webkit-gradient(linear, left top, left bottom, from(#fefefe), to(#d2d2d2));
	background:-moz-linear-gradient(top, #fefefe, #d2d2d2);
filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#fefefe', endColorstr='#d2d2d2')
}
.styled_table table tr td, .styled_table table tr th {
	padding:10px 20px;
	text-align:left;
	border:1px dashed #e3e3e3
}
.styled_table table tr:first-child th {
	border:0
}
.sb_brown, .table_brown table {
	border:1px solid #885020
}
.table_brown table tr td {
	border-top:1px dashed #e7dcd2
}
.sb_brown .box_title, .table_brown table tr th {
	color:#fff;
	background:#ae5445;
	background:-webkit-gradient(linear, left top, left bottom, from(#bc5b4b), to(#ae5445));
	background:-moz-linear-gradient(top, #bc5b4b, #ae5445);
filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#bc5b4b', endColorstr='#ae5445')
}
.sb_blue, .table_blue table {
	border:1px solid #4a98d9
}
.table_blue table tr td {
	border-top:1px solid #d3dee8
}
.sb_blue .box_title, .table_blue table tr th {
	color:#fff;
	background:#2d7cbe;
	background:-webkit-gradient(linear, left top, left bottom, from(#4a98d9), to(#2d7cbe));
	background:-moz-linear-gradient(top, #4a98d9, #2d7cbe);
filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#4a98d9', endColorstr='#2d7cbe')
}
.sb_green, .table_green table {
	border:1px solid #57790d
}
.table_green table tr td {
	border-top:1px solid #ebf1db
}
.sb_green .box_title, .table_green table tr th {
	color:#fff;
	background:#91ac48;
	background:-webkit-gradient(linear, left top, left bottom, from(#9dbb4e), to(#91ac48));
	background:-moz-linear-gradient(top, #9dbb4e, #91ac48);
filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#9dbb4e', endColorstr='#91ac48')
}
.sb_dark_gray, .table_dark_gray table {
	border:1px dashed #363636
}
.table_dark_gray table tr td {
	border:1px dashed #d3d3d3
}
.sb_dark_gray .box_title, .table_dark_gray table tr th {
	color:#fff;
	background:#3f3f3f;
	background-color:#525252
}
.sb_orange, .table_orange table {
	border:1px solid #eb8e21
}
.table_orange table tr td {
	border-top:1px solid #f6e2cc
}
.sb_orange .box_title, .table_orange table tr th {
	color:#fff;
	background:#cf7d1d;
	background:-webkit-gradient(linear, left top, left bottom, from(#eb8e21), to(#cf7d1d));
	background:-moz-linear-gradient(top, #eb8e21, #cf7d1d);
filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#eb8e21', endColorstr='#cf7d1d')
}
.sb_pink, .table_pink table {
	border:1px solid #8c286d
}
.sb_pink .box_title, .table_pink table tr th {
	color:#fff;
	background:#7c2360;
	background:-webkit-gradient(linear, left top, left bottom, from(#8c286d), to(#7c2360));
	background:-moz-linear-gradient(top, #8c286d, #7c2360);
filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#8c286d', endColorstr='#7c2360')
}
.table_pink table tr td {
	border-top:1px solid #f4d5e0
}
.sb_purple, .table_purple table {
	border:1px solid #9665b5
}
.table_purple table tr td {
	border-top:1px solid #e0d7e3
}
.sb_purple .box_title, .table_purple table tr th {
	color:#fff;
	background:#8e60ac;
	background:-webkit-gradient(linear, left top, left bottom, from(#a16dc3), to(#8e60ac));
	background:-moz-linear-gradient(top, #a16dc3, #8e60ac);
filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#a16dc3', endColorstr='#8e60ac')
}
.about-content, .logo-content {
	background:#f4f7fa;
	border-left:none;
	border-top-right-radius:15px
}
.about-content h2, .logo-content h2 {
	height:32px;
	padding-left:20px;
	margin:0 0 0 20px;
	font-family:Microsoft Yahei;
	font-size:20px;
	line-height:32px;
	color:#fff;
	background:#62b651;
	border-top-right-radius:15px;
*margin-left:0;
*padding-left:40px
}
.about-content h2:before, .logo-content h2:before {
	float:left;
	margin-left:-40px;
	content:''
}
.about-content .bd {
	padding:15px 40px 10px;
	border-bottom:1px solid #62b651
}
.about-content .bd .title {
	padding-bottom:15px;
	font-family:Microsoft Yahei;
	font-size:16px;
	line-height:26px;
	border-bottom:1px solid #e5e5e5
}
.about-content .bd p {
	padding-top:15px;
	line-height:26px
}
.about-content .media {
	padding-top:10px;
	padding-bottom:10px;
	font-size:14px;
	color:#626262
}
.about-content .media .pull-left {
	margin-right:60px
}
.about-content .media .icon-weixin2 {
	float:left
}
.about-content .media img {
	display:block;
	margin-top:10px;
	margin-bottom:5px
}
.about-content .media-body {
	margin-top:30px
}
.about-content .media-body .sns {
	width:320px;
	margin-bottom:15px
}
.about-content .media-body .sns span {
	float:left;
	width:160px;
	margin-bottom:15px
}
.about-content .contact {
	font-size:12px;
	line-height:26px
}
.logo-content {
	font-size:14px;
	color:#626262
}
.logo-content .tips {
	margin-top:40px;
	font-family:Microsoft Yahei;
	font-size:16px;
	text-align:center
}
.logo-content .btn {
	color:#626262;
	background:#fff
}
.logo-content .btn:hover {
	background:#d4d9de
}
.logo-content h3 {
	margin-top:50px;
	margin-bottom:15px;
	font-family:Microsoft Yahei;
	font-size:24px;
	text-align:center
}
.logo-content .bd {
	padding-right:35px;
	padding-left:35px
}
.logo-content .logo-lg {
	height:168px;
	margin-bottom:8px;
	border:1px solid #62b651
}
.logo-content .logo-lg>div {
	position:relative;
	width:50%;
	height:100%;
	line-height:168px;
	text-align:center
}
.logo-content .logo-lg>div:hover .backdrop, .logo-content .logo-lg>div:hover .downloads {
	visibility:visible
}
.logo-content .logo-lg .pull-left {
	background:#fff
}
.logo-content .logo-lg .backdrop, .logo-content .logo-lg .downloads {
	visibility:hidden
}
.logo-content .logo-lg .downloads {
	position:absolute;
	top:0;
	right:0;
	bottom:0;
	left:0;
	visibility:hidden
}
.logo-content .logo-lg .backdrop {
	filter:alpha(opacity=30);
	opacity:.3;
	-ms-filter:"alpha(Opacity=30)"
}
.logo-content .logo-lg .downloads .btn {
	margin-top:115px;
	margin-right:5px;
	margin-left:5px
}
.logo-content .logo-lg.logo-accuvally-lg {
	border:1px solid #000
}
.logo-content .logo-lg.logo-accuvally-lg .pull-right {
	background:#000
}
.logo-content .icons {
	line-height:108px;
	background:#62b651;
	border:1px solid #62b651
}
.logo-content .icons .icon-holder {
	float:left;
	padding-right:20px;
	padding-left:20px;
	margin-right:40px;
	background:#fff
}
.logo-content .icons ul {
	margin-top:40px
}
.logo-content .icons ul li {
	float:left;
	margin-right:3px
}
.logo-content .icons ul li a {
	color:#626262
}
.logo-content .icons ul li .btn {
	width:105px;
	padding-right:0;
	padding-left:0;
	font-size:14px
}
.logo-content .divider {
	height:40px;
	margin-top:60px;
	margin-bottom:30px;
	background:#fff
}
.logo-content li {
	line-height:28px
}
.topic-imgs {
	padding:10px;
	background:#f4f7fa;
	border-bottom:4px solid #a1a69f
}
.topic-imgs .img-lg, .topic-imgs .img-md {
	position:relative;
	float:left
}
.topic-imgs .img-lg .info, .topic-imgs .img-md .info {
	position:absolute;
	bottom:0;
	left:0;
	color:#fff
}
.topic-imgs .img-lg .info div, .topic-imgs .img-md .info div {
	position:relative
}
.topic-imgs .img-lg .info .alpha, .topic-imgs .img-md .info .alpha {
	position:absolute;
	top:0;
	left:0;
	width:100%;
	height:100%;
	content:'';
	background:#000;
	filter:alpha(opacity=70);
	opacity:.7;
	-ms-filter:"alpha(Opacity=70)"
}
.topic-imgs .img-lg {
	margin-right:10px
}
.topic-imgs .img-lg .info {
	bottom:20px;
	min-width:300px
}
.topic-imgs .img-lg .info>div {
	padding:9px
}
.topic-imgs .img-lg .info h3 {
	margin-top:0;
	font-family:Microsoft Yahei;
	font-size:20px
}
.topic-imgs .img-md {
	width:430px
}
.topic-imgs .img-md ul {
	margin-top:-10px;
	margin-left:-10px;
*zoom:1
}
.topic-imgs .img-md li {
	float:left;
	padding-left:0;
	margin-top:10px;
	margin-left:10px;
	list-style:none
}
.topic-imgs .img-md li>div {
	position:relative
}
.topic-imgs .img-md li>div .info {
	width:100%
}
.topic-imgs .img-md li>div .info div {
	padding-right:10px;
	font-size:14px;
	line-height:30px;
	text-align:right
}
#topic-img-slides .slidesjs-next, #topic-img-slides .slidesjs-previous {
	top:50%;
	z-index:20;
	margin-top:-15px
}
#topic-img-slides .slidesjs-next {
	right:0
}
#topic-img-slides .slidesjs-previous {
	left:0
}
#topic-img-slides .slidesjs-navigation {
	visibility:hidden
}
#topic-img-slides .slidesjs-navigation.enable {
	visibility:visible
}
#special-topic {
	padding-bottom:20px
}
#special-topic a:hover {
	text-decoration:none
}
#special-topic .icon-topic-down {
	position:absolute;
	top:0;
	left:50%;
	z-index:2;
	margin-left:-36px;
*top:-3px
}
#special-topic .divider-vertical {
	position:absolute;
	left:50%;
	width:4px;
	height:100%;
	margin-left:-2px;
	background-color:#e5e5e5
}
#special-topic .content {
	position:relative;
	overflow:hidden
}
#special-topic .content p {
	margin-bottom:0;
	line-height:18px
}
#special-topic .recent-events {
	padding-top:60px
}
#special-topic .recent-events>h2 {
	margin:40px 0;
	text-align:center
}
#special-topic .popover {
	position:relative;
	top:auto;
	left:auto;
	z-index:0;
	display:block;
	float:left;
	width:408px;
	padding:10px 10px 0;
	margin-bottom:25px;
	margin-left:25px;
	color:#a0a0a0;
	border:1px solid #eee;
	border-radius:2px;
	-webkit-box-shadow:0 1px 1px rgba(191,191,191,.75);
	box-shadow:0 1px 1px rgba(191,191,191,.75)
}
#special-topic .popover.left .arrow, #special-topic .popover.right .arrow {
	top:32px
}
#special-topic .popover.left .arrow:before, #special-topic .popover.right .arrow:before {
	position:absolute;
	top:-11px;
	width:22px;
	height:22px;
	content:''
}
#special-topic .popover.left .arrow:before {
	right:-26px
}
#special-topic .popover.right .arrow:before {
	left:-26px
}
#special-topic .popover+.popover {
	margin-top:60px;
	margin-left:50px
}
#special-topic .popover h3 {
	margin-top:0;
	margin-bottom:10px;
	font-family:Microsoft Yahei;
	font-size:18px
}
#special-topic .popover h3.only-title {
	margin-top:10px;
	margin-bottom:20px
}
#special-topic .popover h3 .lg {
	margin-bottom:15px;
	font-size:24px
}
#special-topic .popover .ft {
	padding-right:15px;
	margin:8px -10px 0;
	line-height:35px;
	color:#333;
	text-align:right;
	background-color:#f9f9f9;
	border-top:1px solid #eee
}
#special-topic .popover .blockquote {
	padding:5px 15px;
	margin:15px -10px 0;
	background-color:#f9f9f9;
	border-top:1px solid #eee
}
#special-topic .popover blockquote.blockquote {
	position:relative;
	padding:10px 45px;
	clear:both;
	line-height:26px
}
#special-topic .popover blockquote.blockquote:after, #special-topic .popover blockquote.blockquote:before {
	position:absolute;
	content:''
}
#special-topic .popover blockquote.blockquote:before {
	top:10px;
	left:13px
}
#special-topic .popover blockquote.blockquote:after {
	right:15px;
	bottom:10px
}
#special-topic .popover p {
	text-indent:2em
}
#special-topic .popover .img-txt {
	padding-top:10px;
	padding-right:12px;
	padding-bottom:5px;
	padding-left:20px;
	border-top:1px solid #eee
}
#special-topic .popover .img-txt img {
	margin-left:10px
}
#special-topic .popover .banner {
	height:auto;
	margin:-9px -9px 15px
}
#special-topic .popover .banner-border {
	width:406px;
	margin-bottom:10px;
	border:1px solid #ccc
}
#special-topic .popover .topic-avatar {
	float:left;
	width:53px;
	height:53px;
	margin-right:10px;
	margin-bottom:10px;
	border:1px solid #ccc;
	border-radius:4px
}
#special-topic .popover .topic-avatar+h3 {
	margin-top:5px;
	margin-bottom:5px
}
#special-topic .popover li {
	padding-left:50px
}
#special-topic .popover li span {
	float:left;
	margin-left:-50px
}
.topic-list {
	margin-bottom:20px
}
.topic-list li {
	padding:10px;
	margin-bottom:20px;
	overflow:hidden;
	background:#f4f7fa;
	border:1px solid #eee
}
.topic-list li:hover {
	border-color:#62b651
}
.topic-list li .bd {
	padding-bottom:10px;
	background:#fff;
	border:1px solid #eee
}
.topic-list li .bd>a:hover {
	text-decoration:none
}
.topic-list li .bd>a:hover h3 {
	color:#2578bf
}
.topic-list li img {
	display:block;
	max-width:100%;
	height:auto;
	border-bottom:2px solid #eee
}
.topic-list li h3 {
	padding-left:10px;
	margin-bottom:0;
	overflow:hidden;
	font-family:Microsoft Yahei;
	font-size:24px;
	color:#333;
	text-overflow:ellipsis;
	white-space:nowrap
}
.topic-list li .share {
	margin-right:-115px
}
.topic-list li .share .default, .topic-list li .share.expand {
	margin-right:10px
}
.topic-list li .share.expand .default {
	margin-right:0
}
.topic-details h2 {
	margin-top:0;
	margin-bottom:5px;
	font-size:24px;
	font-weight:700
}
.topic-details .notions {
	margin-bottom:10px;
	color:#7d7d7d;
	text-align:right
}
.topic-details .topic-details-content {
	margin-bottom:30px
}
.topic-details+.forum {
	width:auto;
	padding-top:20px;
	clear:both
}
.topic-details+.forum .sendbox {
	margin-left:0
}
.sign {
*overflow-x:hidden
}
.sign body {
	background-image:url(sign_bg.jpg);
	background-repeat:repeat-y;
	background-position:50% 0
}
.sign .container {
	width:934px;
	margin-top:100px
}
.sign .container.sign-in {
	position:fixed;
	top:0;
	right:0;
	bottom:0;
	left:0;
	height:380px;
	margin:auto;
*left:50%;
*margin-left:-467px;
*top:50%;
*margin-top:-190px
}
.sign .logo {
	margin-top:36px
}
.sign .links {
	font-size:14px;
	color:#59bb2b
}
.sign .links:active, .sign .links:focus, .sign .links:hover {
	color:#4ca721;
	text-decoration:none
}
.sign-hd {
	margin-bottom:20px;
	text-align:right
}
.sign-hd .icon-index {
	vertical-align:-1px;
*vertical-align:2px
}
.sign-bd {
	position:relative;
	float:right;
	padding-bottom:30px;
	color:#7d7d7d;
	background-color:#f4f7fa;
	border-radius:4px;
	-webkit-box-shadow:0 1px 7px rgba(46,46,46,.3);
	box-shadow:0 1px 7px rgba(46,46,46,.3);
*width:714px
}
.sign-bd .title {
	position:relative;
	margin-top:0;
	margin-bottom:25px;
	font-family:Microsoft Yahei;
	font-size:16px;
	line-height:55px;
	color:#70c846;
	text-align:center;
	border-bottom:1px solid #dedfdf
}
.sign-bd .title .links {
	position:absolute;
	right:10px;
	font-size:12px;
*top:-13px
}
.sign-bd .title .icon-goto {
	vertical-align:-3px;
*vertical-align:1px
}
.sign-bd h4 {
	margin-bottom:18px
}
.sign-bd .login-form {
	padding-left:50px;
	margin-right:50px
}
.sign-bd form {
	width:225px;
	padding-right:50px
}
.sign-bd .form-group {
	margin-bottom:10px
}
.sign-bd .form-group .icon-user {
	margin-top:15px
}
.sign-bd .form-group .icon-mail, .sign-bd .form-group .icon-nickname {
	margin-top:16px
}
.sign-bd .form-group .icon-password {
	margin-top:13px
}
.sign-bd .form-control {
	width:181px;
	height:30px;
	height:24px \9;
	padding-top:11px \9
}
@media screen and (-ms-high-contrast:active), (-ms-high-contrast:none) {
.sign-bd .form-control {
	height:30px;
	padding-top:5px
}
}
.sign-bd .btn-primary, .sign-bd .checkbox-inline, .sign-bd .text-primary {
	margin-top:10px
}
.sign-bd .login-coagent {
	width:338px
}
.sign-bd .login-coagent h4 {
	margin-bottom:20px;
	margin-left:70px
}
.sign-bd .login-coagent a {
	margin-right:35px;
	margin-bottom:25px
}
.sign-bd .tips {
	margin:-5px 0 3px 0;
	color:#333
}
.sign-bd .auth-code {
	width:86px;
	padding-left:10px;
	margin-right:7px
}
.sign-bd .auth-code+img {
	width:100px;
	height:42px
}
.sign-bd .icon-refresh-code {
	position:absolute;
	margin-top:15px;
	margin-left:10px;
	cursor:pointer
}
.sign-bd .phone-code {
	width:122px;
	padding-left:10px;
	margin-right:8px
}
.sign-bd .phone-code+.btn {
	width:70px;
	padding-right:0;
	padding-left:0
}
.sign-bd #alert {
	position:absolute;
	bottom:2px;
	left:50px;
	text-align:center
}
.sign-bd #alert .alert {
	display:inline-block;
	padding-top:7px;
	padding-right:10px;
	padding-bottom:7px;
	padding-left:10px;
	margin-bottom:0;
	text-align:left;
	border-radius:4px;
*display:inline;
*zoom:1
}
.sign-bd #alert .alert.alert-error {
	background-color:#fff1da;
	border:1px solid #facd89
}
.app-intro-img {
	position:relative;
	width:100%;
	height:596px;
	padding-top:59px;
	overflow:hidden
}
.app-intro-img img {
	position:absolute;
	left:50%;
	margin-left:-960px
}
.app-intro-img .container {
	position:relative;
	height:100%
}
.app-intro-img .appstore {
	bottom:138px
}
.app-intro-img .appstore, .app-intro-img .appstore .googleplay {
	position:absolute;
	left:0
}
.app-intro-img .googleplay {
	position:absolute;
	bottom:68px
}
.app-intro {
	width:443px;
	padding-top:92px;
	padding-right:601px;
	margin:0 auto 80px;
	font-family:Microsoft Yahei;
	font-size:20px;
	line-height:34px;
	color:#62b651
}
.app-intro strong {
	font-size:24px;
	font-style:normal
}
.app-intro img {
	float:right;
	margin-top:-50px;
	margin-right:-601px
}
.app-intro .help {
	margin-left:39px;
	font-size:12px
}
.event-news .tabs {
	padding-left:20px;
	margin-bottom:10px;
	font-size:14px;
	line-height:30px;
	background:#f4f7fa;
	border-bottom:none
}
.event-news .tabs li {
	margin-right:20px
}
.event-news .tabs li a {
	color:#7d7d7d
}
.event-news .tabs li a:hover, .event-news .tabs li.active a {
	color:#2578bf
}
.event-news .aligncenter {
	display:block;
	max-width:100%;
	height:auto
}
.event-news-title {
	margin-bottom:30px;
	border-bottom:1px solid #eee
}
.event-news-title h3 {
	position:relative;
	bottom:-1px;
	float:left;
	padding-right:20px;
	padding-bottom:10px;
	padding-left:15px;
	margin:0 48px 0 0;
	font-weight:700;
	border-bottom:2px solid #62b651
}
.event-news-title h3:before {
	position:absolute;
	top:5px;
	left:0;
	width:5px;
	height:5px;
	content:'';
	background:#62b651
}
.event-news-title ul {
	float:left
}
.event-news-title li {
	float:left;
	margin-right:10px
}
.event-news-title li a {
	color:#7d7d7d
}
.event-news-title li a:hover {
	color:#2578bf;
	text-decoration:none
}
.event-news-list {
	margin-bottom:35px
}
.event-news-list li {
	padding-left:14px;
	margin-top:8px;
	font-size:14px;
*padding-left:0
}
.event-news-list li:first-child {
	margin-top:0
}
.event-news-list li span {
	float:right;
	color:#c9c9c9
}
.event-news-list li a {
	float:left;
	max-width:473px;
	overflow:hidden;
	text-overflow:ellipsis;
	white-space:nowrap
}
.event-news-list li a:before {
	position:absolute;
	margin-top:6px;
	margin-left:-14px;
	content:''
}
.event-news-text {
	padding:0 38px 20px;
	margin-bottom:20px
}
.event-news-text h2 {
	margin-bottom:18px;
	font-family:Microsoft Yahei;
	font-size:20px;
	font-weight:700;
	text-align:center
}
.event-news-text .notions {
	margin-bottom:20px;
	color:#a0a0a0;
	text-align:center
}
.event-news-text .notions em {
	margin-right:68px;
	font-style:normal
}
.event-news-text .notions em span {
	margin-right:18px
}
.event-news-text .btn {
	font-size:14px;
	font-weight:700
}
.event-news-text .info {
	position:relative;
	padding:15px 20px 6px;
	margin-bottom:15px;
	font-size:14px;
	line-height:26px;
	color:#7d7d7d;
	border:1px solid #62b651
}
.event-news-text .info .btn-primary {
	position:absolute;
	right:20px;
	bottom:5px;
	padding-top:4px;
	padding-right:21px;
	padding-bottom:4px;
	padding-left:21px
}
.event-news-text .bd {
	margin-bottom:15px;
	font-size:14px;
	line-height:28px
}
.event-news-text label {
	font-size:14px;
	font-weight:700
}
.event-news-text .func {
	margin-top:20px;
	margin-bottom:40px
}
.event-news-text .func .label-list {
	display:inline-block;
	vertical-align:middle;
*display:inline;
*zoom:1
}
.event-news-text .func .label-list li {
	float:left;
	margin-right:5px;
	line-height:1
}
.event-news-text .func .label-list li a {
	display:block;
	padding:4px 10px;
	color:#7d7d7d;
	background:#f4f7fa;
*padding-bottom:8px
}
.event-news-text .func .label-list li a:hover {
	text-decoration:none
}
.event-news-text .func .label-list li a:hover, .event-news-text .func .label-list li.active a {
	color:#fff;
	background:#62b651
}
.event-news-text .other-list {
	line-height:24px
}
.event-news-text .other-list a {
	color:#7d7d7d
}
.event-news-text .other-list span {
	float:right;
	color:#a0a0a0
}
.event-news-services {
	padding-bottom:20px;
	color:#a0a0a0
}
.event-news-services strong {
	color:#e60012
}
.event-news-services p {
	margin-bottom:0;
	text-indent:2em
}
.event-news-services p:first-of-type {
	margin-top:5px
}
.pay-breadcrumb {
	margin-bottom:30px
}
.pay-breadcrumb li {
	position:relative;
	float:left;
	width:140px;
	margin-right:5px;
	font-size:14px;
	font-weight:700;
	line-height:26px;
	color:#000;
	text-align:center;
	text-indent:16px;
	background:#f4f7fa;
*text-indent:0
}
.pay-breadcrumb li:after, .pay-breadcrumb li:before {
	position:absolute;
	top:0;
	width:0;
	height:0;
	content:'';
	border-color:transparent;
	border-style:solid;
	border-width:13px
}
.pay-breadcrumb li:before {
	left:0;
	border-right-width:0;
	border-right-width:13px;
	border-left-color:#fff
}
.pay-breadcrumb li:after {
	right:-26px;
	z-index:1;
	border-right-width:0;
	border-right-width:13px;
	border-left-color:#f4f7fa
}
.pay-breadcrumb li.current {
	color:#fff;
	background:#62b651
}
.pay-breadcrumb li.current:after {
	border-left-color:#62b651
}
.pay-breadcrumb li:first-child {
	text-indent:11px;
	border-top-left-radius:4px;
	border-bottom-left-radius:4px
}
.pay-breadcrumb li:first-child:before {
	content:none
}
.pay-breadcrumb li:last-child {
	text-indent:0;
	border-top-right-radius:4px;
	border-bottom-right-radius:4px
}
.pay-breadcrumb li:last-child:after {
	content:none
}
.pay-order {
	background:#f4f7fa
}
.pay-order h2 {
	padding-left:20px;
	margin-top:0;
	font-weight:700;
	line-height:50px;
	color:#62b651;
	border-bottom:1px solid #c9c9c9
}
.pay-order .bd {
	padding:20px 20px 0;
	margin:10px;
	font-size:14px;
	background:#fff;
	border:1px solid transparent;
	border-radius:4px
}
.pay-order .bd .title {
	margin-top:0;
	margin-bottom:15px;
	font-size:14px;
	font-weight:700;
	color:#000
}
.pay-order .bd h4 {
	margin-top:0;
	margin-bottom:15px;
	font-size:12px
}
.pay-order .bd em {
	font-style:normal;
	font-weight:400;
	color:#f56200
}
.pay-order .bd strong {
	font-weight:400;
	color:#e60012
}
.pay-order .contact li {
	padding-left:28px;
	margin-bottom:20px
}
.pay-order .contact li span {
	margin-right:15px;
	color:#7d7d7d
}
.pay-order .contact li:before {
	position:absolute;
	width:8px;
	height:8px;
	margin-top:6px;
	margin-left:-20px;
	content:'';
	background:#62b651;
	border-radius:50%
}
.pay-order .details table {
	width:100%;
	margin-bottom:20px
}
.pay-order .details table th {
	height:30px;
	font-weight:400;
	color:#7d7d7d;
	text-align:center;
	background:#efeae5;
	border-left:1px solid #f4f7fa
}
.pay-order .details table th:first-child {
	border:none
}
.pay-order .details table td {
	height:116px;
	padding-top:20px;
	text-align:center;
	vertical-align:top;
	background:#f8f6f2
}
.pay-order .details table td.addition {
	height:auto;
	padding-top:0;
	padding-right:10px;
	padding-bottom:5px;
	font-size:12px;
	color:#a0a0a0;
	text-align:right
}
.pay-order .details .media {
	margin-left:10px;
	font-size:12px;
	color:#7d7d7d;
	text-align:left
}
.pay-order .details .media .media-body div {
	line-height:24px
}
.pay-order .details .tips {
	padding-left:10px;
	font-size:12px;
	color:#959595
}
.pay-order .details .pay-total {
	padding-right:20px;
	font-family:Microsoft Yahei;
	font-size:18px
}
.pay-order .details .pay-total strong {
	font-weight:700
}
.pay-order .details .pay-total, .pay-order .details ol {
	margin-bottom:25px
}
.pay-order .details li {
	font-size:12px;
	color:#7d7d7d
}
.pay-order .result {
	padding-bottom:20px;
	padding-left:20px;
	margin:0 -20px 20px;
	border-bottom:1px solid #eee
}
.pay-order .result em {
	display:block;
	margin-bottom:25px;
	font-family:Microsoft Yahei;
	font-size:22px
}
.pay-order .result .share {
	margin-left:-8px;
	font-size:12px;
	filter:alpha(opacity=50);
	opacity:.5;
	-ms-filter:"alpha(Opacity=50)"
}
.pay-order .result .share:focus, .pay-order .result .share:hover {
	filter:alpha(opacity=100);
	opacity:1;
	-ms-filter:"alpha(Opacity=100)"
}
.pay-order .result .share .share-btns {
	margin-right:10px
}
.pay-order .result .qr_scan {
	float:right;
	padding-right:20px;
	padding-left:291px;
	margin-top:-40px;
	font:0/0 a;
	color:transparent;
	text-shadow:none;
	background:url(qr_scan_text.png) no-repeat 0 90%;
	background-color:transparent;
	border:0
}
.pay-order .result .qr_scan img {
	width:100px;
	height:100px;
	padding:9px;
	background:#fff;
	border:solid #ffdb80;
	border-width:1px 1px 5px
}
.pay-order .pay-banks input {
	margin-right:10px;
	vertical-align:middle
}
@media screen and (-webkit-min-device-pixel-ratio:0) {
.pay-order .pay-banks input[type=radio] {
	padding:2px
}
.pay-order .pay-banks input[type=radio]:before {
	position:absolute;
	width:10px;
	height:10px;
	margin-top:1px;
	margin-left:1px;
	content:'';
	background:#c9c9c9;
	border-radius:50%
}
.pay-order .pay-banks input[type=radio]:checked {
	background:#fff
}
.pay-order .pay-banks input[type=radio]:checked:before {
	background:#62b651
}
}
.pay-order .pay-banks label {
	font:0/0 a;
	color:transparent;
	text-shadow:none;
	background-color:transparent;
	border:0
}
.pay-order .pay-banks label+label {
	margin-left:10px
}
.pay-order .pay-banks .divider-vertical {
	margin:20px 0;
	border-top:1px dashed #eee
}
.pay-order .pay-banks ul {
	margin-right:-60px
}
.pay-order .pay-banks li {
	float:left;
	margin-right:60px;
	margin-bottom:20px
}
.pay-order .pay-banks li label>span {
	display:inline-block;
	padding-right:12px;
	vertical-align:middle;
	border:1px solid #b5b5b5;
*display:inline;
*zoom:1
}
.pay-order .pay-banks li label>span span {
	display:block;
	width:126px;
	height:36px;
	background-repeat:no-repeat
}
.pay-order .pay-banks li label input:checked~span {
border-color:#f56200
}
.pay-order .pay-banks li.selected label>span {
	border-color:#f56200
}
.pay-order .back-admin {
	float:left;
	margin-top:30px;
	margin-left:10px
}
.pay-order .btn-lg {
	width:368px;
	margin:10px 0 20px;
	font-family:Microsoft Yahei;
	font-size:24px
}
.pay-order .event-vertical-list {
	padding-bottom:10px;
	margin-right:-20px
}
.pay-order .event-vertical-list li {
	width:168px;
	padding:0;
	margin-right:15px
}
.pay-order .event-vertical-list img {
	width:168px;
	height:100px
}
.pay-order .event-vertical-list h3 {
	font-size:14px
}
.alert {
	padding:8px 35px 8px 14px;
	margin-bottom:20px;
	text-shadow:0 1px 0 rgba(255,255,255,.5);
	background-color:#fcf8e3;
	border:1px solid #fbeed5;
	border-radius:4px;
	-webkit-border-radius:4px;
	-moz-border-radius:4px
}
.alert, .alert h4 {
	color:#c09853
}
.alert h4 {
	margin:0
}
.alert .close {
	position:relative;
	top:-2px;
	right:-21px;
	line-height:20px
}
.alert-success {
	color:#468847;
	background-color:#dff0d8;
	border-color:#d6e9c6
}
.alert-success h4 {
	color:#468847
}
.alert-danger, .alert-error {
	color:#b94a48;
	background-color:#f2dede;
	border-color:#eed3d7
}
.alert-danger h4, .alert-error h4 {
	color:#b94a48
}
.alert-info {
	color:#3a87ad;
	background-color:#d9edf7;
	border-color:#bce8f1
}
.alert-info h4 {
	color:#3a87ad
}
.alert-block {
	padding-top:14px;
	padding-bottom:14px
}
.alert-block>p, .alert-block>ul {
	margin-bottom:0
}
.alert-block p+p {
	margin-top:5px
}
.form-horizontal .help-inline, .form-horizontal .input-append, .form-horizontal .input-prepend, .form-horizontal .uneditable-input, .form-horizontal input, .form-horizontal select, .form-horizontal textarea, .form-inline .help-inline, .form-inline .input-append, .form-inline .input-prepend, .form-inline .uneditable-input, .form-inline input, .form-inline select, .form-inline textarea, .form-search .help-inline, .form-search .input-append, .form-search .input-prepend, .form-search .uneditable-input, .form-search input, .form-search select, .form-search textarea {
	display:inline-block;
	margin-bottom:0;
	vertical-align:middle;
*display:inline;
*zoom:1
}
.form-horizontal .hide, .form-inline .hide, .form-search .hide {
	display:none
}
.form-inline .btn-group, .form-inline label, .form-search .btn-group, .form-search label {
	display:inline-block
}
.form-inline .input-append, .form-inline .input-prepend, .form-search .input-append, .form-search .input-prepend {
	margin-bottom:0
}
.form-inline .checkbox, .form-inline .radio, .form-search .checkbox, .form-search .radio {
	padding-left:0;
	margin-bottom:0;
	vertical-align:middle
}
.form-inline .checkbox input[type=checkbox], .form-inline .radio input[type=radio], .form-search .checkbox input[type=checkbox], .form-search .radio input[type=radio] {
	float:left;
	margin-right:3px;
	margin-left:0
}
.control-group {
	margin-bottom:10px
}
legend+.control-group {
	margin-top:20px;
	-webkit-margin-top-collapse:separate
}
.form-horizontal .control-group {
	margin-bottom:20px;
*zoom:1
}
.form-horizontal .control-group:after, .form-horizontal .control-group:before {
	display:table;
	line-height:0;
	content:""
}
.form-horizontal .control-group:after {
	clear:both
}
.form-horizontal .control-label {
	float:left;
	width:160px;
	padding-top:5px;
	text-align:right
}
.form-horizontal .controls {
	margin-left:180px;
*display:inline-block;
*padding-left:20px;
*margin-left:0
}
.form-horizontal .controls:first-child {
*padding-left:180px
}
.form-horizontal .help-block {
	margin-bottom:0
}
.form-horizontal .input-append+.help-block, .form-horizontal .input-prepend+.help-block, .form-horizontal .uneditable-input+.help-block, .form-horizontal input+.help-block, .form-horizontal select+.help-block, .form-horizontal textarea+.help-block {
	margin-top:10px
}
.form-horizontal .form-actions {
	padding-left:180px
}
.input-mini {
	width:60px
}
.input-small {
	width:90px
}
.input-medium {
	width:150px
}
.input-large {
	width:210px
}
.input-xlarge {
	width:270px
}
.input-xxlarge {
	width:530px
}
.input-block-level {
	display:block;
	width:100%;
	min-height:30px;
	-webkit-box-sizing:border-box;
	-moz-box-sizing:border-box;
	box-sizing:border-box
}
.row {
	margin-left:-20px;
*zoom:1
}
.row:after, .row:before {
	display:table;
	line-height:0;
	content:""
}
.row:after {
	clear:both
}
[class*=span] {
float:left;
min-height:1px;
margin-left:20px
}
.span12 {
	width:940px
}
.span11 {
	width:860px
}
.span10 {
	width:780px
}
.span9 {
	width:700px
}
.span8 {
	width:620px
}
.span7 {
	width:540px
}
.span6 {
	width:460px
}
.span5 {
	width:380px
}
.span4 {
	width:300px
}
.span3 {
	width:220px
}
.span2 {
	width:140px
}
.span1 {
	width:60px
}
.offset12 {
	margin-left:980px
}
.offset11 {
	margin-left:900px
}
.offset10 {
	margin-left:820px
}
.offset9 {
	margin-left:740px
}
.offset8 {
	margin-left:660px
}
.offset7 {
	margin-left:580px
}
.offset6 {
	margin-left:500px
}
.offset5 {
	margin-left:420px
}
.offset4 {
	margin-left:340px
}
.offset3 {
	margin-left:260px
}
.offset2 {
	margin-left:180px
}
.offset1 {
	margin-left:100px
}
.row-fluid [class*=span].hide, [class*=span].hide {
	display:none
}
.row-fluid [class*=span] .label, [class*=span].pull-right {
	border-radius:3px
}
.badge, .label {
	display:inline-block;
	padding:2px 4px;
	font-size:11.84px;
	font-weight:700;
	line-height:14px;
	color:#fff;
	text-shadow:0 -1px 0 rgba(0,0,0,.25);
	white-space:nowrap;
	vertical-align:baseline;
	background-color:#999
}
.badge {
	padding-top:2px;
	padding-right:9px;
	padding-bottom:2px;
	padding-left:9px;
	border-radius:9px
}
.badge:empty, .label:empty {
	display:none
}
a.badge:focus, a.badge:hover, a.label:focus, a.label:hover {
	color:#fff;
	text-decoration:none;
	cursor:pointer
}
.badge-important, .label-important {
	background-color:#b94a48
}
.badge-important[href], .label-important[href] {
	background-color:#953b39
}
.badge-warning, .label-warning {
	background-color:#f89406
}
.badge-warning[href], .label-warning[href] {
	background-color:#c67605
}
.badge-success, .label-success {
	background-color:#468847
}
.badge-success[href], .label-success[href] {
	background-color:#356635
}
.badge-info, .label-info {
	background-color:#3a87ad
}
.badge-info[href], .label-info[href] {
	background-color:#2d6987
}
.badge-inverse, .label-inverse {
	background-color:#333
}
.badge-inverse[href], .label-inverse[href] {
	background-color:#1a1a1a
}
.btn .badge, .btn .label {
	position:relative;
	top:-1px
}
.btn-mini .badge, .btn-mini .label {
	top:0
}
.icon-like {
	width:14px;
	height:12px;
	background-image:url(backgrounds.32.png);
	background-position:-517px 0
}
.icon-like-lg {
	width:22px;
	height:18px;
	background-image:url(backgrounds.32.png);
	background-position:-72px -71px
}
.icon-like-before {
	width:14px;
	height:12px;
	background-image:url(backgrounds.32.png);
	background-position:-517px -17px
}
#slides-multiple-list .slidesjs-previous {
	width:20px;
	height:20px;
	background-image:url(backgrounds.32.png);
	background-position:-197px -69px
}
#slides-multiple-list .slidesjs-previous.enable {
	background-image:url(backgrounds.32.png);
	background-position:-218px -69px
}
#slides-multiple-list .slidesjs-next {
	width:21px;
	height:20px;
	background-image:url(backgrounds.32.png);
	background-position:-240px -69px
}
#slides-multiple-list .slidesjs-next.enable {
	width:21px;
	height:20px;
	background-image:url(backgrounds.32.png);
	background-position:-262px -69px
}
.slidesjs-next {
	width:9px;
	height:17px;
	background-image:url(backgrounds.32.png);
	background-position:-477px -60px
}
.slidesjs-previous {
	width:9px;
	height:17px;
	background-image:url(backgrounds.32.png);
	background-position:-480px 0
}
.slidesjs-next:hover {
	width:9px;
	height:17px;
	background-image:url(backgrounds.32.png);
	background-position:-480px -18px
}
.slidesjs-previous:hover {
	width:9px;
	height:17px;
	background-image:url(backgrounds.32.png);
	background-position:-486px -36px
}
.main_nav strong:before, .main_nav_index strong:before {
	width:9px;
	height:9px;
	background-image:url(backgrounds.32.png);
	background-position:-517px -34px
}
.main_nav strong sup, .main_nav_index strong sup {
	width:8px;
	height:9px;
	background-image:url(backgrounds.32.png);
	background-position:-517px -51px
}
.main_nav .dropdown2-menu a sup, .main_nav_index .dropdown2-menu a sup {
	width:8px;
	height:9px;
	background-image:url(backgrounds.32.png);
	background-position:-517px -68px
}
.icon-search {
	width:17px;
	height:17px;
	background-image:url(backgrounds.32.png);
	background-position:-487px -54px
}
.icon-goto {
	width:14px;
	height:14px;
	background-image:url(backgrounds.32.png);
	background-position:-534px 0
}
.a:active .icon-goto, a:focus .icon-goto, a:hover .icon-goto {
	width:14px;
	height:14px;
	background-image:url(backgrounds.32.png);
	background-position:-534px -17px
}
.icon-index {
	width:11px;
	height:12px;
	background-image:url(backgrounds.32.png);
	background-position:-534px -34px
}
a:active .icon-index, a:focus .icon-index, a:hover .icon-index {
	width:11px;
	height:12px;
	background-image:url(backgrounds.32.png);
	background-position:-534px -51px
}
.icon-login-douban {
	width:22px;
	height:22px;
	background-image:url(backgrounds.32.png);
	background-position:-197px -46px
}
.icon-login-douban:hover {
	width:22px;
	height:22px;
	background-image:url(backgrounds.32.png);
	background-position:-220px -46px
}
.icon-login-qq {
	width:22px;
	height:22px;
	background-image:url(backgrounds.32.png);
	background-position:-243px -46px
}
.icon-login-qq:hover {
	width:22px;
	height:22px;
	background-image:url(backgrounds.32.png);
	background-position:-266px -46px
}
.icon-login-renren {
	width:22px;
	height:22px;
	background-image:url(backgrounds.32.png);
	background-position:-289px -46px
}
.icon-login-renren:hover {
	width:22px;
	height:22px;
	background-image:url(backgrounds.32.png);
	background-position:-312px -46px
}
.icon-login-weibo {
	width:22px;
	height:22px;
	background-image:url(backgrounds.32.png);
	background-position:-335px -46px
}
.icon-login-weibo:hover {
	width:22px;
	height:22px;
	background-image:url(backgrounds.32.png);
	background-position:-358px -46px
}
.icon-login-baidu {
	width:22px;
	height:22px;
	background-image:url(backgrounds.32.png);
	background-position:-381px -46px
}
.icon-login-baidu:hover {
	width:22px;
	height:22px;
	background-image:url(backgrounds.32.png);
	background-position:-404px -46px
}
.icon-login-toggle {
	width:22px;
	height:22px;
	background-image:url(backgrounds.32.png);
	background-position:-416px 0
}
.sign .expand .icon-login-toggle {
	width:22px;
	height:22px;
	background-image:url(backgrounds.32.png);
	background-position:-416px -23px
}
.icon-config:before {
	width:16px;
	height:16px;
	background-image:url(backgrounds.32.png);
	background-position:-534px -68px
}
.icon-dropdown-user:before {
	width:16px;
	height:16px;
	background-image:url(backgrounds.32.png);
	background-position:-551px 0
}
.icon-shutdown:before {
	width:14px;
	height:15px;
	background-image:url(backgrounds.32.png);
	background-position:-551px -17px
}
.icon-create:before {
	width:16px;
	height:16px;
	background-image:url(backgrounds.32.png);
	background-position:-551px -34px
}
.icon-config-inverse:before {
	width:16px;
	height:16px;
	background-image:url(backgrounds.32.png);
	background-position:-551px -51px
}
.icon-dropdown-user-inverse:before {
	width:16px;
	height:16px;
	background-image:url(backgrounds.32.png);
	background-position:-551px -68px
}
.icon-shutdown-inverse:before {
	width:14px;
	height:15px;
	background-image:url(backgrounds.32.png);
	background-position:-568px 0
}
.icon-create-inverse:before {
	width:16px;
	height:16px;
	background-image:url(backgrounds.32.png);
	background-position:-568px -17px
}
.view_list {
	width:19px;
	height:18px;
	background-image:url(backgrounds.32.png);
	background-position:-95px -71px
}
.view_list.active, .view_list:active, .view_list:focus, .view_list:hover {
	width:19px;
	height:18px;
	background-image:url(backgrounds.32.png);
	background-position:-115px -71px
}
.view_block {
	width:19px;
	height:19px;
	background-image:url(backgrounds.32.png);
	background-position:-460px 0
}
.view_block.active, .view_block:active, .view_block:focus, .view_block:hover {
	width:19px;
	height:19px;
	background-image:url(backgrounds.32.png);
	background-position:-460px -20px
}
.icon-time {
	width:13px;
	height:13px;
	background-image:url(backgrounds.32.png);
	background-position:-568px -34px
}
.icon-female {
	width:13px;
	height:12px;
	background-image:url(backgrounds.32.png);
	background-position:-568px -51px
}
.icon-male {
	width:14px;
	height:12px;
	background-image:url(backgrounds.32.png);
	background-position:-568px -68px
}
.icon-tick {
	width:19px;
	height:15px;
	background-image:url(backgrounds.32.png);
	background-position:-48px -73px
}
.icon-message {
	width:10px;
	height:10px;
	background-image:url(backgrounds.32.png);
	background-position:-585px 0
}
.icon-refresh {
	width:11px;
	height:12px;
	background-image:url(backgrounds.32.png);
	background-position:-585px -17px
}
.icon-top span {
	width:13px;
	height:13px;
	background-image:url(backgrounds.32.png);
	background-position:-585px -34px
}
.icon-top:focus span, .icon-top:hover span {
	width:13px;
	height:13px;
	background-image:url(backgrounds.32.png);
	background-position:-585px -51px
}
.icon-top.active span, .icon-top:active span {
	width:13px;
	height:13px;
	background-image:url(backgrounds.32.png);
	background-position:-585px -68px
}
.icon-reply span {
	width:15px;
	height:12px;
	background-image:url(backgrounds.32.png);
	background-position:-602px 0
}
.icon-reply:active span, .icon-reply:focus span, .icon-reply:hover span {
	width:15px;
	height:12px;
	background-image:url(backgrounds.32.png);
	background-position:-602px -17px
}
.icon-password {
	width:12px;
	height:16px;
	background-image:url(backgrounds.32.png);
	background-position:-602px -34px
}
.icon-user {
	width:12px;
	height:13px;
	background-image:url(backgrounds.32.png);
	background-position:-602px -51px
}
.icon-mail {
	width:12px;
	height:11px;
	background-image:url(backgrounds.32.png);
	background-position:-602px -68px
}
.icon-nickname {
	width:12px;
	height:12px;
	background-image:url(backgrounds.32.png);
	background-position:-619px 0
}
.icon-refresh-code {
	width:14px;
	height:14px;
	background-image:url(backgrounds.32.png);
	background-position:-619px -17px
}
#scrollUp {
	width:45px;
	height:45px;
	background-image:url(backgrounds.32.png);
	background-position:-48px 0
}
#scrollUp:hover {
	background-image:url(backgrounds.32.png);
	background-position:-94px 0
}
#feedback {
	width:45px;
	height:45px;
	background-image:url(backgrounds.32.png);
	background-position:-140px 0
}
#feedback:hover {
	background-image:url(backgrounds.32.png);
	background-position:-186px 0
}
.qr_tool {
	width:45px;
	height:45px;
	background-image:url(backgrounds.32.png);
	background-position:-232px 0
}
.qr_tool:hover {
	width:45px;
	height:45px;
	background-image:url(backgrounds.32.png);
	background-position:-278px 0
}
.share_tool_icon {
	width:45px;
	height:45px;
	background-image:url(backgrounds.32.png);
	background-position:-324px 0
}
.share_tool_icon:hover {
	width:45px;
	height:45px;
	background-image:url(backgrounds.32.png);
	background-position:-370px 0
}
.icon-collapse {
	width:14px;
	height:14px;
	background-image:url(backgrounds.32.png);
	background-position:-619px -34px
}
.icon-expand {
	width:14px;
	height:14px;
	background-image:url(backgrounds.32.png);
	background-position:-619px -51px
}
.icon-statistics {
	width:13px;
	height:15px;
	background-image:url(backgrounds.32.png);
	background-position:-619px -68px
}
.icon-master {
	width:13px;
	height:13px;
	background-image:url(backgrounds.32.png);
	background-position:-636px 0
}
.icon-place {
	width:13px;
	height:17px;
	background-image:url(backgrounds.32.png);
	background-position:-487px -72px
}
.icon-weixin {
	width:18px;
	height:15px;
	background-image:url(backgrounds.32.png);
	background-position:-490px 0
}
.icon-weixin2 {
	width:24px;
	height:20px;
	background-image:url(backgrounds.32.png);
	background-position:-284px -69px
}
.btn-primary:active .icon-weixin {
	width:18px;
	height:15px;
	background-image:url(backgrounds.32.png);
	background-position:-490px -16px
}
.share .default {
	width:20px;
	height:20px;
	background-image:url(backgrounds.32.png);
	background-position:-309px -69px
}
.share .default:focus, .share .default:hover {
	width:20px;
	height:20px;
	background-image:url(backgrounds.32.png);
	background-position:-330px -69px
}
.share .weixin {
	width:20px;
	height:20px;
	background-image:url(backgrounds.32.png);
	background-position:-351px -69px
}
.share .weixin:focus, .share .weixin:hover {
	width:20px;
	height:20px;
	background-image:url(backgrounds.32.png);
	background-position:-372px -69px
}
.share .weixin sup {
	width:20px;
	height:11px;
	background-image:url(backgrounds.32.png);
	background-position:-496px -32px
}
.event-like {
	width:20px;
	height:20px;
	background-image:url(backgrounds.32.png);
	background-position:-393px -69px
}
.event-organizer {
	width:20px;
	height:20px;
	background-image:url(backgrounds.32.png);
	background-position:-414px -69px
}
.event-publish {
	width:20px;
	height:20px;
	background-image:url(backgrounds.32.png);
	background-position:-427px -46px
}
.event-join {
	width:20px;
	height:20px;
	background-image:url(backgrounds.32.png);
	background-position:-435px -67px
}
.event-attention {
	width:20px;
	height:20px;
	background-image:url(backgrounds.32.png);
	background-position:-439px 0
}
.event-privacy {
	width:20px;
	height:20px;
	background-image:url(backgrounds.32.png);
	background-position:-439px -21px
}
.feedback-face {
	width:47px;
	height:52px;
	background-image:url(backgrounds.32.png);
	background-position:0 0
}
.icon-note {
	width:12px;
	height:12px;
	background-image:url(backgrounds.32.png);
	background-position:-636px -17px
}
.icon-success {
	width:23px;
	height:26px;
	background-image:url(backgrounds.32.png);
	background-position:0 -53px
}
.icon-warning {
	width:23px;
	height:26px;
	background-image:url(backgrounds.32.png);
	background-position:-24px -53px
}
.icon-danger {
	width:23px;
	height:26px;
	background-image:url(backgrounds.32.png);
	background-position:-48px -46px
}
.icon-circle-xs {
	width:7px;
	height:7px;
	background-image:url(backgrounds.32.png);
	background-position:-636px -34px
}
.event-list-admin.event-list-admin-favorite li .close {
	background-image:url(backgrounds.32.png);
	background-position:-636px -51px
}
.icon-add {
	width:18px;
	height:18px;
	background-image:url(backgrounds.32.png);
	background-position:-135px -71px
}
.page-footer .douban {
	width:24px;
	height:24px;
	background-image:url(backgrounds.32.png);
	background-position:-72px -46px
}
.page-footer .renren {
	width:24px;
	height:24px;
	background-image:url(backgrounds.32.png);
	background-position:-97px -46px
}
.page-footer .weibo {
	width:24px;
	height:24px;
	background-image:url(backgrounds.32.png);
	background-position:-122px -46px
}
.page-footer .weixin {
	width:24px;
	height:24px;
	background-image:url(backgrounds.32.png);
	background-position:-147px -46px
}
.page-footer .qq {
	width:24px;
	height:24px;
	background-image:url(backgrounds.32.png);
	background-position:-172px -46px
}
.icon-close-lg {
	width:18px;
	height:18px;
	background-image:url(backgrounds.32.png);
	background-position:-154px -71px
}
.icon-close-lg:focus, .icon-close-lg:hover {
	width:18px;
	height:18px;
	background-image:url(backgrounds.32.png);
	background-position:-173px -71px
}
.icon-close-green {
	width:20px;
	height:20px;
	background-image:url(backgrounds.32.png);
	background-position:-448px -42px
}
.icon-close-green:focus, .icon-close-green:hover {
	width:20px;
	height:20px;
	background-image:url(backgrounds.32.png);
	background-position:-456px -63px
}
.icon-saying {
	width:16px;
	height:19px;
	background-image:url(backgrounds.32.png);
	background-position:-469px -40px
}
.sign-bd .weibo {
	width:129px;
	height:44px;
	background-image:url(backgrounds2.32.png);
	background-position:-63px -192px
}
.sign-bd .renren {
	width:129px;
	height:44px;
	background-image:url(backgrounds2.32.png);
	background-position:-63px -237px
}
.sign-bd .qq {
	width:129px;
	height:44px;
	background-image:url(backgrounds2.32.png);
	background-position:-79px -131px
}
.sign-bd .douban {
	width:129px;
	height:44px;
	background-image:url(backgrounds2.32.png);
	background-position:-145px 0
}
.sign-bd .baidu {
	width:129px;
	height:44px;
	background-image:url(backgrounds2.32.png);
	background-position:-145px -45px
}
.login-layer .weibo {
	width:102px;
	height:35px;
	background-image:url(backgrounds2.32.png);
	background-position:-193px -176px
}
.login-layer .douban {
	width:102px;
	height:35px;
	background-image:url(backgrounds2.32.png);
	background-position:-193px -212px
}
.login-layer .qq {
	width:102px;
	height:35px;
	background-image:url(backgrounds2.32.png);
	background-position:-193px -248px
}
.login-layer .renren {
	width:102px;
	height:35px;
	background-image:url(backgrounds2.32.png);
	background-position:-203px -90px
}
.login-layer .baidu {
	width:102px;
	height:35px;
	background-image:url(backgrounds2.32.png);
	background-position:-209px -126px
}
.login-list .douban {
	width:88px;
	height:30px;
	background-image:url(backgrounds2.32.png);
	background-position:-275px -34px
}
.login-list .douban:hover {
	width:88px;
	height:30px;
	background-image:url(backgrounds2.32.png);
	background-position:-296px -162px
}
.login-list .qq {
	width:88px;
	height:30px;
	background-image:url(backgrounds2.32.png);
	background-position:-296px -193px
}
.login-list .qq:hover {
	width:88px;
	height:30px;
	background-image:url(backgrounds2.32.png);
	background-position:-296px -224px
}
.login-list .weibo {
	width:89px;
	height:30px;
	background-image:url(backgrounds2.32.png);
	background-position:-296px -255px
}
.login-list .weibo:hover {
	width:89px;
	height:30px;
	background-image:url(backgrounds2.32.png);
	background-position:-306px -65px
}
.login-list .renren {
	width:100px;
	height:30px;
	background-image:url(backgrounds2.32.png);
	background-position:-309px 0
}
.login-list .renren:hover {
	width:100px;
	height:30px;
	background-image:url(backgrounds2.32.png);
	background-position:-312px -96px
}
.login-list .baidu {
	width:113px;
	height:30px;
	background-image:url(backgrounds2.32.png);
	background-position:-312px -127px
}
.login-list .baidu:hover {
	width:113px;
	height:30px;
	background-image:url(backgrounds2.32.png);
	background-position:-364px -31px
}
.about-content h2:before {
	width:33px;
	height:33px;
	background-image:url(backgrounds2.32.png);
	background-position:-18px -242px
}
.logo-content h2:before {
	width:33px;
	height:33px;
	background-image:url(backgrounds2.32.png);
	background-position:-275px 0
}
.goto-mobile .title:before {
	width:44px;
	height:49px;
	background-image:url(backgrounds2.32.png);
	background-position:-18px -192px
}
.btn-cal-down {
	width:40px;
	height:20px;
	background-image:url(backgrounds2.32.png);
	background-position:-405px -264px
}
.btn-cal-down:hover {
	width:40px;
	height:20px;
	background-image:url(backgrounds2.32.png);
	background-position:-426px -136px
}
.btn-cal-next {
	width:18px;
	height:27px;
	background-image:url(backgrounds2.32.png);
	background-position:-385px -189px
}
.btn-cal-next:hover {
	width:18px;
	height:27px;
	background-image:url(backgrounds2.32.png);
	background-position:-385px -217px
}
.btn-cal-prev {
	width:18px;
	height:27px;
	background-image:url(backgrounds2.32.png);
	background-position:-386px -245px
}
.btn-cal-prev:hover {
	width:18px;
	height:27px;
	background-image:url(backgrounds2.32.png);
	background-position:-396px -62px
}
.btn-cal-up {
	width:40px;
	height:20px;
	background-image:url(backgrounds2.32.png);
	background-position:-430px -189px
}
.btn-cal-up:hover {
	width:40px;
	height:20px;
	background-image:url(backgrounds2.32.png);
	background-position:-430px -210px
}
.icon-calendar {
	width:25px;
	height:25px;
	background-image:url(backgrounds2.32.png);
	background-position:-404px -189px
}
a:hover .icon-calendar {
	width:25px;
	height:25px;
	background-image:url(backgrounds2.32.png);
	background-position:-404px -215px
}
.calendar-list-body .day-title:before {
	width:17px;
	height:17px;
	background-image:url(backgrounds2.32.png);
	background-position:-446px -264px
}
.calendar-list-body .more-list:after {
	width:17px;
	height:17px;
	background-image:url(backgrounds2.32.png);
	background-position:-449px -113px
}
.event-news-list li a:before {
	width:5px;
	height:9px;
	background-image:url(backgrounds2.32.png);
	background-position:-482px 0
}
.weibo-follow {
	width:66px;
	height:22px;
	background-image:url(backgrounds2.32.png);
	background-position:-405px -241px
}
.weibo-follow:hover {
	width:66px;
	height:22px;
	background-image:url(backgrounds2.32.png);
	background-position:-410px 0
}
.weixin-follow {
	width:66px;
	height:22px;
	background-image:url(backgrounds2.32.png);
	background-position:-413px -90px
}
.weixin-follow:hover {
	width:66px;
	height:22px;
	background-image:url(backgrounds2.32.png);
	background-position:-415px -62px
}
.weixin-follow .layer .arrow {
	width:22px;
	height:12px;
	background-image:url(backgrounds2.32.png);
	background-position:-79px -176px
}
.icon-topic-down {
	width:72px;
	height:30px;
	background-image:url(backgrounds2.32.png);
	background-position:-385px -158px
}
#special-topic .popover .arrow:before, .icon-circle {
	width:22px;
	height:22px;
	background-image:url(backgrounds2.32.png);
	background-position:-426px -113px
}
.popover blockquote.blockquote:after {
	width:15px;
	height:14px;
	background-image:url(backgrounds2.32.png);
	background-position:-482px -17px
}
.popover blockquote.blockquote:before {
	width:15px;
	height:14px;
	background-image:url(backgrounds2.32.png);
	background-position:-482px -34px
}
.topic-next-round {
	width:60px;
	height:60px;
	background-image:url(backgrounds2.32.png);
	background-position:-18px -131px
}
.icon-rank {
	width:13px;
	height:16px;
	background-image:url(backgrounds2.32.png);
	background-position:-482px -51px
}
.event-rank-list ul:before {
	width:17px;
	height:285px;
	background-image:url(backgrounds2.32.png);
	background-position:0 0
}
.icon-lottery {
	width:23px;
	height:23px;
	background-image:url(backgrounds2.32.png);
	background-position:-275px -65px
}
.wx_qr .tips {
	width:126px;
	height:130px;
	background-image:url(backgrounds2.32.png);
	background-position:-18px 0
}
.icon-mobile {
	width:12px;
	height:15px;
	background-image:url(backgrounds2.32.png);
	background-position:-482px -68px
}
.world_cup div {
	width:57px;
	height:37px;
	background-image:url(backgrounds2.32.png);
	background-position:-145px -90px
}
#CssGaga {
	content:"140613152031,ma,325"
}
