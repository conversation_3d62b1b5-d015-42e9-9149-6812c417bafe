 if(typeof Mo!='function'){var Mo={plugin:[]}};var not_whitespace=new RegExp(/[^\s]/);var parent_count;Mo.xml=function(xmlDoc,parent_count){var arr;var parent="";parent_count=parent_count||new Object;var attribute_inside=0; if(xmlDoc.nodeName&&xmlDoc.nodeName.charAt(0)!="#"){if(xmlDoc.childNodes.length>1){arr=new Object;parent=xmlDoc.nodeName;}};var value=xmlDoc.nodeValue;if(xmlDoc.parentNode&&xmlDoc.parentNode.nodeName&&value){if(not_whitespace.test(value)){arr=new Object;arr[xmlDoc.parentNode.nodeName]=value;}};if(xmlDoc.childNodes.length){if(xmlDoc.childNodes.length==1){arr=Mo.xml(xmlDoc.childNodes[0],parent_count);}else{var index=0;for(var i=0;i<xmlDoc.childNodes.length;i++){var temp=Mo.xml(xmlDoc.childNodes[i],parent_count);if(temp){var assoc=false;var arr_count=0;for(key in temp){if(isNaN(key))assoc=true;arr_count++;if(arr_count>2)break;};if(assoc&&arr_count==1){if(arr[key]){if(!parent_count||!parent_count[key]){parent_count[key]=0;var temp_arr=arr[key];arr[key]=new Object;arr[key][0]=temp_arr;};parent_count[key]++;arr[key][parent_count[key]]=temp[key];}else{parent_count[key]=0;arr[key]=temp[key];if(xmlDoc.childNodes[i].attributes&&xmlDoc.childNodes[i].attributes.length){for(var j=0;j<xmlDoc.childNodes[i].attributes.length;j++){var nname=xmlDoc.childNodes[i].attributes[j].nodeName;if(nname){ if(attribute_inside){var temp_arr=arr[key];arr[key]=new Object;arr[key]['value']=temp_arr;arr[key]['attribute_'+nname]=xmlDoc.childNodes[i].attributes[j].nodeValue;}else{ arr['attribute_'+key+'_'+nname]=xmlDoc.childNodes[i].attributes[j].nodeValue;}}}}}}else{arr[index]=temp;index++;}}}}};if(parent&&arr){var temp=arr;arr=new Object;arr[parent]=temp;};return arr;} ;Mo.plugin.push("xml");
