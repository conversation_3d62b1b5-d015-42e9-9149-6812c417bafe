 if(typeof Mo!='function'){var Mo={plugin:[]}} ;Mo.Grade=function(box,min,max,def,grey,light,func){this.Box=box;this.Min=min;this.Max=max;this.Def=def;this.Grey=grey;this.Light=light;this.Func=func;var self=this;this.Inti=function(){for(var i=this.Min;i<=this.Max;i++){Mo(box).create("img",{"rel":i,"src":this.Grey});};Mo(box+" img").bind('mouseover',function(ele,index,event){self.Play(this.rel);self.Return(this.rel);}).bind('mouseout',function(ele,index,event){self.Play(self.Def);self.Return(self.Def);}).bind('click',function(ele,index,event){self.Def=this.rel;self.Return(this.rel);});} ;this.Play=function(rate){Mo(box+" img").each(function(ele,index){if(ele.rel<=rate){this.src=self.Light;}else{this.src=self.Grey;}});} ;this.Return=function(rate){if(typeof this.Func=='function')this.Func(rate);};this.Inti();this.Play(this.Def);} ;Mo.DateDiff=function(expire,func){if(typeof func!='function')var func=function(){};if(!expire){return false;};var expire=Math.round(parseInt(expire)*1000);if(new Date().getTime()>=expire){func(-1,{"d":0,"h":0,"m":0,"s":0});}else{var asd=expire;window.setInterval(function(){if(new Date().getTime()>expire){func(-1,{"d":0,"h":0,"m":0,"s":0});}else{var DifferenceHour=-1;var DifferenceMinute=-1;var DifferenceSecond=-1;var daysms=24*60*60*1000;var hoursms=60*60*1000;var Secondms=60*1000;var microsecond=1000;var time=new Date();var convertHour=DifferenceHour;var convertMinute=DifferenceMinute;var convertSecond=DifferenceSecond;var Result=Diffms=asd-time.getTime();DifferenceHour=Math.floor(Diffms/daysms);Diffms-=DifferenceHour*daysms;DifferenceMinute=Math.floor(Diffms/hoursms);Diffms-=DifferenceMinute*hoursms;DifferenceSecond=Math.floor(Diffms/Secondms);Diffms-=DifferenceSecond*Secondms;var dSecs=Math.floor(Diffms/microsecond);if(convertHour!=DifferenceHour){var a=DifferenceHour;};if(convertMinute!=DifferenceMinute){var b=DifferenceMinute;};if(convertSecond!=DifferenceSecond){var c=DifferenceSecond;var d=dSecs;} ;func(parseInt(Result/1000),{"d":a,"h":b,"m":c,"s":d});}},1000);}} ;Mo.Process=function(){this.groups=[];var self=this;var $=function(id){return document.getElementById(id);} ;this.Group=function(gid){self.groups[gid]={"GID":gid,"COUNT":0,"OPTIONS":[]};this.gid=gid; this.Option=function(oid,val){if(val<0)val=0;self.groups[this.gid]["COUNT"]+=val;if(oid!==null)self.groups[this.gid]["OPTIONS"][oid]={"OID":oid,"COUNT":val};}};this.Show=function(){for(var x in self.groups){for(var n in self.groups[x]["OPTIONS"]){var p=Math.round(self.groups[x]["OPTIONS"][n]["COUNT"]/ self.groups[x]["COUNT"] *100);
(function(){var N=n;var P=p;var i=0;var func=function(){if(i<=P){$('option-'+N).style.width=i+'%';$('percent-'+N).innerHTML=i+'%';i++;}else{clearInterval(inti);}};var inti=window.setInterval(func,2);})();}}}} ;Mo.plugin.push("effect");
