 var func=function(){Serv.resize();window.status='欢迎使用 VeryIDE Bee*';};Mo.reader(func);<PERSON><PERSON>resize(func);Mo.ready(function(){if(Serv.END){Serv.setup();return;}});Mo.reader(function(){if(Serv.END){return;};var fx=new FX('greet',{opacity:{from:1,to:0}},1,'easeOut',function(){Mo("#greet").hide();});fx.start();Mo("#wrapper").show();Serv.resize();Mo("a").bind('focus',function(){this.blur();});Serv.Init();}); Mo.store.zIndex=999;Mo.store.desktop=false;Mo.store.module={};Mo.store.addons={};Mo.store.widget=[];Mo.store.collect=9;Mo.store.talk=null;var Serv={ END:false, API:"ajax.php", home:"", store:new Object(), prefix:{}, setup:function(){var doc=Mo.document;var pos=Mo("#setup").position();var top=doc.scrollTop+(doc.clientHeight/2)-(pos.height/2);var left=(doc.scrollWidth-pos.width)/2;Mo("#setup").style({"marginTop":top+"px"});Serv.resize=function(){Serv.setup();};}, resize:function(){Serv.prefix.top=Mo("#header").position().height;Serv.prefix.left=0;Serv.prefix.width=Mo.document.clientWidth;Serv.prefix.height=Math.abs(Mo.document.clientHeight-Mo("#header").position().height-Mo("#footer").position().height);Mo("#container").style({"height":Serv.prefix.height+Mo("#footer").position().height+"px"});var top=Math.abs(Mo.document.clientHeight-Mo("#footer").position().height);Mo("#footer").style({"top":top+"px"});Mo("#footbg").style({"top":top+"px"});Mo.store.login&&Mo.store.login.Center();}, empty:function(){}, Dialog:function(style,title,content,width,height,config){config["zindex"]=(function(){return Mo.store.zIndex+=1;})();if(width.toString().indexOf("%")>-1)width=Serv.prefix.width*(parseInt(width)/100);new Mo.Dialog(style,title,content,width,height,config);}, Init:function(){Serv.Login();Serv.Adsense.load();if(!Mo.store.account)return false;Mo("body").create("div",{"id":"tip"});Mo("#tip").hide();Mo("body").create("div",{"id":"tool"});Mo("#tool").hide();Serv.Addons.module();Serv.Widget.reader();Serv.Session.query(1000*60);Mo("head").create("link",{"id":"theme","type":"text/css","rel":"stylesheet"});  Mo("#addon").bind('click',function(ele,index,event){var pos=Mo("#addon").position();Mo("#collect").style({"left":pos.left-160+19+"px","top":pos.top+60+"px"}).toggle();Mo.Event(event).stop();});Mo("#collect").bind('click',function(ele,index,event){});Mo("#collect a,#collect a img").bind('click',function(ele,index,event){Mo("#collect").hide();});Mo(document).bind('click',function(ele,index,event){Mo("#link").hide();Mo("#collect").hide();});Mo("#bind_home").bind('mouseover',function(){Serv.Tip('官方网站',this)}).bind('mouseout',function(){Serv.Tip();}).bind('click',function(){Serv.store.home=new Mo.Dialog('dialog','官方网站','<iframe width="100%" height="500" src="" frameborder="0"></iframe>',1000,500,{"unique":"dialog_home","remove":true,"draged":true,"zindex":(function(){return Mo.store.zIndex++;})()});Serv.store.home.onDrag=function(self){Mo(self.object).style({"zIndex":(Mo.store.zIndex+=1)});return false;};Serv.store.home.Create();});Mo("#bind_desktop").bind('mouseover',function(){Serv.Tip('显示桌面',this)}).bind('mouseout',function(){Serv.Tip();}).bind('click',function(){Serv.System.desktop();});Mo("#bind_help").bind('mouseover',function(){Serv.Tip('网站推荐',this)}).bind('mouseout',function(){Serv.Tip();}).bind('click',function(ele,index,e){Mo("#link").toggle();Mo.Event(e).stop();});Mo("#bind_refresh").bind('mouseover',function(){Serv.Tip('刷新页面',this)}).bind('mouseout',function(){Serv.Tip();}).bind('click',function(){Serv.System.refresh();});Mo("#bind_theme").bind('mouseover',function(){Serv.Tip('外观设置',this)}).bind('mouseout',function(){Serv.Tip();}).bind('click',function(){Serv.System.theme();});Mo("#bind_custom").bind('mouseover',function(){Serv.Tip('偏好设置',this)}).bind('mouseout',function(){Serv.Tip();}).bind('click',function(){Serv.store.custom=new Mo.Dialog('dialog','偏好设置','<iframe width="100%" height="300" src="dialog/custom.php" frameborder="0"></iframe>',600,400,{"unique":"dialog_custom","remove":true,"draged":true,"zindex":(function(){return Mo.store.zIndex++;})()});Serv.store.custom.onDrag=function(self){Mo(self.object).style({"zIndex":(Mo.store.zIndex+=1)});return false;};Serv.store.custom.Create();});Mo("#bind_logout").bind('mouseover',function(){Serv.Tip('退出登录',this)}).bind('mouseout',function(){Serv.Tip();}).bind('click',function(){Serv.Logout();});   Mo("#bind_account").bind('click',function(){Serv.Apps.open('system','数据统计','photos','admin.edit.php?action=edit&id='+Mo.store.aid);});Mo("#bind_avatar").bind('click',function(){Serv.System.avatar();});  Mo("#bind_market").bind('click',function(){Serv.store.module=new Mo.Dialog('dialog','应用市场','<iframe width="100%" height="400" src="dialog/module.php" frameborder="0"></iframe>',795,450,{"unique":"dialog_market","remove":true,"draged":true,"zindex":(function(){return Mo.store.zIndex++;})()});Serv.store.module.onDrag=function(self){Mo(self.object).style({"zIndex":(Mo.store.zIndex+=1)});return false;}
Serv.store.module.onCreate=function(self){var pos=Mo("#apps").position();Mo("#"+self.id).style({"left":pos.left+pos.width+10+"px","top":pos.top+5+"px"});};Serv.store.module.Create();}).show(); Mo("#bind_tasks").bind('click',function(){Serv.store.tasks=new Mo.Dialog('dialog','查看任务','<iframe width="100%" height="400" src="dialog/tasks.php" frameborder="0"></iframe>',600,400,{"unique":"dialog_tasks","remove":true,"draged":true,"zindex":(function(){return Mo.store.zIndex++;})()});Serv.store.tasks.onDrag=function(self){Mo(self.object).style({"zIndex":(Mo.store.zIndex+=1)});return false;};Serv.store.tasks.Create();}); Mo(document.body).bind("mousedown",function(ele,index,event){var ele=Mo.Event(event).target();while(ele=ele.parentNode){if(ele.tagName=="DL"&&ele.getAttribute("dialog")=="dialog"){Mo(ele).style({"zIndex":(Mo.store.zIndex+=1)}).show();break;}};Mo.Event(event).stop();});if(Mo.store.avatar)Serv.System.avatar(Mo.store.avatar,true);if(Mo.store.theme)Serv.System.theme(Mo.store.theme,true);}, Adsense:{ load:function(){Mo.script(Serv.home+"client/adsense.php?action=proxy&rnd="+Mo.date("y-m-d"));window._gaq=[];_gaq.push(['_setAccount','UA-279570-4']);_gaq.push(['_setDomainName','none']);_gaq.push(['_setAllowLinker',true]);_gaq.push(['_trackPageview']);(function(){var ga=document.createElement('script');ga.type='text/javascript';ga.async=true;ga.src=('https:'==document.location.protocol?'https://ssl':'http://www')+'.google-analytics.com/ga.js';var s=document.getElementsByTagName('script')[0];s.parentNode.insertBefore(ga,s);})();}, show:function(zone,config){if(!Mo.store.account&&zone=="banner"){Serv.Adsense.play(zone,config);}
if(Mo.store.account&&zone!="banner"){if(zone=="movie"&&Mo.Array(["full","only"]).indexOf(Mo.store.licence)==-1){Serv.Adsense.play(zone,config);}
if(zone=="version"&&Mo.store.version){var match=config["name"].match(/\[\d.*\]/);var version=match?parseFloat(match.toString().replace(/(\[|\])/g,'')):0;if(version>Mo.store.version){Serv.Adsense.play(zone,config);}}}}, play:function(zone,config){if(new Date().getTime()>(config["expire"]*1000)){Mo.script(Serv.home+"client/adsense.php?action=expire&id="+config["id"]+"&rnd="+Math.random());}
var html='<a href="'+config["link"]+'" target="_blank">'+(config["file"]?'<img src="'+config["file"]+'" title="'+config["name"]+'" />':config["name"])+'</a><button onclick="Mo(\'#'+zone+'\').hide();">关闭</button>';Mo(document.body).create("div",{"id":zone,"innerHTML":html});Mo("#"+zone+" button").hide();if(zone!="banner"){window.setTimeout(function(){Mo("#"+zone+" button").show();},20000);}}}, Method:[],Extend:{ added:function(appid,func){if(typeof Serv.Method[appid]!='function')Serv.Method[appid]=func;}, call:function(appid,param){if(typeof Serv.Method[appid]!='function'){alert('未知方法：'+appid);}else{Serv.Method[appid](param);}}}, Session:{ query:function(time){window.setInterval(function(){Mo.script(Serv.API+"?action=session&callback=Serv.Session.parse&rnd="+Math.random());},time);}, parse:function(result){}}, Widget:{ added:function(appid,widget,saved,data){if(saved&&data){Mo.store.widget.push(data);var xml=Mo.base64(data["xml"]).decode();if(!window.ActiveXObject){var parser=new DOMParser();var xmlDoc=parser.parseFromString(xml,"text/xml");}else{var xmlDoc=new ActiveXObject("Microsoft.XMLDOM");xmlDoc.async="false";xmlDoc.loadXML(xml);}
var arr=Mo.xml(xmlDoc.documentElement);var cfg=arr["widget"];cfg["serve"]?Mo.script(cfg["serve"]):null;if(cfg["target"]){Mo(cfg["target"]).html(cfg["html"],true);return;}
var ap=Mo("#widget").create("div",{"id":data["appid"]+'_'+data["widget"],"className":"widget","innerHTML":cfg["html"]},true);Mo(ap).attr({"appid":data["appid"],"widget":data["widget"],"widgetname":cfg["name"]});Serv.Widget.revise(ap,data);Mo(ap).bind('mouseover',function(ele,index,event){Serv.Widget.tool(event,this,data["appid"],data["widget"]);}).bind('mouseout',function(ele,index,event){Serv.Widget.tool(event,this);});Mo.resize(function(){if(ap&&data){Serv.Widget.revise(ap,data);}});if(cfg["drag"]!='true')return;(function(self){var dg=new Mo.Drag(ap);dg.onStart=function(x,y,e){Mo("#tool").hide();};dg.onDrag=function(x,y,e){Mo("#tool").hide();};dg.onEnd=function(x,y,e){var fx=(Serv.prefix.width-x);var fy=(Serv.prefix.height-y);data["x"]=x;data["y"]=y;data["fx"]=fx;data["fy"]=fy;Mo.script(Serv.API+"?action=widget&execute=saved&appid="+self["appid"]+"&widget="+self["widget"]+"&x="+x+"&y="+y+"&fx="+fx+"&fy="+fy+"&rnd="+Math.random());window.status="x: "+x+" y: "+y+" fx:"+fx+" fy:"+fy;Serv.Widget.tool(e,ap,self["appid"],self["widget"]);}})(data);}else{if(appid)Mo.script(Serv.API+"?action=widget&execute=added&callback=Serv.Widget.added&appid="+appid+"&widget="+widget+"&rnd="+Math.random());}}, revise:function(ap,data){switch(data["x"]){case"left":data["x"]="0";break;case"center":data["x"]=(Serv.prefix.width-ap.offsetWidth)/ 2;
break;case"right":data["x"]=(Serv.prefix.width-ap.offsetWidth);break;}
switch(data["y"]){case"top":data["y"]="0";break;case"center":data["y"]=(Serv.prefix.height-ap.offsetHeight)/ 2;
break;case"bottom":data["y"]=(Serv.prefix.height-ap.offsetHeight);break;}
var x=parseInt(data["x"]);var y=parseInt(data["y"]);var mx=Math.min(data["x"],data["fx"]);var my=Math.min(data["y"],data["fy"]);var fx=parseInt(data["fx"]);var fy=parseInt(data["fy"]);switch(mx){case x:ap.style.left=(data["x"]?data["x"]:cfg["x"])+'px';break;case fx:ap.style.left=(data["fx"]?(Serv.prefix.width-parseInt(data["fx"])):cfg["x"])+'px';break;};switch(my){case y:ap.style.top=(data["y"]?data["y"]:cfg["y"])+'px';break;case fy:ap.style.top=(data["fy"]?(Mo.document.clientHeight-parseInt(data["fy"])):cfg["y"])+'px';break;}
  ap.style.zIndex=(data["z"]?data["z"]:0);}, remove:function(appid,widget,saved){if(saved){Mo("*[appid="+appid+"]").each(function(ele,index){if(Mo(this).attr("widget")==widget)Mo(this).remove();});Mo("#"+appid+"_"+widget).remove();var i=Serv.Widget.indexOf(appid,widget);Mo.store.widget.splice(i,1);}else{if(appid)Mo.script(Serv.API+"?action=widget&execute=remove&callback=Serv.Widget.remove&appid="+appid+"&widget="+widget+"&rnd="+Math.random());}}, front:function(appid,widget,saved){if(Mo("#"+appid+"_"+widget).style("zIndex")==9999){var z=0;}else{var z=9999;}
if(saved){Mo("#"+appid+"_"+widget).style({"zIndex":z});}else{if(appid)Mo.script(Serv.API+"?action=widget&execute=front&callback=Serv.Widget.front&appid="+appid+"&widget="+widget+"&z="+z+"&rnd="+Math.random());}}, reader:function(config){if(config){Mo("#widget").html('');for(var i=0;i<config.length;i++){if(config[i]["xml"])Serv.Widget.added(config[i]["appid"],config[i]["widget"],true,config[i]);}}else{Mo.script(Serv.API+"?action=widget&execute=reader&callback=Serv.Widget.reader&rnd="+Math.random());}}, tool:function(e,refer,appid,widget){if(appid&&widget){var pos=Mo(refer).position();Mo("#tool").style({"left":(pos.left+pos.width-36)+"px","top":pos.top+"px"});var text=' <span class="front" title="最前显示" onclick="Serv.Widget.front(\''+appid+'\',\''+widget+'\');"></span> <span class="close" title="删除 '+appid+' - '+widget+'" onclick="Serv.Widget.remove(\''+appid+'\',\''+widget+'\');"></span>';Mo("#tool").html(text).show();Mo("#tool").bind('mouseover',function(){Mo(this).show();}).bind('mouseout',function(){Mo(this).hide();}).bind('click',function(){Mo(this).hide();});}else{if(Mo.Event(e).element().tagName=='EMBED'||Mo.Event(e).element()==refer){Mo("#tool").hide();}}}, indexOf:function(appid,widget){var config=Mo.store.widget;for(var i=0;i<config.length;i++){if(config[i]["appid"]==appid&&config[i]["widget"]==widget){break;}};return i;}}, Addons:{ added:function(appid,state){if(state){Mo.store.addons.push(appid);Serv.Addons.quick(appid);Serv.Addons.collect();}else{Mo.script(Serv.API+"?action=addons&execute=added&callback=Serv.Addons.added&appid="+appid+"&rnd="+Math.random());}}, remove:function(appid,state){if(state){Mo("#quick_"+appid).remove();var i=Mo.Array(Mo.store.addons).indexOf(appid);Mo.store.addons.splice(i,1);if(Mo.store.addons.length<=Mo.store.collect){Mo("#addon").attr({"class":""}).html('');}
if(Mo.store.addons.length>=Mo.store.collect)Serv.Addons.quick(Mo.store.addons[Mo.store.collect-1]);Serv.Addons.collect();}else{Mo.script(Serv.API+"?action=addons&execute=remove&callback=Serv.Addons.remove&appid="+appid+"&rnd="+Math.random());}},collect:function(){Mo("#collect ul").html('');if(Mo.store.addons.length>Mo.store.collect){for(var i=0;i<Mo.store.addons.length;i++){var appid=Mo.store.addons[i];if(i>=Mo.store.collect){var config=Mo.store.module[appid];if(!config){Serv.Addons.remove(appid);return;}
Serv.Addons.serve(appid);var html='<a href="javascript:void(0);" onclick="Serv.Apps.open(\''+appid+'\',\''+config["name"]+'\',\'\');return false;"><img src="module/'+appid+'/icon.png" /><br /><span>'+config["name"]+'</span></a>';Mo("#collect ul").create("li",{"id":"quick_"+appid,"innerHTML":html});}};Mo("#addon").html(Mo.store.addons.length-Mo.store.collect);}}, quick:function(appid){var size=Mo("#quick li").size();if(size<Mo.store.collect+1){var config=Mo.store.module[appid];if(!config){Serv.Addons.remove(appid);return;}
Serv.Addons.serve(appid);var id="quick_"+appid;var html='<a href="javascript:void(0);" onclick="Serv.Apps.open(\''+appid+'\',\''+config["name"]+'\',\'\');return false;"><img src="module/'+appid+'/icon.png" /><br />'+config["name"]+'</a>';Mo("#quick").create("li",{"id":id,"innerHTML":html});}else{if(Mo.store.addons.length>Mo.store.collect)Mo("#addon").attr({"class":"active"});}}, serve:function(appid){var config=Mo.store.module[appid];if(config&&config["serve"]){Mo.script("module/"+appid+"/"+config["serve"]+"?rnd="+Math.random(),{"charset":"utf-8"});}}, reader:function(config){if(config){Mo.store.addons=config;Mo("#quick li").each(function(ele){if(!ele.getAttribute("reserve")){Mo(ele).remove();}});for(var i=0;i<Mo.store.addons.length;i++){Serv.Addons.quick(Mo.store.addons[i]);}
Serv.Addons.collect();}else{Mo.script(Serv.API+"?action=addons&execute=reader&callback=Serv.Addons.reader&rnd="+Math.random());}}, module:function(config){if(config){Mo.store.module=config;Serv.Addons.reader();}else{Mo.script(Serv.API+"?action=addons&execute=module&callback=Serv.Addons.module&rnd="+Math.random());}}, saved:function(data){if(data){Mo.script(Serv.API+"?action=addons&execute=order&data="+data.join(",")+"&callback=Serv.Addons.reader&rnd="+Math.random());}}}, System:{ refresh:function(){location.reload();}, theme:function(theme,saved){if(theme){Mo("#style").remove();Mo("#theme").attr({"href":"theme/"+theme+"/style.css?"+Mo.random(10)});Mo.store.theme=theme;if(!saved)Mo.script(Serv.API+"?action=theme&theme="+theme+"&rnd="+Math.random());}else{Mo.store.theme=new Mo.Dialog('dialog','设置外观','<iframe src="dialog/theme.php?theme='+Mo.store.theme+'" frameborder="0"></iframe>',562,370,{"unique":"dialog_theme","remove":true,"draged":true,"zindex":(function(){return Mo.store.zIndex++;})()});Mo.store.theme.onDrag=function(self){Mo(self.object).style({"zIndex":(Mo.store.zIndex+=1)});return false;};Mo.store.theme.Create();}}, avatar:function(avatar,saved){if(avatar){Mo("#bind_avatar").html('<img src="image/avatar/'+avatar+'" />');Mo.store.avatar=avatar;if(!saved)Mo.script(Serv.API+"?action=avatar&avatar="+avatar+"&rnd="+Math.random());}else{Mo.store.avatar=new Mo.Dialog('dialog','设置头像','<iframe src="dialog/avatar.php?avatar='+Mo.store.avatar+'" frameborder="0"></iframe>',430,340,{"unique":"dialog_avatar","remove":true,"zindex":(function(){return Mo.store.zIndex++;})()});Mo.store.avatar.onDrag=function(self){Mo(self.object).style({"zIndex":(Mo.store.zIndex+=1)});return false;};Mo.store.avatar.Create();}}, desktop:function(){var hide=false;Mo("#task li").each(function(){var appid=this.id.replace("task_","");if(Mo.store.desktop){if(Mo("#talk_"+appid).item(0)._show!="false"){Mo("#talk_"+appid).show();}}else{Mo("#talk_"+appid).hide();hide=true;}});Mo("#menu").hide();if(!hide){Mo.store.desktop=true;};Mo.store.desktop=Mo.store.desktop?false:true;}}, Tip:function(text,refer){if(text){var pos=Mo(refer).position();if(pos.left+55>Serv.prefix.width){pos.left-=40;cls="last";}else{cls="";};Mo("#tip").attr({"class":cls}).style({"left":pos.left+"px","top":pos.top+"px"});Mo("#tip").html(text).show();}else{Mo("#tip").hide();}}, Apps:{ open:function(appid,name,icon,link,size){var link='module/'+appid+'/'+(link?link:'');var icon=icon?'image/extra/'+icon+'.png':'module/'+appid+'/icon.png';if(Mo("#talk_"+appid).size()){if(Mo("#talk_"+appid).item(0)._link!=link){Mo("#view_"+appid).attr({"src":link});Mo("#talk_"+appid).attr({"_link":link});}
if(Mo("#talk_"+appid).item(0)._icon!=icon){Mo("#icon_"+appid).attr({"src":icon});Mo("#talk_"+appid).attr({"_icon":icon});}
Serv.Apps.show(appid);return true;}
var dg=new Mo.Dialog("window",name,'<iframe id="view_'+appid+'" appid="'+appid+'" src="'+link+'" frameborder="0" scrolling="auto"></iframe>',390,0,{"draged":true,"minimize":true,"maximize":true,"remove":true,"zindex":(Mo.store.zIndex++),"unique":"talk_"+appid});dg.onMinimize=function(){Serv.Apps.hide(appid);return false;}
dg.onRemove=function(){Serv.Apps.close(appid);return false;}
dg.onMaximize=function(btn){if(dg.mode=='Maximize'){Serv.Apps.max(appid);dg.mode='';btn.className='maximize';}else{Mo(dg.object).style({"width":"100%","height":(Mo.document.clientHeight-32)+"px","left":"-5px","top":"-3px"});dg.mode='Maximize';btn.className='toggle';}
Mo("#talk_"+appid).attr({"_show":"true"});return false;}
dg.onDblclick=function(btn){dg.onMaximize(btn);return false;}
dg.onDrag=function(self){Mo(self.object).style({"zIndex":(Mo.store.zIndex+=1)});return false;};dg.Create();if(size){dl.style.top=Serv.prefix.top+5+'px';dl.style.left=(Mo.document.clientWidth-size[0])/2+'px';dl.style.width=size[0];dl.style.height=size[1];}else{Serv.Apps.max(appid);}
Mo.store.talk=appid;Mo("#talk_"+appid).attr({"_show":"true","_link":link});Mo("#task").create("li",{"id":"task_"+appid,"innerHTML":'<a href="javascript:void(0);" onclick="Serv.Apps.show(\''+appid+'\');"><img id="icon_'+appid+'" src="'+icon+'" /></a>'});Mo("#task_"+appid).attr({"appid":appid,"appname":name});Mo("#task_"+appid).bind('mouseover',function(){Serv.Tip(name,this)}).bind('mouseout',function(){Serv.Tip();});return false;}, close:function(appid){Mo("#talk_"+appid).remove();Mo("#task_"+appid).remove();}, show:function(appid,show){Mo("#talk_"+appid).style({"zIndex":(Mo.store.zIndex+=1)}).show();window.status=Mo.store.zIndex;if(!show&&Mo("#talk_"+appid).attr("_show")=="true"&&Mo.store.talk==appid){Serv.Apps.hide(appid);return;}
if(Mo("#talk_"+appid).attr("_show")=="false"){var fx=new FX('talk_'+appid,{left:{to:Serv.prefix.left+5},top:{to:Serv.prefix.top+5},width:{to:Serv.prefix.width-22},height:{to:Serv.prefix.height-52}},.4);fx.start();}
Mo.store.talk=appid;Mo("#talk_"+appid).attr({"_show":"true"});}, hide:function(appid){var pos=Mo("#task_"+appid).position();var fx=new FX('talk_'+appid,{left:{to:pos.left},top:{to:pos.top+50},width:{to:0},height:{to:0}},.4,'easeOut',function(){Mo("#talk_"+appid).hide();});fx.start();Mo.store.talk=null;Mo("#talk_"+appid).attr({"_show":"false"});}, max:function(appid){Mo("#talk_"+appid).style({"top":Serv.prefix.top+5+'px',"left":Serv.prefix.left+5+'px',"width":Serv.prefix.width-22+'px',"height":Serv.prefix.height-52+'px'});}, find:function(win){if(!Mo.store.account)return false;Mo("iframe").each(function(ele){if(ele.contentWindow==win){while(ele=ele.parentNode){if(ele.getAttribute("dialog")=="dialog"){Mo(ele).style({"zIndex":(Mo.store.zIndex+=1)}).show();break;}}
return;}});}}, Login:function(){if(!Mo.store.account){Mo.store.login=new Mo.Dialog('dialog','用户登录','<iframe src="module/system/admin.login.php" frameborder="0"></iframe>',400,250,{"unique":"dialog_login","locked":true,"draged":true});Mo.store.login.Create();Mo("#addon").hide();}}, Logout:function(empty){if(empty){location.reload();}else{if(confirm("现在要退出登录吗？")){Mo.script(Serv.API+"?action=logout&callback=Serv.Logout&rnd="+Math.random());}}}};
