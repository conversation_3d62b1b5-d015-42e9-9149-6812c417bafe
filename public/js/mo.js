 var Mo=function(selector,context){return new Mo.init(selector,context);}
 Mo.version="0.8 Beta"; Mo.build=20110707; Mo.store=new Object(); Mo.plugin=[]; Mo.time=new Date().getTime(); Mo.start=function(){var tags=document.getElementsByTagName("script");var path=tags[tags.length-1].getAttribute('src',2);var base=path?path.substring(0,path.lastIndexOf("/")+1):null;Mo.base=base;  Mo.Browser={msie:false,opera:false,safari:false,chrome:false,firefox:false}; var nav=navigator;var uat=nav.userAgent;var reg='';switch(nav.appName){case"Microsoft Internet Explorer":{Mo.Browser.name="ie";Mo.Browser.msie=true;reg=/^.+MSIE (\d+\.\d+);.+$/;break;}default:{if(uat.indexOf("Chrome")!=-1){Mo<PERSON>Browser.name="chrome";Mo.Browser.chrome=true;reg=/^.+Chrome\/([\d.]+?)([\s].*)$/ig;}else if(uat.indexOf("Safari")!=-1){Mo.Browser.name="safari";Mo.Browser.safari=true;reg=/^.+Version\/([\d\.]+?) (Mobile.)?Safari.+$/;}else if(uat.indexOf("Opera")!=-1){Mo.Browser.name="opera";Mo.Browser.opera=true;reg=/^.{0,}Opera\/(.+?) \(.+$/;}else{Mo.Browser.name="firefox";Mo.Browser.firefox=true;reg=/^.+Firefox\/([\d\.]+).{0,}$/;}};break;}
 Mo.Browser.version=uat.replace(reg,"$1"); Mo.Browser.lang=(!Mo.Browser.msie?nav.language:nav.browserLanguage).toLowerCase(); Mo.Browser.mobile=/(iPhone|iPad|iPod|Android)/i.test(uat); Mo.document=document.compatMode=="CSS1Compat"?document.documentElement:document.body;};Mo.start();  Mo.ready=function(func){if(Mo.Browser.msie){var rnd=Mo.random(10);if(!Mo.store.dri)Mo.store.dri=[];Mo.store.dri[rnd]=setInterval(function(){try{document.documentElement.doScroll('left');clearInterval(Mo.store.dri[rnd]);Mo.store.dri[rnd]=null;func(new Date().getTime()-Mo.time);}catch(ex){}},1);}else{document.addEventListener("DOMContentLoaded",function(){func(new Date().getTime()-Mo.time);},false);}}
 Mo.reader=function(func){Mo(window).bind('load',function(){func(new Date().getTime()-Mo.time);});}; Mo.resize=function(func){Mo(window).bind('resize',func);}
  Mo.date=function(format,datetime){var str=format;var now=datetime?datetime:new Date();var y=now.getFullYear(),m=now.getMonth()+1,d=now.getDate(),h=now.getHours(),i=now.getMinutes(),s=now.getSeconds();str=str.replace('yy',y.toString().substr(y.toString().length-2));str=str.replace('y',y);str=str.replace('mm',('0'+m).substr(m.toString().length-1));str=str.replace('m',m);str=str.replace('dd',('0'+d).substr(d.toString().length-1));str=str.replace('d',d);str=str.replace('hh',('0'+h).substr(h.toString().length-1));str=str.replace('h',h);str=str.replace('ii',('0'+i).substr(i.toString().length-1));str=str.replace('i',i);str=str.replace('ss',('0'+s).substr(s.toString().length-1));str=str.replace('s',s);return str;}
 Mo.random=function(length,upper,lower,number){if(!upper&&!lower&&!number){upper=lower=number=true;};var a=[["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"],["a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z"],["0","1","2","3","4","5","6","7","8","9"]];var b=[];var c="";b=upper?b.concat(a[0]):b;b=lower?b.concat(a[1]):b;b=number?b.concat(a[2]):b;for(var i=0;i<length;i++){c+=b[Math.round(Math.random()*(b.length-1))];};return c;}
 Mo.between=function(min,max){return Math.round(min+(Math.random()*(max-min)));}
 Mo.write=function(){for(var i=0;i<arguments.length;i++){document.write(arguments[i]);}}
 Mo.url=function(url){var a=document.createElement('a');a.href=url;return{source:url,protocol:a.protocol.replace(':',''),host:a.hostname,port:a.port,query:a.search,params:(function(){var ret={},seg=a.search.replace(/^\?/,'').split('&'),len=seg.length,i=0,s;for(;i<len;i++){if(!seg[i]){continue;};s=seg[i].split('=');ret[s[0]]=s[1];};return ret;})(),file:(a.pathname.match(/\/([^\/?#]+)$/i)||[,''])[1],hash:a.hash.replace('#',''),path:a.pathname.replace(/^([^\/])/,'/$1'),relative:(a.href.match(/tps?:\/\/[^\/]+(.+)/)||[,''])[1],segments:a.pathname.replace(/^\//,'').split('/')};}
   Mo.Cookie={ get:function(key){var tmp=document.cookie.match((new RegExp(key+'=[a-zA-Z0-9.()=|%/]+($|;)','g')));if(!tmp||!tmp[0])return null;else return unescape(tmp[0].substring(key.length+1,tmp[0].length).replace(';',''))||null;}, set:function(key,value,ttl,path,domain,secure){var cookie=[key+'='+escape(value),'path='+((!path||path=='')?'/':path),'domain='+((!domain||domain=='')?window.location.hostname:domain)];if(ttl)cookie.push(Mo.Cookie.hoursToExpireDate(ttl));if(secure)cookie.push('secure');return document.cookie=cookie.join('; ');}, unset:function(key,path,domain){path=(!path||typeof path!='string')?'':path;domain=(!domain||typeof domain!='string')?'':domain;if(Mo.Cookie.get(key))Mo.Cookie.set(key,'','Thu, 01-Jan-70 00:00:01 GMT',path,domain);}, hoursToExpireDate:function(ttl){if(parseInt(ttl)=='NaN')return'';else{var now=new Date();now.setTime(now.getTime()+(parseInt(ttl)*60*60*1000));return now.toGMTString();}},   dump:function(){if(typeof console!='undefined'){console.log(document.cookie.split(';'));}},clear:function(){var keys=document.cookie.match(/[^ =;]+(?=\=)/g);if(keys){for(var i=keys.length;i--;){Mo.Cookie.unset(keys[i]);}}}}
 Mo.get=function(key,url){var url=url?url:location.href;var v='';var o=url.indexOf(key+"=");if(o!=-1){o+=key.length+1;e=url.indexOf("&",o);if(e==-1){e=url.length;}
v=url.substring(o,e);};return v;}
 Mo.script=function(src,attr,func,target){if(typeof func!='function')var func=function(){};if(src.indexOf(".")==-1){if(Mo.Array(Mo.plugin).indexOf(src)>-1){func&&func();return;}else{src=Mo.base+"mo."+src+".js";}}
var target=target?target:document.getElementsByTagName("head")[0];attr=attr?attr:{};attr["type"]="text/javascript";attr["src"]=src;var script=Mo.create("script",attr);target.appendChild(script);script.onload=script.onreadystatechange=function(){if(!this.readyState||this.readyState=="loaded"||this.readyState=="complete"){func&&func(this);}};}
 Mo.json=function(src,attr,func){if(typeof func!='function')var func=function(){};var callback=c='cross'+parseInt(Math.random()*1000);var head=document.getElementsByTagName("head")[0];src=src.substr(0,src.length-1)+c;attr=attr?attr:{};attr["type"]="text/javascript";attr["src"]=src;var script=Mo(head).create("script",attr,true);window[c]=window[c]||function(data){func(data);window[c]=undefined;try{delete window[c];}catch(e){};if(head){head.removeChild(script);}};}
 Mo.Array=function(source){var inti=function(source){this.self=source;return this;}
inti.prototype={output:function(){return this.self;},first:function(){return this.self[0];},last:function(){return this.self[this.self.length-1];},clear:function(){this.self=[];return this;}, indexOf:function(value){var l=this.self.length;for(var i=0;i<=l;i++){if(this.self[i]==value)return i;};return-1;}}
return new inti(source);}
  Mo.String=function(source){var inti=function(source){this.self=source;return this;}
inti.prototype={output:function(){return this.self;},long:function(){var str=this.self;var l=str.length;var len=0;for(var i=0;i<l;i++){if(str.charCodeAt(i)>255){len+=2;}else{len++;}};return len;},trim:function(){this.self=this.self.replace(/(^\s*)|(\s*$)/g,"");return this;},leftTrim:function(){this.self=this.self.replace(/(^\s*)/g,"");return this;},rightTrim:function(){this.self=this.self.replace(/(\s*$)/g,"");return this;},stripScript:function(){this.self=this.self.replace(/<script.*?>.*?<\/script>/ig,'');return this;},unicode:function(){if(this.self){var result='';for(var i=0;i<this.self.length;i++){result+='&#'+this.self.charCodeAt(i)+';';};this.self=result;};return this;},ascii:function(){if(this.self){var code=this.self.match(/&#(\d+);/g);if(code!=null){var result='';for(var i=0;i<code.length;i++){result+=String.fromCharCode(code[i].replace(/[&#;]/g,''));};this.self=result;}};return this;},format:function(){var param=[];for(var i=0,l=arguments.length;i<l;i++){param.push(arguments[i]);}
this.self=this.self.replace(/\{(\d+)\}/g,function(m,n){return param[n];});return this;}, htmlDecode:function(string){return this.self.replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,"\"");}, htmlEncode:function(string){return this.self.replace(/&/g,"&amp;").replace(/\"/g,"&quot;").replace(/</g,"&lt;").replace(/>/g,"&gt;");}}
return new inti(source);}
  Mo.Number={test:function(){alert('');return;},test2:function(){alert('');return;}, isNumber:function(num){return/^[0-9]{1,20}$/.exec(num);}}
  Mo.Function=function(func){var inti=function(func){this.self=func||new Function();return this;}
inti.prototype={execute:function(time){if(time){window.setTimeout(this.self,time);}else{this.self();}}}
return new inti(func);}
  Mo.Date=function(date){var inti=function(date){this.self=date||new Date();return this;}
inti.prototype={leapyear:function(){var year=this.self.getFullYear();return(0==year%4&&((year%100!=0)||(year%400==0)));},days:function(){return(new Date(this.self.getFullYear(),this.self.getMonth()+1,0)).getDate();},time:function(){return Math.round(this.self.getTime()/1000);}}
return new inti(date);}
  Mo.Event=function(event){var inti=function(event){this.self=event||window.event;return this;}
inti.prototype={ stop:function(){if(!this.self)return;if(Mo.Browser.msie){this.self.cancelBubble=true;this.self.returnValue=false;}else{this.self.stopPropagation();this.self.preventDefault();}
return this;}, element:function(){if(!this.self)return;if(Mo.Browser.msie){return window.event.srcElement;}else{return this.self.currentTarget;}}, target:function(){if(!this.self)return;if(Mo.Browser.msie){return window.event.srcElement;}else{return this.self.target;}}, mouse:function(){if(!this.self)return;if(Mo.Browser.msie){var x=this.self.x+Mo.document.scrollLeft;var y=this.self.y+Mo.document.scrollTop;}else{var x=this.self.pageX;var y=this.self.pageY;};return{"x":x,"y":y};}, keyboard:function(code,func){if(!this.self)return;if((code>-1&&this.self.keyCode==code)||code==-1){func(this.self,this.self.keyCode);}}}
return new inti(event);}
  Mo.Validate={Array:function(obj){return Object.prototype.toString.apply(obj)==='[object Array]';},Function:function(obj){return Object.prototype.toString.apply(obj)==='[object Function]';}, Object:function(obj){return Object.prototype.toString.apply(obj)==='[object Object]';}, Date:function(o){if(typeof o=='string'){return o.match(/^(\d{4})(\-)(\d{1,2})(\-)(\d{1,2})(\s{1})(\d{1,2})(\:)(\d{1,2})/)!=null||o.match(/^(\d{4})(\-)(\d{1,2})(\-)(\d{1,2})/)!=null;}else{return Object.prototype.toString.apply(o)==='[object Date]';}},Number:function(o){return!isNaN(parseFloat(o))&&isFinite(o);},String:function(o){return typeof o==='string';},Defined:function(o){return typeof o!='undefined';},Empty:function(o){return typeof o=='undefined'||o=='';},Boolean:function(o){return typeof o==='boolean';},Window:function(o){return/\[object Window\]/.test( o );
},Document:function(o){return/\[object HTMLDocument\]/.test( o );
},Element:function(o){return o.tagName?true:false;},   Chinese:function(str,all){if(all){return(str.length*2==str.replace(/[^\x00-\xff]/g,"**").length);}else{return(str.length!=str.replace(/[^\x00-\xff]/g,"**").length);}},Safe:function(str){var chkstr;var i;chkstr="'*%@#^$`~!^&*()=+{}\\|{}[];:/?<>,.";for(i=0;i<str.length;i++){if(chkstr.indexOf(str.charAt(i))!=-1)return false;};return true;},Email:function(str){return/^\s*([A-Za-z0-9_-]+(\.\w+)*@(\w+\.)+\w{2,3})\s*$/.test(str);},URL:function(str){return/^[a-zA-z]+:\/\/(\w+(-\w+)*)(\.(\w+(-\w+)*))*(\?\S*)?$/.test(str);},IP:function(str){return/^[0-9.]{1,20}$/.test(str);},Password:function(str){return/^(\w){6,20}$/.test(str);},Color:function(str){return/^#(\w){6}$/.test(str);},  ID:function(str){if(str.length==15){return Mo.Validate.Number(str.substring(0,14));}else if(str.length==18){return Mo.Validate.Number(str.substring(0,17));}else{return false;}},Phone:function(str){return/(?:^0{0,1}1\d{10}$)|(?:^[+](\d){1,3}1\d{10}$)|(?:^0[1-9]{1,2}\d{1}\-{0,1}[2-9]\d{6,7}$)|(?:^\d{7,8}$)|(?:^0[1-9]{1,2}\d{1}\-{0,1}[2-9]\d{6,7}[\-#]{1}\d{1,5}$)/.test(str);},Mobile:function(str){return/^[1][0-9]{10}$/.test(str);}}
     Mo.find=(function(){var snack=/(?:[\*\w\-\\.#]+)+(?:\[\w+?=([\'"])?(?:\\\1|.)+?\1\])?|\*|>/ig,exprClassName=/^(?:[\w\-_]+)?\.([\w\-_]+)/,exprId=/^(?:[\w\-_]+)?#([\w\-_]+)/,exprNodeName=/^([\w\*\-_]+)/,na=[null,null,null];var exprAttr=/\[([\w\-_][^=]+)=([\'\[\]\w\-_]+)\]/;var exprAttrArr=/\[([\w\-_][^=]+)=(([\w\-_]+)\{([\w\-_]+)\})\]/;function _find(selector,context){ context=context||document;var simple=/^[\w\-_#]+$/.test(selector);if(!simple&&context.querySelectorAll){return realArray(context.querySelectorAll(selector));};if(selector.indexOf(',')>-1){var split=selector.split(/,/g),ret=[],sIndex=0,len=split.length;for(;sIndex<len;++sIndex){ret=ret.concat(_find(split[sIndex],context));};return unique(ret);};var parts=selector.match(snack),part=parts.pop(),id=(part.match(exprId)||na)[1],className=!id&&(part.match(exprClassName)||na)[1],nodeName=!id&&(part.match(exprNodeName)||na)[1],collection;var atNode=(part.match(exprAttr)||na)[1];var atValue=(part.match(exprAttr)||na)[2];if(className&&!nodeName&&context.getElementsByClassName){collection=realArray(context.getElementsByClassName(className));}else{collection=!id&&realArray(context.getElementsByTagName(nodeName||'*'));if(className){collection=filterByAttr(collection,'className',RegExp('(^|\\s)'+className+'(\\s|$)'));};if(id){var byId=context.getElementById(id);return byId?[byId]:[];}
if(atNode&&atValue){collection=filterByAttr(collection,atNode,RegExp('(^'+atValue.replace(/\'/g,'').replace(/\-/g,'\\-').replace(/\[/g,'\\[').replace(/\]/g,'\\]')+'$)'));}
};return parts[0]&&collection[0]?filterParents(parts,collection):collection;};function realArray(c){ try{return Array.prototype.slice.call(c);}catch(e){var ret=[],i=0,len=c.length;for(;i<len;++i){ret[i]=c[i];};return ret;}};function filterParents(selectorParts,collection,direct){ var parentSelector=selectorParts.pop();if(parentSelector==='>'){return filterParents(selectorParts,collection,true);};var ret=[],r=-1,id=(parentSelector.match(exprId)||na)[1],className=!id&&(parentSelector.match(exprClassName)||na)[1],nodeName=!id&&(parentSelector.match(exprNodeName)||na)[1],cIndex=-1,node,parent,matches;nodeName=nodeName&&nodeName.toLowerCase();while((node=collection[++cIndex])){parent=node.parentNode;do{matches=!nodeName||nodeName==='*'||nodeName===parent.nodeName.toLowerCase();matches=matches&&(!id||parent.id===id);matches=matches&&(!className||RegExp('(^|\\s)'+className+'(\\s|$)').test(parent.className));if(direct||matches){break;}}while((parent=parent.parentNode));if(matches){ret[++r]=node;}};return selectorParts[0]&&ret[0]?filterParents(selectorParts,ret):ret;};var unique=(function(){var uid=+new Date();var data=(function(){var n=1;return function(elem){var cacheIndex=elem[uid],nextCacheIndex=n++;if(!cacheIndex){elem[uid]=nextCacheIndex;return true;};return false;};})();return function(arr){ var length=arr.length,ret=[],r=-1,i=0,item;for(;i<length;++i){item=arr[i];if(data(item)){ret[++r]=item;}};uid+=1;return ret;};})();function filterByAttr(collection,attr,regex){ var i=-1,node,r=-1,ret=[];while((node=collection[++i])){if(regex.test(node[attr])){ret[++r]=node;}};return ret;};return _find;})();  Mo.extend=function(func){for(var i in func){Mo.init.prototype[i]=func[i];}}
 Mo.create=function(tag,attr){var obj=document.createElement(tag);if(attr){for(var i in attr){obj[i]=attr[i];}};return obj;}
 Mo.init=function(selector,context){this.self=typeof selector=='string'?Mo.find(selector,context):[selector];};Mo.init.prototype={ size:function(){return this.self.length;}, item:function(i){var size=this.size();if(i>=0){return i<=size?this.self[i]:null;}else{return Math.abs(i)<=size?this.self[(size+i)]:null;}}, hide:function(speed,func){this.each(function(ele){ele.style.display="none";if(typeof(func)=="function")func.call(ele,ele);});return this;}, show:function(speed,func){this.each(function(ele){ele.style.display="";if(typeof(func)=="function")func.call(ele,ele);});return this;}, toggle:function(speed,func){this.each(function(ele){if(ele.offsetHeight==0||ele.offsetWidth==0){ele.style.display="";var res=true;}else{ele.style.display="none";var res=false;}
if(typeof(func)=="function")func.call(ele,ele,res);});return this;}, click:function(){this.each(function(ele){if(typeof(ele.click)=="object")ele.click();});return this;}, value:function(text,add){if(typeof text!="undefined"){this.each(function(ele){var len=ele.length;switch(ele.type){case"select-one":for(var i=0;i<len;i++){if(ele[i].value==text){ele.selectedIndex=i;break;}};break;case"select-multiple":for(var i=0;i<len;i++){if(Mo.Array(text).indexOf(ele[i].value)!==-1){ele[i].selected=true;}else{ele[i].selected=false;}};break;case"radio":case"checkbox":if((Mo.Validate.Array(text)&&Mo.Array(text).indexOf(ele.value)!==-1)||ele.value==text){ele.checked=true;}else{ele.checked=false;};break;case"text":case"hidden":case"textarea":case"password":if(add){ele.value+=text;}else{ele.value=text;};break;}});return this;}
var val=[];this.each(function(ele){var len=ele.length;switch(ele.type){case"select-one":if(len){val=ele[ele.selectedIndex].value;};break;case"select-multiple":for(var i=0;i<len;i++){if(ele[i].selected){val.push(ele[i].value);}};break;case"radio":case"checkbox": if(ele.checked){val.push(ele.value);};break;case"text":case"hidden":case"textarea":case"password":val=ele.value;break;}});return val;}, text:function(text,replace){if(typeof text!="undefined"){this.each(function(ele){var len=ele.length;switch(ele.type){case"select-one":for(var i=0;i<len;i++){if(ele[i].text==text){ele.selectedIndex=i;if(typeof replace!="undefined")ele[i].text=replace;break;}};break;case"select-multiple":for(var i=0;i<len;i++){if(Mo.Array(text).indexOf(ele[i].text)!==-1){ele[i].selected=true;if(typeof replace!="undefined")ele[i].text=replace;}else{ele[i].selected=false;}};break;}});return this;}
var val=[];this.each(function(ele){var len=ele.length;switch(ele.type){case"select-one":if(len){val=ele[ele.selectedIndex].text;};break;case"select-multiple":for(var i=0;i<len;i++){if(ele[i].selected){val.push(ele[i].text);}};break;}});return val;}, html:function(html,add){if(typeof html!="undefined"){this.each(function(ele){if(add){ele.innerHTML+=html;}else{ele.innerHTML=html;}});return this;};var ele=this.self[0];return ele.innerHTML;}, attr:function(key){if(typeof key=="string"){var ele=this.self[0];switch(key){case"class":return ele.className;break;default:return ele.getAttribute(key);break;}}else{this.each(function(ele){for(var x in key){switch(x){case"class":ele.className=key[x];break;default:ele.setAttribute(x,key[x]);break;}}});return this;}}, style:function(key){if(typeof key=="string"){var ele=this.self[0];var $S=function(){var f=document.defaultView;return new Function('el','style',["style.indexOf('-')>-1 && (style=style.replace(/-(\\w)/g,function(m,a){return a.toUpperCase()}));","style=='float' && (style='",f?'cssFloat':'styleFloat',"');return el.style[style] || ",f?'window.getComputedStyle(el, null)[style]':'el.currentStyle[style]',' || null;'].join(''));}();return $S(ele,key); }else{this.each(function(ele){for(var x in key){ele.style[x]=key[x];}});return this;}}, position:function(json){var ele=this.self[0];var width=ele.offsetWidth;var height=ele.offsetHeight;var top=ele.offsetTop;var left=ele.offsetLeft;while(ele=ele.offsetParent){top+=ele.offsetTop;left+=ele.offsetLeft;};return{"width":width,"height":height,"top":top,"left":left};}, each:function(func){var size=this.size();var ele=this.self;for(var i=0;i<size;i++){ var res=func.call(ele[i],ele[i],i); if(res===false)break;};return this;}, bind:function(evt,func){this.each(function(ele,index){var oldonevent=ele['on'+evt];if(typeof ele['on'+evt]!='function'){ele['on'+evt]=function(event){return func.call(ele,ele,index,event);};}else{ele['on'+evt]=function(event){oldonevent(event);return func.call(ele,ele,index,event);}}});return this;}, unbind:function(evt){this.each(function(ele){ele['on'+evt]=null;});return this;}, focus:function(func){this.each(function(ele,index){ele.focus();if(typeof func=='function'){return func.call(ele,ele,index);}});return this;}, blur:function(func){this.each(function(ele,index){ele.blur();if(typeof func=='function'){return func.call(ele,ele,index);}});return this;}, submit:function(func){this.each(function(ele,index){if((typeof func=='function'&&func.call(ele,ele,index))||typeof func=="undefined"){ele.submit();};return false;});return this;}, reset:function(func){this.each(function(ele,index){if((typeof func=='function'&&func.call(ele,ele,index))||typeof func=="undefined"){ele.reset();};return false;});return this;}, disabled:function(){this.each(function(ele){ele.disabled=true;});return this;}, enabled:function(){this.each(function(ele){ele.disabled=false;});return this;}, checked:function(checked){this.each(function(ele){if(Mo.Validate.Boolean(checked)){ele.checked=checked;}else{ele.checked=(ele.checked?false:true);}});return this;}, insert:function(tag,attr,self){var obj=Mo.create(tag,attr);this.each(function(ele){ele.parentNode.insertBefore(obj,ele);});if(self)return obj;return this;}, create:function(tag,attr,self){var obj=Mo.create(tag,attr);this.append(obj);if(self)return obj;return this;}, append:function(){for(var i=0;i<arguments.length;i++){var obj=arguments[i];this.each(function(ele){ele.appendChild(obj);});};return this;}, remove:function(){this.each(function(ele){ele.parentNode.removeChild(ele);});return this;},animation:function(obj,left,top,width,height,time,callbackFun){if(!obj)return;var interval=10;time=(time&&time>100?time:100)/interval;var step=[parseInt((left!=null?parseInt(left)-parseInt(obj.offsetLeft):0)/time),parseInt((top!=null?parseInt(top)-parseInt(obj.offsetTop):0)/time),parseInt((width!=null?parseInt(width)-parseInt(obj.offsetWidth):0)/time),parseInt((height!=null?parseInt(height)-parseInt(obj.offsetHeight):0)/time)];var t=0;var timer=setInterval(function(){if(++t>time){clearInterval(timer);if(callbackFun)callbackFun();return;};obj.style.left=parseInt(obj.offsetLeft)+parseInt(step[0])+"px";obj.style.top=parseInt(obj.offsetTop)+parseInt(step[1])+"px";obj.style.width=parseInt(obj.offsetWidth)+parseInt(step[2])+"px";obj.style.height=parseInt(obj.offsetHeight)+parseInt(step[3])+"px";},interval);return this;}, motion:function(speed,option){ Mo.Logs.start("logs");for(var o in option){var pos=this.position();if(!option[o].min&&!option[o].max)return this;if(!option[o].min)option[o].min=pos[o];if(!option[o].max)option[o].max=pos[o];if(option[o].min&&option[o].min!=pos[o]){this.self.style[o]=option[o].min+"px";};var self=this;(function(){var x=o;var config=option;var root=self;var rnd=Mo.random(10,true);Mo.temp[rnd]=setInterval(function(){var pos=root.position();var val=config[x].max&&pos[x]+speed>config[x].max?config[x].max:pos[x]+speed;root.self.style[x]=val+"px";if(val>=config[x].max){clearInterval(Mo.temp[rnd]); }},10);})();}
 return this;}}
;
