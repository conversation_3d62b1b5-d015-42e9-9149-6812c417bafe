 if(typeof Mo!='function'){var Mo={plugin:[]}} ;<PERSON><PERSON>Batch=function(input,value,config,mode){Mo.write('<textarea name="'+input+'" id="'+input+'" rows="10" style="display:none;">'+value+'</textarea>');var format=config["format"]?"*."+config["format"].join(";*.")+";":"";var group='';var i=0;for(var g in config["group"]){group+='group['+i+']='+config["group"][g]+';';i++;};var thumb=config["thumb"]?config["thumb"]:'';var remote=config["remote"]?config["remote"]:'';var session=config["session"]?config["session"]:'';var watermark=config["watermark"]?config["watermark"]:'';var position=config["position"]?config["position"]:'0';var multiple=config["multiple"]?config["multiple"]:'0';Mo.write('<embed id="'+input+'_flash" name="'+input+'_flash" src="'+Mo.root+'/resources/upload/select.swf?input=file&source='+input+'&amp;format='+format+'&amp;args=model=flash;session='+session+';watermark='+watermark+';position='+position+';multiple='+multiple+';remote='+remote+';thumb='+thumb+';'+group+'&amp;limit='+config["limit"]+'&amp;size='+config["size"]+'&amp;every='+config["every"]+'&amp;finish='+config["finish"]+'&amp;save='+Mo.root+'veryide.upload.php" width="430" height="25"></embed>');};Mo.Batch.Delete=function(input,file){if(confirm("您确认从服务器删除 "+file+" ？\n\n删除的文件将无法恢复！")){var XmlURL=Mo.Manager.action;var ajax=new Mo.Ajax(XmlURL);ajax.method="GET";ajax.setVar({"action":"file-delete","&file":file,"&input":"file","&rnd":Math.random()});ajax.onError=function(){alert(ajax.response)};ajax.onCompletion=function(){var response=ajax.responseXML;var result=response.getElementsByTagName('result')[0].firstChild.data;var error=response.getElementsByTagName('error')[0].firstChild.data;switch(error){case"file":case"not":$(input).value=$(input).value.replace('||'+file,"");$(input).value=$(input).value.replace(file,"");remove($(file));break;case"login":parent.Mo.Alert("warning","文件上传",parent.Mo.loginBox);break;case"config":parent.Mo.Alert("warning","文件上传","您的操作已经取消！<br />您当前没有权限删除文件！");break;}};ajax.send("");}} ;Mo.Batch.onUpload=function(file,current,input){if(file.indexOf(".")==-1){alert("上传出错，可能是文件过小");return;};var obj=document.createElement("li");obj.innerHTML='<a href="'+file+'" target="_blank"><img src="'+file+'" /></a>';$(input+"-album").appendChild(obj);$(input).value+=($(input).value?'||'+file:file);} ;Mo.Batch.onUploadEnd=function(succee,totality,input){return"已上传 "+succee+" 个，共 "+totality+" 个文件"}  ;Serv.Upload=function(input,value,config,mode){eval("($"+input+" = this)");this.input=input;this.value=value;this.extra=value.replace(/.*\./,"").toLowerCase();this.image=[];this.picture=['jpg','jpeg','gif','png','bmp'];this.config=config;this.debug=false; var self=this;var $=function(id){return document.getElementById(id);};var obj=$(input);if(obj||$(input+"_iframe")||$(input+"_form")||$(input+"_span")){parent.Mo.Alert("warning","文件上传","<span class='key'>已经存在 VeryIDE UpLoad 实例: </span><span class='no'>"+input+" </span>");return false;};switch(mode){case"extra":this.Extra='<span id="'+input+'_span"></span>';break;default:Mo.write('<span id="'+input+'_span"></span>');break;};this.Save=function(){remove($(this.input+"_box"));var _html='';var icon=Mo.folder.images+'/attach.png';if(Mo.Array(Mo.Manager.files).indexOf(this.extra)>-1){icon=Mo.folder.images+"files/"+this.extra+".gif";};_html+='<input type="hidden" name="'+this.input+'" id="'+this.input+'" value="'+this.value+'" />'+'<img src="'+icon+'" align="absmiddle" /> <a href="'+this.value+'" target="_blank">'+this.value+'</a>  -  ';if(this.image||this.size){var str='';if(this.image.length){str+='图片尺寸:<br />'+this.image[0]+'px *'+this.image[1]+'px';};if(this.size){str+='<br />文件大小:<br />'+Math.round(parseFloat(this.size)/1024)+' KB';};if(str){_html+=' <span class="upload" title="查看详细文件信息" onclick="window.parent.Mo.Alert(&quot;warning&quot;,&quot;文件详细信息&quot;,&quot;'+str+'&quot;);")"><img src="'+Mo.folder.icons+'/document.png" align="absmiddle" /> ';};if(this.size){_html+=' - '+Math.round(parseFloat(this.size)/1024)+' KB - ';};_html+=' </span>';};if(this.config.again){_html+='<span onclick="javascript:$'+this.input+'.New()" class="upload"  title="添加新文件"><img src="'+Mo.folder.icons+'plus.png" align="absmiddle" /> 新文件</span> - ';};_html+='<span onclick="javascript:$'+this.input+'.Delete()" class="upload" title="删除这个文件"><img src="'+Mo.folder.icons+'/block.png" align="absmiddle" /> 删除</span><img src="'+Mo.folder.images+'spacer.gif" height="20" width="1" />';if(this.image){_html+=' <span class="upload" title="处理图片" onclick="javascript:$'+this.input+'.Editor()"><img src="'+Mo.folder.icons+'/photo.png" align="absmiddle" /> 处理</span>';};$(this.input+"_span").innerHTML=_html;if(this.config.preview&&$(this.config.preview)&&Mo.Array(this.picture).indexOf(this.extra)>-1){$(this.config.preview).innerHTML='<img src="'+this.value+'" />';};if(this.config.ubb&&$(this.config.ubb)){if(Mo.Array(this.picture,this.extra)===false){$(this.config.ubb).value+='[URL]'+this.value+'[/URL]';}else{$(this.config.ubb).value+='[IMG]'+this.value+'[/IMG]';}};if(this.config.callback){this.config.callback({"file":this.value,"size":this.size,"image":this.image,"extra":this.extra});}};this.Editor=function(){parent.window.__PE__=parent.Serv.Dialog('piceditor','图片','<iframe src="resources/piceditor/?picurl='+this.value+'" scrolling="no" width="680" height="530"  frameborder="0"></iframe>',695,570,{"minimize":true,"remove":true});parent.window.__PE__.Create();};this.Select=function(){var _html="";if(this.config.input){_html='<input type="text" class="text" name="'+this.input+'" id="'+this.input+'" value="" size="35" '+this.config.attr+' onchange="javascript:$'+this.input+'.Change(this);" /> ';}else{_html='<input type="hidden" name="'+this.input+'" id="'+this.input+'" value="" />';};_html+='<span class="upload" id="'+this.input+'_btn" title="浏览并选择本地文件"><img src="'+Mo.folder.icons+'plus.png" alt="" align="absmiddle" /> 浏览文件</span>';if(this.config.width){_html+=' - <span class="upload" onclick="window.parent.Mo.Alert(&quot;warning&quot;,&quot;文件上传&quot;,&quot;上传图片最佳尺寸:<br />'+this.config.width+'px * '+this.config.height+'px &quot;);",{})"><img src="'+Mo.folder.icons+'wand.png" align="absmiddle" /> '+this.config.width+'px * '+this.config.height+'px </span>';};if(this.config.recovery){_html+=' - <span onclick="javascript:$'+this.input+'.Back()" class="upload" title="使用最近一次上传的文件"><img src="'+Mo.folder.icons+'clock.png" align="absmiddle" /> 最近上传</span>';};_html+='<iframe width="100%" height="100" frameborder="1" name="'+this.input+'_iframe" id="'+this.input+'_iframe" src="about:blank" style="display:'+(this.debug?'block':'none')+';"></iframe>';if(this.config.format&&this.config.help!==false){_html+=' - <span class="upload" title="查看详细上传支持格式" onclick="javascript:window.parent.Mo.Alert(&quot;warning&quot;,&quot;文件上传&quot;,&quot;支持上传如下格式:<br />'+this.config.format.join(", ")+'&quot;);",{})"><img src="'+Mo.folder.icons+'bubble.png" align="absmiddle" /> 支持格式</span>';};$(this.input+"_span").innerHTML=_html;$(this.input+"_btn").onclick=function(){self.Show();};if(this.config.preview&&$(this.config.preview)){$(this.config.preview).innerHTML="";};if(this.config.callback){this.config.callback({"file":this.value,"size":this.size,"image":this.image,"extra":this.extra});}} ;this.Change=function(o){this.config.callback({"file":o.value,"size":"","image":[],"extra":""});};this.Hide=function(){$(this.input+"_box").style.display="none";};this.Show=function(){if($(this.input+"_box")){$(this.input+"_box").style.display="";return;};var div=document.createElement("div");with(div){id=this.input+"_box";name=this.input+"_box";className="browse";};var html='';html+='<form id="'+this.input+'_form" name="'+this.input+'_form" target="'+this.input+'_iframe" method="post" enctype="multipart/form-data" action=""><em><img onclick="javascript:$'+this.input+'.Hide()" src="'+Mo.folder.images+'layout/cross.png" align="absmiddle" /></em>请选择要上传的文件：<br />';html+='<input name="file" id="'+this.input+'_file" type="file" value="" style="width:210px;" />';html+='<input name="input" type="hidden" value="'+this.input+'" />';html+='<input name="format" type="hidden" value="'+(this.config.format?this.config.format.join(","):'')+'" />';html+='<input name="NewName" type="hidden" value="'+(this.config.newname?this.config.newname:'')+'" />';html+='<input name="remote" type="hidden" value="'+(this.config.remote?this.config.remote:'')+'" />';html+='<input name="thumb" type="hidden" value="'+(this.config.thumb?this.config.thumb:'0')+'" />';html+='<input name="watermark" type="hidden" value="'+(this.config.watermark?this.config.watermark:'')+'" />';html+='<input name="position" type="hidden" value="'+(this.config.position?this.config.position:'0')+'" />';html+='<input name="multiple" type="hidden" value="'+(this.config.multiple?this.config.multiple:'')+'" />';for(var g in this.config.group){html+='<input name="group[]" type="hidden" value="'+this.config.group[g]+'" />';};html+='</form>';div.innerHTML=html;document.body.appendChild(div);var doc=Mo.document;div.style.left=((doc.clientWidth-div.offsetWidth)/2)+"px";div.style.top=doc.scrollTop+((doc.clientHeight-div.offsetHeight)/ 2 )+"px";
$(this.input+"_file").onchange=function(){self.Check(this);};if(!Mo.script["drag"])return false;var dg=new Mo.Drag(div,null,10,doc.clientWidth-div.offsetWidth-10,10);dg.onmouseover=function(){this.style.cursor="move";};dg.onmouseout=function(){this.style.cursor="default";};dg.onStart=function(x,y){div.style.filter="alpha(opacity=60)";div.style.opacity=0.6;};dg.onEnd=function(x,y){div.style.filter="alpha(opacity=100)";div.style.opacity=1;}} ;this.Check=function(file){var value=file.value;var extra=value.replace(/.*\./,"").toLowerCase();if(value&&extra){if(this.config.format&&Mo.Array(this.config.format).indexOf(extra)>-1){window.parent.Mo.Alert("warning","文件上传","请选择正确的文件类型！<br />支持的文件类型："+this.config.format.join(",")+"<br />当前文件类型为："+extra,'Alert');this.Locker(false);$(this.input+"_form").reset();}else{$(this.input+"_form").action=Mo.root+"veryide.upload.php";$(this.input+"_form").submit();$(this.input+"_form").innerHTML='<img src="'+Mo.root+'images/loading.gif" /> 文件上传中，请稍后...';}}};this.Locker=function(lock){var obj=$(this.input);obj.readonly=lock;while(obj=obj.parentElement){if(obj.tagName=="FORM"){var len=obj.elements.length;for(i=0;i<len;i++){var inpElm=obj.elements[i];var inpType=inpElm.getAttribute("type");if(inpType=="submit"){if(inpElm.value!="文件上传中.."){inpElm.defaultValue=inpElm.value;};if(lock){inpElm.value="文件上传中..";}else{inpElm.value=inpElm.defaultValue;};inpElm.disabled=lock;break;}};break;}}};this.New=function(){if(confirm("您确认从新上传一个文件吗？\n\n现有的记录将会丢失！")){this.Select();}};this.Delete=function(){if(confirm("您确认从服务器删除 "+this.value+" ？\n\n删除的文件将无法恢复！")){var XmlURL=Mo.Manager.action;var ajax=new Mo.Ajax(XmlURL);ajax.method="GET";ajax.setVar({"action":"file-delete","&file":this.value,"&input":this.input,"&new":this.config.CopyCorp,"&rnd":Math.random()});ajax.onError=function(){alert(ajax.response)};ajax.onCompletion=function(){var response=ajax.responseXML;var result=response.getElementsByTagName('result')[0].firstChild.data;var error=response.getElementsByTagName('error')[0].firstChild.data;switch(error){case"not":self.value="";self.Select();break;case"login":parent.Mo.Alert("warning","文件上传",parent.Mo.loginBox);break;case"config":parent.Mo.Alert("warning","文件上传","您的操作已经取消！<br />您当前没有权限删除文件！");break;case"file":parent.Mo.Alert("warning","文件上传","未找到目标文件！<br />文件是否已经通过其它方式被删除了？<br />您还可以 <a href='javascript:Mo.Alert(&quot;warning&quot;);Mo.Manager.getBox().$"+this.input+".New();void(0);'>重新上传文件</a>");break;}};ajax.send("");}};this.Back=function(){if(this.config.recovery){var XmlURL=Mo.Manager.action;var ajax=new Mo.Ajax(XmlURL);ajax.method="GET";ajax.setVar({"action":"file-back","&input":this.input,"&rnd":Math.random()});ajax.onError=function(){alert(ajax.response)};ajax.onCompletion=function(){var response=ajax.responseXML;var result=response.getElementsByTagName('result')[0].firstChild.data;var error=response.getElementsByTagName('error')[0].firstChild.data;switch(error){case"not":var file=response.getElementsByTagName('name')[0].firstChild.data;var size=response.getElementsByTagName('size')[0].firstChild.data;var type=response.getElementsByTagName('type')[0].firstChild.data;var width=response.getElementsByTagName('width')[0].firstChild.data;var height=response.getElementsByTagName('height')[0].firstChild.data;self.value=file;self.extra=type;self.size=size;self.image=[width,height];self.Save();break;case"login":window.parent.Mo.Alert("warning",parent.Mo.loginBox);break;case"config":window.parent.Mo.Alert("warning","文件上传","您的操作已经取消！<br />您当前没有权限返回文件！");break;case"file":window.parent.Mo.Alert("warning","文件上传","未找到文件<br />在文件数据库中未找到相匹配的文件！");break;}};ajax.send("");}} ;this.Ok=function(value,size,image){this.value=value;this.size=size;this.image=image;this.extra=value.replace(/.*\./,"").toLowerCase(); this.Save();this.Locker(false);};this.Error=function(s,i){var array=[];array[0]='文件上传被禁止!<br />您当前不在登录状态!';array[1]='文件上传被禁止!<br />系统已禁用文件上传!';array[2]='文件上传被取消!<br />您当前没有上传文件的权限!';array[3]='文件上传被取消!<br />没有收到任何数据!';array[4]='上传失败！<br />您上传的文件超出服务器的限制,最大'+i;array[5]='上传文件的大小超过了 HTML 表单中 MAX_FILE_SIZE 选项指定的值。';array[6]='上传失败！<br />您的文件只有部分被上传';array[7]='上传失败！<br />没有文件被上传';array[8]='该文件类型（'+i+'）不允许上传!';array[9]='上传失败！<br />请确认FTP服务器是否有写权限或者远程目录是否存在！';array[10]='不存在文件存放目录：'+i+'<br />需要手动创建';parent.Mo.Alert("warning","文件上传",array[s]);this.Locker(false);remove($(this.input+"_box"));this.Show();};this.ImageCorpAuto___=function(ObjV){var imgCorp=Mo.Manager.corpImageAuto;var _Ext=this.value.replace(/.*\./,"").toLowerCase();if(_Ext=="jpg"||_Ext=="gif"||_Ext=="jpeg"||_Ext=="png"||_Ext=="bmp"){var ajax=new Mo.Ajax(imgCorp);ajax.method="GET";ajax.setVar({"file":encodeURIComponent(this.config.Value),"&action":"corp","&new":this.config.CopyCorp,"&width":this.config.Width,"&height":this.config.Height,"&rnd":Math.random()});ajax.onError=function(){alert(ajax.response)};ajax.onCompletion=function(){var response=ajax.responseXML;var error=response.getElementsByTagName('error')[0].firstChild.data;switch(error){case"session":parent.Mo.Alert("warning","文件上传",parent.Mo.loginBox);break;case"file":parent.Mo.Alert("warning","文件上传","切图操作失败！<br />未找到图片文件，请检查文件地址是否正确");break;case"image":parent.Mo.Alert("warning","文件上传","切图操作失败！<br />所选文件不是有效的图片格式");break;case"not":if(this.config.Preview&&$(this.config.Preview)){$(this.config.Preview).src+=+"?"+Math.random();};break;};self.Save(ObjV);};ajax.send("");}else{parent.Mo.Alert("warning","文件上传","无法对目标文进行切割！<br />当前文件类型为：."+_Ext+"<br />可以切割的文件类型有：<span class='key'>.jpeg .jpg .gif .png .bmp</span>");self.Save(ObjV);}};this.ImageCorpWindow__=function(ObjV){var imgCorp=Mo.Manager.corpImageWindow;var window='window';if(this.config.Width&&this.config.Height&&this.config.ImageSize){var _sz=this.config.ImageSize.split("*");var _url=imgCorp+'?img='+this.config.Value+'&amp;window='+window+'&amp;img_width='+_sz[0]+'&amp;img_height='+_sz[1]+'&amp;corp_width='+this.config.Width+'&amp;corp_height='+this.config.Height;parent.Mo.Window('window','处理图片',_url,(parseInt(_sz[0])),(parseInt(_sz[1])+30),false);}};if(!mode){if(value){this.Save();}else{this.Select();}}} ;Mo.plugin.push("upload");
