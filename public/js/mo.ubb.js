 if(typeof Mo!='function'){var Mo={plugin:[]}}
 Mo.UBB={version:"2.0",drag:null,config:null,get:function(id){return document.getElementById(id);},bind:function(self,id){Mo(document).bind('click',function(ele,index,event){var ele=Mo.Event(event).element();if(self!=ele){Mo("#"+id).hide();}});var pos=Mo(self).position();Mo("#"+id).style({"position":"absolute","left":pos.left+"px","top":pos.top+"px"}).show();},show:function(config){var obj=config["id"];var cfg=config["toolbar"]?config["toolbar"]:[];this.config=cfg;var html='<div class="ubb">';var array=["字体","font","fontface","大小","size","format","颜色","color","color"];for(var i=0;i<array.length;i+=3){if(<PERSON><PERSON>Array(cfg).indexOf(array[i+1])>-1||cfg.length==0){html+='<img src="'+Mo.store.root+'image/spacer.gif" alt="'+array[i]+'" class="ubb-img img-'+array[i+1]+'" unselectable="on" onclick="Mo.UBB.bind(this,\''+array[i+2]+'\');" />';}}
html+='<div id=fontface class="ubb-menu" style="display:none;"><ul>';var array=["Arial","Arial Black","Impact","Verdana","宋体","黑体","楷体_GB2312","幼圆","Microsoft YaHei"];for(var i=0;i<array.length;i++){html+='<li onclick="Mo.UBB.face(\''+obj+'\',\''+array[i]+'\');" style="font-family:'+array[i]+'" unselectable="on" onfocus="this.blur();">'+array[i]+'</li>';};html+='</ul></div>';html+='<div id=format class="ubb-menu" style="display:none;"><ul>';for(var i=1;i<=6;i++){html+='<li onclick="Mo.UBB.size(\''+obj+'\',\''+i+'\');" unselectable="on" onfocus="this.blur();"><font size="'+i+'" unselectable="on">'+i+'</font></li>';};html+='</ul></div>';html+='<div id=color class="ubb-menu ubb-color" style="display:none;"><ul>';var array=["黑色","black","灰色","gray","茶色","maroon","红色","red","紫色","purple","紫红","fuchsia","绿色","green","亮绿","lime","橄榄","olive","黄色","yellow","深蓝","teal","蓝色","blue","浅绿","aqua","粉红","pink","橙色","orange","褐色","brown"];for(var i=0;i<array.length;i+=2){html+='<li onclick="Mo.UBB.color(\''+obj+'\',\''+array[i+1]+'\');" style="color:'+array[i+1]+'" unselectable="on">'+array[i]+'</li>';};html+='</ul></div>';html+='<div id="smile" class="ubb-menu ubb-smile" style="display:none;"><ul>';var array=["0","1","2","3","4","5","6","7","8","9"];for(var i=0;i<array.length;i++){html+='<li onclick="Mo.UBB.smile(\''+obj+'\',\''+array[i]+'\');" unselectable="on"><img src="'+Mo.store.root+'image/smile/'+array[i]+'.png" alt="'+array[i]+'" unselectable="on" /></li>';};html+='</ul></div>';html+='<div id="code" class="ubb-menu" style="display:none;"><ul>';var array=["JavaScript","js","XML","xml","VB","vb","SQL","sql","Java","java","CSS","css","PHP","php"];for(var i=0;i<array.length;i+=2){html+='<li onclick="Mo.UBB.code(\''+obj+'\',\''+array[i+1]+'\');" unselectable="on">'+array[i]+'</li>';};html+='</ul></div>';var array=["加粗","bold","斜体","italic","下划线","under","左对齐","left","居中","center","右对齐","right"];for(var i=0;i<array.length;i+=2){if(Mo.Array(cfg).indexOf(array[i+1])>-1||cfg.length==0){html+='<img src="'+Mo.store.root+'image/spacer.gif" alt="'+array[i]+'" class="ubb-img img-'+array[i+1]+'" onclick="Mo.UBB.'+array[i+1]+'(\''+obj+'\')" unselectable="on" />';}}
var array=["表情","smile","smile","代码","code","code"];for(var i=0;i<array.length;i+=3){if(Mo.Array(cfg).indexOf(array[i+1])>-1||cfg.length==0){html+='<img src="'+Mo.store.root+'image/spacer.gif" alt="'+array[i]+'" class="ubb-img img-'+array[i+1]+'" unselectable="on" onclick="Mo.UBB.bind(this,\''+array[i+2]+'\');" />';}}
var array=["链接","link","图片","image","FLASH","flash","视频","video","音乐","mp3","引用","quote","仅会员浏览","hidden","转换复制的HTML","html"];for(var i=0;i<array.length;i+=2){if(Mo.Array(cfg).indexOf(array[i+1])>-1||cfg.length==0){html+='<img src="'+Mo.store.root+'image/spacer.gif" alt="'+array[i]+'" class="ubb-img img-'+array[i+1]+'"  onclick="Mo.UBB.'+array[i+1]+'(\''+obj+'\')" unselectable="on" />';}}
var array=["最佳尺寸/原始尺寸","zoom","放大输入框","zoomin","缩小输入框","zoomout","关于","about"];for(var i=0;i<array.length;i+=2){if(Mo.Array(cfg).indexOf(array[i+1])>-1||cfg.length==0){html+='<img src="'+Mo.store.root+'image/spacer.gif" alt="'+array[i]+'" class="ubb-img img-'+array[i+1]+'"  onclick="Mo.UBB.'+array[i+1]+'(\''+obj+'\')" unselectable="on" />';}}
if(Mo.Array(cfg).indexOf("stat")>-1||cfg.length==0){html+='<span id="'+obj+'_stat" class="ubb-stat"></span>';Mo.reader(function(){Mo("#"+obj).bind('keyup',function(){Mo("#"+obj+'_stat').html("字数:"+Mo.String(this.value).long());});});};html+='</div>';Mo.write(html);},face:function(obj,sel){var s="[face="+sel+"]";var e="[/face]";Mo.UBB.insert(obj,s,e);},size:function(obj,sel){var s="[size="+sel+"]";var e="[/size]";Mo.UBB.insert(obj,s,e);},code:function(obj,sel){var s="[code]";var e="[/code]";Mo.UBB.insert(obj,s,e);},color:function(obj,sel){var s="[color="+sel+"]";var e="[/color]";Mo.UBB.insert(obj,s,e);},smile:function(obj,sel){var s="[smile]"+sel;var e="[/smile]";Mo.UBB.insert(obj,s,e,false);},code:function(obj,sel){var s="[code="+sel+"]\n";var e="\n[/code]";Mo.UBB.insert(obj,s,e);},bold:function(obj){var s="[b]";var e="[/b]";Mo.UBB.insert(obj,s,e);},italic:function(obj){var s="[i]";var e="[/i]";Mo.UBB.insert(obj,s,e);},under:function(obj){var s="[u]";var e="[/u]";Mo.UBB.insert(obj,s,e);},left:function(obj){var s="[align=left]";var e="[/align]";Mo.UBB.insert(obj,s,e);},center:function(obj){var s="[align=center]";var e="[/align]";Mo.UBB.insert(obj,s,e);},right:function(obj){var s="[align=right]";var e="[/align]";Mo.UBB.insert(obj,s,e);},link:function(obj){var t=prompt("请输入链接要显示的文字,只能包含中文,英文字母,或中英文混合","请点击这里");if(!t)return;var url=prompt("请输入URL地址","http://");if(!url)return;Mo.UBB.get(obj).value+=(!t)?"[url]"+url+"[\/url]":"[url="+url+"]"+t+"[\/url]";Mo.UBB.get(obj).focus();},flash:function(obj){var a=prompt("请输入Flash的URL地址","http://");if(!a)return;if(!/^http/.test(a)){alert("URL地址格式不对");return;};var b=prompt("请输入Flash高度和宽度","350,200");var c="[flash="+b+"]"+a+"[\/flash]";Mo.UBB.get(obj).value+=c;Mo.UBB.get(obj).focus();},mp3:function(obj){var a=prompt("请输入音频文件的URL地址","http://");if(!a)return;if(!/^http/.test(a)){alert("URL地址格式不对");return;};var b=prompt("请输入音频文件播放器高度和宽度","220,40");var c="[mp3="+b+"]"+a+"[\/mp3]";Mo.UBB.get(obj).value+=c;Mo.UBB.get(obj).focus();},hidden:function(obj){var s="[hidden]";var e="[/hidden]";Mo.UBB.insert(obj,s,e);},image:function(obj){var a=prompt("请输入图片的URL地址","http://");if(!a)return;if(!/^http/.test(a)){alert("URL地址格式不对");return;};var c="[img]"+a+"[\/img]";Mo.UBB.get(obj).value+=c;Mo.UBB.get(obj).focus();},video:function(obj){var autostart="true";var a=prompt("请输入视频文件地址","");if(a==null||a==""||a=='')return;var b=prompt("请输入视频文件显示大小","400,250");if(b==null||b==""||b==''){b="400,250";};var c=prompt("请输入是否自动播放,默认为自动播放(yes自动，no不自动播放）","yes");if(c!="yes"){autostart="false";};var strvideo="[embed="+b+","+autostart+"]"+a+"[/embed]";Mo.UBB.get(obj).value+=strvideo;Mo.UBB.get(obj).focus();},quote:function(obj){var s="[quote]";var e="[/quote]";Mo.UBB.insert(obj,s,e);},zoom:function(obj){var o=Mo("#"+obj).item(0);if(o.scrollHeight>o.offsetHeight){o.style.height=o.scrollHeight+"px";}else{o.style.height="auto";}},zoomin:function(obj){var o=Mo("#"+obj).item(0);o.rows+=5;},zoomout:function(obj){var o=Mo("#"+obj).item(0);if(o.rows>=10){o.rows-=5;}},about:function(obj){alert('VeryIDE UBBeditor '+Mo.UBB.version+' \n\n');},html:function(obj){var ifr=obj+"_iframe";if(!Mo.UBB.get(ifr)){var box=document.createElement('iframe');box.id=ifr;box.name=ifr;box.style.width="0px";box.style.height="0px";box.style.border="0";document.body.appendChild(box);var box=window.frames[ifr].document;box.designMode="On";box.open();box.write("<!DOCTYPE html PUBLIC \"-//W3C//DTD XHTML 1.0 Transitional//EN\" \"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd\">\
				<html xmlns=\"http://www.w3.org/1999/xhtml\">\
				<head>\
				<meta http-equiv=\"Content-Type\" content=\"text/html; charset=UTF-8\" />\
				<title>测试</title>\
				</head>\
				<body >\</body>\
				</html>");box.close();};var box=window.frames[ifr].document;box.execCommand("SelectAll",false,null);box.execCommand("Delete",false,null);try{box.execCommand("paste",false,null);}catch(e){alert("Sorry!\n\n当前浏览器暂不支持粘贴操作");return false;};var str=box.body.innerHTML;if(str){str=str.replace(/\r/g,"");str=str.replace(/on(error|load|unload|resize|blur|change|click|dblclick|focus|keydown|keypress|keyup|mousewheel|mousemove|mousedown|mouseout|mouseover|mouseup|select)="[^"]+"/ig,"");str=str.replace(/<script[^>]*?>([\w\W]*?)<\/script>/ig,"");str=str.replace(/<a[^>]+href="([^"]+)"[^>]*>(.*?)<\/a>/ig,"\n[url=$1]$2[/url]\n");str=str.replace(/<font[^>]+color=([^ >]+)[^>]*>(.*?)<\/font>/ig,"\n[color=$1]$2[/color]\n");str=str.replace(/<img[^>]+src="([^"]+)"[^>]*>/ig,"\n[img]$1[/img]\n");str=str.replace(/<([\/]?)b>/ig,"[$1b]");str=str.replace(/<([\/]?)strong>/ig,"[$1b]");str=str.replace(/<([\/]?)u>/ig,"[$1u]");str=str.replace(/<([\/]?)i>/ig,"[$1i]");str=str.replace(/&nbsp;/g," ");str=str.replace(/&amp;/g,"&");str=str.replace(/&quot;/g,"\"");str=str.replace(/&lt;/g,"<");str=str.replace(/&gt;/g,">");str=str.replace(/<br>/ig,"\n");str=str.replace(/<[^>]*?>/g,"");str=str.replace(/\[url=([^\]]+)\]\n(\[img\]\1\[\/img\])\n\[\/url\]/g,"$2");str=str.replace(/\n+/g,"\n");Mo.UBB.get(obj).value+=str;}else{alert('无需转换的HTML内容');}},getSel:function(){return window.getSelection?window.getSelection():document.selection;},getRng:function(){var sel=this.getSel(),rng;try{rng=sel.rangeCount>0?sel.getRangeAt(0):(sel.createRange?sel.createRange():document.createRange());}catch(ex){};if(!rng)rng=document.all?document.body.createTextRange():document.createRange();return rng;}, insert:function(obj,s,e,f){var obj=Mo("#"+obj).item(0);function strlen(str){return(document.all&&str.indexOf('\n')!=-1)?str.replace(/\r?\n/g,'_').length:str.length;};function checkFocus(obj){if(!obj.hasfocus){obj.focus();}};function isUndefined(variable){return typeof variable=='undefined'?true:false;}
      obj.focus();if(document.selection){var bm=document.selection.createRange().getBookmark();var sel=obj.createTextRange();sel.moveToBookmark(bm);var sleft=obj.createTextRange();sleft.collapse(true);sleft.setEndPoint("EndToStart",sel);obj.selectionStart=strlen(sleft.text);obj.selectionEnd=sleft.text.length+sel.text.length;obj.selectedText=sel.text; var oStr=document.selection.createRange();var leng=strlen(oStr.text);oStr.text=s+oStr.text+e; var start=obj.selectionStart+strlen(s);var end=leng; }else if(window.getSelection&&obj.selectionStart>-1){var st=obj.selectionStart;var ed=obj.selectionEnd;obj.value=obj.value.substring(0,st)+s+obj.value.substring(st,ed)+e+obj.value.slice(ed);var start=st+strlen(s);var end=ed+strlen(s);}else{obj.value+=s+e;obj.focus();}
if(f===false)return;if(obj.createTextRange){var long=obj.value.length;var range=obj.createTextRange();range.moveStart("character",-long);range.moveEnd("character",-long);range.moveStart("character",start);range.moveEnd("character",end);range.select();}else{obj.setSelectionRange(start,end);obj.focus();}
  }}
 Mo.plugin.push("ubb");
