 if(typeof Mo!='function'){var Mo={plugin:[]}}
 Mo.Batch=function(input,value,config,mode){Mo.write('<textarea name="'+input+'" id="'+input+'" rows="10" style="display:none;">'+value+'</textarea>');var format=config["format"]?"*."+config["format"].join(";*.")+";":"";var group='';var i=0;for(var g in config["group"]){group+='group['+i+']='+config["group"][g]+';';i++;}
var thumb=config["thumb"]?config["thumb"]:'';var remote=config["remote"]?config["remote"]:'';var session=config["session"]?config["session"]:'';var watermark=config["watermark"]?config["watermark"]:'';var position=config["position"]?config["position"]:'0';var multiple=config["multiple"]?config["multiple"]:'0';Mo.write('<embed id="'+input+'_flash" name="'+input+'_flash" src="'+Mo.root+'/resources/upload/select.swf?input=file&source='+input+'&amp;format='+format+'&amp;args=model=flash;session='+session+';watermark='+watermark+';position='+position+';multiple='+multiple+';remote='+remote+';thumb='+thumb+';'+group+'&amp;limit='+config["limit"]+'&amp;size='+config["size"]+'&amp;every='+config["every"]+'&amp;finish='+config["finish"]+'&amp;save='+Mo.root+'veryide.upload.php" width="430" height="25"></embed>');};Mo.Batch.Delete=function(input,file){if(confirm("您确认从服务器删除 "+file+" ？\n\n删除的文件将无法恢复！")){var XmlURL=Mo.Manager.action;var ajax=new Mo.Ajax(XmlURL);ajax.method="GET";ajax.setVar({"action":"file-delete","&file":file,"&input":"file","&rnd":Math.random()});ajax.onError=function(){alert(ajax.response)};ajax.onCompletion=function(){var response=ajax.responseXML;var result=response.getElementsByTagName('result')[0].firstChild.data;var error=response.getElementsByTagName('error')[0].firstChild.data;switch(error){case"file":case"not":$(input).value=$(input).value.replace('||'+file,"");$(input).value=$(input).value.replace(file,"");remove($(file));break;case"login":parent.Mo.Alert("warning","文件上传",parent.Mo.loginBox);break;case"config":parent.Mo.Alert("warning","文件上传","您的操作已经取消！<br />您当前没有权限删除文件！");break;}};ajax.send("");}}
 Mo.Batch.onUpload=function(file,current,input){if(file.indexOf(".")==-1){alert("上传出错，可能是文件过小");return;};var obj=document.createElement("li");obj.innerHTML='<a href="'+file+'" target="_blank"><img src="'+file+'" /></a>';$(input+"-album").appendChild(obj);$(input).value+=($(input).value?'||'+file:file);}
 Mo.Batch.onUploadEnd=function(succee,totality,input){return"已上传 "+succee+" 个，共 "+totality+" 个文件"}
  if(typeof Serv!='object'){var Serv={}}
 Serv.Upload=function(input,value,config,mode){var input=input;var value=value;var extra=value.replace(/.*\./,"").toLowerCase();var image={};var config=config;var debug=false; if(Serv.Upload.Data[input]||Mo("#"+input).size()){Mo.Dialog("mo-dialog","文件上传","<p><span class='key'>已经存在 Serv.Upload 实例: </span> </p> <p> <strong>"+input+" </strong></p>",390,0,{"remove":true,"draged":true,"locked":true,"create":true});return false;}else{Serv.Upload.Data[input]={"value":value,"config":config,"image":image,"extra":extra,"mode":mode};}
switch(mode){case"extra":return'<span id="'+input+'_span"></span>';break;default:Mo.write('<span id="'+input+'_span"></span>');break;}
if(!mode){if(value){Serv.Upload.Save(input);}else{Serv.Upload.Select(input);}}}
 Serv.Upload.Data=new Object();Serv.Upload.Debug=false;Serv.Upload.Picture=['jpg','jpeg','gif','png','bmp']; Serv.Upload.Save=function(input){var size=Serv.Upload.Data[input]["size"];var image=Serv.Upload.Data[input]["image"];var config=Serv.Upload.Data[input]["config"];var extra=Serv.Upload.Data[input]["extra"];var value=Serv.Upload.Data[input]["value"];Mo("#"+input+"_box").remove();var icon=Mo.folder.icons+'attach.png';if(Mo.Array(Serv.Manager.files).indexOf(extra)>-1){icon=Mo.folder.image+"format/"+extra+".gif";}
var _html='';_html+='<input type="hidden" name="'+input+'" id="'+input+'" value="'+value+'" />'+'<img src="'+icon+'" align="absmiddle" /> <a href="'+value+'" target="_blank">'+value+'</a>  -  ';if(image||size){var str='';if(image["width"]&&image["height"]){str+='图片尺寸:<br />'+image["width"]+'px *'+image["height"]+'px';};if(size){str+='<br />文件大小:<br />'+Math.round(parseFloat(size)/1024)+' KB';};if(str){_html+=' <span class="upload" title="查看详细文件信息" onclick="window.parent.Mo.Alert(&quot;warning&quot;,&quot;文件详细信息&quot;,&quot;'+str+'&quot;);")"><img src="'+Mo.folder.icons+'document.png" align="absmiddle" /> ';};if(size){_html+=' - '+Math.round(parseFloat(size)/1024)+' KB - ';};_html+=' </span>';}
if(config.again){_html+='<span onclick="javascript:Serv.Upload.New(\''+input+'\');" class="upload"  title="添加新文件"><img src="'+Mo.folder.icons+'plus.png" align="absmiddle" /> 新文件</span> - ';};_html+='<span onclick="javascript:Serv.Upload.Delete(\''+input+'\');" class="upload" title="删除这个文件"><img src="'+Mo.folder.icons+'block.png" align="absmiddle" /> 删除</span><img src="'+Mo.folder.image+'spacer.gif" height="20" width="1" />';if(image){_html+=' <span class="upload" title="处理图片" onclick="javascript:Serv.Upload.Editor(\''+input+'\')"><img src="'+Mo.folder.icons+'photo.png" align="absmiddle" /> 处理</span>';};Mo("#"+input+"_span").html(_html);if(config.ubb){if(Mo.Array(Serv.Upload.Picture).indexOf(extra)==-1){config.ubb.value+='[URL]'+this.value+'[/URL]';}else{config.ubb.value+='[IMG]'+this.value+'[/IMG]';}}
config.callback&&config.callback(Serv.Upload.Data[input]);};Serv.Upload.Editor=function(input){var value=Serv.Upload.Data[input]["value"];parent.window.__PE__=parent.Serv.Dialog('piceditor','图片','<iframe src="resources/piceditor/?picurl='+value+'" scrolling="no" width="680" height="530"  frameborder="0"></iframe>',695,570,{"minimize":true,"remove":true});parent.window.__PE__.Create();};Serv.Upload.Select=function(input){var config=Serv.Upload.Data[input]["config"];var _html="";if(config.input){_html='<input type="text" class="text" name="'+input+'" id="'+input+'" value="" size="35" '+config.attr+' onchange="javascript:Serv.Upload.Change(\''+input+'\',this);" /> ';}else{_html='<input type="hidden" name="'+input+'" id="'+input+'" value="" />';};_html+='<span class="upload" id="'+input+'_btn" title="浏览并选择本地文件"><img src="'+Mo.folder.icons+'plus.png" alt="" align="absmiddle" /> 浏览文件</span>';if(config["width"]&&config["width"]){_html+=' - <span class="upload" onclick="Mo.Dialog(&quot;mo-dialog&quot;,&quot;文件上传&quot;,&quot;<p>上传图片最佳尺寸:</p><p>'+config["width"]+'px * '+config["height"]+'px </p>&quot;, 390, 0, { &quot;remove&quot; : true, &quot;locked&quot; : true, &quot;create&quot; : true } )"><img src="'+Mo.folder.icons+'wand.png" align="absmiddle" /> '+config["width"]+'px * '+config["height"]+'px </span>';}
if(config.recovery){_html+=' - <span onclick="javascript:Serv.Upload.Back(\''+input+'\');" class="upload" title="使用最近一次上传的文件"><img src="'+Mo.folder.icons+'clock.png" align="absmiddle" /> 最近上传</span>';};_html+='<iframe width="100%" height="100" frameborder="1" name="'+input+'_iframe" id="'+input+'_iframe" src="about:blank" style="display:'+(Serv.Upload.Debug?'block':'none')+';"></iframe>';if(config.format&&config.help!==false){_html+=' - <span class="upload" title="查看详细上传支持格式" onclick="javascript:Mo.Dialog(&quot;mo-dialog&quot;,&quot;文件上传&quot;,&quot;<p>支持上传如下格式:</p><p>'+config.format.join(", ")+'</p>&quot;, 390, 0, { &quot;remove&quot; : true, &quot;locked&quot; : true, &quot;create&quot; : true } )"><img src="'+Mo.folder.icons+'bubble.png" align="absmiddle" /> 支持格式</span>';};Mo("#"+input+"_span").html(_html);Mo("#"+input+"_btn").bind('click',function(){Serv.Upload.Show(input);});if(config.callback){}}
 Serv.Upload.Change=function(input,o){var config=Serv.Upload.Data[input]["config"];config.callback&&config.callback({"value":o.value,"size":"","image":[],"extra":""});};Serv.Upload.Hide=function(input){Mo("#"+input+"_box").hide();}
 Serv.Upload.Show=function(input){var config=Serv.Upload.Data[input]["config"];if(Mo("#"+input+"_box").size()){Mo("#"+input+"_box").show();return;};var div=document.createElement("div");with(div){id=input+"_box";name=input+"_box";className="browse";};var html='';html+='<form id="'+input+'_form" name="'+input+'_form" target="'+input+'_iframe" method="post" enctype="multipart/form-data" action=""><em><img onclick="javascript:Serv.Upload.Hide(\''+input+'\');" src="'+Mo.folder.image+'layout/cross.png" align="absmiddle" /></em>请选择要上传的文件：<br />';html+='<input name="file" id="'+input+'_file" type="file" value="" style="width:210px;" />';html+='<input name="input" type="hidden" value="'+input+'" />';html+='<input name="format" type="hidden" value="'+(config.format?config.format.join(","):'')+'" />';html+='<input name="NewName" type="hidden" value="'+(config.newname?config.newname:'')+'" />';html+='<input name="remote" type="hidden" value="'+(config.remote?config.remote:'')+'" />';html+='<input name="thumb" type="hidden" value="'+(config.thumb?config.thumb:'0')+'" />';html+='<input name="watermark" type="hidden" value="'+(config.watermark?config.watermark:'')+'" />';html+='<input name="position" type="hidden" value="'+(config.position?config.position:'0')+'" />';html+='<input name="multiple" type="hidden" value="'+(config.multiple?config.multiple:'')+'" />';html+='<input name="absolute" type="hidden" value="'+(config.absolute?config.absolute:'')+'" />';for(var g in config.group){html+='<input name="group[]" type="hidden" value="'+config.group[g]+'" />';};html+='</form>';div.innerHTML=html;document.body.appendChild(div);var doc=Mo.document;div.style.left=((doc.clientWidth-div.offsetWidth)/2)+"px";div.style.top=doc.scrollTop+((doc.clientHeight-div.offsetHeight)/2)+"px";Mo("#"+input+"_file").bind('change',function(){Serv.Upload.Check(input,this);});if(Mo.Array(Mo.plugin).indexOf("drag")==-1)return false;var dg=new Mo.Drag(div,null,10,doc.clientWidth-div.offsetWidth-10,10);dg.onStart=function(x,y){div.style.filter="alpha(opacity=60)";div.style.opacity=0.6;};dg.onEnd=function(x,y){div.style.filter="alpha(opacity=100)";div.style.opacity=1;}}
 Serv.Upload.Check=function(input,file){var config=Serv.Upload.Data[input]["config"];var value=file.value;var extra=value.replace(/.*\./,"").toLowerCase();if(value&&extra){if(config.format&&Mo.Array(config.format).indexOf(extra)==-1){Mo.Dialog("mo-dialog","文件上传","<p>请选择正确的文件类型！</p> <p>支持的文件类型："+config.format.join(",")+"</p> <p>当前文件类型为："+extra+"</p>",390,0,{"remove":true,"draged":true,"locked":true,"create":true});Serv.Upload.Locker(input,false);Mo("#"+input+"_form").reset();}else{Mo("#"+input+"_form").attr({"action":Mo.store.root+"upload.php"});Mo("#"+input+"_form").submit();Mo("#"+input+"_form").html('<img src="'+Mo.store.root+'image/loading.gif" /> 文件上传中，请稍后...');}}};Serv.Upload.Locker=function(input,lock){var obj=Mo("#"+input).item(0);obj.readonly=lock;while(obj=obj.parentElement){if(obj.tagName=="FORM"){var len=obj.elements.length;for(i=0;i<len;i++){var inpElm=obj.elements[i];var inpType=inpElm.getAttribute("type");if(inpType=="submit"){if(inpElm.value!="请稍后.."){inpElm.defaultValue=inpElm.value;};if(lock){inpElm.value="请稍后..";}else{inpElm.value=inpElm.defaultValue;};inpElm.disabled=lock;break;}};break;}}};Serv.Upload.New=function(input){if(confirm("您确认从新上传一个文件吗？\n\n现有的记录将会丢失！")){Serv.Upload.Change(input,{"value":""});Serv.Upload.Select(input);}};Serv.Upload.Delete=function(input){var value=Serv.Upload.Data[input]["value"];var config=Serv.Upload.Data[input]["config"];if(confirm("您确认从服务器删除 "+value+" ？\n\n删除的文件将无法恢复！")){var ajax=new Mo.Ajax(Mo.store.root+"ajax.php");ajax.method="GET";ajax.setVar({"action":"attach","&execute":"delete","&file":value,"&input":input,"&new":config.CopyCorp,"&rnd":Math.random()});ajax.onError=function(){alert(ajax.response)};ajax.onCompletion=function(){var result=ajax.response;switch(result){case"complete":Mo("#"+input).value("");Serv.Upload.Change(input,{"value":""});Serv.Upload.Select(input);break;case"login":Mo.Dialog("mo-dialog","文件上传","<p>您已经不在登录状态！</p>",390,0,{"remove":true,"draged":true,"locked":true,"create":true});break;case"config":Mo.Dialog("mo-dialog","文件上传","<p>您的操作已经取消！<br />您当前没有权限删除文件！</p>",390,0,{"remove":true,"draged":true,"locked":true,"create":true});break;case"file":Mo.Dialog("mo-dialog","文件上传","<p>未找到目标文件！<br />文件是否已经通过其它方式被删除了？</p>",390,0,{"remove":true,"draged":true,"locked":true,"create":true});break;}};ajax.send("");}};Serv.Upload.Back=function(input){var config=Serv.Upload.Data[input]["config"];if(config.recovery){var ajax=new Mo.Ajax(Mo.store.root+"ajax.php");ajax.method="GET";ajax.setVar({"action":"attach","&execute":"lately","&input":input,"&rnd":Math.random()});ajax.onError=function(){alert(ajax.response)};ajax.onCompletion=function(){var response=ajax.responseXML;var result=response.getElementsByTagName('result')[0].firstChild.data;var error=response.getElementsByTagName('error')[0].firstChild.data;switch(error){case"complete":Serv.Upload.Data[input]["value"]=response.getElementsByTagName('name')[0].firstChild.data;Serv.Upload.Data[input]["size"]=response.getElementsByTagName('size')[0].firstChild.data;Serv.Upload.Data[input]["extra"]=response.getElementsByTagName('type')[0].firstChild.data;Serv.Upload.Data[input]["image"]={"width":response.getElementsByTagName('width')[0].firstChild.data,"height":response.getElementsByTagName('height')[0].firstChild.data};Serv.Upload.Save(input);break;case"login":Mo.Dialog("mo-dialog","文件上传","<p>您已经不在登录状态！</p>",390,0,{"remove":true,"draged":true,"create":true});break;case"config":Mo.Dialog("mo-dialog","文件上传","<p>您的操作已经取消！<br />您当前没有权限返回文件！</p>",390,0,{"remove":true,"draged":true,"create":true});break;case"file":Mo.Dialog("mo-dialog","文件上传","<p>未找到文件<br />在文件数据库中未找到相匹配的文件！</p>",390,0,{"remove":true,"draged":true,"create":true});break;}};ajax.send("");}}
 Serv.Upload.Ok=function(input,value,size,image){Serv.Upload.Data[input]["value"]=value;Serv.Upload.Data[input]["size"]=size;Serv.Upload.Data[input]["image"]=image;Serv.Upload.Data[input]["extra"]=value.replace(/.*\./,"").toLowerCase();Serv.Upload.Save(input);Serv.Upload.Locker(input,false);}
 Serv.Upload.Error=function(input,s,i){var array=[];array[0]='文件上传被禁止!<br />您当前不在登录状态!';array[1]='文件上传被禁止!<br />系统已禁用文件上传!';array[2]='文件上传被取消!<br />您当前没有上传文件的权限!';array[3]='文件上传被取消!<br />没有收到任何数据!';array[4]='上传失败！<br />您上传的文件超出服务器的限制,最大'+i;array[5]='上传文件的大小超过了 HTML 表单中 MAX_FILE_SIZE 选项指定的值。';array[6]='上传失败！<br />您的文件只有部分被上传';array[7]='上传失败！<br />没有文件被上传';array[8]='该文件类型（'+i+'）不允许上传!';array[9]='上传失败！<br />请确认FTP服务器是否有写权限或者远程目录是否存在！';array[10]='不存在文件存放目录：'+i+'<br />需要手动创建';Mo.Dialog("mo-dialog","文件上传","<p>"+array[s]+"</p>",390,0,{"remove":true,"draged":true,"locked":true,"create":true});Serv.Upload.Locker(input,false);Mo("#"+input+"_box").remove();Serv.Upload.Show(input);}
 Mo.plugin.push("upload");
