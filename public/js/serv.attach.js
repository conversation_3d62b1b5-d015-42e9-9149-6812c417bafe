 if(typeof Serv!='object'){var Serv={}}
 Serv.Attach=function(queues,locale,remote,input,mime,option){this.$=function(obj){return document.getElementById(obj);}
this.queues=queues;this.locale=locale;this.remote=remote;this.input=input;this.inti=function(){document.write('<div id="'+this.queues+'-list" '+(option["class"]?'class="'+option["class"]+'"':'')+' class=="attach" ><p><span></span><input type="file" name="'+this.locale+'[]" onchange="new Serv.Attach(\''+this.queues+'\',\''+this.locale+'\',\''+this.remote+'\',\''+this.input+'\',\''+this.mime+'\').add(this);" /></p></div>');}
this.timestamp=function(){var timestamp=Date.parse(new Date());return timestamp/1000;}
this.add=function(file){var box=file.parentNode.firstChild;var id=this.timestamp();var name=file.value.substr(file.value.lastIndexOf('\\')+1);box.innerHTML='<a href="javascript:void(0);" onclick="new Serv.Attach(\''+this.queues+'\',\''+this.locale+'\',\''+this.remote+'\',\''+this.input+'\',\''+this.mime+'\').remove(this,\''+id+'\',\'locale\');">[删除]</a> - <a href="javascript:void(0);" onclick="new Serv.Attach(\''+this.queues+'\',\''+this.locale+'\',\''+this.remote+'\',\''+this.input+'\',\''+this.mime+'\').insert(this,\''+id+'\',\'locale\');">[插入]</a> - <span>'+name+'</span><input type="hidden" name="'+this.queues+'[]" value="'+id+'" />';file.setAttribute('style','display:none;');file.style.display='none';var p=document.createElement('p');p.innerHTML='<span></span><input type="file" name="'+this.locale+'[]" onchange="new Serv.Attach(\''+this.queues+'\',\''+this.locale+'\',\''+this.remote+'\',\''+this.input+'\',\''+this.mime+'\').add(this);"/>';this.$(this.queues+'-list').appendChild(p);}
this.insert=function(file,id,type){var obj=this.$(this.input);var val='['+type+']'+id+'[/'+type+']';selection=document.selection;if(!obj.hasfocus){obj.focus();}
if(typeof obj.selectionStart!='undefined'){obj.value=obj.value.substr(0,obj.selectionStart)+val+obj.value.substr(obj.selectionEnd);obj.select();}else if(selection&&selection.createRange){var sel=selection.createRange();sel.text=val;}else{obj.value+=val;};obj.focus();}
this.remove=function(file,id,type){this.$(this.queues+'-list').removeChild(file.parentNode.parentNode);this.$(this.input).value=this.$(this.input).value.replace('['+type+']'+id+'[/'+type+']','');}
this.push=function(id,name,path,size){var p=document.createElement('p');p.innerHTML='<span><a href="javascript:void(0);" onclick="new Serv.Attach(\''+this.queues+'\',\''+this.locale+'\',\''+this.remote+'\',\''+this.input+'\',\''+this.mime+'\').remove(this,\''+id+'\',\'attach\');">[删除]</a> - <a href="javascript:void(0);" onclick="new Serv.Attach(\''+this.queues+'\',\''+this.locale+'\',\''+this.remote+'\',\''+this.input+'\',\''+this.mime+'\').insert(this,\''+id+'\',\'attach\');">[插入]</a> - <span onmouseover="new Serv.Attach(\''+this.queues+'\').preview(this, \''+encodeURIComponent(path)+'\');" onmouseout="new Serv.Attach(\''+this.queues+'\').hidden(this, \''+encodeURIComponent(path)+'\');">'+name+'</span></span><input type="hidden" name="'+this.remote+'[]" value="'+id+'" />';this.$(this.queues+'-list').insertBefore(p,this.$(this.queues+'-list').firstChild);}
this.preview=function(obj,path){var pos=Mo(obj).position();var div=this.$(path);if(!div){var ext=obj.innerHTML.replace(/.*\./,"").toLowerCase();if(inArray(['gif','jpg','png','jpeg','bmp','ico'],ext)){var div=document.createElement('div');div.id=path;div.className="preview";div.innerHTML='<img src="'+decodeURIComponent(path)+'" style="max-width:100px;width: expression(this.width > 100 ? \'100px\' : true);"  />';div.style.left=pos.left+pos.width+"px";div.style.top=pos.top+"px";div.style.position="absolute";this.$(this.queues+'-list').appendChild(div);}}else{div.style.display='';}}
this.hidden=function(obj,path){var div=this.$(path);if(div){this.$(path).style.display='none';}}};
