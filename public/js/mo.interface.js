 if(typeof Mo!='function'){var Mo={plugin:[]}}
 Mo.Sort=function(url,tag,key,cur,ord){var old=(ord=="desc"?"asc":"desc");var str='<div class="'+(key==cur?ord:'')+'"><a href="'+url.replace('{ord}',old).replace('{key}',key)+'">'+tag+'</a></div>';Mo.write(str);}
function filterSelect(obj,pid,name){var old=$(obj+"_old");if(!old){old=$(obj).cloneNode(true);old.id=obj+"_old";old.style.display="none";document.body.appendChild(old);};var New=$(obj);if(name){New.length=1;}else{New.length=0;};var len=old.length;for(var i=0;i<len;i++){if(pid==old[i].getAttribute("pid")){New[New.length]=new Option(old[i].text,old[i].value);}};New.style.display="";}
 Mo.Status=function(parent,id,baseUrl,array,value,mx,my){if(!$(id)){mx=mx?mx:0;my=my?my:0;var obj=document.createElement("ul");obj.id=id;obj.className="select";var html='';for(var key in array){if(key!=value){html+="<li><a href='"+baseUrl+key+'&amp;jump='+encodeURIComponent(location.href)+"' title='"+array[key]+"'>○ "+array[key]+"</a></li>";}else{html+="<li>● "+array[key]+"</li>";parent.innerHTML=array[key];}}
 obj.innerHTML=html;parent.appendChild(obj);(function(){var o=obj;var p=parent;p.onmouseover=function(){var pos=new getPosition(p);var x=pos.left;var y=pos.top;with(o.style){left=x+mx+"px",top=y+my+"px",position="absolute",display="block"}};p.onmouseout=function(event){o.style.display="none";}})();}}
 function stopBubble(e){if(e&&e.stopPropagation)e.stopPropagation();else
window.event.cancelBubble=true;}
function stopDefault(e){if(e&&e.preventDefault)e.preventDefault();else
window.event.returnValue=false;return false;}
   Mo.Thead=function(thead){var thead=getObject(thead);var pos=getPosition(thead);var doc=Mo.getDocument();var top=0;if(pos.attr["top"]){top=pos.attr["top"].value;}else{top=pos.top;thead.setAttribute("top",pos.top);};if(doc.scrollTop>=top){thead.style.top=(doc.scrollTop)+'px';thead.style.position='absolute';}else{thead.style.top='0px';thead.style.position='relative';}}
 Mo.Command=function(cmd,param,func){if(typeof func!='function')var func=function(){};if(typeof param!='object')var param={"title":document.title,"href":location.href};var text='';switch(cmd){case"favorite":if(!window.netscape){window.external.addFavorite(param.href,param.title);}else{window.sidebar.addPanel(param.title,param.href,"");};break;case"homepage":if(!window.netscape){obj=document.createElement("a");obj.setAttribute("href","javascript:void(0);");document.body.appendChild(obj);obj.style.behavior='url(#default#homepage)';obj.sethomepage(param.href);}else{try{netscape.security.PrivilegeManager.enablePrivilege("UniversalXPConnect");}catch(e){text="此操作被浏览器拒绝！<br />请在浏览器地址栏输入“about:config”并回车<br />然后将[signed.applets.codebase_principal_support]设置为'true'";};var prefs=Components.classes['@mozilla.org/preferences-service;1'].getService(Components.interfaces.nsIPrefBranch);prefs.setCharPref('browser.startup.homepage',param.href);};break;}
func(text);return void(0);}
 Mo.Clipboard=function(text,func){if(typeof func!='function')var func=function(){};if(window.clipboardData){window.clipboardData.setData("Text",text);}else if(window.netscape){try{netscape.security.PrivilegeManager.enablePrivilege("UniversalXPConnect");}catch(e){throw new SecurityException(SecurityException.ERROR,"");};var clip=Components.classes['@mozilla.org/widget/clipboard;1'].createInstance(Components.interfaces.nsIClipboard);if(!clip)return;var trans=Components.classes['@mozilla.org/widget/transferable;1'].createInstance(Components.interfaces.nsITransferable);if(!trans)return;trans.addDataFlavor('text/unicode');var str=new Object();var len=new Object();var str=Components.classes["@mozilla.org/supports-string;1"].createInstance(Components.interfaces.nsISupportsString);var copytext=text;str.data=copytext;trans.setTransferData("text/unicode",str,copytext.length*2);var clipid=Components.interfaces.nsIClipboard;if(!clip)return false;clip.setData(trans,null,clipid.kGlobalClipboard);}
func(text);return false;}
 Mo.plugin.push("interface");
