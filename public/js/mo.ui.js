 if(typeof Mo!='function'){var Mo={plugin:[]}}
 Mo.Dialog=function(style,title,content,width,height,config){this.config=config?config:{};this.id=(config["unique"]?config["unique"]:Mo<PERSON>random(10));this.style=style;this.title=title;this.html=content;this.width=width;this.height=height;this.object=null;this.mode='';this.config["zindex"]=this.config["zindex"]?this.config["zindex"]:9999;var self=this;this.onRemove=function(){return true;}
this.onMinimize=function(){return true;}
this.onMaximize=function(){return true;}
this.onCreate=function(){}
this.onDblclick=function(){}
this.onDrag=function(){}
this.Center=function(){var doc=Mo.document;var div=this.object;if(this.width.toString().indexOf("%")>-1)this.width=doc.scrollWidth*(parseInt(this.width)/100);if(this.height.toString().indexOf("%")>-1)this.height=doc.scrollHeight*(parseInt(this.height)/100);if(this.width>doc.scrollWidth){this.width=doc.scrollWidth-100;this.height+=30;};if(this.height>doc.scrollHeight){this.height=doc.scrollHeight-100;this.width+=30;}
if(this.width)div.style.width=this.width+"px";if(this.height)div.style.height=this.height+"px";var pos=Mo("#"+this.id).position();var top=doc.scrollTop+(doc.clientHeight/2)-(pos.height/2);top=top<0?20:top;var left=(doc.scrollWidth-pos.width)/2;left=left<0?20:left;div.style.left=left+"px";div.style.top=top+"px";if(this.config["locked"]){Mo("#"+this.id+"-bg").style({"height":Math.max(doc.scrollHeight,doc.offsetHeight)+"px","width":"100%"});}}
this.Create=function(){if(Mo("#"+this.id).size()){Mo("#"+this.id).toggle();return true;}
if(this.config["locked"]&&!this.config["minimize"]){var bg=Mo(document.body).create("div",{"id":this.id+"-bg"},true);Mo(bg).style({"position":"absolute","background":"#666","opacity":"0.2","filter":"alpha(opacity=20)","zIndex":this.config["zindex"],"top":"0","left":"0"});}
var dl=Mo.create("dl",{"id":this.id,"className":this.style,"dialog":"dialog"});dl.setAttribute("dialog","dialog");this.object=dl;var dt=document.createElement("dt");var sg=document.createElement("strong");sg.innerHTML=this.title;dt.appendChild(sg);if(this.config["remove"]){var sp=document.createElement("span");sp.className="remove";dt.appendChild(sp);sp.onclick=function(){self.Remove(this)};Mo(sp).bind("mousedown",function(ele,index,e){Mo.Event(e).stop();});}
if(this.config["maximize"]){var mx=document.createElement("span");mx.className="maximize";dt.appendChild(mx);mx.onclick=function(){self.Maximize(this)};Mo(mx).bind("mousedown",function(ele,index,e){Mo.Event(e).stop();});}
if(this.config["minimize"]){var sp=document.createElement("span");sp.className="minimize";dt.appendChild(sp);sp.onclick=function(){self.Minimize(this)};Mo(sp).bind("mousedown",function(ele,index,e){Mo.Event(e).stop();});}
Mo(dt).bind("dblclick",function(ele,index,e){self.onDblclick(self.config["maximize"]?mx:null);});dl.appendChild(dt);var dd=Mo.create("dd",{"id":this.id+"-dd","innerHTML":this.html});dl.appendChild(dd);document.body.appendChild(dl);Mo(dl).style({"zIndex":(parseInt(this.config["zindex"])+1)});self.Center();self.onCreate(self);if(this.config["draged"]&&typeof Mo.Drag=="function"){dt.className="drag";var dg=new Mo.Drag(dt,dl);if(Mo("iframe",dd).size()){var fix=Mo(document.body).create("div",{"id":this.id+"-fix"},true);Mo(fix).style({"position":"absolute","zIndex":this.config["zindex"]+1,"top":"0","left":"0","width":"100%","height":"100%"}).hide();}
dg.onStart=function(){if(self.config["opacity"]){this.style.filter="alpha(opacity=70)";this.style.opacity=0.7;}
self.onDrag(self);Mo("#"+self.id+"-fix").show();}
dg.onEnd=function(x,y){if(self.config["opacity"]){this.style.filter="alpha(opacity=100)";this.style.opacity=1;};Mo("#"+self.id+"-fix").hide();}}}
this.Remove=function(btn){if(self.onRemove(self)){Mo("#"+self.id).remove();Mo("#"+self.id+"-bg").remove();}}
this.Minimize=function(btn){if(self.onMinimize(self)){Mo("#"+self.id).toggle();}}
this.Maximize=function(btn){if(self.onMaximize(btn)){if(self.mode=='Maximize'){Mo(self.object).style({"height":""});self.Center();self.mode='';btn.className='maximize';}else{Mo(self.object).style({"width":"100%","height":"100%","left":"0px","top":"-2px"});self.mode='Maximize';btn.className='toggle';}}}
if(this.config["create"]){this.Create();}}
 Mo.Selector=function(path,value,callback){var box=$E(path);if(typeof callback!='function'){var callback=function(value,rel,box){if(value==rel){box.className='active';}else{box.className='';}};};for(var i=0;i<box.length;i++){callback(value,box[i].getAttribute("rel"),box[i]);}}
 Mo.Planer=function(box){var box=box;this.getTable=function(obj){var tab=obj.parentNode.parentNode.parentNode.parentNode.parentNode;return tab;}
this.remove=function(obj){if(this.getNodes(box)==1){alert('只剩下一个啦！');return;};if(confirm('确定要删除掉这个吗？')){var tab=this.getTable(obj);Mo(tab).remove();}}
 this.copy=function(func){var tag=this.getFirst(box).cloneNode(true);try{tag.getElementsByTagName("input")[0].value="";tag.getElementsByTagName("textarea")[0].value="";}catch(e){};box.appendChild(tag);if(typeof func=='function'){func(tag);}};this.move=function(obj,dest){var tab=this.getTable(obj);if(tab.getAttribute("fixed"))return;if(dest=="down"){if(tab.nextSibling==null){return false;}else{if(this.getNext(tab).getAttribute("fixed"))return;tab.swapNode(this.getNext(tab));}};if(dest=="up"){if(tab.previousSibling==null){return false;}else{if(this.getPrev(tab).getAttribute("fixed"))return;tab.swapNode(this.getPrev(tab));}}}
this.getNodes=function(n){var x=n.childNodes;var z=0;for(var i=0;i<x.length;i++){if(x[i].nodeType==1){z++;}};return z;}
this.getFirst=function(n){var x=n.firstChild;while(x.nodeType!=1){x=x.nextSibling;};return x;}
this.getNext=function(n){var x=n.nextSibling;while(x.nodeType!=1){x=x.nextSibling;};return x;}
this.getPrev=function(n){var x=n.previousSibling;while(x.nodeType!=1){x=x.previousSibling;};return x;}}
 Mo.Table=function(table,funs){if(typeof table=='string')table=$(table);var tr=table.getElementsByTagName("tr");for(var i=0;i<tr.length;i++){var self=tr[i];for(var n=0;n<funs.length;n+=2){(function(){var evt=funs[n];var fun=funs[n+1];self['on'+evt]=function(e){var e=e||event;fun(this,e);}})();}}}
 Mo.Message=function(style,html,time,config,func){if(typeof func!='function')var func=function(){};var config=config?config:{};var id=(config["unique"]?config["unique"]:Mo.random(10));if(!html)return;Mo("#"+id).remove();Mo(document.body).create("div",{"id":id,"className":style,"innerHTML":html});if(config["center"]){var doc=Mo.document;var pos=Mo("#"+id).position();Mo("#"+id).style({"position":"absolute","left":(doc.scrollWidth-pos.width)/2+"px","top":doc.scrollTop+(doc.clientHeight/2)-(pos.height/2)+"px"});};window.setTimeout(function(){Mo("#"+id).hide();func(Mo("#"+id).item(0));},1000*time);}
 Mo.Tips=function(self,event,style,html,action,fX,fY,config){Mo.Event(event).stop();var config=config?config:{};var id=(config["unique"]?config["unique"]:Mo.random(10));Mo("#"+id).remove();Mo(document.body).create("div",{"id":id,"className":style});var pos=Mo(self).position();fX=fX?fX:0;fY=fY?fY:0;Mo("#"+id).style({"position":"absolute","left":pos.left+fX+"px","top":pos.top+fY+"px"}).bind('click',function(ele,index,event){Mo.Event(event).stop();}).html(html);Mo(document).bind(action?action:'mouseover',function(ele,index,event){var ele=Mo.Event(event).element();if(self!=ele){Mo("#"+id).hide();}});}
 Mo.FadeBox=function(){this.Speed=10;this.Timer=2000;this.Alpha=100;this.iCounter=0;this.iCurrent=0;this.iClock=null;this.Images=new Array();this.Add=function(o){this.Images[this.iCounter]=o;this.iCounter++;};var self=this;this.onChange=function(){}
this.FadeIn=function(){var obj=this.Images[this.iCurrent];var style="filter:alpha(opacity="+parseInt(this.Alpha++)+");-moz-opacity:"+(this.Alpha++/100) +";opacity:"+ (this.Alpha++/100)+";";obj.style.cssText=style;obj.setAttribute("style",style);if(this.Alpha>=100){window.clearInterval(this.iClock);this.iClock=null;this.Play();this.onChange(this.iCurrent);}}
this.FadeOut=function(){var obj=this.Images[this.iCurrent];var style="filter:alpha(opacity="+parseInt(this.Alpha--)+");-moz-opacity:"+(this.Alpha--/100) +";opacity:"+ (this.Alpha--/100)+";";obj.style.cssText=style;obj.setAttribute("style",style);if(this.Alpha<=-10){window.clearInterval(this.iClock);this.iClock=null;this.Images[this.iCurrent].style.display="none";if(this.iCurrent+1==this.Images.length){this.iCurrent=0;}else{this.iCurrent++;}
var obj=this.Images[this.iCurrent];var style="filter:alpha(opacity=1);-moz-opacity:0.01;opacity:0.01;display:;";obj.style.cssText=style;obj.setAttribute("style",style);this.iClock=setInterval(function(){self.FadeIn();},this.Speed);}}
this.PlayNext=function(){this.iClock=setInterval(function(){self.FadeOut();},this.Speed);}
this.Play=function(n){if(typeof(n)=="number"){for(var i=0;i<this.iCounter;i++){this.Images[i].style.display="none";};this.iCurrent=n;this.Images[n].style.display="";};setTimeout(function(){self.PlayNext();},this.Timer);}}
 Mo.TabMenu=function(e){this.Event=e;this.Cur=-1;this.Inter=null;this.Speed=0;this.Array=new Array();this.TClass=["",""];this.BClass=["",""];this.TabClass=function(a,d){this.TClass=[a,d];}
this.BoxClass=function(a,d){this.BClass=[a,d];}
this.Add=function(o,t){if(o&&t){this.Array[this.Array.length]=[o,t];}}
this.onChange=function(){}
this.Change=function(tab){for(var n=0;n<this.Array.length;n++){if(this.TClass[0]||this.TClass[1]){this.Array[n][0].className=this.TClass[1];}
if(this.BClass[0]||this.BClass[1]){this.Array[n][1].className=this.BClass[1];}else{this.Array[n][1].style.display="none";}};var obj=this.Array[tab][0];if(this.TClass[0]||this.TClass[1]){obj.className=this.TClass[0];};var box=this.Array[tab][1];if(this.BClass[0]||this.BClass[1]){box.className=this.BClass[0];}else{box.style.display="";}
if(this.Inter){var self=this;box.onmouseover=function(){clearInterval(self.Inter);};box.onmouseout=function(){self.Auto(self.Speed);}};this.Cur=tab;this.onChange(tab);}
this.Play=function(t){var self=this;for(var n=0;n<this.Array.length;n++){var obj=this.Array[n][0];(function(){var tab=n;obj["on"+self.Event]=function(){self.Change(tab);clearInterval(self.Inter);};obj["onmouseout"]=function(){self.Auto(self.Speed);}})();};if(t<=this.Array.length-1){this.Change(t);}else{this.Change(0);}}
this.Auto=function(s){if(s){this.Speed=s;var self=this;this.Inter=window.setInterval(function(){if((self.Cur+1)<=self.Array.length-1){self.Cur++;self.Change(self.Cur);}else{self.Change(0);}},s);}}}
     Mo.Soler=function(self,event,value,style,data,func,fX,fY){if(typeof func!='function')var func=function(){};Mo.Event(event).stop();var id="_timer_";Mo("#"+id).remove();Mo(document.body).create("dl",{"id":id,"className":style});var pos=Mo(self).position();fX=fX?fX:0;fY=fY?fY:0;Mo("#"+id).style({"position":"absolute","left":pos.left+fX+"px","top":pos.top+fY+"px","zIndex":"99999"}).bind('click',function(index,event){Mo.Event(event).stop();});var selected=value?value:Mo(self).attr("_value_");for(var key in data){var dt=Mo.create("dt",{"innerHTML":key});var dd=Mo.create("dd");var ul=Mo.create("ul");for(var i in data[key]){(function(){var value=data[key][i];var li=Mo.create("li",{"innerHTML":value,"className":(selected==value?"active":"")});Mo(li).bind('click',function(ele,index,event){ func.call(self,value,self);Mo.Event(event).stop();Mo("#"+id).hide();Mo(self).attr({"_value_":value});});Mo(ul).append(li);})();};Mo(dd).append(ul);Mo("#"+id).append(dt,dd);}
Mo(document).bind('click',function(ele,index,event){var ele=Mo.Event(event).element();if(self!=ele){Mo("#"+id).hide();}});}
 Mo.plugin.push("ui");
