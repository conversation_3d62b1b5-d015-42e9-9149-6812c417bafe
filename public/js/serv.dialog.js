  if(typeof Serv!='object'){var Serv={}};Serv.Manager={ id:"",name:"",rank:"",skin:"", date:{"时段":["01","02"],"分钟":["01","02"]}, files:["acrobat","asp","avi","bin","bmp","chm","dll","doc","exe","fla","gzip","htm","html","ico","ini","iso","jad","jar","gif","jpg","jpeg","ms","mbk","mdb","mid","mp","msi","ms-project","msstyles","mtf","pdf","png","ppt","psd","rar","reg","rm","sis","skn","swf","tar","thm","torrent","ttc","ttf","txt","wmv","xls","xml","zip"], editMode:false, box:null,ready:function(){}, reader:function(){if(Mo("#naver").size()){Serv.Naver(location.href,Mo("#naver").item(0),true);}
Mo(document).bind('click',function(ele,index,event){if(parent.window!=self.window){parent.Mo("#link").hide();parent.Mo("#collect").hide();parent.Serv.Apps.find(self.window);}});}, setTitle:function(){try{window.parent.document.title=document.title+" - "+Serv.site+" - Powered By VeryIDE";}catch(e){}}, setMode:function(mode){try{parent.Serv.Manager.editMode=mode;}catch(e){}},  setHash:function(key,url){parent.location.hash=key+"="+encodeURIComponent(url);}, frLoad:function(event){ if(Mo("#table").size()){ var mk=Mo.get("mark");if(mk){Mo.Tips(Mo("#mark_"+mk).item(0),event,'',Mo("#mark_"+mk).attr("text"),'click',80,0,{"unique":"bubble"});}
 Mo("#table tr").bind('mousedown',function(ele,index,event){var target=Mo.Event(event).target();var mark=ele.getAttribute("mark");if(!mark||target.tagName!="TD"){return false;};var obj=Mo("input[type=checkbox]",ele).item(0);obj.click();}).bind('dblclick',function(ele,index,event){var edit=ele.getAttribute("edit");if(!edit){return false;};location.href=edit.replace('{jump}',encodeURIComponent(location.href));}); Mo("#table tr td input[type=checkbox]").bind('click',function(ele,index,event){var tr=ele.parentNode.parentNode;Mo(tr).style({"background":(ele.checked?'#eee':'#fff')});});}
 if(Mo("#saving button").size()&&((Mo.Browser.msie&&Mo.Browser.version>6)||Mo.Browser.name!="ie")){Mo("#saving").attr({"class":"adv"}).show();}
 Mo("form").each(function(form,index){if(form.getAttributeNode("verify")&&form.getAttributeNode("verify").nodeValue=="true"){form.onsubmit=function(){var result=Mo.Form(this,function(x){Mo.Message("wrong",'<div class="s"></div><div class="c">'+x+'</div><div class="e"></div>',3,{"unique":"message","center":true});});if(result){};return result;}}}); }};Mo.ready(Serv.Manager.ready);Mo.reader(Serv.Manager.reader); function VeryIDE_Hello(){var hour=new Date().getHours();if(hour<4){hello="夜深了，";}else if(hour<7){hello="早安，";}else if(hour<9){hello="早上好，";}else if(hour<12){hello="上午好，";}else if(hour<14){hello="中午好，";}else if(hour<17){hello="下午好，";}else if(hour<19){hello="您好，";}else if(hour<22){hello="晚上好，";}else{hello="夜深了，";};Mo.write(hello);}
 Serv.SayHello=function(){var h=new Date().getHours();var b=[6,12,14,18,24];var w=["清晨","上午","中午","下午","晚上"];var i=0;for(;h>=b[i++];);return w[i-1]+"好！";}
 Serv.SayHello2=function(){return new Date().toLocaleString()+' 星期'+'日一二三四五六'.charAt(new Date().getDay());};function VeryIDE_GetDate(){var rnd=getRnd(10,true,true,false);Mo.write("<span id='"+rnd+"' class='text-key'>Loading...</span>");setInterval(function(){var holiday="";var calendar=new Date();var day=calendar.getDay();var month=calendar.getMonth();var date=calendar.getDate();var year=calendar.getFullYear();month++;var _date=new Date("May 0 "+year);if(_date.getDay()==0){var _n=14}else{var _n=14-_date.getDay();};if((month==1)&&(date==1))holiday="元旦";if((month==2)&&(date==14))holiday="情人节";if((month==3)&&(date==15))holiday="消费者权益日";if((month==3)&&(date==8))holiday="妇女节";if((month==4)&&(date==1))holiday="愚人节";if((month==3)&&(date==12))holiday="植树节 孙中山逝世纪念日";if((month==5)&&(date==1))holiday="国际劳动节";if((month==5)&&(date==_n)&&day==0)holiday="母亲节";if((month==5)&&(date==4))holiday="青年节";if((month==6)&&(date==1))holiday="国际儿童节";if((month==7)&&(date==1))holiday="香港回归纪念日";if((month==9)&&(date==10))holiday="中国教师节";if((month==9)&&(date==18))holiday="九·一八事变纪念日";if((month==9)&&(date==28))holiday="孔子诞辰";if((month==10)&&(date==6))holiday="老人节";if((month==12)&&(date==20))holiday="澳门回归纪念";if((month==12)&&(date==24))holiday="平安夜";if((month==12)&&(date==25))holiday="圣诞节";$(rnd).innerHTML=new Date().toLocaleString()+' 星期'+'日一二三四五六'.charAt(new Date().getDay())+" "+holiday;},1000);}
 Serv.Naver=function(url,box,set){var tag=box.getElementsByTagName("A");var len=tag.length;for(var i=0;i<len;i++){var rel=tag[i].getAttribute("rel");var pos=Mo.url(url);if(url.indexOf(tag[i].href)>-1||(rel&&rel.indexOf(pos.file)>-1)){if(set){tag[i].parentNode.className="active";}else{tag[i].parentNode.className="";};continue;}}}
 function getSelect2Select(Select1,Select2,ToSelect,ToValue){var _select1=new getSelect(Select1);var _select2=new getSelect(Select2);var _select3=$(ToSelect);var _input=$(ToValue);var _s=false;for(var i=0;i<_select3.length;i++){if(_select3[i].value==_select2.value){_s=true;}};if(_s||_select3.length>2)return true;var _value=_select1.value+","+_select2.value+"|";_select3[_select3.length]=new Option(_select1.text+" >> "+_select2.text,_value);_input.value+=_value;}
 function add2Select(ToSelect,Text,Value){var sel=document.getElementById(ToSelect);var add=true;var len=sel.length;for(var i=0;i<len;i++){if(sel[i].text==Text&&sel[i].value==Value){add=false;break;}};if(Text&&add)sel[sel.length]=new Option(Text,Value);}
 function delete2Select(ToSelect,ToValue){var _select=document.getElementById(ToSelect);var _input=document.getElementById(ToValue);if(_select.selectedIndex>-1){_input.value=_input.value.replace(_select[_select.selectedIndex].value+",","");document.getElementById(ToSelect).remove(_select.selectedIndex);}}
 function loadSelect(s,i){var sel=document.getElementById(s);var ipt=document.getElementById(i);var str="";var len=sel.length;for(var i=0;i<len;i++){str+=sel[i].value+",";};ipt.value=str;}
Mo.folder={};Mo.folder.image=Mo.store.root+"image/";Mo.folder.icons=Mo.store.root+"image/icon/";Mo.Tools=function(s,v,args){switch(s){case"Top":Mo.write("<a href='javascript:window.scroll(0,0);' name='"+v+"'><img src='"+Mo.folder.image+"top.gif' alt='Go To Top!' align='absmiddle' style='float:right' /></a>");break; case"Tip":if(!v)return true;Mo.write("<img src='"+Mo.folder.image+"question.gif' align='absmiddle' onmouseover='this.src=this.src.replace(\"question.gif\",\"answer.gif\");Mo.Tips(this,event,\"tips\",\""+v+"\");' onmouseout='this.src=this.src.replace(\"answer.gif\",\"question.gif\");Mo(\"#tips\").hide();' /> ");break;case"RsURL":if(v.indexOf("?")>-1){v=v+"&";}else{v=v+"?";};v=v+"jump="+encodeURIComponent(location.href);Mo.write(" <a href='"+v+"'>"+args+"</a> ");break;case"RsFile":var t=v.split(".")[v.split(".").length-1];if(!v||Mo.Array(Serv.Manager.files).indexOf(t)==-1){Mo.write("<img src='"+Mo.folder.image+"format/unknow.gif' title='未知文件:"+t+"'  align='absmiddle' /> ");}else{Mo.write("<a href='"+args+v+"' target='_blank'><img src='"+Mo.folder.image+"format/"+t+".gif' title='新窗口打开文件'  align='absmiddle' /></a> ");};break;case"RsType":var t=v.split(".")[v.split(".").length-1];if(!v||Mo.Array(Serv.Manager.files).indexOf(t)==-1){Mo.write("<img src='"+Mo.folder.image+"format/unknow.gif' title='未知类型'  align='absmiddle' /> ");}else{if(args){Mo.write("<a href='"+args+t+"'><img src='"+Mo.folder.image+"format/"+t+".gif' title='"+t+"格式'  align='absmiddle' /></a> ");}else{Mo.write("<img src='"+Mo.folder.image+"format/"+t+".gif' title='"+t.toUpperCase()+"格式'  align='absmiddle' /> ");}};break;case"RsImage":if(!v){Mo.write("<img src='"+Mo.folder.image+"file.png' title='无图片'  align='absmiddle' /> ");}else{if(args){Mo.write("<a href='"+v+"' target='_blank'><img src='"+Mo.folder.image+"image.png' align='absmiddle' onmouseover='Mo.Tips(event,this,\"tips\",\"<img src="+v+" />\",this,\"mouseout\");' /></a> ");}else{Mo.write("<a href='"+v+"' target='_blank'><img src='"+Mo.folder.image+"/image.png' title='新窗口打开图片'  align='absmiddle' /></a> ");}};break;case"RsDelete":if(v.indexOf("?")>-1){v=v+"&";}else{v=v+"?";};v=v+"jump="+encodeURIComponent(location.href);var i=args==""?i="确定要删除这条记录吗?":i=args;Mo.write(" <a href='"+v+"' onclick='return confirm(&quot;"+i+"&quot;);'><img src='"+Mo.folder.icons+"trash.png' alt='删除' align='absmiddle' /> ");break;case"RsEdit":if(v.indexOf("?")>-1){v=v+"&";}else{v=v+"?";};v=v+"jump="+encodeURIComponent(location.href);Mo.write(" <a href='"+v+"'><img src='"+Mo.folder.icons+"pencil.png' alt='编辑' align='absmiddle' /></a> ");break;case"RsNew":Mo.write(" <a href='"+v+"'><img src='"+Mo.folder.icons+"plus.png' alt='添加内容' align='absmiddle' /></a> ");break;}}
 Mo.Status=function(parent,id,baseUrl,array,value,mx,my){if(!Mo(id).size()){mx=mx?mx:0;my=my?my:0;var obj=document.createElement("ul");obj.id=id;obj.className="select";var html='';for(var key in array){if(key!=value){html+="<li><a href='"+baseUrl+key+'&amp;jump='+encodeURIComponent(location.href)+"' title='"+array[key]+"'>○ "+array[key]+"</a></li>";}else{html+="<li>● "+array[key]+"</li>";Mo(parent).html(array[key]);}}
 obj.innerHTML=html;Mo(parent).append(obj);(function(){var o=obj;var p=parent;p.onmouseover=function(){var pos=Mo(p).position();var x=pos.left;var y=pos.top;with(o.style){left=x+mx+"px",top=y+my+"px",position="absolute",display="block"}};p.onmouseout=function(event){o.style.display="none";}})();}};
