 if(typeof Mo!='function'){var Mo={plugin:[]}} ;Mo<PERSON>Drag=function(a){var self=this;this.obj=null;  this.init=function(o,oRoot,minX,maxX,minY,maxY,bSwapHorzRef,bSwapVertRef,fXMapper,fYMapper){ o.onmousedown=self.start;o.hmode=bSwapHorzRef?false:true;o.vmode=bSwapVertRef?false:true;o.root=oRoot&&oRoot!=null?oRoot:o;if(o.hmode&&isNaN(parseInt(o.root.style.left)))o.root.style.left="0px";if(o.vmode&&isNaN(parseInt(o.root.style.top)))o.root.style.top="0px";if(!o.hmode&&isNaN(parseInt(o.root.style.right)))o.root.style.right="0px";if(!o.vmode&&isNaN(parseInt(o.root.style.bottom)))o.root.style.bottom="0px";o.minX=typeof minX!='undefined'?minX:null;o.minY=typeof minY!='undefined'?minY:null;o.maxX=typeof maxX!='undefined'?maxX:null;o.maxY=typeof maxY!='undefined'?maxY:null;o.xMapper=fXMapper?fXMapper:null;o.yMapper=fYMapper?fYMapper:null; self.onStart=new Function();self.onEnd=new Function();self.onDrag=new Function();};this.start=function(e){var o=self.obj=this;e=self.fixE(e);var y=parseInt(o.vmode?o.root.style.top:o.root.style.bottom);var x=parseInt(o.hmode?o.root.style.left:o.root.style.right);self.onStart.call(o.root,x,y,e);o.lastMouseX=e.clientX;o.lastMouseY=e.clientY;if(o.hmode){if(o.minX!=null)o.minMouseX=e.clientX-x+o.minX;if(o.maxX!=null)o.maxMouseX=o.minMouseX+o.maxX-o.minX;}else{if(o.minX!=null)o.maxMouseX=-o.minX+e.clientX+x;if(o.maxX!=null)o.minMouseX=-o.maxX+e.clientX+x;};if(o.vmode){if(o.minY!=null)o.minMouseY=e.clientY-y+o.minY;if(o.maxY!=null)o.maxMouseY=o.minMouseY+o.maxY-o.minY;}else{if(o.minY!=null)o.maxMouseY=-o.minY+e.clientY+y;if(o.maxY!=null)o.minMouseY=-o.maxY+e.clientY+y;} ;document.onmousemove=self.drag;document.onmouseup=self.end;return false;};this.drag=function(e){e=self.fixE(e);var o=self.obj;var ey=e.clientY;var ex=e.clientX;var y=parseInt(o.vmode?o.root.style.top:o.root.style.bottom);var x=parseInt(o.hmode?o.root.style.left:o.root.style.right);var nx,ny;if(o.minX!=null)ex=o.hmode?Math.max(ex,o.minMouseX):Math.min(ex,o.maxMouseX);if(o.maxX!=null)ex=o.hmode?Math.min(ex,o.maxMouseX):Math.max(ex,o.minMouseX);if(o.minY!=null)ey=o.vmode?Math.max(ey,o.minMouseY):Math.min(ey,o.maxMouseY);if(o.maxY!=null)ey=o.vmode?Math.min(ey,o.maxMouseY):Math.max(ey,o.minMouseY);nx=x+((ex-o.lastMouseX)*(o.hmode?1:-1));ny=y+((ey-o.lastMouseY)*(o.vmode?1:-1));if(o.xMapper){nx=o.xMapper(y)}else if(o.yMapper){ny=o.yMapper(x)};self.obj.root.style[o.hmode?"left":"right"]=nx+"px";self.obj.root.style[o.vmode?"top":"bottom"]=ny+"px";self.obj.lastMouseX=ex;self.obj.lastMouseY=ey;self.onDrag.call(self.obj.root,nx,ny,e);return false;};this.end=function(e){e=self.fixE(e);document.onmousemove=null;document.onmouseup=null;self.onEnd.call(self.obj.root,parseInt(self.obj.root.style[self.obj.hmode?"left":"right"]),parseInt(self.obj.root.style[self.obj.vmode?"top":"bottom"]),e);self.obj=null;};this.fixE=function(e){if(typeof e=='undefined')e=window.event;if(typeof e.layerX=='undefined')e.layerX=e.offsetX;if(typeof e.layerY=='undefined')e.layerY=e.offsetY;return e;};var arg=arguments;if(arg.length){this.init(arg[0],arg[1],arg[2],arg[3],arg[4],arg[5],arg[6],arg[7],arg[8],arg[9]);}} ;Mo.plugin.push("drag");
