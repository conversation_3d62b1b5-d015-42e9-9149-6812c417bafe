﻿@charset "utf-8";
.calendar {
	border-right: #999 2px solid;border-bottom: #999 2px solid;display: none; font-size: 11px; background: window;  cursor: default; color: windowtext;  font-family:verdana,sans-serif; position: relative;
}
	.calendar table {
		border: #ccc 1px solid;  font-size: 12px; background: window; cursor: default; color: windowtext; font-family: tahoma,verdana,sans-serif;
		width:220px;
	}
	.calendar .button {
		padding: 1px; border-right: buttonshadow 1px solid;  border-top: buttonhighlight 1px solid; background: buttonface; border-left: buttonhighlight 1px solid; border-bottom: buttonshadow 1px solid; text-align: center;color:#666;
	}
	.calendar .button:hover{
		padding:1px;border-width:1px;border:#666 solid 1px;color:#333;
	}
	.calendar thead .title {
		border: #000 1px solid; font-weight: bold; background: activecaption; padding: 1px; color: captiontext; text-align: center;
	}
	.calendar thead .headrow {
	}
	.calendar thead .daynames {
		
	}
	.calendar thead .name {
		padding-right: 2px; padding-left: 2px; background: buttonface; padding-bottom: 2px; color: buttontext; padding-top: 2px; border-bottom: buttonshadow 1px solid; text-align: center;
	}
	.calendar thead .weekend {
		color: #f00
	}
	.calendar thead .hilite {
		border-top-width: 2px; padding-right: 0px; padding-left: 0px; border-left-width: 2px; border-bottom-width: 2px; padding-bottom: 0px; padding-top: 0px; border-right-width: 2px
	}
	.calendar thead .active {
		border-top-width: 1px; padding-right: 0px; padding-left: 2px; border-left-width: 1px; border-left-color: buttonshadow; border-bottom-width: 1px; border-bottom-color: buttonhighlight; padding-bottom: 0px; border-top-color: buttonshadow; padding-top: 2px; border-right-width: 1px; border-right-color: buttonhighlight
	}
	.calendar tbody .day {
		padding-right: 4px; padding-left: 2px; padding-bottom: 2px; width: 2em; padding-top: 2px; text-align: right
	}
	.calendar table .wn {
		border-right: buttonshadow 1px solid; padding-right: 3px; padding-left: 2px; background: buttonface; padding-bottom: 2px; color: buttontext; padding-top: 2px
	}
	.calendar tbody .rowhilite td {
		background: #eee; color: #000
	}
	.calendar tbody td.hilite {
		border-right: #000 1px solid; padding-right: 3px; border-top: #fff 1px solid; padding-left: 1px; padding-bottom: 1px; border-left: #fff 1px solid; padding-top: 1px; border-bottom: #000 1px solid;background:#FFFFCC;
	}
	.calendar tbody td.active {
		border-right: buttonhighlight 1px solid; padding-right: 2px; border-top: buttonshadow 1px solid; padding-left: 2px; padding-bottom: 0px; border-left: buttonshadow 1px solid; padding-top: 2px; border-bottom: buttonhighlight 1px solid; 
	}
	.calendar tbody td.selected {
		border-right: buttonhighlight 1px solid; padding-right: 2px; border-top: buttonshadow 1px solid; padding-left: 2px; font-weight: bold; padding-bottom: 0px; border-left: buttonshadow 1px solid; padding-top: 2px; border-bottom: buttonhighlight 1px solid; background:#f5f5f5;
	}
	.calendar tbody td.weekend {
		color: #f00
	}
	.calendar tbody td.today {
		font-weight: bold; color: #00f
	}
	.calendar tbody td.disabled {
		color: graytext
	}
	.calendar tbody .emptycell {
		visibility: hidden
	}
	.calendar tbody .emptyrow {
		display: none
	}
	.calendar tfoot .footrow {
		
	}
	.calendar tfoot .ttip {
		border-right: buttonhighlight 1px solid; padding-right: 1px; border-top: buttonshadow 1px solid; padding-left: 1px; background: buttonface; padding-bottom: 1px; border-left: buttonshadow 1px solid; color: buttontext; padding-top: 1px; border-bottom: buttonhighlight 1px solid; text-align: center; line-height:18px;
	}
	.calendar tfoot .hilite {
		border-right: #000 1px solid; padding-right: 1px; border-top: #fff 1px solid; padding-left: 1px; background: #e4e0d8; padding-bottom: 1px; border-left: #fff 1px solid; padding-top: 1px; border-bottom: #000 1px solid
	}
	.calendar tfoot .active {
		border-right: #fff 1px solid; padding-right: 0px; border-top: #000 1px solid; padding-left: 2px; padding-bottom: 0px; border-left: #000 1px solid; padding-top: 2px; border-bottom: #fff 1px solid
	}
	
.combo {
	border-right: buttonshadow 1px solid; padding-right: 1px; border-top: buttonhighlight 1px solid; display: none; padding-left: 1px; font-size: smaller; background: menu; left: 0px; padding-bottom: 1px; border-left: buttonhighlight 1px solid; width: 4em; cursor: default; color: menutext; padding-top: 1px; border-bottom: buttonshadow 1px solid; position: absolute; top: 0px
}
	.combo .label {
		padding-right: 1px; padding-left: 1px; padding-bottom: 1px; padding-top: 1px; text-align: center
	}
	.combo .active {
		border-right: #000 1px solid; padding-right: 0px; border-top: #000 1px solid; padding-left: 0px; padding-bottom: 0px; border-left: #000 1px solid; padding-top: 0px; border-bottom: #000 1px solid
	}
	.combo .hilite {
		background: highlight; color: highlighttext
	}

/* 时段组件 */
.timer{ border:#bbb solid 1px; padding:5px; background:#fff; position:absolute; width:300px; line-height:22px; FILTER: progid:DXImageTransform.Microsoft.Shadow(direction=135,color=#999999,strength=2);}

	.timer p{ text-align:center; }
		.timer p button{ background:#333; color:#fff; border:none; padding:3px; margin:0 5px; cursor:pointer;}

	.timer div{ padding:0 5px; background:#efefef; color:#000; font-weight:bold; margin:8px 0;}
	
	.timer table{ width:100%; overflow:hidden; padding:0; margin:5px 0; background:#ccc;}
		.timer table td{ text-align:center; line-height:20px; background:#fff;}
		.timer table td:hover{ background:#999; color:#fff; cursor:pointer;}
		.timer table td.active{ background:#666; color:#fff;}