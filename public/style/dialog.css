@charset "utf-8";
/* 对话样式 */
html,body{ margin:0;padding:0; /* overflow-x:auto!important; overflow-x:hidden; */ }

form,ul,li { margin:0;padding:0; list-style:none; }
body, td, input, textarea, select, button{ color:#555; font:12px "Lucida Grande", Verdana, Lucida, Helvetica, Arial, "宋体", sans-serif; }
td,input,select,body { font-family:Verdana; font-size:12px; }

/* 表单元素 */
input, button, select, textarea{ vertical-align:middle; }
textarea, input{ padding:3px; border:1px solid #ccc; border-color:#666 #ccc #ccc #666; background:#F9F9F9; color:#333;}
textarea{ font-family:Verdana;width:500px;border:1px solid #718da6; line-height:18px;}
select{ font:normal 13px Verdana; padding:1px; border:1px solid #ccc; border-color:#666 #ccc #ccc #666; background:#F9F9F9; color:#333; }
input.text:hover, input.text:focus, input.datetime:hover, input.datetime:focus, textarea:hover, textarea:focus{ border-color:#09C; }

/* 重置元素 */
p{ padding:2px; margin:2px;}
hr{color:#efefef; height:1px; clear:both; border:#efefef solid 1px;}
label{ color:#666;}
img {border:none; vertical-align:middle;}
a {text-decoration:none;cursor:pointer;}
	a,a:link,a:visited{color:#1E5494;}
	a:hover{text-decoration:underline;}

/*
	主体_new
*/
#wrapper{ padding:10px; background:url(pic/dialog.gif) right bottom no-repeat; }

	/*body*/
	/*#main{  margin:10px; height__:100%; border:red solid 1px; overflow:hidden; font-family:"Microsoft YaHei";}*/
	
	/*
		页面名称
	*/
	#nav{padding:4px; padding:1px 5px 0 5px; color:#333;}
		#nav .action{float:right;}
		#nav strong{font-size:16px;}
		#nav img{ vertical-align:middle;}
	
	/*
		选项卡_new
	*/
	#naver{ background:url(pic/bg.png) 0 -620px repeat-x; height:35px; clear:both; overflow:hidden; margin-bottom:10px;  padding:0 5px; }
		#naver li{font-weight:bold; line-height:30px; height:30px; text-align:center;margin:5px 0; display:inline-block; float:left; overflow:hidden;}
		
		#naver li.active{background:#fff;}
		#naver li.active a{ color:#4B90B2; text-decoration:none;}
		
		#naver li.menu{ float:right;}
		
		#naver li a{ padding:5px 10px; color:#fff; line-height:30px;}
				
	/*
		选项子菜单
	*/
	#subnav{ color:#ccc; overflow:hidden; }
		#subnav a{padding:2px 5px 0 5px; color:#4B90B2;text-decoration:underline;}
		#subnav a.active{text-decoration:none; color:#fff; background:#999;}
		#subnav a.action{ float:right;}
				
	/*子选项卡*/
	#subtab{ line-height:20px; border-bottom:#666 solid 2px; margin-bottom:10px; height:35px; overflow:hidden;}
		#subtab li{padding:5px 10px; margin:5px 10px; float:left; color:#333; cursor:pointer;}
		#subtab li.action{ float:right;}
		#subtab li.active{text-decoration:none; color:#fff; background:#666;}
		#subtab li.active a{ color:#fff;}
	
	/*
		状态
	*/
	#state{ line-height:25px; background:#EDF8DF url(pic/success.gif) 10px center no-repeat; border:#4E9B06 solid 1px; padding:10px; padding-left:50px; margin:10px 0; clear:both; color:#333;}
		#state.failure{ background:#FFFFCC url(pic/failure.gif) 10px center no-repeat; border:#CC3300 solid 1px;}
		
	/*
		主内容块
	*/
	#box{ border:#AAC1DE solid 1px; margin:5px 0; padding:10px; background:#fff; width:auto; overflow:hidden;}
	
		#adsense{float:right; position:absolute; right:0; margin:40px 16px; border:#efefef solid 1px; padding:5px;}
	
		/*
			搜索框
		*/
		#search{ padding:10px; margin:10px 0; line-height:20px; border:#B7CDE5 solid 1px; background:#fff;}
			#search .action{float:right;}
			#search .advanced{ background:#F2F7FC; padding:10px; margin-top:10px;}
			#search .go{background:url(pic/bg.png) -160px -320px repeat-x; width:22px; height:22px; margin:0 2px; vertical-align:middle; border:none; cursor:pointer;}
		
		/*saving*/
		#saving{ background:#E3EDF9; padding:8px; line-height:20px; clear:both; overflow:hidden; }
			#saving .action{ float:right;}
			/*ie7*/ 
			#saving.adv{ position:fixed; bottom:0; left:0; margin-bottom:30px; width:100%;}
			#saving button{ margin:0 90px;}
	
		/*
			查看方式
		*/
		#viewer{ color:#999; line-height:20px; margin:10px 0; }
			#viewer strong{ color:#333;}
			#viewer span{ float:right; display:inline-block;}
			#viewer a{ display:inline-block; text-align:center; padding:0 5px;}
			#viewer a.active{ background:#4B90B2; color:#fff;}
			
		/*
			皮肤预览
		*/
		#preview{  cursor:pointer; margin-left:5px; float:left; border:#999 solid 1px; text-align:center; padding:6px; width:160px; background:#fff; }
			#preview:hover{ border-color:#09C; }
	
	/*
		页尾
	*/
	#footer{ padding:5px; clear:both; color:#999;}
		#footer.powered{ text-align:right;}
		#footer a{ color:#666;}
	
	
	/*
		引子
	*/
	#setp{ float:right; height:16px; margin:5px; display_:none;}
		#setp li{ background:url(pic/bg.png) -160px -350px no-repeat; color:#557814; float:left; margin-right:15px; padding:2px;}
		#setp li.first{ background:none; color:#666;}
		#setp li a{ color:#557814;}
		#setp li span{ color:#fff; width:12px; float:left; text-align:center; margin-right:10px; font-family:Geneva, Arial, Helvetica, sans-serif; font-size:14px; font-weight:bold; line-height:12px;}

	/********* common ********/
		
	/* 下拉菜单 */
	.next{ background:url(pic/arrow.gif) right top no-repeat; width:60px; padding-right:10px;}
		
	/* 上拉菜单 */
	.prev{ background:url(pic/arrow.gif) right -22px no-repeat; width:60px; padding-right:10px;}
	
	/*
		浮动菜单
	*/
	.select{ position:absolute; border:#999 solid 1px; background:#fff;}
		.select li{line-height:25px; text-indent:0; }
		.select li a{ display:block; padding:0 15px; color:#999; text-decoration:none;}
		.select li a:link{ background:#fff; color:#666;}
		.select li a:hover{ background:#efefef; color:#333;}
	
	/*
		页码链接
	*/
	.page{ line-height:20px; text-decoration: none; color:#666;}
		.page strong{font-weight:bold;padding: 2px 6px 2px 6px;color: #ccc;margin-right:2px;vertical-align: middle;}
		.page span{ margin-left:5px;}
		.page a {border:#ddd 1px solid;padding: 2px 6px 2px 6px;background:#FFF;color: #333;margin-right:2px;vertical-align: middle; text-decoration:none;}
			.page a:hover {border-color:#4B90B2;color:#4B90B2; text-decoration:none;}
	
	/*
		列表页数据_new
	*/
	.table{ background:#C4CACD; margin:0 auto 10px auto; overflow:hidden;border-collapse:separate !important;}
		.table td{text-indent:5px; padding:5px;line-height:20px;}
		.table img{ vertical-align:middle;}
		.table a img{ margin:0 2px; border:#fff solid 1px; padding:2px; background:#fff;}
		.table a img:hover{ border-color:#ccc;}
		
		/* 无边框图片 */
		.table a img.nobr{ border:none; padding:0; }
		
		/*
			表头
		*/
		.table .thead{background:#215978;}
			.table .thead td{ color:#fff;}
			.table .thead td.first{background:#215978 url(pic/bg.png) -155px -375px no-repeat; padding-left:35px;}
			.table .thead td.first .action{ float:right; padding-right:10px;}
			.table .thead a{ color:#fff;}
		
		/*
			表尾
		*/
		.table .tfoot{background:#E9F3FD;}
			.table .tfoot td{ color:#333;}
			.table .tfoot td.first{background:#E9F3FD url(pic/bg.png) -155px -555px no-repeat; padding-left:35px;}
			.table .tfoot a{ color:#333;}
		
		/*
			标题
		*/
		.table .title{}
			.table .title td{background:#DEEAF8; color:#333;}
		
			/*
				排序
			*/
			.table .title div{ padding-right:10px;}
			.table .title div a{ color:#039; text-decoration:underline;}
			.table .title div.asc{ background:url(pic/asc.gif) right center no-repeat;}
			.table .title div.desc{ background:url(pic/desc.gif) right center no-repeat;}
		
		.table .red td,.table .red td{background:#FFFFCC; color:red;}
		
		.table tr td img{ margin:0 2px;}
		
		.table .green td,.table .green td{background:#fff; color:green;}
		
		.table .line{ background:#fff;color:#333;}
			.table .line:hover,.table .line:hover .next{background-color:#F4F4F4;}
			
			/* 高亮状态 */
			.table .line:hover a img{border:#F4F4F4 solid 1px; background:#F4F4F4;}
			.table .line:hover a img:hover{ background:#FFF; border:#ccc solid 1px;}
		
		/* 斑马线 */
		.table .band{ background:#F4F4F4; color:#333;}	
		
		.table .block{ background:#F6F9FB; padding:10px; border-left:3px solid #B7CDE5; word-break:break-all;}
		.table .key{color:#000099;}
	
	/*
		表单
	*/
	.form{ width:100%; margin:0 auto 10px auto; clear:both; }
		.form .pointer{ cursor:pointer;}
		.form .section{ background:#F2F7FC; padding:5px; margin:1px 0; border-bottom:#fff solid 2px;}
			.form .section strong{color:#519506; font-weight:bold;}
			.form .section span{ float:right;}
			.form .close{ background:url(pic/bg.png) -10px -160px no-repeat; width:8px; height:9px; display:inline-block; float:right; vertical-align:middle; margin:5px;}
			.form .open{ background:url(pic/bg.png) -10px -200px no-repeat; width:8px; height:9px; display:inline-block; float:right; vertical-align:middle; margin:5px;}
		
		.form .block{ background:#FFFFCC;}
		.form tr.nobr td{border-bottom:none;}
		.form tr td a{ text-decoration:underline;}
		.form tr td{padding:6px;line-height:20px; vertical-align:text-top; color:#999;}
		.form tr strong{ color:#333; font-weight:normal;}
		.form .colspan{ background:#FFFFCC; padding:10px; border-left:5px solid #FF9900;}
		.form .key{color:#000099;}
		.form input.login{ width:150px;}
		.form input.captcha{ width:100px;}
	
	/*
		表单内数据
	*/
	.gird{ margin:0; margin-bottom:5px; padding:1px; background:#ccc; width:514px;}
		.gird tr td{padding:8px;line-height:20px; width:50%; vertical-align:text-top; color:#666; background:#fff;}
		.gird .action{float:right; color:#999;}
		.gird textarea{width:486px;}
	
	/*
		表单框架
	*/
	.frame{ margin:0; margin-bottom:5px; padding:1px; background:#ccc;}
		.frame tr td{padding:8px;line-height:20px; vertical-align:text-top; color:#666; background:#fff;}
		.frame tr.band td{ background:#F4F4F4; color:#333;}

	/*
		文件列表
	*/
	.files{ list-style:none; clear:both; width:100%; background:#ccc; margin:10px 0; }
		.files td{ width:20%; background:#fff; text-align:center; line-height:20px;}
		.files td:hover{ background:#efefef;}
		.files td a{ overflow:hidden;}
		.files td img{ vertical-align:middle;}
		
		/*
			内容块
		*/
		.files td .box{ padding:10px; }
		
		/*
			预览
		*/
		.files td .preview{ width:140px; height:130px; display:block; margin:0 auto 5px auto; overflow:hidden;}
			.files td .preview img{ height:130px;}
			.files td .preview embed{ vertical-align:middle; width:100%; height:135px;}
		
		/*
			名片
		*/
		.files td .card{ padding:20px; background:#fff; line-height:20px; text-align:left;}
			.files td .card .avatar{ padding:2px; float:left; margin-right:10px;}
			.files td .card strong{ color:#333;}
		
		/*
			选中
		*/
		.files td.sel{ background:#FFFFCC; border:#ccc solid 1px;}
		
	/*
		文件列表
	*/
	/*
	.files{ list-style:none; clear:both;}
		.files li{ width:185px; height:150px; overflow:hidden; float:left; padding:10px; background:#fff url(pic/new.gif) 185px 150px no-repeat; border:#ccc solid 1px; margin:0 5px 10px 5px; line-height:20px;}
		.files li a{ display:inline-block; overflow:hidden;}
		.files li embed{ vertical-align:middle; width:100%; height:135px;}
		.files li img{ vertical-align:middle;}
			.files li img.preview{ height:130px; display:block; margin:0 auto;}
		.files li.sel{ background:#FFFFCC; border:#ccc solid 1px;}
		*/
	
	/*
		图片专辑
	*/
	.album{ list-style:none; clear:both;}
		.album li{ text-align:center; float:left; padding:10px; background:#fff; line-height:20px; margin:10px 5px 10px 0; border:#ccc solid 1px;}
		.album li.img{ width:210px; height:150px;}
			.album li.img img{ height:130px; display:block;}
			
		.album li.sel{ background:#FFFFCC; border:#ccc solid 1px;}
		
		.album li span.del{ background:#333; color:#fff; padding:2px;}
	
	/*
		权限
	*/
	.func{ margin:0 0;}
		.func li{ line-height:25px; margin-right:10px; display:inline;}
		.func li strong{ color:#519506; display:inline-block; width:60px; text-align:right;}

	/********* page ********/

	/*
		错误信息_new
	*/
	#error{ background:#fff url(pic/error.gif) 17px 15px no-repeat; padding:10px 10px 10px 70px; color:#666; border:#E94700 solid 1px; margin:5px 0; clear:both; word-break:break-all; overflow:hidden;}
		#error em{ color:red; font-size:110%;}
		
	/*
		皮肤列表_new
	*/
	#theme{ color:#666; overflow:hidden; zoom:1; }
		#theme li{ width:150px; height:140px; float:left; text-align:center; margin:5px; cursor:pointer; padding:10px; background:#fff; overflow:hidden; }
		#theme li.active{background:url(pic/sprite.png) -10px -10px no-repeat; color:#333;}
		#theme li img{ vertical-align:middle; border:#999 solid 1px; display:block; width:150px; margin:0 auto 5px auto; }

	/*
		头像_new
	*/
	#avatars{ overflow:hidden; }
		#avatars li{ line-height:22px; width:60px; float:left; font-size:10px; text-align:center; height:60px; overflow:hidden; cursor:pointer; padding:10px;background:#fff;}
		#avatars li.active{background:#fff url(pic/sprite.png) -190px -10px no-repeat; color:#333;}
			#avatars li img{ vertical-align:middle;}

	/*
		系统模块_new
	*/
	#module{ overflow:hidden; line-height:22px; }
	
		#module p{ text-align:center; padding:20px 0;}
	
		#module li{ overflow:hidden; padding:10px; zoom:1;}
		#module li.zebra{ background:#efefef; color:#333;}
			#module li img{ vertical-align:middle; margin-right:10px;}
			#module li span{ color:#fff; font:10px "Lucida Grande", Verdana; background:#999; padding:0 2px 2px 2px;}

			#module li .icon{ width:80px; text-align:center; float:left; }
			#module li .name{ width:350px; float:left; color:#999; }
			#module li .name strong{ color:#000; }
			#module li .site{ width:200px; float:left; }
			#module li .plus{ width:80px; text-align:center; float:left; }

			#module li button{ border:none;  cursor:pointer; margin:15px 0; width:82px; height:27px; color:#fff; font-size:14px; text-align:center; background:url(pic/sprite.png) -340px -110px no-repeat;}
				#module li button.added{ background:url(pic/sprite.png) -340px -143px no-repeat; color:#666;}
				
	/*
		用户模块_new
	*/
	#addons{ overflow:hidden; background:url(pic/quick.png) repeat-x; }
		#addons li{ text-align:center; background:#fff; line-height:18px; float:left; width:62px; height:85px; margin:1px; margin-bottom:10px; border:#ccc solid 1px; padding:10px;}
		#addons li.temp{ border:#999 dashed 1px; }
		#addons li.light{ border:#0059A5 solid 1px; background:#efefef; }
			#addons li img{  margin-bottom:4px; }	
			#addons li a{ display:block; margin:0; color:#fff; text-decoration:none; }
			#addons li span{ display:block; padding:2px 1px 0 1px; background:url(pic/sprite.png) -280px -50px no-repeat; }

	/*
		小工具_new
	*/
	#widget{ line-height:20px;}
		#widget dt{ background:#F2F7FC; padding:5px;}
		#widget dd{ padding:10px 0; margin:0;}

		#widget ul{ padding:0; margin:0; line-height:30px; overflow:hidden; zoom:1;}
		#widget li{ color:#333; text-align:center; width:25%; margin:5px 0; float:left;}
			#widget li img{ vertical-align:middle; width:120px; height:120px;}
			#widget li button{ border:none; cursor:pointer; margin:5px 0; width:82px; height:27px; color:#fff; font-size:14px; text-align:center; background:url(pic/sprite.png) -340px -110px no-repeat;}
				#widget li button.added{ background:url(pic/sprite.png) -340px -143px no-repeat; color:#666;}
				
	/*
		任务_new
	*/
	#tasks{ line-height:20px;}
		#tasks dt{ background:#F2F7FC; padding:5px;}
		#tasks dd{ padding:10px 0; margin:0;}

		#tasks ul{ padding:0; margin:0; text-align:center; line-height:25px; overflow:hidden;}
		#tasks li{ color:#333; width:25%; float:left; margin-bottom:10px;}
			#tasks li img{ vertical-align:middle;}
			#tasks li button{ border:none;  cursor:pointer; width:56px; height:23px; color:#fff; font-size:13px; text-align:center; background:url(pic/sprite.png) -430px -110px no-repeat;}
				#tasks li button.added{ background:url(pic/sprite.png) -430px -143px no-repeat; color:#666;}
			
	
	/*
		公告板
	*/
	#board{ background:#FFFFCC; padding:5px; line-height:20px; color:#FF9900; margin:5px 0; clear:both; overflow:hidden;}
		#board strong{ font-size:14px; line-height:25px; color:#333;}
		
		#board img.logo{float:left; margin:5px 10px;}
		#board div.news{float:right; border-left:#ddd dashed 1px; width:300px; color:#999; margin:5px; padding:2px 10px;}
		
		#calendar{ margin:5px 10px 5px 5px; background:url(pic/bg.png) -80px -320px no-repeat; width:50px; height:60px; line-height:20px; float:left; text-align:center;}
			#calendar span{ color:#333333; text-align:center; display:block;}
			#calendar .month{ color:#fff;}
			#calendar .day{ font-size:22px; line-height:22px;}
	
	/*
		快捷菜单
	*/
	#quick{ width:760px; float:left; overflow:hidden;}
		#quick dl{ background:#fff; line-height:20px; color:#666; border:#B7CDE5 solid 1px; margin:0 0 10px 0; clear:both; overflow:hidden;}	
			#quick dt{ background:#F3F7FD; line-height:30px; margin:0; font-size:12px; padding:0 10px; overflow:hidden;}
				#quick dt .more{ float:right;}
				#quick dt .tab{ float:right;}
					#quick dt .tab a{ float:left; width:8px; height:8px; margin:11px 5px; background:url(pic/bg.png) -145px -150px no-repeat;}
					#quick dt .tab a.active{ background:url(pic/bg.png) -125px -150px no-repeat;}
			
			#quick dd{ padding:0; margin:0;}
				#quick dd ul{ color:#999; padding:0 0 5px 0; margin:5px; overflow:hidden;}
				#quick dd ul li{ float:left; margin:5px 5px; width:139px; overflow:hidden;}
				#quick dd ul span{color:#999;}
				#quick dd ul a{ border:#efefef solid 1px; width:127px; height:100%; display:block; padding:5px; text-decoration: none;}
				#quick dd ul a:hover{ border:#4B90B2 solid 1px; border:#ccc solid 1px; background:#F5F5F5; background__:url(pic/bg.png) right -710px no-repeat;}
				#quick dd ul img{ float:left; margin:5px 5px; vertical-align:middle; width:32px; height:32px;}
	
	/*
		右侧面板
	*/
	#panel{ float:left; overflow:hidden; width:340px; height:340px; margin-left:10px; border:#B7CDE5 solid 1px; background:#fff; margin-right:-5px;}
		#panel ol{background:#F3F7FD; line-height:30px; overflow:hidden; margin:0; padding:0; width:100%;}
		#panel ol li{float:left; color:#666; cursor:pointer; padding:0 9px;}
		#panel ol li.active{text-decoration:none; color:#1E5494; background:#B7CDE5;}
		
		#panel ul{ }
			#panel ul li{padding:5px; margin:5px; line-height:20px;}
			#panel ul li.adsense{ text-align:center;}
		
		/* 广告 324 * 150 */
		#panel div{ text-align:center; margin:10px; overflow:hidden; padding-top:10px; border-top:#ccc dashed 1px; display:none;}
		
	
	/*
		分组
	*/
	#groups{ }
		#groups li{ float:left; text-align:center; cursor:pointer; padding:5px;background:#fff;}
		#groups li.active{background:#ddd;}
			#groups li img{ vertical-align:middle;}

	
	/*
		推送信息
	*/
	#push{padding:5px 10px;; position:absolute; background:#fff;color:#666;font-family:Verdana;line-height:20px; background:url(pic/push.png) 0 0 no-repeat; top:450px; left:987px; width:315px; height:120px; z-index:120;}
	
		#push dl{ padding:0; margin:0;}	
			#push dl dt{ margin-bottom:5px;}
				#push dl dt strong{ color:#333;}
				#push dl dt a{ width:9px; height:9px; display:inline-block; overflow:hidden; float:right; background:url(pic/push.png) -350px -40px no-repeat; margin-top:2px;}	
				#push dl dt a:hover{background:url(pic/push.png) -380px -40px no-repeat;}
		#push dl dd{ padding:0; margin:0;}
		
		#push div{ text-align:right;}	
			#push div span{ display:inline-block; padding:0 5px; margin:0 5px; font-size:10px;}	
			#push div a{ display:inline-block; width:5px; height:9px; overflow:hidden;}	
			
			#push div a.prev{ background:url(pic/push.png) -350px 0 no-repeat;}	
			#push div a.prev:hover{ background:url(pic/push.png) -350px -20px no-repeat;}	
			
			#push div a.next{ background:url(pic/push.png) -380px 0 no-repeat;}	
			#push div a.next:hover{ background:url(pic/push.png) -380px -20px no-repeat;}	
	
		#push ul{ padding:9; margin:0;  height:67px; overflow:hidden;}
		#push ul li{ padding:9; margin:0;}
		#push ul li img{ float:left; border:#DDE4E9 solid 1px; padding:2px; margin-right:10px;}
		#push ul li strong a{ color:#002B4D;}
		#push ul li a{ color:#333; text-decoration:none;}
	
	/*
		工具提示信息
	*/
	.tips{padding:10px; margin:0 0 0 20px; position:absolute; border:#699596 solid 1px; background:#fff;color:#666;font-family:Verdana;line-height:22px; background:#fff url(pic/bg.png) 0 -850px repeat-x; z-index:120;}
		.tips strong{ color:#333;}
	
	/*
		用户名片
	*/
	#card{padding:10px;margin:0 0 0 20px;position:absolute;border:#4B90B2 solid 1px; border-top-width:3px; background:#F2F6F9;color:#666;font-family:Verdana;line-height:22px; z-index:120; width:200px; filter:alpha(opacity=90);opacity:0.9;}
		#card strong{ color:#333;}
		#card img.avatar{ padding:2px; border:#ccc solid 1px;}
		
	/*
		带箭头提示信息
	*/
	/*
	.info { color:#444; padding:13px 10px 10px 10px; line-height:16px; background:url(pic/bg.png) 0 -410px no-repeat; width:170px; height:30px; font-size:12px; position:absolute; z-index:100; overflow:hidden;}
		.info a{ text-decoration:underline;}
		*/
	
	/*
		页面消息_new
	*/	
	#message{ margin:0; position:absolute; line-height:54px; height:54px; z-index:110; overflow:hidden; font-family:"Microsoft YaHei"; color:#333; top:300px; left:200px; font-size:16px;}
		#message div{ height:54px; float:left;}
		#message .s{ background:url(pic/message.png) -6px 0 no-repeat; width:45px;}
		#message .c{ background:url(pic/message.png) 0 -161px repeat-x; padding:0 10px;}
		#message .e{ background:url(pic/message.png) 0 0 no-repeat; width:5px;}
		
		#message.info .s{ background:url(pic/message.png) -6px -54px no-repeat;}
		#message.wrong .s{ background:url(pic/message.png) -6px -108px no-repeat;}
		#message.success .s{ background:url(pic/message.png) -6px 0 no-repeat;}
	
	/*
		汽泡提示
		用于用户操作引导和其他提示信息
	*/
	#bubble{ margin:0;position:absolute; font-size:110%; background:url(pic/block.png) -10px -10px no-repeat; color:#F60; line-height:21px; padding:10px 5px 10px 20px; width:275px; height:50px; z-index:110; position:absolute; top:200px; left:300px;}
	
	/*
		密码极别
	*/
	#level{ background:#efefef; width:235px; height:12px; margin-top:5px; overflow:hidden;}
		#level.l0{ background:url(pic/block.png) -20px -154px no-repeat;}
		#level.l1{ background:url(pic/block.png) -20px -134px no-repeat;}
		#level.l2{ background:url(pic/block.png) -20px -112px no-repeat;}
		#level.l3{ background:url(pic/block.png) -20px -92px no-repeat;}
	
	/*
		插件
	*/
	/*
	#plugins{}
		#plugins li{border:1px solid #efefef; width:48%; margin:5px 10px 5px 0;height:125px;float:left; line-height:20px;background:#fff; padding:0px;}
			#plugins li.use{ background:#FFFFCC; border:#CC9900 solid 1px;}
			#plugins li span.name{ color:green;font-weight:bold;line-height:30px;}
			#plugins li span.set{ color:#FF6600; cursor:pointer;line-height:30px;}
			#plugins li img.demo{ width:150px; height:111px; float:left;margin:5px 5px 180px 5px; border:1px solid #ccc;display:block;}
	
	/*
		错误信息
	*/
	#warning{padding:0 0 5px 0; margin:0;position:absolute; width:400px; background:#fff; border:1px solid #538BC8; z-index:10001; -moz-user-select: none !important; color:#333;}
		#warning dt{background:#006699 url(pic/bg.png) 0 -590px repeat-x;line-height:30px;text-indent:1em;color:#fff; font-weight:bold;}
		#warning dd{word-wrap:break-word;word-break:break-all; padding:10px; color:#333;line-height:24px; margin:5px;}
		#warning .bar{text-align:right;padding:5px;}
		#warning strong{ color:#333;}
		#warning button{background:url(pic/bg.png) -30px -170px repeat; color:#fff; text-align:center; width:62px; height:25px; border:none; line-height:20px; cursor:pointer;}
		
	/*
		浮动窗口
	*/
	/*
	#window{padding:0; margin:0;position:absolute; background:#fff ; border:1px solid #215978; border:2px solid #7698C0; z-index:10001; -moz-user-select: none !important; color:#fff;}
		#window .title{background:#006699 url(pic/bg.png) 0 -590px repeat-x; padding:6px; text-indent:5px; line-height:16px; cursor:move;}
		#window .title strong{font-weight:normal;}
		
			#window .close{cursor:pointer; text-decoration:underline;background:#006699 url(pic/bg.png) -140px -320px repeat-x; width:13px; height:13px; display:block; font-size:0; float:right;}
			#window .close em{ display:none;}
			
		#window .bar{padding:4px 5px;background:#d8d8d8;border-bottom:#ccc solid 1px;}
			#window .bar input{padding:3px;border:#ccc solid 1px;font-size:11px;height:20px;}
			#window .bar img{vertical-align:middle;cursor:pointer;}
		#window .box{padding:0px;}
		
	/*
		操作窗口
	*/
	/*
	#dialog{padding:0; margin:0;position:absolute; background:#fff; border:1px solid #4D7720; border:2px solid #62AE06; z-index:10001; -moz-user-select: none !important; color:#fff;}
		#dialog .title{background:#006699 url(pic/bg.png) 0 -780px repeat-x; padding:6px; text-indent:5px; line-height:16px; cursor:move;}
			#dialog .close{cursor:pointer;text-decoration:underline; background:#006699 url(pic/bg.png) -175px -280px repeat-x; width:13px; height:13px; display:inline-block; font-size:0; float:right;}
			#dialog .close em{ display:none;}
			
		#dialog .bar{padding:4px 5px;background:#d8d8d8;border-bottom:#ccc solid 1px;}
			#dialog .bar input{padding:3px;border:#ccc solid 1px;font-size:11px;height:20px;}
			#dialog .bar img{vertical-align:middle;cursor:pointer;}
		#dialog .box{padding:0px;}
	
	/********* class ********/
	
	/* 子标题 */
	.item{ font-size:12px; font-weight:bold; line-height:30px; margin:0; color:#09C;}
	
	/*
		插件容器
	*/
	.package{ width:200px; height:200px; position:absolute; background:#fff;}
		.package dl{ border:#57B11E solid 1px; margin:0; padding:0; height:100%; /*FILTER: progid:DXImageTransform.Microsoft.Shadow(direction=135,color=#999999,strength=2);*/}		
		.package dl dt{background:#57B11E; padding:6px; text-indent:5px; line-height:16px; cursor:move; color:#fff;}
			.package dl dt span{ float:right; width:12px; height:12px; overflow:hidden; display:inline-block; cursor:pointer; margin:2px 5px;}
			.package dl dt .minimize{ background:url(pic/bg.png) -15px -394px;}
			.package dl dt .maximize{ background:url(pic/bg.png) -30px -394px;}
			.package dl dt .remove{ background:url(pic/bg.png) 0 -394px;}
			.package dl dt .toggle{ background:url(pic/bg.png) -50px -394px;}
			.package dl dd{ padding:5px; margin:0; background:#fff;}
	
	/*
		虚化背景
	*/
	.background{background:url(pic/thick.png); filter:alpha(opacity=20); opacity:0.2; top:0;left:0;}
	
	/*
		上传组件
	*/
	.browse{position:absolute; border:#4C94EA solid 2px; line-height:30px; color:#333; background:#fff url(pic/bg.png) 0 -850px repeat-x; padding:10px; width:220px;}
		.browse em{ width:20px; height:20px; float:right; cursor:pointer;}
	
	.upload{cursor:pointer; color:#4D5D2C;}
		.browse img,.upload img{ vertical-align:middle;}

	/*clear*/
	.clear{clear:both; width:80%; margin:auto; height:0px; overflow:hidden;}
	
	/*百分比*/
	.plan{background:url(pic/bg.png) -30px -150px repeat-x; height:7px; width:90px; overflow:hidden; vertical-align:middle;}
	.ratio{height:12px; display:block; float:left; background:#ccc url(pic/ratio.png) repeat; width:100px; margin:0 5px; overflow:hidden; text-indent:0;}
		.ratio span{ display:inline-block; height:12px;}
	
	/*日期与时间控件*/
	.date{background:#F9F9F9 url(pic/bg.png) -166px -517px no-repeat; width:75px; padding-left:25px; ime-mode:disabled;}	
	.time{background:#F9F9F9 url(pic/bg.png) -166px -687px no-repeat; width:40px; padding-left:25px; ime-mode:disabled;}
	.digi{background:#F9F9F9 url(pic/bg.png) -166px -135px no-repeat; width:60px; padding-left:25px; ime-mode:disabled; cursor:default;}
	
	.link{background:#F9F9F9 url(pic/bg.png) -166px -717px no-repeat; padding-left:27px; }

	/*
		常规按钮
	*/
	.signin,.submit,.button,.cancel{font-size:14px; border:none; background:url(pic/bg.png) -90px -280px repeat-x; cursor:pointer; color:#fff; padding:0; font-family:Verdana, Geneva, sans-serif;}
	
	.signin{width:82px; height:30px; line-height:30px;}
	
	.submit{width:82px; height:30px; line-height:30px; background:url(pic/bg.png) 0 -280px repeat-x;}
	
	.button{width:61px; height:25px; line-height:25px; font-size:12px; background:url(pic/bg.png) -30px -170px repeat-x; margin:0 5px;}
	
	.cancel{width:61px; height:25px; line-height:25px; font-size:12px; color:#666; background:url(pic/bg.png) -100px -170px repeat-x; margin:0 5px;}
	
	/* 扩展按钮 */
	.post,.list,.ctrl,.search{font-size:12px;line-height:23px; padding-left:15px; width:85px!important; *width:75px!important; width:85px; height:23px; margin:0 5px; color:#2F5F82; border:none; background:url(pic/bg.png) -10px -540px repeat-x; cursor:pointer; vertical-align:middle; }
	
	.post{background:url(pic/bg.png) -100px -540px repeat-x;}
	.list{background:url(pic/bg.png) -100px -570px repeat-x;}
	.ctrl{background:url(pic/bg.png) -10px -570px repeat-x;}
	.search{background:url(pic/bg.png) -10px -600px repeat-x; color:#008000;}
	
	/* 文本样式 */
	.text-no{ color:red;}
	.text-yes{ color:#339900;}
	.text-gray{ color:#999;}
	.text-key{color:#F60;}
	.text-small{font-size:12px;}
	.text-bold{font-size:12px;font-weight:bold;}	
	.text-main{font-size:14px;font-weight:bold; color:#390;}
	
	.text-tag{ border:#ccc solid 1px; padding:3px 2px; text-decoration:none;}
	.text-tag:hover{ color:#006; border:#999 solid 1px;}
	.text-tag:link,.text-tag:visited{text-decoration:none;}
	
	.highlight{ color:red;}

	.captcha{ime-mode:disabled;}
	.txtnobd{ border:1px solid #FFF; background:#FFF; cursor:pointer; }
	.radio, .checkbox{ border:none; background:none; vertical-align:middle; }
	.checkbox{ height:14px; }
	.btn{ margin:3px 0; padding:2px 5px; *padding:4px 5px 1px; border-color:#ddd #666 #666 #ddd; background:#DDD; color:#000; cursor:pointer; vertical-align:middle; }
