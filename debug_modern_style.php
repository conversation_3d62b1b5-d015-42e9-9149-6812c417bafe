<?php
/**
 * 现代化样式调试工具
 * 专门用于排查美化后前端空白问题
 */

// 检查是否在正确的目录中
if (!file_exists('template/form/show.htm')) {
    die('错误：请在插件根目录中运行此脚本');
}

// 错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>现代化样式调试工具</title>";
echo "<style>
body{font-family:Arial,sans-serif;margin:20px;background:#f5f5f5;}
.container{max-width:1000px;margin:0 auto;background:white;padding:20px;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);}
.section{margin:20px 0;padding:15px;border:1px solid #ddd;border-radius:5px;}
.success{background:#d4edda;color:#155724;border-color:#c3e6cb;}
.error{background:#f8d7da;color:#721c24;border-color:#f5c6cb;}
.warning{background:#fff3cd;color:#856404;border-color:#ffeaa7;}
.info{background:#d1ecf1;color:#0c5460;border-color:#bee5eb;}
.code{background:#f8f9fa;padding:10px;border-radius:3px;font-family:monospace;font-size:12px;overflow-x:auto;}
h1,h2,h3{color:#333;}
.btn{background:#007cba;color:white;padding:8px 16px;text-decoration:none;border-radius:3px;display:inline-block;margin:5px;}
.btn:hover{background:#005a87;}
table{width:100%;border-collapse:collapse;margin:10px 0;}
th,td{padding:8px;text-align:left;border-bottom:1px solid #ddd;}
th{background:#f8f9fa;font-weight:bold;}
.status-ok{color:#28a745;font-weight:bold;}
.status-error{color:#dc3545;font-weight:bold;}
.status-warning{color:#ffc107;font-weight:bold;}
</style>";
echo "</head><body>";

echo "<div class='container'>";
echo "<h1>🔍 现代化样式调试工具</h1>";

// 1. 检查文件存在性
echo "<div class='section'>";
echo "<h2>📁 文件检查</h2>";

$files_to_check = [
    'css/hejin-forms-modern.css' => '现代化主样式文件',
    'css/hejin-forms-async.css' => '异步功能样式文件',
    'js/hejin-forms-modern.js' => '现代化交互脚本',
    'js/hejin-forms-async-compatible.js' => '异步兼容脚本',
    'template/form/show.htm' => '当前模板文件',
    'template/form/show_modern.htm' => '现代化模板文件'
];

echo "<table>";
echo "<tr><th>文件</th><th>描述</th><th>状态</th><th>大小</th></tr>";

foreach ($files_to_check as $file => $desc) {
    echo "<tr>";
    echo "<td><code>$file</code></td>";
    echo "<td>$desc</td>";
    
    if (file_exists($file)) {
        $size = filesize($file);
        if ($size > 0) {
            echo "<td class='status-ok'>✓ 存在</td>";
            echo "<td>" . number_format($size) . " bytes</td>";
        } else {
            echo "<td class='status-error'>✗ 文件为空</td>";
            echo "<td>0 bytes</td>";
        }
    } else {
        echo "<td class='status-error'>✗ 不存在</td>";
        echo "<td>-</td>";
    }
    echo "</tr>";
}
echo "</table>";
echo "</div>";

// 2. 检查模板文件内容
echo "<div class='section'>";
echo "<h2>📄 模板文件分析</h2>";

$template_file = 'template/form/show.htm';
if (file_exists($template_file)) {
    $content = file_get_contents($template_file);
    
    echo "<h3>当前模板文件：$template_file</h3>";
    
    // 检查是否包含现代化样式
    $has_modern_css = strpos($content, 'hejin-forms-modern.css') !== false;
    $has_modern_js = strpos($content, 'hejin-forms-modern.js') !== false;
    $has_async_css = strpos($content, 'hejin-forms-async.css') !== false;
    $has_async_js = strpos($content, 'hejin-forms-async') !== false;
    
    echo "<table>";
    echo "<tr><th>检查项</th><th>状态</th></tr>";
    echo "<tr><td>包含现代化CSS</td><td>" . ($has_modern_css ? "<span class='status-ok'>✓ 是</span>" : "<span class='status-error'>✗ 否</span>") . "</td></tr>";
    echo "<tr><td>包含现代化JS</td><td>" . ($has_modern_js ? "<span class='status-ok'>✓ 是</span>" : "<span class='status-error'>✗ 否</span>") . "</td></tr>";
    echo "<tr><td>包含异步CSS</td><td>" . ($has_async_css ? "<span class='status-ok'>✓ 是</span>" : "<span class='status-error'>✗ 否</span>") . "</td></tr>";
    echo "<tr><td>包含异步JS</td><td>" . ($has_async_js ? "<span class='status-ok'>✓ 是</span>" : "<span class='status-error'>✗ 否</span>") . "</td></tr>";
    echo "</table>";
    
    // 显示模板头部
    $lines = explode("\n", $content);
    $header_lines = array_slice($lines, 0, 20);
    
    echo "<h4>模板文件头部（前20行）：</h4>";
    echo "<div class='code'>";
    foreach ($header_lines as $i => $line) {
        $line_num = $i + 1;
        echo "<div><span style='color:#666;'>$line_num:</span> " . htmlspecialchars($line) . "</div>";
    }
    echo "</div>";
    
} else {
    echo "<div class='error'>模板文件不存在！</div>";
}
echo "</div>";

// 3. 检查CSS语法
echo "<div class='section'>";
echo "<h2>🎨 CSS文件检查</h2>";

$css_files = ['css/hejin-forms-modern.css', 'css/hejin-forms-async.css'];

foreach ($css_files as $css_file) {
    if (file_exists($css_file)) {
        $css_content = file_get_contents($css_file);
        
        echo "<h4>$css_file</h4>";
        
        // 简单的CSS语法检查
        $open_braces = substr_count($css_content, '{');
        $close_braces = substr_count($css_content, '}');
        $has_charset = strpos($css_content, '@charset') !== false || strpos($css_content, 'charset') !== false;
        
        echo "<table>";
        echo "<tr><th>检查项</th><th>结果</th></tr>";
        echo "<tr><td>文件大小</td><td>" . number_format(strlen($css_content)) . " 字符</td></tr>";
        echo "<tr><td>开括号数量</td><td>$open_braces</td></tr>";
        echo "<tr><td>闭括号数量</td><td>$close_braces</td></tr>";
        echo "<tr><td>括号匹配</td><td>" . ($open_braces === $close_braces ? "<span class='status-ok'>✓ 匹配</span>" : "<span class='status-error'>✗ 不匹配</span>") . "</td></tr>";
        echo "</table>";
        
        // 显示CSS开头
        $css_lines = explode("\n", $css_content);
        $css_header = array_slice($css_lines, 0, 10);
        
        echo "<h5>CSS文件开头：</h5>";
        echo "<div class='code'>";
        foreach ($css_header as $i => $line) {
            $line_num = $i + 1;
            echo "<div><span style='color:#666;'>$line_num:</span> " . htmlspecialchars($line) . "</div>";
        }
        echo "</div>";
        
    } else {
        echo "<div class='error'>CSS文件 $css_file 不存在</div>";
    }
}
echo "</div>";

// 4. 检查JavaScript语法
echo "<div class='section'>";
echo "<h2>⚡ JavaScript文件检查</h2>";

$js_files = ['js/hejin-forms-modern.js', 'js/hejin-forms-async-compatible.js'];

foreach ($js_files as $js_file) {
    if (file_exists($js_file)) {
        $js_content = file_get_contents($js_file);
        
        echo "<h4>$js_file</h4>";
        
        // 简单的JS语法检查
        $has_syntax_error = false;
        $error_message = '';
        
        // 检查常见语法错误
        if (substr_count($js_content, '{') !== substr_count($js_content, '}')) {
            $has_syntax_error = true;
            $error_message .= '括号不匹配; ';
        }
        
        if (substr_count($js_content, '(') !== substr_count($js_content, ')')) {
            $has_syntax_error = true;
            $error_message .= '圆括号不匹配; ';
        }
        
        echo "<table>";
        echo "<tr><th>检查项</th><th>结果</th></tr>";
        echo "<tr><td>文件大小</td><td>" . number_format(strlen($js_content)) . " 字符</td></tr>";
        echo "<tr><td>语法检查</td><td>" . ($has_syntax_error ? "<span class='status-error'>✗ $error_message</span>" : "<span class='status-ok'>✓ 基础检查通过</span>") . "</td></tr>";
        echo "</table>";
        
    } else {
        echo "<div class='error'>JavaScript文件 $js_file 不存在</div>";
    }
}
echo "</div>";

// 5. 生成修复建议
echo "<div class='section'>";
echo "<h2>🔧 修复建议</h2>";

$issues = [];
$solutions = [];

// 检查文件是否存在
if (!file_exists('css/hejin-forms-modern.css')) {
    $issues[] = '现代化CSS文件缺失';
    $solutions[] = '重新上传 css/hejin-forms-modern.css 文件';
}

if (!file_exists('js/hejin-forms-modern.js')) {
    $issues[] = '现代化JS文件缺失';
    $solutions[] = '重新上传 js/hejin-forms-modern.js 文件';
}

// 检查模板文件
if (file_exists('template/form/show.htm')) {
    $content = file_get_contents('template/form/show.htm');
    if (strpos($content, 'hejin-forms-modern.css') === false) {
        $issues[] = '模板文件未引用现代化CSS';
        $solutions[] = '在模板文件中添加现代化CSS引用';
    }
}

if (empty($issues)) {
    echo "<div class='success'>✓ 未发现明显问题</div>";
} else {
    echo "<div class='warning'>";
    echo "<h4>发现的问题：</h4>";
    echo "<ul>";
    foreach ($issues as $issue) {
        echo "<li>$issue</li>";
    }
    echo "</ul>";
    
    echo "<h4>建议的解决方案：</h4>";
    echo "<ol>";
    foreach ($solutions as $solution) {
        echo "<li>$solution</li>";
    }
    echo "</ol>";
    echo "</div>";
}

echo "</div>";

// 6. 快速修复工具
echo "<div class='section'>";
echo "<h2>⚡ 快速修复工具</h2>";

echo "<div class='info'>";
echo "<h4>可用的修复操作：</h4>";
echo "<a href='?action=restore_original' class='btn'>恢复原版样式</a>";
echo "<a href='?action=apply_modern' class='btn'>重新应用现代化样式</a>";
echo "<a href='?action=check_permissions' class='btn'>检查文件权限</a>";
echo "<a href='style_switcher.php' class='btn'>打开样式切换器</a>";
echo "</div>";

// 处理快速修复操作
if (isset($_GET['action'])) {
    echo "<div class='section'>";
    echo "<h3>修复操作结果</h3>";
    
    switch ($_GET['action']) {
        case 'restore_original':
            // 恢复原版样式
            if (file_exists('template/form/show.htm.backup')) {
                copy('template/form/show.htm.backup', 'template/form/show.htm');
                echo "<div class='success'>✓ 已恢复原版样式</div>";
            } else {
                echo "<div class='error'>✗ 找不到原版样式备份</div>";
            }
            break;
            
        case 'apply_modern':
            // 重新应用现代化样式
            if (file_exists('template/form/show_modern.htm')) {
                copy('template/form/show_modern.htm', 'template/form/show.htm');
                echo "<div class='success'>✓ 已重新应用现代化样式</div>";
            } else {
                echo "<div class='error'>✗ 现代化模板文件不存在</div>";
            }
            break;
            
        case 'check_permissions':
            // 检查文件权限
            $dirs = ['css', 'js', 'template', 'template/form'];
            echo "<table>";
            echo "<tr><th>目录</th><th>权限</th><th>可写</th></tr>";
            foreach ($dirs as $dir) {
                if (is_dir($dir)) {
                    $perms = substr(sprintf('%o', fileperms($dir)), -4);
                    $writable = is_writable($dir) ? '是' : '否';
                    echo "<tr><td>$dir</td><td>$perms</td><td>$writable</td></tr>";
                }
            }
            echo "</table>";
            break;
    }
    echo "</div>";
}

echo "</div>";

// 7. 浏览器测试
echo "<div class='section'>";
echo "<h2>🌐 浏览器测试</h2>";

echo "<div class='info'>";
echo "<h4>请在浏览器中测试以下链接：</h4>";
echo "<ul>";
echo "<li><a href='css/hejin-forms-modern.css' target='_blank'>测试现代化CSS文件</a></li>";
echo "<li><a href='js/hejin-forms-modern.js' target='_blank'>测试现代化JS文件</a></li>";
echo "<li><a href='test_form.html' target='_blank'>测试表单页面</a></li>";
echo "<li><a href='test_compatibility.html' target='_blank'>兼容性测试</a></li>";
echo "</ul>";
echo "</div>";

echo "<div class='warning'>";
echo "<h4>⚠️ 调试步骤：</h4>";
echo "<ol>";
echo "<li>按F12打开浏览器开发者工具</li>";
echo "<li>查看Console标签是否有错误信息</li>";
echo "<li>查看Network标签检查文件加载情况</li>";
echo "<li>查看Elements标签检查HTML结构</li>";
echo "</ol>";
echo "</div>";

echo "</div>";

echo "</div>"; // container
echo "</body></html>";
?>
