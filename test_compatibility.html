<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>和金表单异步版本兼容性测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .test-item:last-child {
            border-bottom: none;
        }
        .test-status {
            padding: 4px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-pass {
            background: #d4edda;
            color: #155724;
        }
        .status-fail {
            background: #f8d7da;
            color: #721c24;
        }
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
        .status-unknown {
            background: #e2e3e5;
            color: #383d41;
        }
        .browser-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .feature-test {
            margin: 10px 0;
        }
        .run-test-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .run-test-btn:hover {
            background: #2980b9;
        }
        .test-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>和金表单异步版本兼容性测试</h1>
        
        <div class="browser-info">
            <h3>浏览器信息</h3>
            <div id="browser-details"></div>
        </div>

        <div class="test-section">
            <h3>基础功能测试</h3>
            <div class="test-item">
                <span>ES6语法支持</span>
                <span class="test-status" id="es6-status">未测试</span>
            </div>
            <div class="test-item">
                <span>Promise支持</span>
                <span class="test-status" id="promise-status">未测试</span>
            </div>
            <div class="test-item">
                <span>Fetch API支持</span>
                <span class="test-status" id="fetch-status">未测试</span>
            </div>
            <div class="test-item">
                <span>FormData支持</span>
                <span class="test-status" id="formdata-status">未测试</span>
            </div>
            <div class="test-item">
                <span>LocalStorage支持</span>
                <span class="test-status" id="localstorage-status">未测试</span>
            </div>
        </div>

        <div class="test-section">
            <h3>文件上传功能测试</h3>
            <div class="test-item">
                <span>File API支持</span>
                <span class="test-status" id="fileapi-status">未测试</span>
            </div>
            <div class="test-item">
                <span>拖拽API支持</span>
                <span class="test-status" id="dragapi-status">未测试</span>
            </div>
            <div class="test-item">
                <span>XMLHttpRequest上传进度</span>
                <span class="test-status" id="xhr-progress-status">未测试</span>
            </div>
        </div>

        <div class="test-section">
            <h3>CSS功能测试</h3>
            <div class="test-item">
                <span>CSS3动画支持</span>
                <span class="test-status" id="css-animation-status">未测试</span>
            </div>
            <div class="test-item">
                <span>CSS3变换支持</span>
                <span class="test-status" id="css-transform-status">未测试</span>
            </div>
            <div class="test-item">
                <span>Flexbox支持</span>
                <span class="test-status" id="flexbox-status">未测试</span>
            </div>
        </div>

        <div class="test-section">
            <h3>移动设备兼容性</h3>
            <div class="test-item">
                <span>触摸事件支持</span>
                <span class="test-status" id="touch-status">未测试</span>
            </div>
            <div class="test-item">
                <span>视口缩放支持</span>
                <span class="test-status" id="viewport-status">未测试</span>
            </div>
        </div>

        <button class="run-test-btn" onclick="runAllTests()">运行所有测试</button>
        <button class="run-test-btn" onclick="clearLog()">清除日志</button>
        
        <div class="test-section">
            <h3>测试日志</h3>
            <div class="test-log" id="test-log"></div>
        </div>
    </div>

    <script>
        // 测试日志
        function log(message) {
            const logElement = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('test-log').textContent = '';
        }

        // 设置测试状态
        function setTestStatus(testId, status, message = '') {
            const element = document.getElementById(testId + '-status');
            element.className = 'test-status status-' + status;
            element.textContent = status === 'pass' ? '通过' : 
                                 status === 'fail' ? '失败' : 
                                 status === 'warning' ? '警告' : '未知';
            if (message) {
                element.title = message;
            }
        }

        // 显示浏览器信息
        function showBrowserInfo() {
            const info = {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine,
                screenResolution: `${screen.width}x${screen.height}`,
                windowSize: `${window.innerWidth}x${window.innerHeight}`
            };

            const browserDetails = document.getElementById('browser-details');
            browserDetails.innerHTML = `
                <p><strong>用户代理:</strong> ${info.userAgent}</p>
                <p><strong>平台:</strong> ${info.platform}</p>
                <p><strong>语言:</strong> ${info.language}</p>
                <p><strong>Cookie启用:</strong> ${info.cookieEnabled ? '是' : '否'}</p>
                <p><strong>在线状态:</strong> ${info.onLine ? '在线' : '离线'}</p>
                <p><strong>屏幕分辨率:</strong> ${info.screenResolution}</p>
                <p><strong>窗口大小:</strong> ${info.windowSize}</p>
            `;

            log('浏览器信息已显示');
        }

        // 基础功能测试
        function testBasicFeatures() {
            log('开始基础功能测试...');

            // ES6语法测试
            try {
                eval('const test = () => {}; class TestClass {}');
                setTestStatus('es6', 'pass');
                log('ES6语法支持: 通过');
            } catch (e) {
                setTestStatus('es6', 'fail', e.message);
                log('ES6语法支持: 失败 - ' + e.message);
            }

            // Promise测试
            if (typeof Promise !== 'undefined') {
                setTestStatus('promise', 'pass');
                log('Promise支持: 通过');
            } else {
                setTestStatus('promise', 'fail');
                log('Promise支持: 失败');
            }

            // Fetch API测试
            if (typeof fetch !== 'undefined') {
                setTestStatus('fetch', 'pass');
                log('Fetch API支持: 通过');
            } else {
                setTestStatus('fetch', 'warning', '可使用XMLHttpRequest替代');
                log('Fetch API支持: 警告 - 可使用XMLHttpRequest替代');
            }

            // FormData测试
            if (typeof FormData !== 'undefined') {
                setTestStatus('formdata', 'pass');
                log('FormData支持: 通过');
            } else {
                setTestStatus('formdata', 'fail');
                log('FormData支持: 失败');
            }

            // LocalStorage测试
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                setTestStatus('localstorage', 'pass');
                log('LocalStorage支持: 通过');
            } catch (e) {
                setTestStatus('localstorage', 'fail', e.message);
                log('LocalStorage支持: 失败 - ' + e.message);
            }
        }

        // 文件上传功能测试
        function testFileUpload() {
            log('开始文件上传功能测试...');

            // File API测试
            if (typeof File !== 'undefined' && typeof FileReader !== 'undefined') {
                setTestStatus('fileapi', 'pass');
                log('File API支持: 通过');
            } else {
                setTestStatus('fileapi', 'fail');
                log('File API支持: 失败');
            }

            // 拖拽API测试
            const div = document.createElement('div');
            if ('draggable' in div && typeof DataTransfer !== 'undefined') {
                setTestStatus('dragapi', 'pass');
                log('拖拽API支持: 通过');
            } else {
                setTestStatus('dragapi', 'fail');
                log('拖拽API支持: 失败');
            }

            // XMLHttpRequest上传进度测试
            const xhr = new XMLHttpRequest();
            if (xhr.upload && 'onprogress' in xhr.upload) {
                setTestStatus('xhr-progress', 'pass');
                log('XMLHttpRequest上传进度: 通过');
            } else {
                setTestStatus('xhr-progress', 'fail');
                log('XMLHttpRequest上传进度: 失败');
            }
        }

        // CSS功能测试
        function testCSSFeatures() {
            log('开始CSS功能测试...');

            const testElement = document.createElement('div');
            document.body.appendChild(testElement);

            // CSS3动画测试
            testElement.style.animation = 'test 1s';
            if (testElement.style.animation) {
                setTestStatus('css-animation', 'pass');
                log('CSS3动画支持: 通过');
            } else {
                setTestStatus('css-animation', 'fail');
                log('CSS3动画支持: 失败');
            }

            // CSS3变换测试
            testElement.style.transform = 'scale(1)';
            if (testElement.style.transform) {
                setTestStatus('css-transform', 'pass');
                log('CSS3变换支持: 通过');
            } else {
                setTestStatus('css-transform', 'fail');
                log('CSS3变换支持: 失败');
            }

            // Flexbox测试
            testElement.style.display = 'flex';
            if (testElement.style.display === 'flex') {
                setTestStatus('flexbox', 'pass');
                log('Flexbox支持: 通过');
            } else {
                setTestStatus('flexbox', 'fail');
                log('Flexbox支持: 失败');
            }

            document.body.removeChild(testElement);
        }

        // 移动设备兼容性测试
        function testMobileCompatibility() {
            log('开始移动设备兼容性测试...');

            // 触摸事件测试
            if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
                setTestStatus('touch', 'pass');
                log('触摸事件支持: 通过');
            } else {
                setTestStatus('touch', 'warning', '非触摸设备');
                log('触摸事件支持: 警告 - 非触摸设备');
            }

            // 视口缩放测试
            const viewport = document.querySelector('meta[name="viewport"]');
            if (viewport) {
                setTestStatus('viewport', 'pass');
                log('视口缩放支持: 通过');
            } else {
                setTestStatus('viewport', 'warning', '未设置viewport meta标签');
                log('视口缩放支持: 警告 - 未设置viewport meta标签');
            }
        }

        // 运行所有测试
        function runAllTests() {
            log('开始运行兼容性测试...');
            clearLog();
            
            showBrowserInfo();
            testBasicFeatures();
            testFileUpload();
            testCSSFeatures();
            testMobileCompatibility();
            
            log('所有测试完成！');
            
            // 生成测试报告
            generateReport();
        }

        // 生成测试报告
        function generateReport() {
            const statuses = document.querySelectorAll('.test-status');
            let passCount = 0;
            let failCount = 0;
            let warningCount = 0;

            statuses.forEach(status => {
                if (status.classList.contains('status-pass')) passCount++;
                else if (status.classList.contains('status-fail')) failCount++;
                else if (status.classList.contains('status-warning')) warningCount++;
            });

            log(`\n测试报告:`);
            log(`通过: ${passCount} 项`);
            log(`失败: ${failCount} 项`);
            log(`警告: ${warningCount} 项`);
            log(`总计: ${statuses.length} 项`);

            if (failCount === 0) {
                log('\n✅ 兼容性测试全部通过，可以正常使用异步功能！');
            } else if (failCount <= 2) {
                log('\n⚠️ 存在少量兼容性问题，建议升级浏览器或使用备用方案。');
            } else {
                log('\n❌ 存在较多兼容性问题，建议升级浏览器后再使用。');
            }
        }

        // 页面加载完成后显示浏览器信息
        document.addEventListener('DOMContentLoaded', function() {
            showBrowserInfo();
            log('兼容性测试页面已加载，点击"运行所有测试"开始测试。');
        });
    </script>
</body>
</html>
