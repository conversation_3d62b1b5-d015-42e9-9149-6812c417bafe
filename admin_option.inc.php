<?php
/*
 * 出处：魔趣吧
 * 官网: Www.moqu8.com
 * 备用网址: www.moqu8.com (请收藏备用!)
 * 技术支持/更新维护：QQ 1218894030
 * 
 */
if(!defined('IN_DISCUZ') || !defined('IN_ADMINCP')) {
	exit('Access Denied');
}
require_once DISCUZ_ROOT.'./source/plugin/hejin_forms/config.inc.php';
include_once ('function.func.php');
$pmodel = addslashes($_POST['model']);

if(submitcheck('submitadd')){
	if($pmodel=='add'){
				$j = 0;
		for($i=0;$i<count($_POST['_name']);$i++){//判断_name数组的子元素个数
			$post_add = array(
	       			 'fid'  =>intval($_POST['fid']),               
	       			 'gid'  =>intval($_POST['gid']),              
	        		'name'  =>addslashes($_POST['_name'][$i]),             
	        		'sort'  =>intval($_POST['_sort'][$i]),              
	        		'state'  =>intval($_POST['state']),              
	        		'dateline'  =>time(),              
	        		'stat'  =>0,              
					);	
			$opadd = C::t('#hejin_forms#hejin_option')->insert($post_add);
			$fid = intval($_POST['fid']);
			$gid =  intval($_POST['gid']);
			if($opadd){
				$j =$j +1;
				}
		}
		$gorupin = C::t('#hejin_forms#hejin_group')->fetch_by_id($gid);
		$statnum = $gorupin['stat']+$j;
		
			 $data =array();
			 $data['stat'] = $statnum;
			$addstat =  C::t('#hejin_forms#hejin_group')->update_by_id($data,$gid);
			 			
			if($addstat){
			 $url = 'action=plugins&identifier=hejin_forms&pmod=admin_option&model=list&formid='.$fid.'&groupid='.$gid;
			cpmsg(lang('plugin/hejin_forms', 'addok'), $url, 'succeed');	
				}
		
		
		
		
	}
	
	
	}



if(submitcheck('submitup')){
	if($pmodel=='update'){

			$data = array();
	         $data['name']  =addslashes($_POST['name']);           
	         $data['sort']  =intval($_POST['sort']);      
	         $data['dateline']  =time();              
	         $data['state']  =intval($_POST['state']);          

			 $oid = intval($_POST['oid']);
			 $fid = intval($_POST['fid']);
			 $gid = intval($_POST['gid']);
			  $optionup = C::t('#hejin_forms#hejin_option')->update_by_id($data,$oid);
		if($optionup){
			 $url = 'action=plugins&identifier=hejin_forms&pmod=admin_option&formid='.$fid.'&model=list&groupid='.$gid;
	    		cpmsg(lang('plugin/hejin_forms', 'editok'), $url, 'succeed');	
	
	}
	 

	}
}




$model = addslashes($_GET['model']);
$gmodel = !empty($model) ? $model : 'list';



if($gmodel == 'list'){
	
	$fid = intval($_GET['formid']);
	$gid = intval($_GET['groupid']);

$form = C::t('#hejin_forms#hejin_form')->fetch_by_id($fid);
$group = C::t('#hejin_forms#hejin_group')->fetch_by_id($gid);
	
$options = C::t('#hejin_forms#hejin_option')->fetch_by_gid($gid);

include template('hejin_forms:admin/option_list');
	}

elseif($gmodel == 'add'){
	$fid = intval($_GET['formid']);
	$gid = intval($_GET['groupid']);

$form = C::t('#hejin_forms#hejin_form')->fetch_by_id($fid);
$group = C::t('#hejin_forms#hejin_group')->fetch_by_id($gid);
include template('hejin_forms:admin/option_add');

	}elseif($gmodel == 'del'){
		
		$oid= intval($_GET['oid']);
		$gid = intval($_GET['gid']);
		$fid = intval($_GET['fid']);
		if(!empty($oid)){
			
	   $del= C::t('#hejin_forms#hejin_option')->delete_by_id($oid);
	   
	   if($del){
		$group = C::t('#hejin_forms#hejin_group')->fetch_by_id($gid);
		$gstat = --$group['stat'];
		
		$data =array();
	        $data['stat']  =$gstat;        
		$statj = C::t('#hejin_forms#hejin_group')->update_by_id($data,$gid);
			 
		if($statj){
$url = 'action=plugins&identifier=hejin_forms&pmod=admin_option&formid='.$fid.'&model=list&groupid='.$gid;
			cpmsg(lang('plugin/hejin_forms', 'delok'), $url, 'succeed');				}				
		
		   }
		
			}

		
		}elseif($gmodel == 'edit'){
		$oid= intval($_GET['oid']);
		$gid = intval($_GET['gid']);
		$fid = intval($_GET['fid']);

		$forminfo = C::t('#hejin_forms#hejin_form')->fetch_by_id($fid);
		$groupinfo =  C::t('#hejin_forms#hejin_group')->fetch_by_id($gid);
		    if(!empty($oid)){
				
       $optioninfo = C::t('#hejin_forms#hejin_option')->fetch_by_id($oid);
	   
       }
	
		include template('hejin_forms:admin/option_edit');
			}



?>