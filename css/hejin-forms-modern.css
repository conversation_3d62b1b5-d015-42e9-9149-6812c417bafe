/**
 * 和金表单现代化样式 - Element UI风格
 * 扁平化设计 + 动画效果 + 阴影
 */

/* ===== 基础变量 ===== */
:root {
    --primary-color: #409EFF;
    --success-color: #67C23A;
    --warning-color: #E6A23C;
    --danger-color: #F56C6C;
    --info-color: #909399;
    
    --text-primary: #303133;
    --text-regular: #606266;
    --text-secondary: #909399;
    --text-placeholder: #C0C4CC;
    
    --border-base: #DCDFE6;
    --border-light: #E4E7ED;
    --border-lighter: #EBEEF5;
    --border-extra-light: #F2F6FC;
    
    --background-base: #F5F7FA;
    --background-light: #FAFAFA;
    --background-white: #FFFFFF;
    
    --box-shadow-base: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04);
    --box-shadow-dark: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .12);
    --box-shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    
    --border-radius-base: 4px;
    --border-radius-small: 2px;
    --border-radius-large: 6px;
    
    --transition-base: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    --transition-fast: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}

/* ===== 全局重置 ===== */
* {
    box-sizing: border-box;
}

/* ===== 表单容器 ===== */
.hf-form-container {
    background: var(--background-white);
    border-radius: var(--border-radius-large);
    box-shadow: var(--box-shadow-light);
    padding: 32px;
    margin: 20px auto;
    max-width: 800px;
    position: relative;
    overflow: hidden;
}

.hf-form-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--success-color));
    border-radius: var(--border-radius-large) var(--border-radius-large) 0 0;
}

/* ===== 表单标题 ===== */
.hf-form-title {
    font-size: 24px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
    text-align: center;
    position: relative;
}

.hf-form-description {
    font-size: 14px;
    color: var(--text-secondary);
    text-align: center;
    margin-bottom: 32px;
    line-height: 1.6;
}

/* ===== 字段容器 ===== */
.field {
    margin-bottom: 24px;
    position: relative;
    transition: var(--transition-base);
}

.field:hover {
    transform: translateY(-1px);
}

/* ===== 标签样式 ===== */
.control-label {
    display: block;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-regular);
    margin-bottom: 8px;
    position: relative;
    transition: var(--transition-fast);
}

.control-label .required {
    color: var(--danger-color);
    margin-left: 4px;
    font-weight: 600;
}

/* ===== 输入框基础样式 ===== */
.hf-input-base {
    width: 100%;
    padding: 12px 16px;
    font-size: 14px;
    color: var(--text-regular);
    background-color: var(--background-white);
    border: 1px solid var(--border-base);
    border-radius: var(--border-radius-base);
    transition: var(--transition-fast);
    outline: none;
    position: relative;
}

.hf-input-base:hover {
    border-color: var(--text-secondary);
}

.hf-input-base:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.hf-input-base::placeholder {
    color: var(--text-placeholder);
}

/* ===== 具体输入控件 ===== */
input[type="text"],
input[type="email"],
input[type="tel"],
input[type="password"],
input[type="number"],
input[type="url"] {
    @extend .hf-input-base;
    height: 40px;
}

textarea {
    @extend .hf-input-base;
    min-height: 80px;
    resize: vertical;
    line-height: 1.5;
    font-family: inherit;
}

select {
    @extend .hf-input-base;
    height: 40px;
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 12px center;
    background-repeat: no-repeat;
    background-size: 16px;
    padding-right: 40px;
    appearance: none;
}

/* ===== 文件上传样式 ===== */
.hf-file-upload {
    position: relative;
    display: inline-block;
    width: 100%;
}

.hf-file-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
}

.hf-file-trigger {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    border: 2px dashed var(--border-base);
    border-radius: var(--border-radius-base);
    background: var(--background-light);
    color: var(--text-secondary);
    font-size: 14px;
    cursor: pointer;
    transition: var(--transition-fast);
    min-height: 100px;
    flex-direction: column;
    gap: 8px;
}

.hf-file-trigger:hover {
    border-color: var(--primary-color);
    background: rgba(64, 158, 255, 0.05);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-base);
}

.hf-file-icon {
    font-size: 32px;
    opacity: 0.6;
}

.hf-file-text {
    font-weight: 500;
}

.hf-file-hint {
    font-size: 12px;
    color: var(--text-placeholder);
    margin-top: 4px;
}

/* ===== 拖拽上传样式 ===== */
.hf-drop-zone {
    border: 2px dashed var(--border-base);
    border-radius: var(--border-radius-large);
    padding: 40px 20px;
    text-align: center;
    background: var(--background-light);
    transition: var(--transition-base);
    position: relative;
    overflow: hidden;
}

.hf-drop-zone::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(64, 158, 255, 0.1), transparent);
    transition: left 0.6s ease;
}

.hf-drop-zone:hover::before {
    left: 100%;
}

.hf-drop-zone:hover {
    border-color: var(--primary-color);
    background: rgba(64, 158, 255, 0.05);
    transform: scale(1.02);
    box-shadow: var(--box-shadow-base);
}

.hf-drag-over {
    border-color: var(--primary-color) !important;
    background: rgba(64, 158, 255, 0.1) !important;
    transform: scale(1.05) !important;
    box-shadow: var(--box-shadow-dark) !important;
}

.hf-drop-content {
    position: relative;
    z-index: 1;
}

.hf-drop-icon {
    font-size: 48px;
    color: var(--text-secondary);
    margin-bottom: 16px;
    display: block;
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.hf-drop-text {
    color: var(--text-regular);
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
}

.hf-browse-link {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 600;
    border-bottom: 1px solid transparent;
    transition: var(--transition-fast);
}

.hf-browse-link:hover {
    border-bottom-color: var(--primary-color);
}

.hf-drop-hint {
    font-size: 12px;
    color: var(--text-placeholder);
    margin-top: 8px;
}

/* ===== 单选框和复选框 ===== */
.radio-group,
.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-top: 8px;
}

.hf-radio,
.hf-checkbox {
    position: relative;
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    padding: 8px 16px;
    border: 1px solid var(--border-base);
    border-radius: var(--border-radius-base);
    background: var(--background-white);
    transition: var(--transition-fast);
    user-select: none;
}

.hf-radio:hover,
.hf-checkbox:hover {
    border-color: var(--primary-color);
    background: rgba(64, 158, 255, 0.05);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.15);
}

.hf-radio input,
.hf-checkbox input {
    margin-right: 8px;
    transform: scale(1.2);
    accent-color: var(--primary-color);
}

.hf-radio.checked,
.hf-checkbox.checked {
    border-color: var(--primary-color);
    background: rgba(64, 158, 255, 0.1);
    color: var(--primary-color);
    font-weight: 500;
}

/* ===== 按钮样式 ===== */
.hf-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 500;
    border-radius: var(--border-radius-base);
    border: 1px solid transparent;
    cursor: pointer;
    transition: var(--transition-fast);
    text-decoration: none;
    outline: none;
    position: relative;
    overflow: hidden;
}

.hf-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.hf-btn:active::before {
    width: 300px;
    height: 300px;
}

.hf-btn-primary {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 2px 4px rgba(64, 158, 255, 0.3);
}

.hf-btn-primary:hover {
    background: #337ecc;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
}

.hf-btn-success {
    background: var(--success-color);
    color: white;
    box-shadow: 0 2px 4px rgba(103, 194, 58, 0.3);
}

.hf-btn-success:hover {
    background: #529b2e;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(103, 194, 58, 0.4);
}

.hf-btn-danger {
    background: var(--danger-color);
    color: white;
    box-shadow: 0 2px 4px rgba(245, 108, 108, 0.3);
}

.hf-btn-danger:hover {
    background: #dd6161;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(245, 108, 108, 0.4);
}

.hf-btn:disabled {
    background: var(--text-placeholder) !important;
    color: white !important;
    cursor: not-allowed !important;
    transform: none !important;
    box-shadow: none !important;
}

/* ===== 提交按钮特殊样式 ===== */
.submit-field {
    text-align: center;
    margin-top: 40px;
    padding-top: 24px;
    border-top: 1px solid var(--border-lighter);
}

.submit {
    @extend .hf-btn;
    @extend .hf-btn-primary;
    min-width: 120px;
    height: 44px;
    font-size: 16px;
    font-weight: 600;
    border-radius: var(--border-radius-large);
}

/* ===== 验证状态样式 ===== */
.field.hf-success .hf-input-base {
    border-color: var(--success-color);
    box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2);
}

.field.hf-success .control-label::after {
    content: "✓";
    color: var(--success-color);
    margin-left: 8px;
    font-weight: 600;
    animation: checkmark 0.5s ease;
}

@keyframes checkmark {
    0% { transform: scale(0); opacity: 0; }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); opacity: 1; }
}

.field.field_with_errors .hf-input-base {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2);
}

.field.field_with_errors .control-label::after {
    content: "✗";
    color: var(--danger-color);
    margin-left: 8px;
    font-weight: 600;
    animation: errormark 0.5s ease;
}

@keyframes errormark {
    0% { transform: scale(0) rotate(0deg); opacity: 0; }
    50% { transform: scale(1.2) rotate(180deg); }
    100% { transform: scale(1) rotate(360deg); opacity: 1; }
}

.field.hf-loading .hf-input-base {
    border-color: var(--primary-color);
    position: relative;
}

.field.hf-loading::after {
    content: "";
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    border: 2px solid var(--border-lighter);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
}

/* ===== 错误提示 ===== */
.hf-field-error {
    color: var(--danger-color);
    font-size: 12px;
    margin-top: 6px;
    padding: 6px 12px;
    background: rgba(245, 108, 108, 0.1);
    border-radius: var(--border-radius-small);
    border-left: 3px solid var(--danger-color);
    animation: slideDown 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.hf-field-error::before {
    content: "⚠";
    font-weight: 600;
}

@keyframes slideDown {
    0% {
        opacity: 0;
        transform: translateY(-10px);
        max-height: 0;
    }
    100% {
        opacity: 1;
        transform: translateY(0);
        max-height: 50px;
    }
}

/* ===== 帮助文本 ===== */
.help-block {
    margin-bottom: 8px;
}

.help-block p {
    font-size: 13px;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.4;
}

/* ===== 分割线 ===== */
.section-break {
    margin: 32px 0;
}

.section-break hr {
    border: none;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--border-base), transparent);
    margin: 0;
}

/* ===== 加载遮罩 ===== */
.hf-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(4px);
}

.hf-loading-spinner {
    background: var(--background-white);
    padding: 40px;
    border-radius: var(--border-radius-large);
    text-align: center;
    box-shadow: var(--box-shadow-dark);
    animation: fadeInScale 0.3s ease;
}

@keyframes fadeInScale {
    0% {
        opacity: 0;
        transform: scale(0.8);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.hf-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-lighter);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 16px;
}

.hf-loading-text {
    color: var(--text-regular);
    font-size: 14px;
    font-weight: 500;
}

/* ===== 消息提示 ===== */
.hf-message {
    padding: 12px 16px;
    margin-bottom: 16px;
    border-radius: var(--border-radius-base);
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    animation: slideDown 0.3s ease;
    box-shadow: var(--box-shadow-base);
}

.hf-message::before {
    font-weight: 600;
    font-size: 16px;
}

.hf-message-info {
    background: rgba(64, 158, 255, 0.1);
    color: var(--primary-color);
    border-left: 4px solid var(--primary-color);
}

.hf-message-info::before {
    content: "ℹ";
}

.hf-message-success {
    background: rgba(103, 194, 58, 0.1);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.hf-message-success::before {
    content: "✓";
}

.hf-message-error {
    background: rgba(245, 108, 108, 0.1);
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

.hf-message-error::before {
    content: "✗";
}

.hf-message-warning {
    background: rgba(230, 162, 60, 0.1);
    color: var(--warning-color);
    border-left: 4px solid var(--warning-color);
}

.hf-message-warning::before {
    content: "⚠";
}

/* ===== 成功页面 ===== */
.hf-success-page {
    text-align: center;
    padding: 60px 20px;
    background: var(--background-white);
    border-radius: var(--border-radius-large);
    box-shadow: var(--box-shadow-light);
    animation: successPageIn 0.6s ease;
}

@keyframes successPageIn {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.hf-success-icon {
    font-size: 80px;
    color: var(--success-color);
    margin-bottom: 24px;
    display: block;
    animation: successBounce 0.8s ease;
}

@keyframes successBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-20px);
    }
    60% {
        transform: translateY(-10px);
    }
}

.hf-success-page h2 {
    color: var(--text-primary);
    margin-bottom: 16px;
    font-size: 28px;
    font-weight: 600;
}

.hf-success-page p {
    color: var(--text-secondary);
    margin-bottom: 32px;
    font-size: 16px;
    line-height: 1.6;
}

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
    .hf-form-container {
        margin: 10px;
        padding: 20px;
        border-radius: var(--border-radius-base);
    }
    
    .hf-form-title {
        font-size: 20px;
    }
    
    .field {
        margin-bottom: 20px;
    }
    
    .radio-group,
    .checkbox-group {
        flex-direction: column;
        gap: 8px;
    }
    
    .hf-drop-zone {
        padding: 30px 15px;
    }
    
    .hf-drop-icon {
        font-size: 36px;
    }
    
    .hf-success-page {
        padding: 40px 15px;
    }
    
    .hf-success-icon {
        font-size: 60px;
    }
    
    .hf-success-page h2 {
        font-size: 24px;
    }
}

/* ===== 深色模式支持 ===== */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #E4E7ED;
        --text-regular: #CFD3DC;
        --text-secondary: #A3A6AD;
        --text-placeholder: #6C6E72;
        
        --border-base: #4C4D4F;
        --border-light: #414243;
        --border-lighter: #363637;
        --border-extra-light: #2B2B2C;
        
        --background-base: #1D1E1F;
        --background-light: #25262A;
        --background-white: #2B2F36;
    }
}

/* ===== 打印样式 ===== */
@media print {
    .hf-loading-overlay,
    .hf-btn,
    .submit-field {
        display: none !important;
    }
    
    .hf-form-container {
        box-shadow: none;
        border: 1px solid #ccc;
    }
    
    .field {
        break-inside: avoid;
    }
}
