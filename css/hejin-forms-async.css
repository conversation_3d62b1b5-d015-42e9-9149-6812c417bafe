/**
 * 和金表单异步样式
 * 现代化的表单样式和动画效果
 */

/* 加载遮罩 */
.hf-loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.hf-loading-spinner {
    background: white;
    padding: 30px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.hf-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #3498db;
    border-radius: 50%;
    animation: hf-spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes hf-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.hf-loading-text {
    color: #666;
    font-size: 14px;
}

/* 字段验证状态 */
.field.hf-success {
    border-left: 3px solid #27ae60;
}

.field.field_with_errors {
    border-left: 3px solid #e74c3c;
}

.hf-field-error {
    color: #e74c3c;
    font-size: 12px;
    margin-top: 5px;
    display: none;
    animation: hf-fadeIn 0.3s ease-in;
}

@keyframes hf-fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 成功状态图标 */
.field.hf-success .control-label::after {
    content: "✓";
    color: #27ae60;
    margin-left: 5px;
    font-weight: bold;
}

/* 错误状态图标 */
.field.field_with_errors .control-label::after {
    content: "✗";
    color: #e74c3c;
    margin-left: 5px;
    font-weight: bold;
}

/* 文件上传进度 */
.hf-upload-progress {
    margin-top: 10px;
    display: none;
}

.hf-progress-bar {
    width: 100%;
    height: 20px;
    background-color: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.hf-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.hf-progress-text {
    text-align: center;
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

/* 消息提示 */
.hf-message {
    padding: 12px 15px;
    margin-bottom: 15px;
    border-radius: 5px;
    font-size: 14px;
    animation: hf-slideDown 0.3s ease-out;
}

.hf-message-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.hf-message-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.hf-message-error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.hf-message-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

@keyframes hf-slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 成功页面 */
.hf-success-page {
    text-align: center;
    padding: 50px 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.hf-success-icon {
    font-size: 60px;
    color: #27ae60;
    margin-bottom: 20px;
    animation: hf-bounce 0.6s ease-out;
}

@keyframes hf-bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-20px);
    }
    60% {
        transform: translateY(-10px);
    }
}

.hf-success-page h2 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 24px;
}

.hf-success-page p {
    color: #7f8c8d;
    margin-bottom: 30px;
    font-size: 16px;
}

.hf-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 5px;
    font-size: 16px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.hf-btn:hover {
    background: #2980b9;
}

/* 表单字段增强 */
.field input[type="text"],
.field input[type="email"],
.field input[type="tel"],
.field textarea,
.field select {
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.field input[type="text"]:focus,
.field input[type="email"]:focus,
.field input[type="tel"]:focus,
.field textarea:focus,
.field select:focus {
    border-color: #3498db;
    box-shadow: 0 0 5px rgba(52, 152, 219, 0.3);
    outline: none;
}

/* 文件上传区域增强 */
.field input[type="file"] {
    padding: 10px;
    border: 2px dashed #ddd;
    border-radius: 5px;
    background: #f9f9f9;
    transition: border-color 0.3s ease;
}

.field input[type="file"]:hover {
    border-color: #3498db;
    background: #f0f8ff;
}

/* 拖拽上传区域 */
.hf-drop-zone {
    border: 2px dashed #ddd;
    border-radius: 8px;
    padding: 30px;
    text-align: center;
    background: #fafafa;
    transition: all 0.3s ease;
    cursor: pointer;
    margin-bottom: 15px;
}

.hf-drop-zone:hover {
    border-color: #3498db;
    background: #f0f8ff;
}

.hf-drag-over {
    border-color: #3498db !important;
    background: #e3f2fd !important;
    transform: scale(1.02);
}

.hf-drop-content {
    pointer-events: none;
}

.hf-drop-icon {
    font-size: 48px;
    margin-bottom: 15px;
    opacity: 0.6;
}

.hf-drop-text p {
    margin: 5px 0;
    color: #666;
}

.hf-browse-link {
    color: #3498db;
    text-decoration: underline;
    cursor: pointer;
    pointer-events: all;
}

.hf-browse-link:hover {
    color: #2980b9;
}

.hf-drop-hint {
    font-size: 12px;
    color: #999;
}

/* 文件列表 */
.hf-file-list {
    margin-top: 20px;
}

.hf-file-item {
    display: flex;
    align-items: center;
    padding: 12px;
    border: 1px solid #eee;
    border-radius: 6px;
    margin-bottom: 10px;
    background: white;
    transition: box-shadow 0.2s ease;
}

.hf-file-item:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.hf-file-info {
    flex: 1;
    text-align: left;
}

.hf-file-name {
    display: block;
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
}

.hf-file-size {
    font-size: 12px;
    color: #666;
}

.hf-file-progress {
    flex: 0 0 150px;
    margin: 0 15px;
}

.hf-file-progress .hf-progress-bar {
    height: 6px;
    background: #f0f0f0;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 4px;
}

.hf-file-progress .hf-progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    transition: width 0.3s ease;
    border-radius: 3px;
}

.hf-file-progress .hf-progress-text {
    font-size: 11px;
    color: #666;
    text-align: center;
    display: block;
}

.hf-file-status {
    flex: 0 0 80px;
    font-size: 12px;
    text-align: center;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 500;
}

.hf-file-status.hf-uploading {
    background: #fff3cd;
    color: #856404;
}

.hf-file-status.hf-success {
    background: #d4edda;
    color: #155724;
}

.hf-file-status.hf-error {
    background: #f8d7da;
    color: #721c24;
}

/* 多文件上传增强 */
.hf-drop-zone.hf-multiple {
    min-height: 120px;
}

.hf-drop-zone.hf-multiple .hf-drop-text {
    margin-bottom: 20px;
}

/* 文件类型图标 */
.hf-file-item::before {
    content: "📄";
    font-size: 20px;
    margin-right: 10px;
}

.hf-file-item[data-type*="image"]::before {
    content: "🖼️";
}

.hf-file-item[data-type*="doc"]::before {
    content: "📝";
}

.hf-file-item[data-type*="xls"]::before {
    content: "📊";
}

.hf-file-item[data-type*="pdf"]::before {
    content: "📕";
}

/* 响应式设计 */
@media (max-width: 768px) {
    .hf-loading-spinner {
        padding: 20px;
    }
    
    .hf-spinner {
        width: 30px;
        height: 30px;
    }
    
    .hf-success-page {
        padding: 30px 15px;
    }
    
    .hf-success-icon {
        font-size: 40px;
    }
    
    .hf-success-page h2 {
        font-size: 20px;
    }
    
    .hf-success-page p {
        font-size: 14px;
    }
}

/* 提交按钮状态 */
.submit:disabled {
    background: #bdc3c7 !important;
    cursor: not-allowed !important;
    opacity: 0.6;
}

/* 实时验证动画 */
.field {
    transition: border-left 0.3s ease;
}

/* 必填字段标识 */
.control-label span[style*="color: red"] {
    color: #e74c3c !important;
    font-weight: bold;
}

/* 日期时间选择器增强 */
.xdsoft_datetimepicker {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
    border: 1px solid #ddd !important;
    border-radius: 5px !important;
}

/* 单选框和复选框增强 */
.radio-group label {
    transition: background-color 0.2s ease;
    padding: 8px 12px;
    border-radius: 4px;
    margin: 2px;
    display: inline-block;
}

.radio-group label:hover {
    background-color: #f8f9fa;
}

.radio-group input[type="radio"]:checked + span,
.radio-group input[type="checkbox"]:checked + span {
    color: #3498db;
    font-weight: bold;
}

/* 选择框增强 */
select {
    appearance: none;
    background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="%23666" d="M2 0L0 2h4zm0 5L0 3h4z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 10px center;
    background-size: 12px;
    padding-right: 30px;
}

/* 加载状态的字段 */
.field.hf-loading {
    position: relative;
}

.field.hf-loading::after {
    content: "";
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #3498db;
    border-radius: 50%;
    animation: hf-spin 1s linear infinite;
}

/* 成功和错误动画 */
@keyframes hf-success-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); box-shadow: 0 0 20px rgba(39, 174, 96, 0.3); }
    100% { transform: scale(1); }
}

@keyframes hf-error-shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* 草稿相关样式 */
.hf-draft-prompt {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 20px;
    animation: hf-slideDown 0.3s ease-out;
}

.hf-draft-content h4 {
    margin: 0 0 10px 0;
    color: #856404;
    font-size: 16px;
}

.hf-draft-content p {
    margin: 0 0 15px 0;
    color: #856404;
    font-size: 14px;
}

.hf-draft-actions {
    display: flex;
    gap: 10px;
}

.hf-btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.2s ease;
}

.hf-btn-primary {
    background: #3498db;
    color: white;
}

.hf-btn-primary:hover {
    background: #2980b9;
}

.hf-btn-secondary {
    background: #95a5a6;
    color: white;
}

.hf-btn-secondary:hover {
    background: #7f8c8d;
}

/* 草稿保存指示器 */
.hf-draft-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #27ae60;
    color: white;
    padding: 10px 15px;
    border-radius: 4px;
    font-size: 14px;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
    display: none;
}

/* 工具提示 */
.hf-tooltip {
    position: relative;
    cursor: help;
    margin-left: 5px;
}

.hf-tooltip::before {
    content: "?";
    display: inline-block;
    width: 16px;
    height: 16px;
    background: #3498db;
    color: white;
    border-radius: 50%;
    text-align: center;
    font-size: 12px;
    line-height: 16px;
    font-weight: bold;
}

.hf-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease;
    z-index: 1000;
}

.hf-tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

/* 文件上传提示 */
.hf-file-hint {
    margin-top: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #3498db;
}

.hf-file-hint small {
    color: #666;
    font-size: 12px;
    line-height: 1.4;
}

/* 键盘快捷键提示 */
.hf-shortcuts-hint {
    position: fixed;
    bottom: 20px;
    left: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 15px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 1000;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.hf-shortcuts-hint.show {
    opacity: 1;
}

/* 表单进度指示器 */
.hf-progress-indicator {
    position: sticky;
    top: 0;
    background: white;
    border-bottom: 1px solid #eee;
    padding: 10px 0;
    z-index: 100;
}

.hf-progress-bar-container {
    width: 100%;
    height: 4px;
    background: #f0f0f0;
    border-radius: 2px;
    overflow: hidden;
}

.hf-progress-bar-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    width: 0%;
    transition: width 0.3s ease;
}

.hf-progress-text {
    text-align: center;
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

/* 响应式增强 */
@media (max-width: 768px) {
    .hf-draft-indicator {
        top: 10px;
        right: 10px;
        font-size: 12px;
        padding: 8px 12px;
    }

    .hf-shortcuts-hint {
        bottom: 10px;
        left: 10px;
        font-size: 11px;
        padding: 8px 12px;
    }

    .hf-draft-actions {
        flex-direction: column;
    }

    .hf-tooltip::after {
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        white-space: normal;
        max-width: 200px;
    }
}
