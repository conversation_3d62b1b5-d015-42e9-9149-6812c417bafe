# 和金表单异步版本升级说明

## 概述

本次升级将原有的Discuz X3.4表单插件改造为现代化的异步版本，提供更好的用户体验和性能。

## 主要改进

### 1. 异步表单提交
- 使用Ajax技术实现无刷新提交
- 实时验证表单字段
- 智能错误提示和成功反馈

### 2. 文件上传增强
- 支持拖拽上传
- 实时上传进度显示
- 多文件上传支持
- 文件类型和大小验证

### 3. 用户体验优化
- 自动保存草稿功能
- 键盘快捷键支持
- 加载动画和状态指示
- 响应式设计

### 4. 性能优化
- 验证结果缓存
- 防抖处理
- 异步加载

## 文件结构

```
hejin_forms/
├── api.inc.php                    # 新增：API接口文件
├── js/hejin-forms-async.js       # 新增：异步JavaScript库
├── css/hejin-forms-async.css     # 新增：异步样式文件
├── hejin_forms.inc.php           # 修改：主文件，添加API路由
├── template/form/show.htm        # 修改：表单模板，集成异步功能
└── README_ASYNC.md               # 新增：升级说明文档
```

## 安装说明

### 1. 备份原文件
在升级前，请备份以下文件：
- `hejin_forms.inc.php`
- `template/form/show.htm`

### 2. 上传新文件
将以下新文件上传到插件目录：
- `api.inc.php`
- `js/hejin-forms-async.js`
- `css/hejin-forms-async.css`

### 3. 替换修改的文件
用新版本替换：
- `hejin_forms.inc.php`
- `template/form/show.htm`

## 兼容性

### 浏览器支持
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- IE 11（基础功能）

### Discuz版本
- Discuz! X3.4
- 向下兼容X3.2、X3.3

### PHP版本
- PHP 5.6+
- 推荐PHP 7.0+

## 功能说明

### 异步验证
表单字段支持实时验证，包括：
- 必填字段检查
- 数据格式验证（邮箱、手机号、身份证等）
- 自定义验证规则

### 文件上传
增强的文件上传功能：
- 拖拽文件到上传区域
- 实时显示上传进度
- 支持的文件类型：jpg, jpeg, gif, png, doc, docx, xls, xlsx, wps
- 最大文件大小：10MB

### 草稿保存
自动保存功能：
- 每2秒自动保存表单数据
- 页面刷新后可恢复未完成的表单
- 草稿有效期7天
- 支持手动保存（Ctrl+S）

### 键盘快捷键
- `Ctrl+S`：保存草稿
- `Ctrl+Enter`：提交表单
- `Esc`：清除当前输入字段

## API接口

### 获取表单信息
```
GET plugin.php?id=hejin_forms&action=get_form&formid=123
```

### 验证字段
```
POST plugin.php?id=hejin_forms&action=validate_field
参数：field_id, value, form_id
```

### 提交表单
```
POST plugin.php?id=hejin_forms&action=submit_form
参数：表单数据 + action=submit_form
```

### 文件上传
```
POST plugin.php?id=hejin_forms&action=upload_file
参数：field_name, 文件数据
```

## 配置选项

可以通过修改JavaScript初始化参数来自定义行为：

```javascript
window.hejinForms = new HejinFormsAsync({
    formSelector: '#new_entry',        // 表单选择器
    apiUrl: 'plugin.php?id=hejin_forms', // API地址
    loadingClass: 'hf-loading',        // 加载状态CSS类
    errorClass: 'field_with_errors',   // 错误状态CSS类
    successClass: 'hf-success'         // 成功状态CSS类
});
```

## 故障排除

### 常见问题

1. **异步功能不工作**
   - 检查JavaScript文件是否正确加载
   - 确认浏览器支持ES6语法
   - 查看浏览器控制台错误信息

2. **文件上传失败**
   - 检查PHP上传限制设置
   - 确认目录权限（data/uploads/submit需要写权限）
   - 验证文件类型和大小

3. **验证不生效**
   - 确认API接口可以正常访问
   - 检查表单字段的name属性格式
   - 验证数据库表结构

### 调试模式

在浏览器控制台中启用调试：
```javascript
window.hejinForms.options.debug = true;
```

## 性能优化建议

1. **启用Gzip压缩**
2. **使用CDN加速静态资源**
3. **开启浏览器缓存**
4. **定期清理过期草稿数据**

## 更新日志

### v2.0.0 (异步版本)
- 新增异步表单提交功能
- 新增拖拽文件上传
- 新增草稿自动保存
- 新增实时字段验证
- 新增键盘快捷键支持
- 优化用户界面和体验
- 提升性能和兼容性

## 技术支持

如遇到问题，请提供以下信息：
- Discuz版本
- PHP版本
- 浏览器版本
- 错误信息截图
- 浏览器控制台错误日志

## 许可证

本插件遵循原有许可证条款。

---

**注意：** 升级前请务必备份数据，建议先在测试环境中验证功能正常后再部署到生产环境。
