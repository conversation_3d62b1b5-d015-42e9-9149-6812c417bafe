<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>和金表单测试页面</title>
    <link href="css/hejin-forms-async.css" rel="stylesheet" type="text/css">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .debug-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        .error-info {
            background: #ffebee;
            color: #c62828;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>和金表单异步功能测试</h1>
        
        <div class="debug-info">
            <h3>调试信息</h3>
            <div id="debug-output"></div>
        </div>

        <form id="new_entry" method="post" enctype="multipart/form-data">
            <input type="hidden" name="formhash" value="test123" />
            <input type="hidden" name="fid" value="1" />
            <input type="hidden" name="uid" value="1" />
            <input type="hidden" name="account" value="testuser" />

            <h2>测试表单</h2>
            <p>这是一个用于测试异步功能的表单</p>

            <div class="field">
                <label class="control-label" for="G-1">
                    姓名 <span style="color: red;">*</span>
                </label>
                <div class="field_content">
                    <div class="help-block"><p>请输入您的姓名</p></div>
                    <div class="controls">
                        <input name='G-1' id='G-1' style="height:30px; line-height:30px;" type='text' value='' size='30' fix_name='姓名' required />
                    </div>
                </div>
            </div>

            <div class="section-break">
                <hr>
            </div>

            <div class="field">
                <label class="control-label" for="G-2">
                    邮箱 <span style="color: red;">*</span>
                </label>
                <div class="field_content">
                    <div class="help-block"><p>请输入您的邮箱地址</p></div>
                    <div class="controls">
                        <input name='G-2' id='G-2' style="height:30px; line-height:30px;" type='email' value='' size='30' fix_name='邮箱' required />
                    </div>
                </div>
            </div>

            <div class="section-break">
                <hr>
            </div>

            <div class="field">
                <label class="control-label" for="G-3">
                    手机号
                </label>
                <div class="field_content">
                    <div class="help-block"><p>请输入您的手机号码</p></div>
                    <div class="controls">
                        <input name='G-3' id='G-3' style="height:30px; line-height:30px;" type='tel' value='' size='30' fix_name='手机号' />
                    </div>
                </div>
            </div>

            <div class="section-break">
                <hr>
            </div>

            <div class="field">
                <label class="control-label" for="G-4">
                    文件上传
                </label>
                <div class="field_content">
                    <div class="help-block"><p>请选择要上传的文件</p></div>
                    <div class="controls">
                        <input name='G-4' id='G-4' type='file' fix_name='文件' />
                    </div>
                </div>
            </div>

            <div class="section-break">
                <hr>
            </div>

            <div class="field submit-field">
                <div class="value">
                    <input id="submita" class="submit" name="commit" value="提交表单" type="submit">
                </div>
            </div>
        </form>

        <div class="debug-info" style="margin-top: 30px;">
            <h3>测试功能</h3>
            <button onclick="testValidation()" style="margin: 5px; padding: 8px 16px; background: #007cba; color: white; border: none; border-radius: 3px;">测试字段验证</button>
            <button onclick="testDraft()" style="margin: 5px; padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 3px;">测试草稿保存</button>
            <button onclick="testAPI()" style="margin: 5px; padding: 8px 16px; background: #17a2b8; color: white; border: none; border-radius: 3px;">测试API</button>
            <button onclick="clearDebug()" style="margin: 5px; padding: 8px 16px; background: #6c757d; color: white; border: none; border-radius: 3px;">清除日志</button>
        </div>
    </div>

    <script src="js/hejin-forms-async.js"></script>
    <script>
        // 调试输出函数
        function debugLog(message, type = 'info') {
            const output = document.getElementById('debug-output');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'red' : type === 'success' ? 'green' : 'blue';
            output.innerHTML += `<div style="color: ${color}; margin: 2px 0;">[${timestamp}] ${message}</div>`;
            output.scrollTop = output.scrollHeight;
        }

        function clearDebug() {
            document.getElementById('debug-output').innerHTML = '';
        }

        // 测试字段验证
        function testValidation() {
            debugLog('开始测试字段验证...');
            
            const nameField = document.getElementById('G-1');
            const emailField = document.getElementById('G-2');
            
            if (nameField && emailField) {
                // 触发验证事件
                nameField.value = 'Test User';
                nameField.dispatchEvent(new Event('input'));
                
                emailField.value = '<EMAIL>';
                emailField.dispatchEvent(new Event('input'));
                
                debugLog('字段验证测试完成', 'success');
            } else {
                debugLog('找不到测试字段', 'error');
            }
        }

        // 测试草稿保存
        function testDraft() {
            debugLog('开始测试草稿保存...');
            
            if (window.hejinForms && typeof window.hejinForms.saveDraft === 'function') {
                window.hejinForms.saveDraft();
                debugLog('草稿保存测试完成', 'success');
            } else {
                debugLog('草稿保存功能不可用', 'error');
            }
        }

        // 测试API
        function testAPI() {
            debugLog('开始测试API连接...');
            
            // 简单的API测试
            fetch('plugin.php?id=hejin_forms&action=get_form&formid=1', {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                debugLog(`API响应状态: ${response.status}`);
                return response.text();
            })
            .then(data => {
                debugLog(`API响应数据: ${data.substring(0, 100)}...`);
                try {
                    const jsonData = JSON.parse(data);
                    debugLog('API返回有效JSON数据', 'success');
                } catch (e) {
                    debugLog('API返回非JSON数据，可能是HTML页面', 'error');
                }
            })
            .catch(error => {
                debugLog(`API测试失败: ${error.message}`, 'error');
            });
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('页面加载完成');
            
            // 检查异步库是否加载
            if (typeof HejinFormsAsync !== 'undefined') {
                debugLog('HejinFormsAsync 类已加载', 'success');
                
                // 检查是否自动初始化
                if (window.hejinForms) {
                    debugLog('异步表单已自动初始化', 'success');
                } else {
                    debugLog('尝试手动初始化异步表单...');
                    try {
                        window.hejinForms = new HejinFormsAsync({
                            apiUrl: 'plugin.php?id=hejin_forms'
                        });
                        debugLog('异步表单手动初始化成功', 'success');
                    } catch (e) {
                        debugLog(`异步表单初始化失败: ${e.message}`, 'error');
                    }
                }
            } else {
                debugLog('HejinFormsAsync 类未加载，请检查JS文件路径', 'error');
            }

            // 检查CSS是否加载
            const testElement = document.createElement('div');
            testElement.className = 'hf-loading';
            document.body.appendChild(testElement);
            const styles = window.getComputedStyle(testElement);
            if (styles.position === 'relative') {
                debugLog('异步CSS样式已加载', 'success');
            } else {
                debugLog('异步CSS样式未加载，请检查CSS文件路径', 'error');
            }
            document.body.removeChild(testElement);

            // 添加表单事件监听
            const form = document.getElementById('new_entry');
            if (form) {
                form.addEventListener('submit', function(e) {
                    debugLog('表单提交事件触发');
                });
            }
        });

        // 键盘快捷键提示
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                debugLog('检测到 Ctrl+S 快捷键');
            }
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                debugLog('检测到 Ctrl+Enter 快捷键');
            }
        });
    </script>
</body>
</html>
